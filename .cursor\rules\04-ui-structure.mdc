---
description:
globs:
alwaysApply: false
---
# UI Structure and Styling

VisionPoint uses a modular UI structure with WPF components organized in multiple directories.

## Views
The [views](mdc:VisionPoint.UI/views/) directory contains:
- **Windows**: Main application windows
- **Pages**: Content pages for different application sections
- **Dialogs**: Modal dialog windows

## Controls
The [Controls](mdc:VisionPoint.UI/Controls/) directory contains custom UI controls used throughout the application.

## Styling
The application uses WPF styling with resources defined in:
- [styles](mdc:VisionPoint.UI/styles/): Style resources
- [themes](mdc:VisionPoint.UI/themes/): Theme definitions

## Value Converters
The [converters](mdc:VisionPoint.UI/converters/) directory contains WPF value converters that transform data for UI display.

## Assets
The [Assets](mdc:VisionPoint.UI/Assets/) directory contains static resources like images used in the UI.
