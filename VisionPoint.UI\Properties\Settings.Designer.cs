﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace VisionPoint.UI.Properties {
    
    
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "17.13.0.0")]
    internal sealed partial class Settings : global::System.Configuration.ApplicationSettingsBase {
        
        private static Settings defaultInstance = ((Settings)(global::System.Configuration.ApplicationSettingsBase.Synchronized(new Settings())));
        
        public static Settings Default {
            get {
                return defaultInstance;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("Microsoft Print to PDf")]
        public string DefaultPrinter {
            get {
                return ((string)(this["DefaultPrinter"]));
            }
            set {
                this["DefaultPrinter"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("سوق الجمعة -عمارة مصرف الادخار مقبل كلية سوق الجمعة")]
        public string ArabicAddress {
            get {
                return ((string)(this["ArabicAddress"]));
            }
            set {
                this["ArabicAddress"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("Souq Al Juma\'a - Al Idikhar Bank Building, In front of Souq Al Juma\'a College")]
        public string EnglishAddress {
            get {
                return ((string)(this["EnglishAddress"]));
            }
            set {
                this["EnglishAddress"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("**********")]
        public string PhoneNumber {
            get {
                return ((string)(this["PhoneNumber"]));
            }
            set {
                this["PhoneNumber"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("0")]
        public int LicenseRemainingDays {
            get {
                return ((int)(this["LicenseRemainingDays"]));
            }
            set {
                this["LicenseRemainingDays"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("False")]
        public bool AllowNegativeQuantities {
            get {
                return ((bool)(this["AllowNegativeQuantities"]));
            }
            set {
                this["AllowNegativeQuantities"] = value;
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("Server=.;Database=VisionPoint;Trusted_Connection=false;User id=user;password=***;" +
            "TrustServerCertificate=True;MultipleActiveResultSets=true;")]
        public string ConnectionString {
            get {
                return ((string)(this["ConnectionString"]));
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("سوق الجمعة")]
        public string BranchName {
            get {
                return ((string)(this["BranchName"]));
            }
            set {
                this["BranchName"] = value;
            }
        }
        

        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("False")]
        public bool AutoBackupEnabled {
            get {
                return ((bool)(this["AutoBackupEnabled"]));
            }
            set {
                this["AutoBackupEnabled"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string AutoBackupPath {
            get {
                return ((string)(this["AutoBackupPath"]));
            }
            set {
                this["AutoBackupPath"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("5")]
        public int AutoBackupMaxCount {
            get {
                return ((int)(this["AutoBackupMaxCount"]));
            }
            set {
                this["AutoBackupMaxCount"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("2023-01-01")]
        public global::System.DateTime LastAutoBackupDate {
            get {
                return ((global::System.DateTime)(this["LastAutoBackupDate"]));
            }
            set {
                this["LastAutoBackupDate"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool EnableMinimumQuantityCheck {
            get {
                return ((bool)(this["EnableMinimumQuantityCheck"]));
            }
            set {
                this["EnableMinimumQuantityCheck"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("مجموعة النمارق للبصريات")]
        public string CompanyName {
            get {
                return ((string)(this["CompanyName"]));
            }
            set {
                this["CompanyName"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("Namariq Optics Group")]
        public string CompanyNameEng {
            get {
                return ((string)(this["CompanyNameEng"]));
            }
            set {
                this["CompanyNameEng"] = value;
            }
        }
    }
}
