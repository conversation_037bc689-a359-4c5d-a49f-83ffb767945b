﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using VisionPoint.UI.ViewModel;

namespace VisionPoint.UI.Reports.client
{
    /// <summary>
    /// Interaction logic for ClientReportList.xaml
    /// </summary>
    public partial class ClientReportList : Page
    {
        public ClientStatementVM Statement { get; set; }

        public ClientReportList(ClientStatementVM statementVM)
        {
            InitializeComponent(); 
            if (statementVM != null)
            {
                list.ItemsSource = new List<ClientStatementVM> { statementVM };
            }

        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (sender is ListView listView && listView.View is GridView gView)
            {
                var workingWidth = listView.ActualWidth - SystemParameters.VerticalScrollBarWidth;

                var columnWidths = new double[] { 0.15, 0.15, 0.2, 0.2, 0.1, 0.1, 0.1 };
                for (int i = 0; i < gView.Columns.Count - 1; i++)
                {
                    gView.Columns[i].Width = workingWidth * columnWidths[i];
                }
            }
        }
    }
}
