using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;
using VisionPoint.UI.ViewModel;

namespace VisionPoint.UI.views.Pages.ImportExport
{
    public partial class LensExporterPage : Window
    {
        private readonly ExportService _exportService;
        private readonly string _savePath;
        private bool _isDone = false;
        private readonly List<string> _selectedFields;
        private readonly int? _warehouseId;

        // متغيرات تصدير جرد العدسات
        private readonly bool _isInventoryExport;
        private readonly LensVM? _selectedLens;
        private readonly decimal? _sphereFrom;
        private readonly decimal? _sphereTo;
        private readonly decimal? _cylinderFrom;
        private readonly decimal? _cylinderTo;
        private readonly decimal? _powerFrom;
        private readonly decimal? _powerTo;

        /// <summary>
        /// إنشاء نافذة تصدير العدسات مع تحديد خدمة التصدير والحقول المختارة ومسار الحفظ مسبقاً
        /// </summary>
        /// <param name="exportService">خدمة التصدير</param>
        /// <param name="selectedFields">الحقول المختارة للتصدير</param>
        /// <param name="savePath">مسار حفظ الملف</param>
        /// <param name="warehouseId">معرف المخزن (null للجميع)</param>
        public LensExporterPage(ExportService exportService, List<string> selectedFields, string savePath, int? warehouseId = null)
        {
            InitializeComponent();
            _exportService = exportService;
            _selectedFields = selectedFields;
            _savePath = savePath;
            _warehouseId = warehouseId;
            _isInventoryExport = false;

            // إخفاء شريط التقدم في البداية
            exportProgress.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// إنشاء نافذة تصدير العدسات مع تحديد خدمة التصدير والحقول المختارة ومسار الحفظ مسبقاً (للجرد)
        /// </summary>
        /// <param name="exportService">خدمة التصدير</param>
        /// <param name="selectedFields">الحقول المختارة للتصدير</param>
        /// <param name="savePath">مسار حفظ الملف</param>
        /// <param name="isInventoryExport">هل هو تصدير جرد العدسات</param>
        /// <param name="selectedLens">العدسة المحددة (لتصدير الجرد)</param>
        /// <param name="sphereFrom">نطاق SPH من</param>
        /// <param name="sphereTo">نطاق SPH إلى</param>
        /// <param name="cylinderFrom">نطاق CYL من</param>
        /// <param name="cylinderTo">نطاق CYL إلى</param>
        /// <param name="powerFrom">نطاق POW من</param>
        /// <param name="powerTo">نطاق POW إلى</param>
        /// <param name="warehouseId">معرف المخزن (null للجميع)</param>
        public LensExporterPage(ExportService exportService, List<string> selectedFields, string savePath,
            bool isInventoryExport, LensVM? selectedLens,
            decimal? sphereFrom = null, decimal? sphereTo = null,
            decimal? cylinderFrom = null, decimal? cylinderTo = null,
            decimal? powerFrom = null, decimal? powerTo = null,
            int? warehouseId = null)
        {
            InitializeComponent();
            _exportService = exportService;
            _selectedFields = selectedFields;
            _savePath = savePath;
            _warehouseId = warehouseId;
            _isInventoryExport = isInventoryExport;
            _selectedLens = selectedLens;
            _sphereFrom = sphereFrom;
            _sphereTo = sphereTo;
            _cylinderFrom = cylinderFrom;
            _cylinderTo = cylinderTo;
            _powerFrom = powerFrom;
            _powerTo = powerTo;

            // إخفاء شريط التقدم في البداية
            exportProgress.Visibility = Visibility.Collapsed;
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // تأخير بدء عملية التصدير للسماح للنافذة بالظهور أولاً
            await Dispatcher.BeginInvoke(DispatcherPriority.ContextIdle, new Action(() => { }));

            try
            {
                // بدء عملية التصدير
                exportProgress.Visibility = Visibility.Visible;

                // تحديد نوع التصدير ونص الحالة
                string statusText = _isInventoryExport ? "جارٍ تصدير جرد العدسات..." : "جارٍ تصدير العدسات...";
                txtStatus.Text = statusText;

                // إنشاء كائن Progress لتحديث واجهة المستخدم
                var progress = new Progress<int>(percentage =>
                {
                    // استخدام Dispatcher.InvokeAsync بدلاً من Invoke للسماح بتحديث واجهة المستخدم بشكل أسرع
                    Dispatcher.InvokeAsync(() =>
                    {
                        exportProgress.Value = percentage;
                        string progressText = _isInventoryExport ? $"جارٍ تصدير جرد العدسات... {percentage}%" : $"جارٍ تصدير العدسات... {percentage}%";
                        txtStatus.Text = progressText;
                    }, DispatcherPriority.Render);
                });

                // تنفيذ عملية التصدير حسب النوع
                if (_isInventoryExport && _selectedLens != null)
                {
                    // تصدير جرد العدسات
                    await Task.Run(async () => await _exportService.ExportLensInventoryToExcel(
                        _savePath, _selectedLens, _sphereFrom, _sphereTo,
                        _cylinderFrom, _cylinderTo, _powerFrom, _powerTo, _warehouseId, progress));
                }
                else
                {
                    // تصدير العدسات العادي
                    await Task.Run(async () => await _exportService.ExportLensesToExcel(_savePath, _selectedFields, _warehouseId, progress));
                }

                // تحديث واجهة المستخدم بعد الانتهاء
                string successText = _isInventoryExport ? "تم تصدير جرد العدسات بنجاح" : "تم تصدير العدسات بنجاح";
                txtStatus.Text = successText;
                txtButton.Text = "إغلاق";
                _isDone = true;
                DialogBox.Show(successText, "نجاح");
            }
            catch (Exception ex)
            {
                exportProgress.Visibility = Visibility.Collapsed;
                string errorText = _isInventoryExport ? "حدث خطأ أثناء تصدير جرد العدسات" : "حدث خطأ أثناء تصدير العدسات";
                txtStatus.Text = $"حدث خطأ: {ex.Message}";
                ErrorBox.Show($"{errorText}: {ex.Message}", "خطأ", true);
            }
        }

        private void btnCancel_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (_isDone)
            {
                Close();
            }
            else
            {
                if (QuestionBox.Show("إلغاء التصدير", "هل تريد إلغاء التصدير؟") == MessageBoxResult.Yes)
                {
                    Close();
                }
            }
        }
    }
}
