<?xml version="1.0" encoding="utf-8" ?>
<Window
    x:Class="VisionPoint.UI.views.Pages.ImportExport.LensInventoryExportDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="تصدير جرد العدسات"
    AllowsTransparency="True"
    Background="Transparent"
    FlowDirection="RightToLeft"
    
    ResizeMode="NoResize"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">

    <Viewbox Stretch="Uniform">

        <Grid Width="1920" Height="1080">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="10*" />
                <ColumnDefinition Width="12*" />
                <ColumnDefinition Width="10*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="3*" />
                <RowDefinition Height="12*" />
                <RowDefinition Height="3*" />
            </Grid.RowDefinitions>

            <!--  Centered 720x720 content  -->
            <Border
                Grid.Row="1"
                Grid.Column="1"
                Background="{StaticResource backgroundColor}"
                BorderBrush="{StaticResource PrimaryColor}"
                BorderThickness="1"
                CornerRadius="16">

                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="40" />
                        <RowDefinition Height="80" />
                        <RowDefinition Height="auto" />
                        <RowDefinition Height="1.5*" />
                        <RowDefinition Height="60" />
                    </Grid.RowDefinitions>

                    <!--  عنوان النافذة  -->
                    <StackPanel Grid.Row="0">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="21"
                            FontWeight="Bold"
                            Text="تصدير جرد العدسات" />
                    </StackPanel>


                    <TextBlock
                        x:Name="txtLensInfo"
                        Grid.Row="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        FontSize="18"
                        FontWeight="Bold"
                        Foreground="{StaticResource SuccessColor}" />


                    <!--  اختيار نطاق الأرقام  -->
                    <GroupBox
                        Grid.Row="2"
                        Padding="8,16,8,0"
                        Header="نطاق الأرقام المطلوب تصديرها">
                        <GroupBox.HeaderTemplate>
                            <DataTemplate>
                                <TextBlock
                                    FontSize="24"
                                    FontWeight="Bold"
                                    Text="{Binding}" />
                            </DataTemplate>
                        </GroupBox.HeaderTemplate>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="100" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="16" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  SPH  -->
                            <TextBlock
                                x:Name="lblSphere"
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="0,0,0,16"
                                VerticalAlignment="Center"
                                FontWeight="Bold"
                                Text="SPH"
                                Visibility="Collapsed" />
                            <ComboBox
                                x:Name="cmbSphereFrom"
                                Grid.Row="0"
                                Grid.Column="1"
                                Height="60"
                                Margin="0,0,0,16"
                                FontSize="16"
                                SelectedValuePath="Value"
                                Tag="من"
                                ToolTip="القيمة الدنيا لـ SPH"
                                Visibility="Collapsed">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Value, StringFormat=F2}" />
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>

                            <ComboBox
                                x:Name="cmbSphereTo"
                                Grid.Row="0"
                                Grid.Column="3"
                                Height="60"
                                Margin="0,0,0,16"
                                SelectedValuePath="Value"
                                Tag="إلى"
                                ToolTip="القيمة العليا لـ SPH"
                                Visibility="Collapsed">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Value, StringFormat=F2}" />
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>

                            <!--  CYL  -->
                            <TextBlock
                                x:Name="lblCylinder"
                                Grid.Row="1"
                                Grid.Column="0"
                                Margin="0,0,0,16"
                                VerticalAlignment="Center"
                                FontWeight="Bold"
                                Text="CYL"
                                Visibility="Collapsed" />
                            <ComboBox
                                x:Name="cmbCylinderFrom"
                                Grid.Row="1"
                                Grid.Column="1"
                                Height="60"
                                Margin="0,0,0,16"
                                SelectedValuePath="Value"
                                Tag="من"
                                ToolTip="القيمة الدنيا لـ CYL"
                                Visibility="Collapsed">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Value, StringFormat=F2}" />
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>

                            <ComboBox
                                x:Name="cmbCylinderTo"
                                Grid.Row="1"
                                Grid.Column="3"
                                Height="60"
                                Margin="0,0,0,16"
                                SelectedValuePath="Value"
                                Tag="إلى"
                                ToolTip="القيمة العليا لـ CYL"
                                Visibility="Collapsed">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Value, StringFormat=F2}" />
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>

                            <!--  POW  -->
                            <TextBlock
                                x:Name="lblPower"
                                Grid.Row="2"
                                Grid.Column="0"
                                Margin="0,0,0,16"
                                VerticalAlignment="Center"
                                FontWeight="Bold"
                                Text="POW"
                                Visibility="Collapsed" />
                            <ComboBox
                                x:Name="cmbPowerFrom"
                                Grid.Row="2"
                                Grid.Column="1"
                                Height="60"
                                SelectedValuePath="Value"
                                Tag="من"
                                ToolTip="القيمة الدنيا لـ POW"
                                Visibility="Collapsed">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Value, StringFormat=F2}" />
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                            <ComboBox
                                x:Name="cmbPowerTo"
                                Grid.Row="2"
                                Grid.Column="3"
                                Height="60"
                                SelectedValuePath="Value"
                                Tag="إلى"
                                ToolTip="القيمة العليا لـ POW"
                                Visibility="Collapsed">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Value, StringFormat=F2}" />
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                        </Grid>
                    </GroupBox>

                    <!--  معاينة التصدير  -->
                    <GroupBox
                        Grid.Row="3"
                        Padding="0,8,0,0"
                        Header="معاينة التصدير">
                        <GroupBox.HeaderTemplate>
                            <DataTemplate>
                                <TextBlock
                                    FontSize="24"
                                    FontWeight="Bold"
                                    Text="{Binding}" />
                            </DataTemplate>
                        </GroupBox.HeaderTemplate>
                        <ScrollViewer>
                            <TextBlock
                                x:Name="txtPreview"
                                FontFamily="Consolas"
                                FontSize="18"
                                Text="اختر نوع العدسة والنطاقات المطلوبة لمعاينة التصدير" />
                        </ScrollViewer>
                    </GroupBox>

                    <!--  أزرار التحكم  -->
                    <Grid Grid.Row="4" Margin="0,15,0,0">

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Border
                            x:Name="btnExport"
                            Width="150"
                            Height="44"
                            Background="{StaticResource PrimaryColor}"
                            BorderThickness="0"
                            CornerRadius="12"
                            IsEnabled="False"
                            MouseLeftButtonDown="btnExport_Click">
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="12"
                                FontWeight="Bold"
                                Foreground="White"
                                Text="تصدير" />
                        </Border>
                        <Border
                            x:Name="btnCancel"
                            Grid.Column="1                     "
                            Width="150"
                            Height="44"
                            Background="Transparent"
                            BorderBrush="{StaticResource errorColor}"
                            BorderThickness="1"
                            CornerRadius="16"
                            MouseLeftButtonDown="btnCancel_Click">
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="12"
                                FontWeight="Bold"
                                Foreground="{StaticResource errorColor}"
                                Text="إلغاء" />
                        </Border>
                    </Grid>
                </Grid>
            </Border>
        </Grid>
    </Viewbox>
</Window>
