﻿using System.Windows;
using System.Windows.Input;
using System.Windows.Media;

namespace VisionPoint.UI.views.Dialogs
{

    public partial class ErrorBox : Window
    {
        private static ErrorBox instance;

        // Removed windowResult, as DialogResult should suffice

        private string header = "";
        public string TextHeader
        {
            set
            {
                header = value;
                txtHeader.Text = header;
            }
            get { return header; }
        }

        private string message = "";
        public string TextMessage
        {
            set
            {
                message = value;
                txtMessage.Text = message;
            }
            get { return message; }
        }

        private ErrorBox() // Private constructor
        {
            InitializeComponent();

        }

        public static ErrorBox Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new ErrorBox();
                }
                return instance;
            }
        }

        public static bool? Show(string message, string header, bool isError = true)
        {
            var dialog = Instance;
            dialog.TextHeader = header;
            dialog.TextMessage = message;

            // Set the appropriate icon depending on error type
            DrawingImage warningImage = (DrawingImage)dialog.FindResource("warningIcon");
            DrawingImage errorImage = (DrawingImage)dialog.FindResource("errorIcon");

            if (isError)
            {
                if (errorImage != null)
                {
                    dialog.iconBox.Source = errorImage;
                }
            }
            else
            {
                if (warningImage != null)
                {
                    dialog.iconBox.Source = warningImage;
                }
            }

            dialog.ShowDialog(); // This blocks execution until the dialog is closed
            return dialog.DialogResult; // Return the DialogResult (true/false)
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnYes.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnYes.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private void btnYes_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                Instance.DialogResult = true; // Set DialogResult to true (user clicked "Yes")
                this.Hide(); // Close the window instead of hiding it
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            // Prevent the window from closing using Alt+F4 or close button by default.
            try
            {
                if ((Keyboard.Modifiers & ModifierKeys.Alt) == ModifierKeys.Alt)
                {
                    e.Cancel = true;
                }

                // Reset DialogResult to null to avoid retaining previous state
                Instance.DialogResult = null;

                // Hide the window rather than closing it directly.
                e.Cancel = true; // Prevent the window from being closed entirely (default close behavior).
                this.Hide();
            }
            catch { return; }

        }

    }



}
