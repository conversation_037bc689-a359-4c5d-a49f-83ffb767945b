﻿<Page
    x:Class="VisionPoint.UI.Reports.Reciept.PrintableReciept"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converter="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.Reports.Reciept"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="PrintableReciept"
    Width="700"
    Height="480"
    FlowDirection="RightToLeft"
    mc:Ignorable="d">
    <Page.Resources>
        <converter:BoolToReceiptLabelConverter x:Key="ReceiptLabelConverter" />
        <converter:StringVisibilityConverter x:Key="StringVisibilityConverter" />
    </Page.Resources>
    <Border Padding="16" Background="White">

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="60" />
                <RowDefinition Height="60" />
                <RowDefinition Height="auto" />
                <RowDefinition Height="16" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!--  Header  -->
            <Grid HorizontalAlignment="Stretch">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition />
                    <ColumnDefinition />
                </Grid.ColumnDefinitions>
                <TextBlock
                    x:Name="txtCompanyName"
                    HorizontalAlignment="Stretch"
                    d:Text="مجموعة النمارق"
                    FontSize="16"
                    FontWeight="Bold"
                    TextAlignment="Left"
                    TextWrapping="Wrap" />
                <TextBlock
                    x:Name="txtCompanyNameEng"
                    Grid.Column="2"
                    HorizontalAlignment="Stretch"
                    d:Text="مجموعة النمارق"
                    FontSize="16"
                    FontWeight="Bold"
                    TextAlignment="Right"
                    TextWrapping="Wrap" />
            </Grid>


            <StackPanel
                Grid.Row="2"
                HorizontalAlignment="Center"
                Orientation="Horizontal">
                <TextBlock
                    Margin="2,0"
                    FontSize="20"
                    FontWeight="Bold"
                    Text="ايصال" />
                <TextBlock
                    Grid.Row="0"
                    Margin="2,0"
                    HorizontalAlignment="Center"
                    d:Text="قبض"
                    FontSize="20"
                    FontWeight="Bold"
                    Text="{Binding IsExchange, Converter={StaticResource BoolToExchangeTypeConverter}}"
                    TextAlignment="Center" />
            </StackPanel>


            <Image
                x:Name="LogoImageElement"
                Grid.Row="0"
                Grid.RowSpan="2"
                Grid.ColumnSpan="3"
                MaxHeight="80"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                d:Source="/Assets/company_logo.png"
                Source="{x:Null}"
                Stretch="Uniform" />


            <StackPanel
                Grid.Row="1"
                HorizontalAlignment="Left"
                Orientation="Horizontal">
                <TextBlock
                    Margin="2,0"
                    FontSize="16"
                    FontWeight="Bold"
                    Text="رقم الهاتف: "
                    TextAlignment="Left" />
                <TextBlock
                    x:Name="txtPhone"
                    HorizontalAlignment="Right"
                    d:Text="0922284531"
                    FontSize="16"
                    FontWeight="Bold"
                    TextAlignment="Left" />
            </StackPanel>

            <StackPanel
                Grid.Row="1"
                HorizontalAlignment="Right"
                Orientation="Horizontal">
                <TextBlock
                    x:Name="txtPhoneEng"
                    HorizontalAlignment="Right"
                    d:Text="0922284531"
                    FontSize="16"
                    FontWeight="Bold"
                    TextAlignment="Right" />
                <TextBlock
                    Margin="2,0"
                    FontSize="16"
                    FontWeight="Bold"
                    Text=": Phone No"
                    TextAlignment="Right" />
            </StackPanel>


            <!--  Body  -->
            <Grid Grid.Row="4">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!--  Header  -->



                <StackPanel
                    Grid.Row="1"
                    Margin="0,0,0,8"
                    Orientation="Horizontal">
                    <TextBlock
                        Width="100"
                        VerticalAlignment="Center"
                        FontWeight="Bold"
                        Text="التاريخ: " />
                    <TextBlock VerticalAlignment="Center" Text="{Binding Date, StringFormat='{}{0:yyyy/MM/dd}'}" />
                </StackPanel>

                <StackPanel
                    Grid.Row="2"
                    Margin="0,0,0,8"
                    Orientation="Horizontal">
                    <TextBlock
                        MaxWidth="200"
                        Margin="8,0"
                        VerticalAlignment="Center"
                        FontWeight="Bold"
                        Text="{Binding IsExchange, Converter={StaticResource ReceiptLabelConverter}}" />
                    <TextBlock HorizontalAlignment="Right" Visibility="{Binding Employee.Name, Converter={StaticResource StringVisibilityConverter}}">
                        <TextBlock.Text>
                            <MultiBinding StringFormat="الموظف {0}">
                                <Binding Path="Employee.Name" />
                            </MultiBinding>
                        </TextBlock.Text>
                    </TextBlock>

                    <!--  Client Name (visible if not empty)  -->
                    <TextBlock
                        HorizontalAlignment="Right"
                        Text="{Binding Client.Name}"
                        Visibility="{Binding Client.Name, Converter={StaticResource StringVisibilityConverter}}" />
                </StackPanel>

                <StackPanel
                    Grid.Row="3"
                    Margin="0,0,0,8"
                    Orientation="Horizontal">
                    <TextBlock
                        Width="120"
                        VerticalAlignment="Center"
                        FontWeight="Bold"
                        Text="مبلغ وقدره: " />
                    <TextBlock VerticalAlignment="Center" Text="{Binding Value, StringFormat='{}{0:N2} دينار ليبي'}" />
                </StackPanel>
                <StackPanel
                    Grid.Row="4"
                    Margin="0,0,0,8"
                    Orientation="Horizontal">
                    <TextBlock
                        Width="120"
                        VerticalAlignment="Center"
                        FontWeight="Bold"
                        Text="نوع العملية: " />
                    <TextBlock VerticalAlignment="Center" Text="{Binding Financial.Name}" />
                </StackPanel>

                <StackPanel
                    Grid.Row="5"
                    Margin="0,0,0,16"
                    Orientation="Horizontal">
                    <TextBlock
                        Width="120"
                        VerticalAlignment="Center"
                        FontWeight="Bold"
                        Text="بيان: " />
                    <TextBlock
                        MaxWidth="450"
                        MaxHeight="50"
                        VerticalAlignment="Center"
                        d:Text="هذا نص تجريبي طويل يُستخدم لأغراض المعاينة والتصميم داخل واجهة المستخدم، حيث يمكن أن يحتوي على تفاصيل مطولة لتمثيل البيانات الفعلية مثل وصف المعاملة أو الغرض من الدفع أو القبض بطريقة واضحة ومفصلة تساعد المصمم على تصور الشكل النهائي للإيصال المطبوع."
                        Text="{Binding Statement}"
                        TextWrapping="WrapWithOverflow" />
                </StackPanel>


                <StackPanel Grid.Row="6" Orientation="Horizontal">
                    <StackPanel Width="300">
                        <TextBlock
                            HorizontalAlignment="Center"
                            FontWeight="Bold"
                            Text="المعني به" />
                        <TextBlock HorizontalAlignment="Center" Text=".................................................." />
                    </StackPanel>
                    <StackPanel Width="300">
                        <TextBlock
                            HorizontalAlignment="Center"
                            FontWeight="Bold"
                            Text="المحاسب:" />
                        <TextBlock HorizontalAlignment="Center" Text=".................................................." />
                    </StackPanel>
                </StackPanel>
            </Grid>
        </Grid>
    </Border>
</Page>
