﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq; // Required for .Max() and .Sum()
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using PdfSharp.Drawing; // For XImage, XGraphics
using PdfSharp.Pdf;     // For PdfDocument, PdfPage

namespace VisionPoint.UI.Helper
{
    public class PdfGenerator<T>
    {
        public Page HeaderPage { get; set; }
        public Page ListHeaderPage { get; set; }
        public Page FooterPage { get; set; }

        // Instead of a template, we use a factory method
        public Func<T, int, Page> ListItemPageBuilder { get; set; }

        public void ExportToTallPdf(List<T> listItems, string outputFolder, string fileName, string printerName, bool Landscape = false)
        {
            string fullPath = null;
            string tempImagePath = null;
            List<Page> generatedPages = new List<Page>();

            try
            {
                if (!Directory.Exists(outputFolder))
                    Directory.CreateDirectory(outputFolder);

                fullPath = Path.Combine(outputFolder, $"{fileName}.pdf");
                List<BitmapSource> bitmaps = new List<BitmapSource>();

                // Render all WPF pages into bitmaps
                if (HeaderPage != null)
                {
                    bitmaps.Add(RenderPageToBitmap(HeaderPage));
                    generatedPages.Add(HeaderPage);
                }
                if (ListHeaderPage != null)
                {
                    bitmaps.Add(RenderPageToBitmap(ListHeaderPage));
                    generatedPages.Add(ListHeaderPage);
                }

                for (int i = 0; i < listItems.Count; i++)
                {
                    var itemPage = ListItemPageBuilder?.Invoke(listItems[i], i + 1);
                    if (itemPage != null)
                    {
                        bitmaps.Add(RenderPageToBitmap(itemPage));
                        generatedPages.Add(itemPage);
                    }
                }

                if (FooterPage != null)
                {
                    bitmaps.Add(RenderPageToBitmap(FooterPage));
                    generatedPages.Add(FooterPage);
                }

                // Combine all into one tall bitmap
                BitmapSource finalBitmap = CombineBitmapsVertically(bitmaps);

                // Convert tall bitmap into multiple page-sized slices
                const double paperHeightInInches = 11.0;
                const int dpi = 96;
                int sliceHeightPx = (int)(paperHeightInInches * dpi);

                // Instead of slicing one tall image, split bitmaps into page groups
                var bitmapPages = GroupBitmapsIntoPages(bitmaps, sliceHeightPx);

                // Generate multi-page PDF
                PdfDocument pdf = new PdfDocument();

                foreach (var pageBitmaps in bitmapPages)
                {
                    BitmapSource pageBitmap = CombineBitmapsVertically(pageBitmaps);

                    // OPTIONAL: Remove alpha channel to avoid issues with PNGs
                    pageBitmap = RemoveAlphaIfNeeded(pageBitmap);

                    using (var ms = new MemoryStream())
                    {
                        var encoder = new JpegBitmapEncoder(); // Use JPEG for compatibility
                        encoder.Frames.Add(BitmapFrame.Create(pageBitmap));
                        encoder.Save(ms); // Encode the image into the memory stream
                        ms.Position = 0;

                        using (var image = XImage.FromStream(ms))
                        {
                            var pdfPage = pdf.AddPage();
                            double inchToPoint = 72.0;
                            double pageWidthPoints = 8.5 * inchToPoint;
                            double pageHeightPoints = 11 * inchToPoint;

                            pdfPage.Width = pageWidthPoints;
                            pdfPage.Height = pageHeightPoints;

                            using (XGraphics gfx = XGraphics.FromPdfPage(pdfPage))
                            {
                                double imageWidthPoints = image.PixelWidth * inchToPoint / dpi;
                                double imageHeightPoints = image.PixelHeight * inchToPoint / dpi;

                                double scale = pageWidthPoints / imageWidthPoints;
                                double scaledHeight = imageHeightPoints * scale;

                                double yOffset = 6.0;
                                gfx.DrawImage(image, 0, yOffset, pageWidthPoints, scaledHeight);
                            }
                        }
                    }
                }




                pdf.Save(fullPath);
                pdf.Close();

                // Send to printer
                PrintPdf(fullPath, printerName, Landscape);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                throw;
            }
            finally
            {
                // Unparent and clean up WPF Page objects
                foreach (var pageToDispose in generatedPages)
                {
                    if (pageToDispose?.Parent is ContentControl cc)
                        cc.Content = null;
                    else if (pageToDispose?.Parent is Panel panel)
                        panel.Children.Remove(pageToDispose);
                    else if (pageToDispose?.Parent is Window win)
                        win.Content = null;
                }

                generatedPages.Clear();

                if (File.Exists(tempImagePath))
                {
                    try { File.Delete(tempImagePath); } catch { }
                }
            }
        }

        private BitmapSource RemoveAlphaIfNeeded(BitmapSource source)
        {
            if (source.Format == PixelFormats.Bgra32 || source.Format == PixelFormats.Pbgra32)
            {
                var converted = new FormatConvertedBitmap();
                converted.BeginInit();
                converted.Source = source;
                converted.DestinationFormat = PixelFormats.Bgr24;
                converted.EndInit();
                converted.Freeze();
                return converted;
            }

            return source;
        }

        private List<List<BitmapSource>> GroupBitmapsIntoPages(List<BitmapSource> bitmaps, int maxPageHeight)
        {
            List<List<BitmapSource>> pages = new List<List<BitmapSource>>();
            List<BitmapSource> currentPage = new List<BitmapSource>();
            int currentHeight = 0;

            foreach (var bmp in bitmaps)
            {
                if (bmp == null) continue;

                int bmpHeight = bmp.PixelHeight;

                // If adding this bitmap exceeds the page height, start a new page
                if (currentHeight + bmpHeight > maxPageHeight)
                {
                    // Start new page
                    if (currentPage.Count > 0)
                        pages.Add(new List<BitmapSource>(currentPage));

                    currentPage.Clear();
                    currentHeight = 0;
                }

                currentPage.Add(bmp);
                currentHeight += bmpHeight;
            }

            // Add last page
            if (currentPage.Count > 0)
            {
                pages.Add(currentPage);
            }

            return pages;
        }


        private BitmapSource RenderPageToBitmap(Page page)
        {
            // Ensure page is not null before rendering
            if (page == null)
            {
                // Return an empty or placeholder bitmap, or throw an exception
                return new RenderTargetBitmap(1, 1, 96, 96, PixelFormats.Pbgra32);
            }

            double dpi = 96d;

            // Force layout and measure the actual size
            // If the page is not displayed, you might need a dummy window or container
            // to properly measure and arrange it, especially if it contains complex controls.
            // For pages that are already part of the active UI, this is usually fine.
            page.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
            page.Arrange(new Rect(0, 0, page.DesiredSize.Width, page.DesiredSize.Height)); // Arrange at 0,0 with its desired size
            page.UpdateLayout();

            // Force image refresh to ensure images are loaded and frozen
            ForceImageRefresh(page);

            int width = (int)(page.ActualWidth == 0 ? page.DesiredSize.Width : page.ActualWidth);
            int height = (int)(page.ActualHeight == 0 ? page.DesiredSize.Height : page.ActualHeight);

            if (width <= 0 || height <= 0) // Use <= 0 to catch zero values too
            {
                // Default to common print dimensions if actual size isn't available or is invalid
                width = 800; // Example: suitable for portrait A4/Letter roughly
                height = 1120; // Example: suitable for portrait A4/Letter roughly
            }

            var renderBitmap = new RenderTargetBitmap(
                width, height, dpi, dpi, PixelFormats.Pbgra32);

            renderBitmap.Render(page);

            // Freeze the bitmap to make it immutable and potentially more efficient
            renderBitmap.Freeze();

            return renderBitmap;
        }

        private void ForceImageRefresh(DependencyObject obj)
        {
            if (obj == null) return;

            // If the object is an Image and has a BitmapSource, freeze it
            if (obj is Image img && img.Source is BitmapSource bitmapSource)
            {
                if (!bitmapSource.IsFrozen) // Only freeze if not already frozen
                {
                    try
                    {
                        bitmapSource.Freeze();
                    }
                    catch (InvalidOperationException)
                    {
                        // Handle cases where the BitmapSource cannot be frozen (e.g., it's being written to)
                        // You might log this or decide it's acceptable depending on your use case.
                    }
                }
            }

            // Recurse through visual tree
            int count = VisualTreeHelper.GetChildrenCount(obj);
            for (int i = 0; i < count; i++)
            {
                var child = VisualTreeHelper.GetChild(obj, i);
                if (child != null)
                {
                    ForceImageRefresh(child);
                }
            }
        }

        private BitmapSource CombineBitmapsVertically(List<BitmapSource> bitmaps)
        {
            if (bitmaps == null || bitmaps.Count == 0)
                return new RenderTargetBitmap(1, 1, 96, 96, PixelFormats.Pbgra32);

            int width = bitmaps.Max(b => b.PixelWidth);
            int height = bitmaps.Sum(b => b.PixelHeight);

            DrawingVisual drawingVisual = new DrawingVisual();

            using (DrawingContext ctx = drawingVisual.RenderOpen())
            {
                int offsetY = 0;
                foreach (BitmapSource bmp in bitmaps)
                {
                    ctx.DrawImage(bmp, new Rect(0, offsetY, bmp.PixelWidth, bmp.PixelHeight));
                    offsetY += bmp.PixelHeight;
                }
            }

            RenderTargetBitmap finalImage = new RenderTargetBitmap(width, height, 96, 96, PixelFormats.Pbgra32);
            finalImage.Render(drawingVisual);
            finalImage.Freeze();
            return finalImage;
        }


        public void PrintPdf(string pdfPath, string printerName, bool Landscape = false)
        {
            try
            {
                var sumatraPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Sumatra", "SumatraPDF.exe");

                if (!File.Exists(sumatraPath))
                {
                    Console.WriteLine($"Error: SumatraPDF not found at {sumatraPath}");
                    // Optionally notify the user
                    return;
                }
                if (!File.Exists(pdfPath))
                {
                    Console.WriteLine($"Error: PDF file not found at {pdfPath}");
                    // Optionally notify the user
                    return;
                }


                string settings = "fit";
                Landscape = false;
                if (Landscape)
                {
                    settings = "fit,landscape";
                }
                var psi = new ProcessStartInfo
                {
                    FileName = sumatraPath,
                    Arguments = $"-silent -print-to \"{printerName}\" -print-settings \"noscale,single,portrait\" \"{pdfPath}\"",
                    CreateNoWindow = true,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                };


                using (var process = Process.Start(psi))
                {
                    process?.WaitForExit(60000); // Wait up to 60 seconds
                    if (process?.HasExited == false)
                    {
                        process.Kill(); // Force kill if it times out
                        Console.WriteLine("SumatraPDF process timed out and was killed.");
                    }

                    var errorOutput = process?.StandardError.ReadToEnd();
                    if (!string.IsNullOrEmpty(errorOutput))
                    {
                        Console.WriteLine($"SumatraPDF Error: {errorOutput}");
                        // Optionally notify the user about printing errors
                    }
                    var stdOutput = process?.StandardOutput.ReadToEnd();
                    if (!string.IsNullOrEmpty(stdOutput))
                    {
                        Console.WriteLine($"SumatraPDF Output: {stdOutput}");
                    }
                }

                // Give the printer spooler a moment to grab the file before deleting
                System.Threading.Thread.Sleep(2000); // Wait 2 seconds

                if (File.Exists(pdfPath))
                {
                    try
                    {
                        File.Delete(pdfPath);
                        Console.WriteLine($"Successfully deleted PDF file: {pdfPath}");
                    }
                    catch (IOException ex)
                    {
                        Console.WriteLine($"Could not delete PDF file {pdfPath}: {ex.Message}. It might still be in use by the printer spooler.");
                        // You might want to implement a retry mechanism or schedule deletion later
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error deleting PDF file {pdfPath}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred during PDF printing: {ex.Message}");
                // Log or handle the exception
            }
        }
    }
}