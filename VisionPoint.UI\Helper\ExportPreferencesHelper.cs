using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using VisionPoint.UI.Models;

namespace VisionPoint.UI.Helper
{
    /// <summary>
    /// أنواع التصدير
    /// </summary>
    public enum ExportType
    {
        Sales,
        Products,
        Lenses
    }

    /// <summary>
    /// مساعد لحفظ وتحميل تفضيلات التصدير
    /// </summary>
    public static class ExportPreferencesHelper
    {
        private static readonly string SalesPreferencesPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "VisionPoint",
            "SalesExportPreferences.json");

        private static readonly string ProductsPreferencesPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "VisionPoint",
            "ProductsExportPreferences.json");

        private static readonly string LensesPreferencesPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "VisionPoint",
            "LensesExportPreferences.json");

        private static readonly string GeneralPreferencesPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "VisionPoint",
            "ExportPreferences.json");

        /// <summary>
        /// الحصول على مسار ملف التفضيلات حسب نوع التصدير
        /// </summary>
        /// <param name="exportType">نوع التصدير</param>
        /// <returns>مسار ملف التفضيلات</returns>
        private static string GetPreferencesPath(ExportType exportType)
        {
            return exportType switch
            {
                ExportType.Sales => SalesPreferencesPath,
                ExportType.Products => ProductsPreferencesPath,
                ExportType.Lenses => LensesPreferencesPath,
                _ => GeneralPreferencesPath
            };
        }

        /// <summary>
        /// نموذج تفضيلات التصدير
        /// </summary>
        public class ExportPreferences
        {
            public bool ExportInvoices { get; set; } = true;
            public bool ExportItems { get; set; } = true;
            public List<FieldPreference> InvoiceFields { get; set; } = new();
            public List<FieldPreference> ItemFields { get; set; } = new();
            public string LastExportPath { get; set; } = string.Empty;
        }

        /// <summary>
        /// تفضيل حقل واحد
        /// </summary>
        public class FieldPreference
        {
            public string FieldKey { get; set; } = string.Empty;
            public bool IsSelected { get; set; }
            public string CustomHeader { get; set; } = string.Empty;
        }

        /// <summary>
        /// حفظ تفضيلات التصدير
        /// </summary>
        /// <param name="model">نموذج معاينة التصدير</param>
        /// <param name="exportType">نوع التصدير</param>
        public static void SavePreferences(ExportPreviewModel model, ExportType exportType = ExportType.Sales)
        {
            try
            {
                var preferences = new ExportPreferences
                {
                    ExportInvoices = model.ExportInvoices,
                    ExportItems = model.ExportItems,
                    InvoiceFields = model.InvoiceFields.Select(f => new FieldPreference
                    {
                        FieldKey = f.FieldKey,
                        IsSelected = f.IsSelected,
                        CustomHeader = f.CustomHeader
                    }).ToList(),
                    ItemFields = model.ItemFields.Select(f => new FieldPreference
                    {
                        FieldKey = f.FieldKey,
                        IsSelected = f.IsSelected,
                        CustomHeader = f.CustomHeader
                    }).ToList()
                };

                var preferencesPath = GetPreferencesPath(exportType);

                // إنشاء المجلد إذا لم يكن موجوداً
                var directory = Path.GetDirectoryName(preferencesPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // حفظ التفضيلات
                var json = JsonSerializer.Serialize(preferences, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                File.WriteAllText(preferencesPath, json);
            }
            catch (Exception ex)
            {
                // تجاهل الأخطاء في حفظ التفضيلات
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ تفضيلات التصدير: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل تفضيلات التصدير
        /// </summary>
        /// <param name="exportType">نوع التصدير</param>
        /// <returns>تفضيلات التصدير أو null إذا لم توجد</returns>
        public static ExportPreferences LoadPreferences(ExportType exportType = ExportType.Sales)
        {
            try
            {
                var preferencesPath = GetPreferencesPath(exportType);
                if (!File.Exists(preferencesPath))
                    return null;

                var json = File.ReadAllText(preferencesPath);
                return JsonSerializer.Deserialize<ExportPreferences>(json);
            }
            catch (Exception ex)
            {
                // تجاهل الأخطاء في تحميل التفضيلات
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل تفضيلات التصدير: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// تطبيق التفضيلات المحفوظة على نموذج التصدير
        /// </summary>
        /// <param name="model">نموذج معاينة التصدير</param>
        /// <param name="exportType">نوع التصدير</param>
        public static void ApplyPreferences(ExportPreviewModel model, ExportType exportType = ExportType.Sales)
        {
            try
            {
                var preferences = LoadPreferences(exportType);
                if (preferences == null) return;

                // تطبيق خيارات التصدير
                model.ExportInvoices = preferences.ExportInvoices;
                model.ExportItems = preferences.ExportItems;

                // تطبيق تفضيلات حقول الفواتير
                foreach (var field in model.InvoiceFields)
                {
                    var savedField = preferences.InvoiceFields.FirstOrDefault(f => f.FieldKey == field.FieldKey);
                    if (savedField != null)
                    {
                        if (!field.IsRequired) // لا نغير الحقول المطلوبة
                        {
                            field.IsSelected = savedField.IsSelected;
                        }
                        if (!string.IsNullOrEmpty(savedField.CustomHeader))
                        {
                            field.CustomHeader = savedField.CustomHeader;
                        }
                    }
                }

                // تطبيق تفضيلات حقول الأصناف
                foreach (var field in model.ItemFields)
                {
                    var savedField = preferences.ItemFields.FirstOrDefault(f => f.FieldKey == field.FieldKey);
                    if (savedField != null)
                    {
                        if (!field.IsRequired) // لا نغير الحقول المطلوبة
                        {
                            field.IsSelected = savedField.IsSelected;
                        }
                        if (!string.IsNullOrEmpty(savedField.CustomHeader))
                        {
                            field.CustomHeader = savedField.CustomHeader;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // تجاهل الأخطاء في تطبيق التفضيلات
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق تفضيلات التصدير: {ex.Message}");
            }
        }

        /// <summary>
        /// حفظ مسار التصدير الأخير
        /// </summary>
        /// <param name="path">مسار التصدير</param>
        /// <param name="exportType">نوع التصدير</param>
        public static void SaveLastExportPath(string path, ExportType exportType = ExportType.Sales)
        {
            try
            {
                var preferences = LoadPreferences(exportType) ?? new ExportPreferences();
                preferences.LastExportPath = Path.GetDirectoryName(path) ?? string.Empty;

                var json = JsonSerializer.Serialize(preferences, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                var preferencesPath = GetPreferencesPath(exportType);
                var directory = Path.GetDirectoryName(preferencesPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllText(preferencesPath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ مسار التصدير: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على مسار التصدير الأخير
        /// </summary>
        /// <param name="exportType">نوع التصدير</param>
        /// <returns>مسار التصدير الأخير أو سلسلة فارغة</returns>
        public static string GetLastExportPath(ExportType exportType = ExportType.Sales)
        {
            try
            {
                var preferences = LoadPreferences(exportType);
                return preferences?.LastExportPath ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }
    }
}
