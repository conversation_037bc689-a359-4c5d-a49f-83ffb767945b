﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace VisionPoint.UI.Models;
[Index(nameof(Name), IsUnique = true)]
[Index(nameof(CategoryId))]
public class Lens : BaseEntity
{
    [StringLength(100, ErrorMessage = "اسم العدسة يجب ألا يتجاوز 100 حرف")]
    public string Name { get; set; } = string.Empty;
    public bool Sphere { get; set; }
    public bool Cylinder { get; set; }
    public bool Power { get; set; }
    [Precision(18, 3)] public decimal? Axis { get; set; }
    [Precision(18, 3)] public decimal? Addtion { get; set; }
    [Precision(18, 3)] public decimal? BC { get; set; }
    [Precision(18, 3)] public decimal? Dia { get; set; }
   // public bool Color { get; set; }
    public bool Exp { get; set; }
    /// <summary>
    /// الحد الأدنى للكمية
    /// </summary>
    public int MinimumQuantity { get; set; } = 0;

    // Relación con categoría
    public int? CategoryId { get; set; }
    public LensCategory? Category { get; set; }

    public ICollection<LensPrescription> LensPrescriptions { get; set; } = new List<LensPrescription>();
}
