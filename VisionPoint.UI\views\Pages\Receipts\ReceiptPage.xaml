﻿<Page
    x:Class="VisionPoint.UI.views.Pages.Receipts.ReceiptPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Receipts"
    xmlns:converters="clr-namespace:VisionPoint.UI.Converters"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils"
    Title="صفحة الايصالات"
    d:Background="White"
    d:Height="1080"
    d:Width="1570"
    FlowDirection="RightToLeft"
    Loaded="Page_Loaded"
    mc:Ignorable="d">
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- إضافة المحولات المطلوبة -->
            <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter" />
            <converters:BoolToExchangeTypeConverter x:Key="BoolToExchangeTypeConverter" />
        </ResourceDictionary>
    </Page.Resources>


    <Grid Margin="16">

        <Grid.ColumnDefinitions>
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
        </Grid.RowDefinitions>


        <Grid Grid.ColumnSpan="2" Margin="8,0">


            <TextBox
                x:Name="txtReceiptNo"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                IsReadOnly="True"
                Tag="رقم الايصال" />

        </Grid>


        <ComboBox
            x:Name="cmbWarehouse"
            Grid.Column="2"
            Margin="8,0"
            FontSize="21"
            SelectionChanged="cmbWarehouse_SelectionChanged"
            Tag="المخزن" />

        <ComboBox
            x:Name="cmbType"
            Grid.Column="3"
            Margin="8,0"
            FontSize="21"
            SelectionChanged="cmbType_SelectionChanged"
            Tag="نوع الايصال" />

        <DatePicker
            x:Name="DtpGeneralExpireOn"
            Grid.Column="4"
            Grid.ColumnSpan="2"
            Height="60"
            Margin="8,0"
            BorderBrush="{StaticResource PrimaryTextColor}"
            BorderThickness="1"
            DisplayDateStart="2000-01-01"
            FontSize="18"
            SelectedDateFormat="Short">
            <DatePicker.Resources>
                <Style TargetType="{x:Type DatePickerTextBox}">
                    <Setter Property="Control.Template">
                        <Setter.Value>
                            <ControlTemplate>
                                <TextBox
                                    x:Name="PART_TextBox"
                                    VerticalAlignment="Stretch"
                                    BorderThickness="0"
                                    Foreground="{StaticResource PrimaryTextColor}"
                                    IsReadOnly="True"
                                    Tag="تاريخ الايصال"
                                    Text="{Binding Path=SelectedDate, StringFormat='yyyy-MM-dd', RelativeSource={RelativeSource AncestorType={x:Type DatePicker}}}" />
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </DatePicker.Resources>
        </DatePicker>


        <Grid
            Grid.Row="1"
            Grid.RowSpan="2"
            Grid.Column="4"
            Grid.ColumnSpan="2"
            Margin="8,0">


            <TextBox
                x:Name="txtStatment"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                Tag="البيان"
                TextWrapping="Wrap" />
        </Grid>

        <ComboBox
            x:Name="cmbClient"
            Grid.Row="1"
            Grid.Column="2"
            Margin="8,0"
            FontSize="21"
            Tag="العميل/المورد" />

        <ComboBox
            x:Name="cmbEmploye"
            Grid.Row="1"
            Grid.Column="2"
            Margin="8,0"
            FontSize="21"
            Tag="الموظف"
            Visibility="Collapsed" />

        <ComboBox
            x:Name="cmbExpense"
            Grid.Row="1"
            Grid.Column="2"
            Margin="8,0"
            FontSize="21"
            Tag="المصروف"
            Visibility="Collapsed" />

        <Grid
            Grid.Row="1"
            Grid.Column="3"
            Margin="8,0">
            <TextBox
                x:Name="txtInvoiceNo"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                utils:NumericInputControl.IsNumericOnly="True"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                Tag="رقم الفاتورة" />
        </Grid>

        <ComboBox
            x:Name="cmbTreasury"
            Grid.Row="2"
            Grid.ColumnSpan="2"
            Margin="8,0"
            FontSize="21"
            Tag="طريقة الدفع" />

        <!-- عناصر التحويل بين طرق الدفع -->
        <ComboBox
            x:Name="cmbSourceTreasury"
            Grid.Row="2"
            Grid.ColumnSpan="2"
            Margin="8,0"
            FontSize="21"
            Tag="من طريقة الدفع"
            Visibility="Collapsed" />

        <ComboBox
            x:Name="cmbTargetTreasury"
            Grid.Row="2"
            Grid.Column="2"
            Grid.ColumnSpan="2"
            Margin="8,0"
            FontSize="21"
            Tag="إلى طريقة الدفع"
            Visibility="Collapsed" />


        <Grid
            Grid.Row="1"
            Grid.ColumnSpan="2"
            Margin="8,0">

            <Grid.RowDefinitions>
                <RowDefinition Height="60" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <TextBox
                x:Name="txtValue"
                Grid.Row="0"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                utils:NumericInputControl.AllowNegative="False"
                utils:NumericInputControl.IsDecimalOnly="True"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                Tag="القيمة" />

            <TextBlock
                x:Name="txtValueHint"
                Grid.Row="1"
                Margin="4,2"
                FontSize="12"
                Foreground="Gray"
                Text="القيمة الموجبة: مدين (يدين للمتجر) | القيمة السالبة: دائن (المتجر يدين له)"
                TextWrapping="Wrap"
                Visibility="Collapsed" />

        </Grid>
        <Grid
            x:Name="GrdExchange"
            Grid.Column="6"
            Margin="8,0">
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition />
            </Grid.RowDefinitions>
            <TextBlock
                VerticalAlignment="Center"
                FontSize="21"
                TextAlignment="Center">
                قبض
            </TextBlock>

            <RadioButton
                x:Name="RdbExchange"
                Grid.Row="1"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FlowDirection="LeftToRight"
                GroupName="ProductType"
                IsChecked="True" />
        </Grid>

        <Grid
            x:Name="GrdCatch"
            Grid.Column="7"
            Margin="8,0">
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition />
            </Grid.RowDefinitions>
            <TextBlock
                VerticalAlignment="Center"
                FontSize="21"
                TextAlignment="Center">
                صرف
            </TextBlock>

            <RadioButton
                x:Name="RdbCatch"
                Grid.Row="1"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FlowDirection="LeftToRight"
                GroupName="ProductType" />
        </Grid>


        <Border
            x:Name="btnSave"
            Grid.Row="3"
            Grid.Column="0"
            MaxHeight="44"
            Margin="8,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnSave_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White">
                حفظ
            </TextBlock>
        </Border>

        <Border
            x:Name="btnNew"
            Grid.Row="3"
            Grid.Column="1"
            MaxHeight="44"
            Margin="8,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnNew_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}">
                جديد
            </TextBlock>
        </Border>
        <Border
            x:Name="btnDelete"
            Grid.Row="3"
            Grid.Column="2"
            MaxHeight="44"
            Margin="8,0"
            Background="Transparent"
            BorderBrush="{StaticResource errorColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnDelete_MouseLeftButtonDown">

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition Width="30" />
                </Grid.ColumnDefinitions>
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="18"
                    Foreground="{StaticResource errorColor}">
                    حذف
                </TextBlock>

                <Path
                    Grid.Column="1"
                    Width="24"
                    Height="24"
                    HorizontalAlignment="Center"
                    Cursor="Hand"
                    Data="{StaticResource Trash}"
                    Fill="{StaticResource errorColor}"
                    FlowDirection="LeftToRight"
                    Stretch="Uniform"
                    StrokeThickness="1" />
            </Grid>

        </Border>

        <Border
            x:Name="btnSearch"
            Grid.Row="3"
            Grid.Column="3"
            MaxHeight="44"
            Margin="8,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnSearch_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White">
                بحث
            </TextBlock>
        </Border>


        <Border
            x:Name="btnPrint"
            Grid.Row="3"
            Grid.Column="4"
            MaxHeight="44"
            Margin="8,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnPrint_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}">
                طباعة
            </TextBlock>
        </Border>

        <ListView
            x:Name="list"
            Grid.Row="4"
            Grid.RowSpan="6"
            Grid.ColumnSpan="8"
            Background="{DynamicResource PageColor}"
            BorderThickness="1"
            
            FontWeight="Normal"
            ItemsSource="{Binding}"
            MouseDoubleClick="list_MouseDoubleClick"
            ScrollViewer.HorizontalScrollBarVisibility="Hidden"
            SizeChanged="list_SizeChanged">
            <ListView.BorderBrush>
                <SolidColorBrush Opacity="0.42" Color="Black" />
            </ListView.BorderBrush>
            <!--  SelectionChanged="ListView_SelectionChanged"  -->

            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Style.Triggers>
                        <Trigger Property="Control.IsMouseOver" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>

                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="False" />
                                <Condition Property="IsMouseOver" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter Property="FontWeight" Value="Thin" />
                            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextColor}" />
                        </MultiTrigger>

                    </Style.Triggers>
                    <Setter Property="Control.Background" Value="{DynamicResource PageColor}" />
                    <Setter Property="Control.HorizontalContentAlignment" Value="Stretch" />
                    <Setter Property="Control.VerticalContentAlignment" Value="Center" />
                    <Setter Property="MinHeight" Value="90" />
                    <Setter Property="Padding" Value="8,10" />
                    <Setter Property="Margin" Value="0,2" />
                </Style>

            </ListView.ItemContainerStyle>
            <ListView.View>
                <GridView AllowsColumnReorder="False">
                    <GridView.ColumnHeaderContainerStyle>
                        <Style BasedOn="{StaticResource ListViewHeader}" TargetType="{x:Type GridViewColumnHeader}">
                            <Setter Property="IsEnabled" Value="False" />
                            <Setter Property="Height" Value="80" />
                            <Setter Property="FontSize" Value="16" />
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="FontFamily" Value="Segoe UI" />
                            <Setter Property="Padding" Value="10,8" />
                            <Style.Triggers>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="TextElement.Foreground" Value="Black" />
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </GridView.ColumnHeaderContainerStyle>
                    <!-- العمود الأول: رقم الإيصال والتاريخ -->
                    <GridViewColumn Width="280" Header="رقم الإيصال والتاريخ">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Vertical" HorizontalAlignment="Center" Margin="15,12">
                                    <TextBlock
                                        Height="Auto"
                                        MinHeight="35"
                                        MinWidth="250"
                                        MaxWidth="250"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="Transparent"
                                        FontWeight="Bold"
                                        FontSize="20"

                                        TextWrapping="Wrap"
                                        TextAlignment="Center"
                                        Text="{Binding ReceiptNumber}" />
                                    <TextBlock
                                        Height="Auto"
                                        MinHeight="25"
                                        MinWidth="250"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="Transparent"
                                        FontSize="16"

                                        Foreground="Gray"
                                        TextAlignment="Center"
                                        Margin="0,8,0,0"
                                        Text="{Binding Date, StringFormat={}{0:yyyy-MM-dd}}" />
                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <!-- العمود الثاني: النوع والاتجاه والبيان -->
                    <GridViewColumn Width="350" Header="تفاصيل العملية">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Vertical" HorizontalAlignment="Center" Margin="15,12">
                                    <TextBlock
                                        Height="Auto"
                                        MinHeight="30"
                                        MinWidth="320"
                                        MaxWidth="320"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="Transparent"
                                        FontWeight="Bold"
                                        FontSize="18"

                                        TextWrapping="Wrap"
                                        TextAlignment="Center"
                                        Text="{Binding Financial.Name}" />
                                    <TextBlock
                                        Height="Auto"
                                        MinHeight="26"
                                        MinWidth="320"
                                        MaxWidth="320"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="Transparent"
                                        FontSize="16"

                                        Foreground="Blue"
                                        TextAlignment="Center"
                                        TextWrapping="Wrap"
                                        Margin="0,6,0,0"
                                        Text="{Binding IsExchange, Converter={StaticResource BoolToExchangeTypeConverter}}" />
                                    <TextBlock
                                        Height="Auto"
                                        MinHeight="24"
                                        MinWidth="320"
                                        MaxWidth="320"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="Transparent"
                                        FontSize="14"

                                        Foreground="Gray"
                                        TextAlignment="Center"
                                        TextWrapping="Wrap"
                                        Margin="0,4,0,0"
                                        Text="{Binding Statement}" />
                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <!-- العمود الثالث: القيمة مع التنسيق -->
                    <GridViewColumn Width="220" Header="المبلغ">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <Border
                                    Background="LightGray"
                                    CornerRadius="10"
                                    Padding="20,15"
                                    Margin="12,10"
                                    MinWidth="180"
                                    MaxWidth="180">
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="Transparent"
                                        FontWeight="Bold"
                                        FontSize="22"

                                        MinWidth="140"
                                        MaxWidth="140"
                                        TextAlignment="Center"
                                        TextWrapping="Wrap"
                                        Text="{Binding Value, StringFormat={}{0:N2}}" />
                                </Border>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <!-- العمود الرابع: طرق الدفع موحدة -->
                    <GridViewColumn Width="220" Header="طريقة الدفع">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Vertical" HorizontalAlignment="Center" Margin="10,8">
                                    <!-- للعمليات العادية -->
                                    <Border
                                        Background="LightBlue"
                                        CornerRadius="6"
                                        Padding="12,6"
                                        MinWidth="180"
                                        Visibility="{Binding Treasury, Converter={StaticResource NullToVisibilityConverter}}">
                                        <TextBlock
                                            HorizontalAlignment="Center"
                                            FontWeight="Bold"
                                            FontSize="14"
    
                                            Foreground="DarkBlue"
                                            Text="{Binding Treasury.Name}" />
                                    </Border>

                                    <!-- للتحويلات -->
                                    <StackPanel
                                        Orientation="Horizontal"
                                        HorizontalAlignment="Center"
                                        Visibility="{Binding SourceTreasury, Converter={StaticResource NullToVisibilityConverter}}">
                                        <Border Background="LightCoral" CornerRadius="5" Padding="8,4">
                                            <TextBlock
                                                FontWeight="Bold"
                                                FontSize="12"
        
                                                Foreground="DarkRed"
                                                Text="{Binding SourceTreasury.Name}" />
                                        </Border>
                                        <TextBlock
                                            Margin="8,0"
                                            FontWeight="Bold"
                                            FontSize="14"
    
                                            VerticalAlignment="Center"
                                            Text=" → " />
                                        <Border Background="LightGreen" CornerRadius="5" Padding="8,4">
                                            <TextBlock
                                                FontWeight="Bold"
                                                FontSize="12"
        
                                                Foreground="DarkGreen"
                                                Text="{Binding TargetTreasury.Name}" />
                                        </Border>
                                    </StackPanel>
                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <!-- العمود الخامس: الطرف المقابل موحد -->
                    <GridViewColumn Width="200" Header="الطرف المقابل">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Vertical" HorizontalAlignment="Center" Margin="10,8">
                                    <!-- العميل -->
                                    <Border
                                        Background="LightSkyBlue"
                                        CornerRadius="6"
                                        Padding="12,6"
                                        MinWidth="180"
                                        Visibility="{Binding Client, Converter={StaticResource NullToVisibilityConverter}}">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <TextBlock
                                                FontWeight="Bold"
                                                FontSize="12"
        
                                                Foreground="DarkBlue"
                                                Text="عميل: " />
                                            <TextBlock
                                                FontWeight="Bold"
                                                FontSize="14"
        
                                                Foreground="DarkBlue"
                                                Text="{Binding Client.Name}" />
                                        </StackPanel>
                                    </Border>

                                    <!-- الموظف -->
                                    <Border
                                        Background="Plum"
                                        CornerRadius="6"
                                        Padding="12,6"
                                        MinWidth="180"
                                        Visibility="{Binding Employee, Converter={StaticResource NullToVisibilityConverter}}">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <TextBlock
                                                FontWeight="Bold"
                                                FontSize="12"
        
                                                Foreground="Purple"
                                                Text="موظف: " />
                                            <TextBlock
                                                FontWeight="Bold"
                                                FontSize="14"
        
                                                Foreground="Purple"
                                                Text="{Binding Employee.UserName}" />
                                        </StackPanel>
                                    </Border>

                                    <!-- المصروف -->
                                    <Border
                                        Background="PeachPuff"
                                        CornerRadius="6"
                                        Padding="12,6"
                                        MinWidth="180"
                                        Visibility="{Binding Expense, Converter={StaticResource NullToVisibilityConverter}}">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <TextBlock
                                                FontWeight="Bold"
                                                FontSize="12"
        
                                                Foreground="DarkOrange"
                                                Text="مصروف: " />
                                            <TextBlock
                                                FontWeight="Bold"
                                                FontSize="14"
        
                                                Foreground="DarkOrange"
                                                Text="{Binding Expense.Name}" />
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <!-- العمود السادس: معلومات إضافية -->
                    <GridViewColumn Width="280" Header="معلومات إضافية">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Vertical" HorizontalAlignment="Center" Margin="15,12">
                                    <!-- رقم الفاتورة -->
                                    <TextBlock
                                        FontSize="16"
                                        FontWeight="Bold"

                                        Foreground="Green"
                                        MinWidth="250"
                                        MaxWidth="250"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        TextAlignment="Center"
                                        TextWrapping="Wrap"
                                        Margin="0,4"
                                        Text="{Binding Sale.Id, StringFormat='مبيعات: {0}', TargetNullValue='', FallbackValue=''}"
                                        Visibility="{Binding Sale, Converter={StaticResource NullToVisibilityConverter}}" />
                                    <TextBlock
                                        FontSize="16"
                                        FontWeight="Bold"

                                        Foreground="Red"
                                        MinWidth="250"
                                        MaxWidth="250"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        TextAlignment="Center"
                                        TextWrapping="Wrap"
                                        Margin="0,4"
                                        Text="{Binding Purchase.Id, StringFormat='مشتريات: {0}', TargetNullValue='', FallbackValue=''}"
                                        Visibility="{Binding Purchase, Converter={StaticResource NullToVisibilityConverter}}" />

                                    <!-- المخزن -->
                                    <TextBlock
                                        FontSize="14"

                                        Foreground="Gray"
                                        MinWidth="250"
                                        MaxWidth="250"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        TextAlignment="Center"
                                        TextWrapping="Wrap"
                                        Margin="0,6,0,0"
                                        Text="{Binding Warehouse.Name, StringFormat='مخزن: {0}', TargetNullValue='', FallbackValue=''}"
                                        Visibility="{Binding Warehouse, Converter={StaticResource NullToVisibilityConverter}}" />
                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                </GridView>
            </ListView.View>
        </ListView>



    </Grid>
</Page>

