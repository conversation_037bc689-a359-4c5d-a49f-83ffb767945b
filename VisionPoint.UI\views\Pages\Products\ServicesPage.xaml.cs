﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.Products
{
    /// <summary>
    /// Interaction logic for ServicesPage.xaml
    /// </summary>
    public partial class ServicesPage : Page
    {
        private readonly ServiceService _serviceService;
        private Service _selectedService = new();
        List<Service> _services = new();

        public ServicesPage()
        {
            InitializeComponent();
            _serviceService = new ServiceService();
        }

        private async void Page_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadData();
        }

        private async Task LoadData()
        {
            try
            {
                // The BusyService is automatically set by the ServiceService
                _services = await _serviceService.GetAllServices();
                list.ItemsSource = _services;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل بيانات الخدمات: {ex.Message}", "خطأ في التحميل", true);
            }
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnSave.IsEnabled = false;
            btnNew.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnSave.IsEnabled = true;
            btnNew.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private async void btnSave_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق من صلاحيات المستخدم
                bool canAdd = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ServiceRole");
                bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditServiceRole");

            // إذا كانت خدمة جديدة، نحتاج صلاحية الإضافة
            if (_selectedService.Id == 0 && !canAdd)
            {
                ErrorBox.Show("لا تملك صلاحية إضافة الخدمات", "خطأ في الصلاحيات", true);
                return;
            }

            // إذا كان تعديل خدمة موجودة، نحتاج صلاحية التعديل
            if (_selectedService.Id != 0 && !canEdit)
            {
                ErrorBox.Show("لا تملك صلاحية تعديل الخدمات", "خطأ في الصلاحيات", true);
                return;
            }

            if (string.IsNullOrEmpty(txtName.Text.Trim()))
            {
                ErrorBox.Show("الرجاء إدخال اسم الخدمة لإكمال العملية", "بيانات ناقصة", true);
                txtName.Focus();
                return;
            }
            if (_services.Any(x=>x.Name.Contains(txtName.Text.Trim())&&x.Id!= _selectedService.Id))
            {
                ErrorBox.Show("اسم الخدمة موجود مسبقاً", "بيانات ناقصة", true);
                txtName.Focus();
                return;
            }
            if (string.IsNullOrEmpty(txtPrice.Text.Trim()))
            {
                ErrorBox.Show("الرجاء إدخال سعر الخدمة لإكمال العملية", "بيانات ناقصة", true);
                txtPrice.Focus();
                return;
            }

          
                _selectedService.Name = txtName.Text.Trim();
                _selectedService.Price = decimal.Parse(txtPrice.Text);

                var result = _selectedService.Id == 0
                    ? await _serviceService.CreateService(_selectedService)
                    : await _serviceService.UpdateService(_selectedService);

                if (result.State)
                {
                    DialogBox.Show("تم حفظ بيانات الخدمة بنجاح", "عملية ناجحة");
                    btnNew_MouseLeftButtonDown(sender, e);
                    await LoadData();
                }
                else
                {
                    ErrorBox.Show(result.Message, "خطأ في الحفظ", true);
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ في النظام", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnNew_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                _selectedService = new Service();
                txtName.Clear();
                txtPrice.Clear();
                list.SelectedItem = null;
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnDelete_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // التحقق من صلاحية الحذف
            bool canDelete = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("DeleteServiceRole");
            if (!canDelete)
            {
                ErrorBox.Show("لا تملك صلاحية حذف الخدمات", "خطأ في الصلاحيات", true);
                return;
            }

            if (_selectedService.Id == 0)
            {
                ErrorBox.Show("الرجاء اختيار خدمة من القائمة للحذف", "عنصر غير محدد", false);
                return;
            }

            var result = DeleteBox.Show("هل انت متاكد من حذف الخدمة", _selectedService.Name);

            if (result == true)
            {
                try
                {
                    var deleteResult = await _serviceService.DeleteService(_selectedService.Id);

                    if (deleteResult.State)
                    {
                        DialogBox.Show("تم حذف الخدمة بنجاح", "عملية ناجحة");
                        btnNew_MouseLeftButtonDown(sender, e);
                        await LoadData();
                    }
                    else
                    {
                        ErrorBox.Show(deleteResult.Message, "خطأ في الحذف", true);
                    }
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"حدث خطأ أثناء حذف الخدمة: {ex.Message}", "خطأ في النظام", true);
                }
            }
        }

        private async void btnSearch_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                var searchTerm = txtName.Text.Trim();
                var services = await _serviceService.GetAllServices();
                var searchResults = services.Where(s => s.Name.Contains(searchTerm)).ToList();
                list.ItemsSource = searchResults;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء البحث عن الخدمات: {ex.Message}", "خطأ في البحث", true);
            }
        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            try
            {
                ListView listView = sender as ListView;
                if (listView != null)
                {
                    GridView gView = listView.View as GridView;
                    if (gView != null)
                    {
                        var workingWidth = listView.ActualWidth - SystemParameters.VerticalScrollBarWidth;
                        var col1 = 0.1; // numbering column
                        var col2 = 0.45; // name column
                        var col3 = 0.45; // price column

                        gView.Columns[0].Width = workingWidth * col1;
                        gView.Columns[1].Width = workingWidth * col2;
                        gView.Columns[2].Width = workingWidth * col3;

                    }
                }
            }
            catch
            {
                return;
            }
        }

        private void list_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (list.SelectedItem is Service selectedService)
            {
                _selectedService = selectedService;
                txtName.Text = selectedService.Name;
                txtPrice.Text = selectedService.Price.ToString();
            }
            else
            {
                ErrorBox.Show("الرجاء اختيار خدمة من القائمة للتعديل", "عنصر غير محدد", false);
            }
        }

        private void TextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                textBox.SelectAll();
            }
        }
    }
}
