using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;
using VisionPoint.UI.ViewModel;

namespace VisionPoint.UI.Helper.ImportExport
{
    /// <summary>
    /// مساعد لعملية تصدير المبيعات
    /// </summary>
    public class SalesExportHelper
    {
        private readonly SaleService _saleService;
        private readonly List<SaleViewModel> _sales;
        private readonly int? _clientId;
        private readonly int? _warehouseId;

        public SalesExportHelper(List<SaleViewModel> sales, int? clientId = null, int? warehouseId = null)
        {
            _saleService = new SaleService();
            _sales = sales;
            _clientId = clientId;
            _warehouseId = warehouseId;
        }

        /// <summary>
        /// بدء عملية تصدير المبيعات مع نافذة المعاينة
        /// </summary>
        /// <returns>قيمة تشير إلى نجاح العملية</returns>
        public async Task<bool> StartExportProcess()
        {
            try
            {
                if (_sales == null || _sales.Count == 0)
                {
                    ErrorBox.Show("لا توجد بيانات لتصديرها", "تنبيه", false);
                    return false;
                }

                // فتح نافذة معاينة التصدير
                var previewWindow = new SalesExportPreviewWindow(_sales, _clientId, _warehouseId);
                previewWindow.ShowDialog();

                // إرجاع نتيجة التصدير
                return previewWindow.ExportCompleted;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تصدير البيانات: {ex.Message}", "خطأ في التصدير", true);
                return false;
            }
        }

        /// <summary>
        /// بدء عملية تصدير المبيعات بالطريقة القديمة (بدون معاينة)
        /// </summary>
        /// <returns>قيمة تشير إلى نجاح العملية</returns>
        public async Task<bool> StartLegacyExportProcess()
        {
            try
            {
                if (_sales == null || _sales.Count == 0)
                {
                    ErrorBox.Show("لا توجد بيانات لتصديرها", "تنبيه", false);
                    return false;
                }

                // إنشاء خدمة تصدير
                var exportService = new ExportService();

                // عرض نافذة حفظ الملف
                var savePath = SelectSaveLocation();
                if (string.IsNullOrEmpty(savePath))
                {
                    return false; // تم إلغاء العملية
                }

                // تنفيذ عملية التصدير
                var progress = new Progress<int>(percentage =>
                {
                    // يمكن إضافة تحديث لشريط التقدم هنا إذا لزم الأمر
                });

                await exportService.ExportSalesToExcel(savePath, _sales, _clientId, _warehouseId, progress);

                DialogBox.Show("تم بنجاح", "تم تصدير البيانات بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تصدير البيانات: {ex.Message}", "خطأ في التصدير", true);
                return false;
            }
        }

        /// <summary>
        /// اختيار مكان حفظ الملف
        /// </summary>
        /// <returns>مسار الملف المختار أو سلسلة فارغة إذا تم الإلغاء</returns>
        private string SelectSaveLocation()
        {
            SaveFileDialog saveFileDialog = new SaveFileDialog
            {
                Filter = "Excel Files|*.xlsx",
                FileName = $"تقرير_المبيعات_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx",
                Title = "حفظ تقرير المبيعات"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                return saveFileDialog.FileName;
            }

            return string.Empty;
        }
    }
}
