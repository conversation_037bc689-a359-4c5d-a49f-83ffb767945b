﻿using Microsoft.EntityFrameworkCore;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Models;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Pages.Expire;
using VisionPoint.UI.views.Pages.MinimumQuantity;

namespace VisionPoint.UI.PL;

public class LensService : IDisposable
{
    private readonly AppDbContext _context = ServiceLocator.GetService<AppDbContext>();
    private bool _disposed = false;

    public LensService()
    {
    }

    // Add this method to LensService class
    public async Task<List<LensPrescriptionColor>> GetLensPrescriptionColorsByPrescriptionIdAsync(int prescriptionId)
    {
        return await _context.LensPrescriptionColors
            .Include(lpc => lpc.Color)
            .Where(lpc => lpc.LensPrescriptionId == prescriptionId)
            .AsNoTracking()
            .ToListAsyncWithBusy("GetLensPrescriptionColorsByPrescriptionId");
    }

    // إضافة طريقة للبحث عن العدسة بالباركود
    public async Task<LensPrescriptionColor?> GetLensPrescriptionColorByBarcodeAsync(string barcode)
    {
        if (string.IsNullOrEmpty(barcode))
            return null;

        return await _context.LensPrescriptionColors
            .Include(lpc => lpc.LensPrescription)
                .ThenInclude(lp => lp.Lens)
                .ThenInclude(lp => lp.LensPrescriptions)
                .ThenInclude(lp => lp.LensPrescriptionColors)
            .Include(lpc => lpc.LensPrescription)
                .ThenInclude(lp => lp.Sphere)
            .Include(lpc => lpc.LensPrescription)
                .ThenInclude(lp => lp.Cylinder)
            .Include(lpc => lpc.LensPrescription)
                .ThenInclude(lp => lp.Pow)
            .Include(lpc => lpc.Color)
            .FirstOrDefaultAsyncWithBusy(lpc => lpc.Barcode == barcode, "GetLensPrescriptionColorByBarcode");
    }

    /// <summary>
    /// البحث عن العدسة بالباركود مع التحقق من وجودها في المخزن المحدد
    /// </summary>
    /// <param name="barcode">الباركود</param>
    /// <param name="warehouseId">معرف المخزن</param>
    /// <returns>لون العدسة إذا كان متوفراً في المخزن المحدد</returns>
    public async Task<LensPrescriptionColor?> GetLensPrescriptionColorByBarcodeAndWarehouseAsync(string barcode, int warehouseId)
    {
        if (string.IsNullOrEmpty(barcode))
            return null;

        return await _context.LensPrescriptionColors
            .Include(lpc => lpc.LensPrescription)
                .ThenInclude(lp => lp.Lens)
                .ThenInclude(lp => lp.LensPrescriptions)
                .ThenInclude(lp => lp.LensPrescriptionColors)
            .Include(lpc => lpc.LensPrescription)
                .ThenInclude(lp => lp.Sphere)
            .Include(lpc => lpc.LensPrescription)
                .ThenInclude(lp => lp.Cylinder)
            .Include(lpc => lpc.LensPrescription)
                .ThenInclude(lp => lp.Pow)
            .Include(lpc => lpc.Color)
            .Include(lpc => lpc.LensQuantity)
            .Where(lpc => lpc.Barcode == barcode &&
                         lpc.LensQuantity.Any(lq => lq.WarehouseId == warehouseId))
            .FirstOrDefaultAsyncWithBusy("GetLensPrescriptionColorByBarcodeAndWarehouse");
    }

    // Get all lenses with their prescriptions
    public async Task<List<LensVM>> GetAllVMAsync()
    {
        return await GetAllVMAsync(null);
    }

    // Get all lenses with their prescriptions with optional warehouse filter
    public async Task<List<LensVM>> GetAllVMAsync(int? warehouseId)
    {
        return await _context.Lenses
            .Include(l => l.LensPrescriptions)
            .ThenInclude(lp => lp.LensPrescriptionColors)
            .ThenInclude(lpc => lpc.LensQuantity)
            .Include(l => l.Category)
            .Select(l => new LensVM
            {
                Id = l.Id,
                Name = l.Name,
                Sphere = l.Sphere,
                Cylinder = l.Cylinder,
                Power = l.Power,
                Axis = l.Axis,
                Addtion = l.Addtion,
                BC = l.BC,
                Dia = l.Dia,
                IsAbleToEditExp = !l.LensPrescriptions.SelectMany(z => z.LensPrescriptionColors).SelectMany(z => z.LensQuantity).Any(),
                Exp = l.Exp,
                MinimumQuantity = l.MinimumQuantity,
                CategoryId = l.CategoryId,
                CategoryName = l.Category != null ? l.Category.Name : string.Empty,
                TotalQuantity = warehouseId.HasValue
                    ? l.LensPrescriptions.SelectMany(lp => lp.LensPrescriptionColors)
                        .SelectMany(lpc => lpc.LensQuantity)
                        .Where(lq => lq.WarehouseId == warehouseId.Value)
                        .Sum(lq => lq.Quantity)
                    : l.LensPrescriptions.SelectMany(lp => lp.LensPrescriptionColors).SelectMany(lpc => lpc.LensQuantity).Sum(lq => lq.Quantity)
            }).ToListAsyncWithBusy("GetAllLensesVMQuery");
    }

    public async Task<List<Lens>> GetAllLensesAsync()
    {
        return await _context.Lenses
            .AsNoTracking()
            .ToListAsyncWithBusy("GetAllLenses");
    }

    /// <summary>
    /// جلب العدسات المتاحة في مخزن محدد
    /// </summary>
    /// <param name="warehouseId">معرف المخزن</param>
    /// <returns>قائمة العدسات التي لها سجلات كمية في المخزن المحدد</returns>
    public async Task<List<Lens>> GetLensesByWarehouseAsync(int warehouseId)
    {
        return await _context.Lenses
            .Include(l => l.LensPrescriptions)
            .ThenInclude(lp => lp.LensPrescriptionColors)
            .ThenInclude(lpc => lpc.LensQuantity)
            .Where(lens => lens.LensPrescriptions.Any(lp =>
                lp.LensPrescriptionColors.Any(lpc =>
                    lpc.LensQuantity.Any(lq => lq.WarehouseId == warehouseId))))
            .AsNoTracking()
            .ToListAsyncWithBusy("GetLensesByWarehouse");
    }

    // Get lens by id with prescriptions
    public async Task<Lens?> GetByIdAsync(int id)
    {
        return await _context.Lenses
            .Include(l => l.LensPrescriptions)
            .FirstOrDefaultAsyncWithBusy(l => l.Id == id, "GetLensByIdQuery");
    }

    // Add new lens
    public async Task<(bool State, string Message)> AddAsync(LensVM lensVM)
    {
        // Validar que se haya seleccionado una categoría
        if (!lensVM.CategoryId.HasValue)
        {
            return (false, "يجب اختيار نوع للعدسة");
        }

        var lens = new Lens
        {
            Name = lensVM.Name,
            Sphere = lensVM.Sphere,
            Cylinder = lensVM.Cylinder,
            Power = lensVM.Power,
            Axis = lensVM.Axis,
            Addtion = lensVM.Addtion,
            BC = lensVM.BC,
            Dia = lensVM.Dia,
            //Color = lensVM.Color,
            Exp = lensVM.Exp,
            MinimumQuantity = lensVM.MinimumQuantity,
            CategoryId = lensVM.CategoryId,
            CreatedById = CurrentUser.Id,
            ModifiedById = CurrentUser.Id
        };

        await _context.Lenses.AddAsyncWithBusy(lens);
        return await _context.SaveWithTransactionAndBusy("AddLens");
    }

    // Update existing lens
    public async Task<(bool State, string Message)> UpdateAsync(LensVM lensVM)
    {
        // Validar que se haya seleccionado una categoría
        if (!lensVM.CategoryId.HasValue)
        {
            return (false, "يجب اختيار نوع للعدسة");
        }

        var lens = await _context.Lenses.FindAsyncWithBusy(lensVM.Id);
        if (lens == null)
        {
            return (false, "العدسة غير موجودة");
        }

        lens.Name = lensVM.Name;
        lens.Sphere = lensVM.Sphere;
        lens.Cylinder = lensVM.Cylinder;
        lens.Power = lensVM.Power;
        lens.Axis = lensVM.Axis;
        lens.Addtion = lensVM.Addtion;
        lens.BC = lensVM.BC;
        lens.Dia = lensVM.Dia;
        // lens.Color = lensVM.Color;
        lens.Exp = lensVM.Exp;
        lens.MinimumQuantity = lensVM.MinimumQuantity;
        lens.CategoryId = lensVM.CategoryId;
        lens.ModifiedById = CurrentUser.Id;

        _context.Lenses.UpdateWithBusy(lens);
        return await _context.SaveWithTransactionAndBusy("UpdateLens");
    }

    // Delete lens
    public async Task<(bool State, string Message)> DeleteAsync(int id)
    {
        var lens = await _context.Lenses.FindAsyncWithBusy(id);
        if (lens == null)
        {
            return (false, "العدسة غير موجودة");
        }

        // التحقق من وجود عناصر مبيعات مرتبطة بالعدسة
        bool hasSaleItems = await _context.SaleItems
            .Include(si => si.LensQuantityLeft)
            .ThenInclude(lq => lq.LensPrescriptionColor)
            .ThenInclude(lpc => lpc.LensPrescription)
            .Include(si => si.LensQuantityRight)
            .ThenInclude(lq => lq.LensPrescriptionColor)
            .ThenInclude(lpc => lpc.LensPrescription)
            .AnyAsyncWithBusy(si =>
                (si.LensQuantityLeft != null &&
                 si.LensQuantityLeft.LensPrescriptionColor != null &&
                 si.LensQuantityLeft.LensPrescriptionColor.LensPrescription != null &&
                 si.LensQuantityLeft.LensPrescriptionColor.LensPrescription.LensId == id) ||
                (si.LensQuantityRight != null &&
                 si.LensQuantityRight.LensPrescriptionColor != null &&
                 si.LensQuantityRight.LensPrescriptionColor.LensPrescription != null &&
                 si.LensQuantityRight.LensPrescriptionColor.LensPrescription.LensId == id), "CheckLensSaleItems");

        if (hasSaleItems)
        {
            return (false, "لا يمكن حذف العدسة لأنها مرتبطة بعناصر مبيعات");
        }

        // التحقق من وجود عناصر مشتريات مرتبطة بالعدسة
        bool hasPurchaseItems = await _context.PurchaseItems
            .Include(pi => pi.LensPrescriptionColor)
            .ThenInclude(lpc => lpc.LensPrescription)
            .AnyAsyncWithBusy(pi =>
                pi.LensPrescriptionColor != null &&
                pi.LensPrescriptionColor.LensPrescription != null &&
                pi.LensPrescriptionColor.LensPrescription.LensId == id, "CheckLensPurchaseItemsQuery");

        if (hasPurchaseItems)
        {
            return (false, "لا يمكن حذف العدسة لأنها مرتبطة بعناصر مشتريات");
        }

        // التحقق من وجود خصومات مرتبطة بالعدسة
        bool hasDiscountLenses = await _context.DiscountLenses
            .AnyAsyncWithBusy(dl => dl.LensId == id, "CheckLensDiscountsQuery");

        if (hasDiscountLenses)
        {
            return (false, "لا يمكن حذف العدسة لأنها مرتبطة بخصومات");
        }

        _context.Lenses.RemoveWithBusy(lens);
        return await _context.SaveWithTransactionAndBusy("DeleteLens");
    }

    // Search lenses by name
    public async Task<List<LensVM>> SearchByNameAsync(string searchTerm)
    {
        return await SearchByNameAsync(searchTerm, null);
    }

    // Search lenses by name with optional warehouse filter
    public async Task<List<LensVM>> SearchByNameAsync(string searchTerm, int? warehouseId)
    {
        return await _context.Lenses
            .Include(l => l.LensPrescriptions)
            .ThenInclude(lp => lp.LensPrescriptionColors)
            .ThenInclude(lpc => lpc.LensQuantity)
            .Include(l => l.Category)
            .Where(l => l.Name.Contains(searchTerm))
            .Select(l => new LensVM
            {
                Id = l.Id,
                Name = l.Name,
                Sphere = l.Sphere,
                Cylinder = l.Cylinder,
                Power = l.Power,
                Axis = l.Axis,
                Addtion = l.Addtion,
                BC = l.BC,
                Dia = l.Dia,
                // Color = l.Color,
                Exp = l.Exp,
                MinimumQuantity = l.MinimumQuantity,
                CategoryId = l.CategoryId,
                CategoryName = l.Category != null ? l.Category.Name : string.Empty,
                TotalQuantity = warehouseId.HasValue
                    ? l.LensPrescriptions.SelectMany(lp => lp.LensPrescriptionColors)
                        .SelectMany(lpc => lpc.LensQuantity)
                        .Where(lq => lq.WarehouseId == warehouseId.Value)
                        .Sum(lq => lq.Quantity)
                    : l.LensPrescriptions.SelectMany(lp => lp.LensPrescriptionColors).SelectMany(lpc => lpc.LensQuantity).Sum(lq => lq.Quantity)
            })
            .ToListAsyncWithBusy("SearchLensesByNameQuery");
    }

    // Get lenses with specific properties
    public async Task<List<Lens>> GetLensesByPropertiesAsync(bool hasSphere = false, bool hasCylinder = false, bool hasPower = false, bool hasColor = false)
    {
        return await _context.Lenses
            .Include(l => l.LensPrescriptions)
            .Where(l =>
                l.Sphere == hasSphere ||
                l.Cylinder == hasCylinder ||
                l.Power == hasPower  /*||
               l.Color == hasColor*/)
            .ToListAsyncWithBusy("GetLensesByPropertiesQuery");
    }

    // Get lenses with their complete prescription hierarchy
    public async Task<List<Lens>> GetLensesWithFullDetailsAsync()
    {
        return await _context.Lenses
            .Include(l => l.LensPrescriptions)
                .ThenInclude(lp => lp.LensPrescriptionColors)
                    .ThenInclude(lpc => lpc.LensQuantity)
            .Include(l => l.LensPrescriptions)
                .ThenInclude(lp => lp.LensPrescriptionColors)
                    .ThenInclude(lpc => lpc.Color)
            .ToListAsyncWithBusy("GetLensesWithFullDetailsQuery");
    }
    // Add or update lens prescription color
    public async Task<(bool State, string Message)> SaveLensPrescriptionColorAsync(
        int lensId,
        short? sphereId,
        short? cylinderId,
        short? powId,
        decimal sellPrice,
        string barcode,
        byte colorId,
        int prescriptionColorId = 0)
    {
        try
        {
            // Get or create LensPrescription
            LensPrescription lensPrescription = null;
            var query = _context.LensPrescriptions.Where(lp => lp.LensId == lensId);

            // Add filters based on prescription values
            if (sphereId.HasValue)
            {
                query = query.Where(lp => lp.SphereId == sphereId);
            }

            if (cylinderId.HasValue)
            {
                query = query.Where(lp => lp.CylinderId == cylinderId);
            }

            if (powId.HasValue)
            {
                query = query.Where(lp => lp.PowId == powId);
            }

            lensPrescription = await query.FirstOrDefaultAsyncWithBusy("FindLensPrescription");

            // If no prescription exists, create a new one
            if (lensPrescription == null)
            {
                lensPrescription = new LensPrescription
                {
                    LensId = lensId,
                    SphereId = sphereId,
                    CylinderId = cylinderId,
                    PowId = powId,
                    SellPrice = sellPrice,
                    CostPrice = 0
                };

                await _context.LensPrescriptions.AddAsyncWithBusy(lensPrescription);
                var result = await _context.SaveWithTransactionAndBusy("AddLensPrescription");
                if (!result.State)
                    return (false, result.Message);
            }
            else
            {
                // Update prescription price
                lensPrescription.SellPrice = sellPrice;
                _context.LensPrescriptions.UpdateWithBusy(lensPrescription);
                var result = await _context.SaveWithTransactionAndBusy("UpdateLensPrescription");
                if (!result.State)
                    return (false, result.Message);
            }

            // Check if barcode is unique (only if barcode is not empty)
            if (!string.IsNullOrEmpty(barcode))
            {
                bool isBarcodeUnique = !await _context.LensPrescriptionColors
                    .AnyAsyncWithBusy(lpc => lpc.Barcode == barcode && lpc.Id != prescriptionColorId, "CheckBarcodeUniqueQuery");

                if (!isBarcodeUnique)
                {
                    return (false, "الباركود موجود مسبقاً");
                }
            }

            // Handle LensPrescriptionColor
            if (prescriptionColorId == 0)
            {
                // Add new color
                var newColor = new LensPrescriptionColor
                {
                    LensPrescriptionId = lensPrescription.Id,
                    ColorId = colorId,
                    Barcode = barcode,
                };

                await _context.LensPrescriptionColors.AddAsyncWithBusy(newColor);
                return await _context.SaveWithTransactionAndBusy("AddLensPrescriptionColor");
            }
            else
            {
                // Update existing color
                var existingColor = await _context.LensPrescriptionColors.FindAsyncWithBusy(prescriptionColorId);
                if (existingColor != null)
                {
                    existingColor.ColorId = colorId;
                    existingColor.Barcode = barcode;

                    _context.LensPrescriptionColors.UpdateWithBusy(existingColor);
                    return await _context.SaveWithTransactionAndBusy("UpdateLensPrescriptionColor");
                }
                return (false, "لون العدسة غير موجود");
            }
        }
        catch (Exception ex)
        {
            // Reverse any changes made before the exception
            _context.Reverse();
            return (false, $"حدث خطأ: {ex.Message}");
        }
    }

    // Delete lens prescription color
    public async Task<(bool State, string Message)> DeleteLensPrescriptionColorAsync(int id)
    {
        try
        {
            var prescriptionColor = await _context.LensPrescriptionColors.FindAsyncWithBusy(id);
            if (prescriptionColor == null)
            {
                return (false, "لون العدسة غير موجود");
            }

            // التحقق من وجود كميات عدسات مرتبطة بلون العدسة
            bool hasLensQuantities = await _context.LensQuantities
                .AnyAsyncWithBusy(lq => lq.LensPrescriptionColorId == id, "CheckLensQuantitiesQuery");

            if (hasLensQuantities)
            {
                return (false, "لا يمكن حذف لون العدسة لأنه مرتبط بكميات عدسات");
            }

            // التحقق من وجود عناصر مبيعات مرتبطة بلون العدسة
            bool hasSaleItems = await _context.SaleItems
                .AnyAsyncWithBusy(si =>
                    (si.LensQuantityLeft != null && si.LensQuantityLeft.LensPrescriptionColorId == id) ||
                    (si.LensQuantityRight != null && si.LensQuantityRight.LensPrescriptionColorId == id), "CheckLensPrescriptionColorSaleItemsQuery");

            if (hasSaleItems)
            {
                return (false, "لا يمكن حذف لون العدسة لأنه مرتبط بعناصر مبيعات");
            }

            // التحقق من وجود عناصر مشتريات مرتبطة بلون العدسة
            bool hasPurchaseItems = await _context.PurchaseItems
                .AnyAsyncWithBusy(pi => pi.LensPrescriptionColorId == id, "CheckLensPrescriptionColorPurchaseItemsQuery");

            if (hasPurchaseItems)
            {
                return (false, "لا يمكن حذف لون العدسة لأنه مرتبط بعناصر مشتريات");
            }

            _context.LensPrescriptionColors.RemoveWithBusy(prescriptionColor);
            return await _context.SaveWithTransactionAndBusy("DeleteLensPrescriptionColor");
        }
        catch (Exception ex)
        {
            // Reverse any changes made before the exception
            _context.Reverse();
            return (false, $"حدث خطأ أثناء حذف لون العدسة: {ex.Message}");
        }
    }
    public async Task<List<LensPrescription>> GetLensPrescriptionsByLensIdAsync(int lensId)
    {
        return await _context.LensPrescriptions
             .Where(lp => lp.LensId == lensId)
             .Include(lp => lp.Sphere)
             .Include(lp => lp.Cylinder)
             .Include(lp => lp.Pow)
             //.Include(lp => lp.LensPrescriptionColors)
             //    .ThenInclude(lpc => lpc.Color)
             .ToListAsyncWithBusy("GetLensPrescriptionsByLensIdQuery");
    }

    public async Task<List<LensPrescriptionColor>> GetLensPrescriptionColorsByLensPrescriptionIdAsync(int LensPrescriptionId)
    {
        return await _context.LensPrescriptionColors
             .Where(lp => lp.LensPrescriptionId == LensPrescriptionId)
             .Include(lpc => lpc.Color)
             .ToListAsyncWithBusy("GetLensPrescriptionColorsByLensPrescriptionIdQuery");
    }

    public async Task<List<LensPrescriptionColorVM>> GetLensPrescriptionColorsAsync(int lensId, short? sphereId, short? cylinderId, short? powId)
    {
        return await GetLensPrescriptionColorsAsync(lensId, sphereId, cylinderId, powId, null);
    }

    public async Task<List<LensPrescriptionColorVM>> GetLensPrescriptionColorsAsync(int lensId, short? sphereId, short? cylinderId, short? powId, int? warehouseId)
    {
        // After validation checks, add this code:
        return await _context.LensPrescriptionColors
          .Include(lpc => lpc.LensPrescription)
          .Include(lpc => lpc.LensQuantity)
          .Where(lpc => lpc.LensPrescription.LensId == lensId
          && (lpc.LensPrescription.SphereId == sphereId || sphereId == null)
          && (lpc.LensPrescription.CylinderId == cylinderId || cylinderId == null)
          && (lpc.LensPrescription.PowId == powId || powId == null)
          ).Select(x => new LensPrescriptionColorVM()
          {
              Id = x.Id,
              LensPrescriptionId = x.LensPrescriptionId,
              LensPrescription = x.LensPrescription,
              ColorId = x.ColorId,
              Color = x.Color,
              Barcode = x.Barcode,
              TotalQuantity = warehouseId.HasValue
                  ? x.LensQuantity.Where(lq => lq.WarehouseId == warehouseId.Value).Sum(lq => lq.Quantity)
                  : x.LensQuantity.Sum(lq => lq.Quantity)
          }).ToListAsyncWithBusy("GetLensPrescriptionColorsQuery");
    }

    // جلب كميات العدسات (LensQuantity) المرتبطة بلون محدد (LensPrescriptionColor)
    public async Task<List<LensQuantity>> GetLensQuantitiesByColorPrescriptionIdAsync(int colorPrescriptionId)
    {
        return await _context.LensQuantities
            .Where(lq => lq.LensPrescriptionColorId == colorPrescriptionId)
            .AsNoTracking()
            .ToListAsyncWithBusy("GetLensQuantitiesByColorPrescriptionId");
    }

    // Get the database context (for use in ExpireWindow)
    public AppDbContext GetContext()
    {
        return _context;
    }

    // Get lenses by category ID
    public async Task<List<Lens>> GetLensesByCategoryIdAsync(int? categoryId)
    {
        if (categoryId == null)
        {
            return await GetAllLensesAsync();
        }

        return await _context.Lenses
            .Where(l => l.CategoryId == categoryId)
            .AsNoTracking()
            .ToListAsyncWithBusy("GetLensesByCategoryId");
    }

    /// <summary>
    /// جلب العدسات حسب النوع والمخزن
    /// </summary>
    /// <param name="categoryId">معرف نوع العدسة</param>
    /// <param name="warehouseId">معرف المخزن</param>
    /// <returns>قائمة العدسات المفلترة حسب النوع والمخزن</returns>
    public async Task<List<Lens>> GetLensesByCategoryAndWarehouseAsync(int? categoryId, int warehouseId)
    {
        var query = _context.Lenses
            .Include(l => l.LensPrescriptions)
            .ThenInclude(lp => lp.LensPrescriptionColors)
            .ThenInclude(lpc => lpc.LensQuantity)
            .Where(lens => lens.LensPrescriptions.Any(lp =>
                lp.LensPrescriptionColors.Any(lpc =>
                    lpc.LensQuantity.Any(lq => lq.WarehouseId == warehouseId))));

        if (categoryId.HasValue)
        {
            query = query.Where(l => l.CategoryId == categoryId.Value);
        }

        return await query
            .AsNoTracking()
            .ToListAsyncWithBusy("GetLensesByCategoryAndWarehouse");
    }

    // Get all lens categories
    public async Task<List<LensCategory>> GetAllLensCategoriesAsync()
    {
        return await _context.LensCategories
            .AsNoTracking()
            .ToListAsyncWithBusy("GetAllLensCategories");
    }

    // Get all prescriptions
    public async Task<List<Prescription>> GetAllPrescriptionsAsync()
    {
        return await _context.Prescriptions
            .AsNoTracking()
            .OrderBy(p => p.Value)
            .ToListAsyncWithBusy("GetAllPrescriptions");
    }

    /// <summary>
    /// جلب العدسات منتهية الصلاحية مجمعة ومنسقة للعرض
    /// </summary>
    /// <param name="warehouseId">معرف المخزن (null لجميع المخازن)</param>
    /// <param name="remainingDays">عدد الأيام المتبقية للتنبيه</param>
    /// <param name="today">التاريخ الحالي</param>
    /// <returns>قائمة العدسات منتهية الصلاحية مجمعة ومنسقة</returns>
    public async Task<List<ExpiredItemViewModel>> GetExpiredLensesViewModelAsync(int? warehouseId, int remainingDays, DateOnly today)
    {
        try
        {
            var thresholdDate = today.AddDays(remainingDays);

            var query = _context.LensQuantities
                .Include(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                        .ThenInclude(lp => lp.Lens)
                .Include(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.Color)
                .Include(lq => lq.Warehouse)
                .Where(lq => lq.Exp.HasValue &&
                       (lq.Exp.Value <= today || (lq.Exp.Value > today && lq.Exp.Value <= thresholdDate)) &&
                       lq.Quantity > 0);

            if (warehouseId.HasValue)
                query = query.Where(lq => lq.WarehouseId == warehouseId.Value);

            return await query
                .GroupBy(lq => new
                {
                    Name = lq.LensPrescriptionColor.LensPrescription.Lens.Name,
                    Color = lq.LensPrescriptionColor.Color.Name,
                    ExpirationDate = lq.Exp
                })
                .Select(g => new ExpiredItemViewModel
                {
                    Id = g.First().Id,
                    Name = g.Key.Name ?? "غير معروف",
                    Color = g.Key.Color ?? "غير محدد",
                    ExpirationDate = g.Key.ExpirationDate.HasValue ? g.Key.ExpirationDate.Value.ToString("yyyy-MM-dd") : "غير محدد",
                    Quantity = g.Sum(lq => lq.Quantity),
                    ItemType = "عدسة",
                    Status = g.Key.ExpirationDate.HasValue && g.Key.ExpirationDate.Value <= today ? "منتهي الصلاحية" : "على وشك الانتهاء",
                    RemainingDays = g.Key.ExpirationDate.HasValue ? (g.Key.ExpirationDate.Value.DayNumber - today.DayNumber) : 0,
                    WarehouseName = g.First().Warehouse.Name ?? "غير محدد"
                })
                .ToListAsyncWithBusy("GetExpiredLensesViewModel");
        }
        catch (Exception ex)
        {
            throw new Exception($"خطأ في جلب العدسات منتهية الصلاحية: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// جلب العدسات التي وصلت للحد الأدنى للكمية مجمعة ومنسقة للعرض
    /// </summary>
    /// <param name="warehouseId">معرف المخزن (null لجميع المخازن)</param>
    /// <returns>قائمة العدسات التي وصلت للحد الأدنى مجمعة ومنسقة</returns>
    public async Task<List<MinimumQuantityItemViewModel>> GetMinimumQuantityLensesViewModelAsync(int? warehouseId)
    {
        try
        {
            var query = _context.LensQuantities
                .Include(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                        .ThenInclude(lp => lp.Lens)
                .Include(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.Color)
                .Include(lq => lq.Warehouse)
                .Where(lq => lq.LensPrescriptionColor.LensPrescription.Lens.MinimumQuantity > 0 &&
                           lq.Quantity < lq.LensPrescriptionColor.LensPrescription.Lens.MinimumQuantity);

            if (warehouseId.HasValue)
                query = query.Where(lq => lq.WarehouseId == warehouseId.Value);

            return await query
                .Select(lq => new MinimumQuantityItemViewModel
                {
                    Id = lq.LensPrescriptionColor.LensPrescription.Lens.Id,
                    Name = lq.LensPrescriptionColor.LensPrescription.Lens.Name ?? "غير معروف",
                    ColorName = lq.LensPrescriptionColor.Color.Name ?? "غير محدد",
                    CurrentQuantity = lq.Quantity,
                    MinimumQuantity = lq.LensPrescriptionColor.LensPrescription.Lens.MinimumQuantity,
                    ItemType = "عدسة",
                    WarehouseName = lq.Warehouse.Name ?? "غير محدد",
                    Details = $"SPH: {lq.LensPrescriptionColor.LensPrescription.Sphere}, CYL: {lq.LensPrescriptionColor.LensPrescription.Cylinder}, POW: {lq.LensPrescriptionColor.LensPrescription.Pow}"
                })
                .ToListAsyncWithBusy("GetMinimumQuantityLensesViewModel");
        }
        catch (Exception ex)
        {
            throw new Exception($"خطأ في جلب العدسات التي وصلت للحد الأدنى: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// الحصول على بيانات العدسات المسطحة للتصدير والمعاينة
    /// </summary>
    /// <param name="warehouseId">معرف المخزن للتصفية (اختياري)</param>
    /// <returns>قائمة العدسات المسطحة للتصدير</returns>
    public async Task<List<LensExportItemVM>> GetLensesForExportAsync(int? warehouseId = null)
    {
        try
        {
            // الحصول على بيانات العدسات مع جميع العلاقات
            var lensesQuery = _context.Lenses
                .Include(l => l.Category)
                .Include(l => l.LensPrescriptions)
                .ThenInclude(lp => lp.Sphere)
                .Include(l => l.LensPrescriptions)
                .ThenInclude(lp => lp.Cylinder)
                .Include(l => l.LensPrescriptions)
                .ThenInclude(lp => lp.Pow)
                .Include(l => l.LensPrescriptions)
                .ThenInclude(lp => lp.LensPrescriptionColors)
                .ThenInclude(lpc => lpc.Color)
                .Include(l => l.LensPrescriptions)
                .ThenInclude(lp => lp.LensPrescriptionColors)
                .ThenInclude(lpc => lpc.LensQuantity)
                .ThenInclude(lq => lq.Warehouse);

            // تطبيق تصفية المخزن إذا كان محدداً
            IQueryable<Lens> filteredLensesQuery = lensesQuery;
            if (warehouseId.HasValue)
            {
                filteredLensesQuery = lensesQuery.Where(l => l.LensPrescriptions.Any(lp =>
                    lp.LensPrescriptionColors.Any(lpc =>
                        lpc.LensQuantity.Any(lq => lq.WarehouseId == warehouseId.Value))));
            }

            var lenses = await filteredLensesQuery.ToListAsyncWithBusy("GetLensesForExport");

            // تسطيح البيانات لإنشاء قائمة العناصر للتصدير
            var flattenedLensData = lenses
                .SelectMany(lens => lens.LensPrescriptions
                    .SelectMany(prescription => prescription.LensPrescriptionColors
                        .SelectMany(color =>
                        {
                            // تطبيق تصفية المخزن على الكميات
                            var quantities = warehouseId.HasValue
                                ? color.LensQuantity.Where(lq => lq.WarehouseId == warehouseId.Value)
                                : color.LensQuantity;

                            return quantities.Select(quantity => new LensExportItemVM
                            {
                                // بيانات العدسة الأساسية
                                LensId = lens.Id,
                                Name = lens.Name,
                                BC = lens.BC,
                                Dia = lens.Dia,
                                Addtion = lens.Addtion,
                                Axis = lens.Axis,
                                CategoryName = lens.Category?.Name ?? "",

                                // بيانات الوصفة
                                PrescriptionId = prescription.Id,
                                SphereValue = prescription.Sphere?.Value,
                                CylinderValue = prescription.Cylinder?.Value,
                                PowerValue = prescription.Pow?.Value,
                                CostPrice = prescription.CostPrice,
                                SellPrice = prescription.SellPrice,

                                // بيانات اللون
                                ColorId = color.ColorId,
                                ColorName = color.Color?.Name ?? "",
                                ColorHexCode = color.Color?.HexCode ?? "",
                                Barcode = color.Barcode ?? "",

                                // بيانات الكمية
                                QuantityId = quantity.Id,
                                Quantity = quantity.Quantity,
                                Expiration = quantity.Exp,
                                WarehouseName = quantity.Warehouse?.Name ?? ""
                            });
                        })))
                .ToList();

            return flattenedLensData;
        }
        catch (Exception ex)
        {
            throw new Exception($"خطأ في جلب بيانات العدسات للتصدير: {ex.Message}", ex);
        }
    }

    // Implement IDisposable pattern
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // Dispose managed resources
                _context.Dispose();
            }

            // Free unmanaged resources
            _disposed = true;
        }
    }

    // Destructor
    ~LensService()
    {
        Dispose(false);
    }
}
