﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VisionPoint.UI.Models;

[Index(nameof(LensId))]
[Index(nameof(SphereId))]
[Index(nameof(CylinderId))]
[Index(nameof(PowId))]
[Index(nameof(LensId), nameof(SphereId), nameof(CylinderId), nameof(PowId))]
public class LensPrescription
{
    [Key]
    public int Id { get; set; }
    public int? LensId { get; set; }
    public Lens? Lens { get; set; }
    public short? SphereId { get; set; }
    public Prescription? Sphere { get; set; }
    public short? CylinderId { get; set; }
    public Prescription? Cylinder { get; set; }
    public short? PowId { get; set; }
    public Prescription? Pow { get; set; }
    [Precision(18, 3)] public decimal CostPrice { get; set; } = decimal.Zero;
    [Precision(18, 3)] public decimal SellPrice { get; set; } = decimal.Zero;
    public ICollection<LensPrescriptionColor>? LensPrescriptionColors { get; set; } = new List<LensPrescriptionColor>();

    /// <summary>
    /// Row GUID for SQL Server Merge Replication - managed by database
    /// </summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid RowGuid { get; set; }
}
