﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace VisionPoint.UI.ViewModel;

public class ProductVM
{
    public int Id { get; set; }
    public string Name { get; set; }
    [Precision(18, 3)] public decimal CostPrice { get; set; }
    [Precision(18, 3)] public decimal SellPrice { get; set; }
    public bool Color { get; set; }
    public bool Exp { get; set; }
    public int MinimumQuantity { get; set; } = 0;
    public int TotalQuantity { get; set; }
}

/// <summary>
/// نموذج تصدير المنتجات مع تفاصيل الألوان والكميات والمخازن
/// </summary>
public class ProductExportVM
{
    public int ProductId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string ColorName { get; set; } = string.Empty;
    public string ColorCode { get; set; } = string.Empty;
    public string Barcode { get; set; } = string.Empty;
    [Precision(18, 3)] public decimal CostPrice { get; set; }
    [Precision(18, 3)] public decimal SellPrice { get; set; }
    public int Quantity { get; set; }
    public string WarehouseName { get; set; } = string.Empty;
    public DateOnly? ExpiryDate { get; set; }
    public int MinimumQuantity { get; set; } = 0;
    public bool HasColor { get; set; }
    public bool HasExpiry { get; set; }

    // خصائص إضافية للعرض المنسق
    public string ExpiryDateFormatted => ExpiryDate?.ToString("yyyy-MM-dd") ?? "لا يوجد";
    public string HasColorText => HasColor ? "نعم" : "لا";
    public string HasExpiryText => HasExpiry ? "نعم" : "لا";
}
