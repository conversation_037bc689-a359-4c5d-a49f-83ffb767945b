﻿<Page
    x:Class="VisionPoint.UI.Reports.client.ClientReportList"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converter="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.Reports.client"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="ClientReportList"
    Width="1080"
    Height="auto"
    Background="White"
    FlowDirection="RightToLeft"
    mc:Ignorable="d">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <converter:PriceMultiplierConverter x:Key="PriceMultiplierConverter" />
            <converter:IndexToNumberConverter x:Key="IndexToNumberConverter" />
            <converter:NullToVisibilityConverter x:Key="NullToVisibilityConverter" />
        </ResourceDictionary>
    </Page.Resources>

    <Grid Margin="32,0">
        <ListView
            x:Name="list"
            Grid.Row="5"
            Grid.RowSpan="4"
            Grid.ColumnSpan="8"
            VerticalAlignment="Center"
            HorizontalContentAlignment="Center"
            VerticalContentAlignment="Center"
            Background="{DynamicResource PageColor}"
            BorderThickness="1"
            FontFamily="pack://application:,,,/Assets/#Cairo"
            ItemsSource="{Binding}"
            ScrollViewer.HorizontalScrollBarVisibility="Hidden"
            SizeChanged="list_SizeChanged">
            <!--  Added this line  -->

            <ListView.BorderBrush>
                <SolidColorBrush Opacity="0.42" Color="Black" />
            </ListView.BorderBrush>

            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Style.Triggers>
                        <Trigger Property="Control.IsMouseOver" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="Foreground" Value="Black" />
                            <Setter Property="Padding" Value="0,8" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="False" />
                                <Condition Property="IsMouseOver" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter Property="FontWeight" Value="Thin" />
                            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextColor}" />
                        </MultiTrigger>
                    </Style.Triggers>
                    <Setter Property="Control.Background" Value="{DynamicResource PageColor}" />
                    <Setter Property="Control.HorizontalContentAlignment" Value="Center" />
                    <!--  Added this line  -->
                </Style>
            </ListView.ItemContainerStyle>

            <ListView.View>
                <GridView AllowsColumnReorder="False">
                    <GridView.ColumnHeaderContainerStyle>
                        <Style BasedOn="{StaticResource ListViewHeader}" TargetType="{x:Type GridViewColumnHeader}">
                            <Setter Property="IsEnabled" Value="False" />
                            <Setter Property="Visibility" Value="Collapsed" />
                            <Style.Triggers>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="TextElement.Foreground" Value="Black" />
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </GridView.ColumnHeaderContainerStyle>
                    <GridViewColumn Header="التاريخ">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    d:Text="27/05/2025"
                                    Text="{Binding Date, StringFormat=\{0:dd/MM/yyyy\}}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>


                    <GridViewColumn Header="نوع العملية">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    d:Text="فاتورة مبيعات"
                                    Text="{Binding OperationType}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Header="طريقة الدفع">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    d:Text="نقدي"
                                    Text="{Binding PaymentMethod}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Header="البيان">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    d:Text="شراء منتجات"
                                    Text="{Binding Description}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Header="قبض">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    d:Text="1500.00"
                                    Text="{Binding Incoming}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Header="دفع">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    d:Text="0.00"
                                    Text="{Binding Outgoing}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Header="الرصيد">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    d:Text="1500.00"
                                    Text="{Binding Balance}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>

            </ListView.View>
        </ListView>
    </Grid>
</Page>

