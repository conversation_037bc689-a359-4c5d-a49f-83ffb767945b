using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.ImportExport
{
    public partial class ProductExporterPage : Window
    {
        private readonly ExportService _exportService;
        private readonly string _savePath;
        private bool _isDone = false;
        private readonly List<string> _selectedFields;
        private readonly int? _warehouseId;

        /// <summary>
        /// إنشاء نافذة تصدير المنتجات مع تحديد خدمة التصدير والحقول المختارة ومسار الحفظ مسبقاً
        /// </summary>
        /// <param name="exportService">خدمة التصدير</param>
        /// <param name="selectedFields">الحقول المختارة للتصدير</param>
        /// <param name="savePath">مسار حفظ الملف</param>
        /// <param name="warehouseId">معرف المخزن (null للجميع)</param>
        public ProductExporterPage(ExportService exportService, List<string> selectedFields, string savePath, int? warehouseId = null)
        {
            InitializeComponent();
            _exportService = exportService;
            _selectedFields = selectedFields;
            _savePath = savePath;
            _warehouseId = warehouseId;

            // إخفاء شريط التقدم في البداية
            exportProgress.Visibility = Visibility.Collapsed;
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // بدء عملية التصدير مباشرة لأن الحقول ومسار الحفظ تم تحديدهما مسبقاً

                // بدء عملية التصدير
                exportProgress.Visibility = Visibility.Visible;
                txtStatus.Text = "جارٍ تصدير المنتجات...";

                // إنشاء كائن Progress لتحديث واجهة المستخدم
                var progress = new Progress<int>(percentage =>
                {
                    // استخدام Dispatcher.InvokeAsync بدلاً من Invoke للسماح بتحديث واجهة المستخدم بشكل أسرع
                    Dispatcher.InvokeAsync(() =>
                    {
                        exportProgress.Value = percentage;
                        txtStatus.Text = $"جارٍ تصدير المنتجات... {percentage}%";
                    });
                });

                // تنفيذ عملية التصدير في thread منفصل باستخدام Task.Run
                await Task.Run(async () => await _exportService.ExportProductsToExcel(_savePath, _selectedFields, _warehouseId, progress));

                // تحديث واجهة المستخدم بعد الانتهاء
                txtStatus.Text = "تم تصدير المنتجات بنجاح";
                txtButton.Text = "إغلاق";
                _isDone = true;
                DialogBox.Show("تم تصدير المنتجات بنجاح", "نجاح");
            }
            catch (Exception ex)
            {
                exportProgress.Visibility = Visibility.Collapsed;
                txtStatus.Text = $"حدث خطأ: {ex.Message}";
                ErrorBox.Show($"حدث خطأ أثناء تصدير المنتجات: {ex.Message}", "خطأ", true);
            }
        }



        private void btnCancel_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (_isDone)
            {
                Close();
            }
            else
            {
                if (QuestionBox.Show("إلغاء التصدير", "هل تريد إلغاء التصدير؟") == MessageBoxResult.Yes)
                {
                    Close();
                }
            }
        }
    }
}
