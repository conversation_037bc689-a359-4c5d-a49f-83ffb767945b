using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using VisionPoint.UI.Helper;
using VisionPoint.UI.Helper.ImportExport;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.Sales
{
    /// <summary>
    /// Interaction logic for SalesListView.xaml
    /// </summary>
    public partial class SalesListView : Page
    {
        private readonly SaleService _saleService;
        private readonly ClientService _clientService;
        private readonly WarehouseService _warehouseService;
        private ObservableCollection<SaleViewModel> _sales;
        bool _firstLoad = true; // متغير لتتبع ما إذا كانت الصفحة قد تم تحميلها لأول مرة
        // فلاتر البحث
        private DateTime? _fromDate;
        private DateTime? _toDate;
        private int? _clientId;
        private bool? _hasRemaining;
        private int? _invoiceId;
        private int? _warehouseId;
        private string? _returnsFilter; // فلتر المسترجعات: "All", "HasReturns", "NoReturns"

        public SalesListView()
        {
            InitializeComponent();
            _saleService = new SaleService();
            _clientService = new ClientService();
            _warehouseService = new WarehouseService();
            _sales = new ObservableCollection<SaleViewModel>();
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            if (!_firstLoad) return; // إذا كانت الصفحة قد تم تحميلها بالفعل، لا نقوم بإعادة تحميل البيانات
            // تحميل المخازن في القائمة المنسدلة
            await LoadWarehousesAsync();

            // تحميل العملاء في القائمة المنسدلة
            //await LoadClientsAsync();

            // تعيين تاريخ اليوم كتاريخ افتراضي للبحث
            _fromDate = DateTime.Today;
            dpFromDate.SelectedDate = _fromDate;

            // تهيئة فلتر المسترجعات
            _returnsFilter = "All";
            cmbReturnsFilter.SelectedIndex = 0;

            // تعيين حالة عناصر التاريخ
            chkAllPeriods.IsChecked = false;
            UpdateDateControlsState();

            // تحميل فواتير اليوم فقط
            await ApplyFiltersAsync();
        }

        /// <summary>
        /// تحديث حالة عناصر التاريخ بناءً على خيار "كل الفترات"
        /// </summary>
        private void UpdateDateControlsState()
        {
            bool isAllPeriods = chkAllPeriods.IsChecked == true;

            // تعطيل/تفعيل عناصر التاريخ
            dpFromDate.IsEnabled = !isAllPeriods;
            dpToDate.IsEnabled = !isAllPeriods;
            dpFromDate.Opacity = isAllPeriods ? 0.5 : 1;
            dpToDate.Opacity = isAllPeriods ? 0.5 : 1;
        }

        private async Task LoadWarehousesAsync()
        {
            try
            {
                // تحميل المخازن
                var warehouses = await _warehouseService.GetAllWarehousesAsync();

                // إضافة عنصر "الكل" في بداية القائمة
                var allWarehousesItem = new Warehouse { Id = -1, Name = "الكل" };
                var warehousesList = new List<Warehouse> { allWarehousesItem };
                warehousesList.AddRange(warehouses);

                cmbWarehouses.ItemsSource = warehousesList;

                // تحديد المخزن الافتراضي بناءً على المستخدم الحالي
                if (CurrentUser.WarehouseId.HasValue && warehouses.Any(w => w.Id == CurrentUser.WarehouseId.Value))
                {
                    // اختيار مخزن المستخدم الحالي
                    cmbWarehouses.SelectedValue = CurrentUser.WarehouseId.Value;
                }
                else
                {
                    // اختيار "الكل" كقيمة افتراضية
                    cmbWarehouses.SelectedIndex = 0;
                }

                // تطبيق منطق الصلاحيات لتغيير المخزن
                // إذا لم يكن المستخدم مديراً أو لا يملك صلاحية تغيير المخزن، يتم تعطيل الكومبو
                cmbWarehouses.IsEnabled = CurrentUser.CanChangeWarehouse;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل بيانات المخازن: {ex.Message}", "خطأ في التحميل", true);
            }
        }

        private async Task LoadClientsAsync()
        {
            try
            {
                // تحديد المخزن المختار
                int? selectedWarehouseId = null;
                if (cmbWarehouses.SelectedValue != null && cmbWarehouses.SelectedValue is int warehouseId && warehouseId != -1)
                {
                    selectedWarehouseId = warehouseId;
                }

                // تحميل العملاء حسب المخزن المختار
                List<Client> clients;
                if (selectedWarehouseId.HasValue)
                {
                    // جلب العملاء (الزبائن) المرتبطين بالمخزن المختار أو العملاء العامين (null warehouse)
                    clients = await _clientService.GetCustomersByWarehouseAsync(selectedWarehouseId.Value);
                }
                else
                {
                    // جلب جميع العملاء (الزبائن) إذا لم يتم اختيار مخزن محدد
                    clients = await _clientService.GetCustomersOnlyAsync();
                }

                // إضافة عنصر "الكل" في بداية القائمة
                var allClientsItem = new Client { Id = -1, Name = "الكل" };
                var clientsList = new List<Client> { allClientsItem };
                clientsList.AddRange(clients);

                cmbClients.ItemsSource = clientsList;
                cmbClients.SelectedIndex = 0; // اختيار "الكل" كقيمة افتراضية
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل بيانات العملاء: {ex.Message}", "خطأ في التحميل", true);
            }
        }

        /// <summary>
        /// تطبيق الفلاتر وتحميل الفواتير المصفاة
        /// </summary>
        private async Task ApplyFiltersAsync()
        {
            try
            {
                // تحقق من رقم الفاتورة
                _invoiceId = null;
                if (!string.IsNullOrWhiteSpace(txtInvoiceNumber.Text) && int.TryParse(txtInvoiceNumber.Text.Trim(), out int invoiceId))
                {
                    _invoiceId = invoiceId;
                }

                // تحقق من العميل المحدد
                _clientId = null;
                if (cmbClients.SelectedValue != null && cmbClients.SelectedValue is int clientId && clientId != -1)
                {
                    _clientId = clientId;
                }

                // تحقق من المخزن المحدد
                _warehouseId = null;
                if (cmbWarehouses.SelectedValue != null && cmbWarehouses.SelectedValue is int warehouseId && warehouseId != -1)
                {
                    _warehouseId = warehouseId;
                }

                // تحقق من خيار "كل الفترات"
                if (chkAllPeriods.IsChecked == true)
                {
                    // إذا تم اختيار "كل الفترات"، نعين قيم التاريخ بـ null
                    _fromDate = null;
                    _toDate = null;
                }

                // تحميل الفواتير المصفاة
                _sales.Clear();
                var salesData = await _saleService.GetFilteredSalesAsync(
                    _fromDate,
                    _toDate,
                    _clientId,
                    _hasRemaining,
                    _invoiceId,
                    _warehouseId);

                // تطبيق فلتر المسترجعات محلياً
                if (!string.IsNullOrEmpty(_returnsFilter) && _returnsFilter != "All")
                {
                    if (_returnsFilter == "HasReturns")
                    {
                        // فواتير بها مسترجعات (TotalReturned > 0)
                        salesData = salesData.Where(s => s.TotalReturned > 0).ToList();
                    }
                    else if (_returnsFilter == "NoReturns")
                    {
                        // فواتير بدون مسترجعات (TotalReturned = 0)
                        salesData = salesData.Where(s => s.TotalReturned == 0).ToList();
                    }
                }

                _sales = new ObservableCollection<SaleViewModel>(salesData);
                list.ItemsSource = _sales;

                // لا نظهر أي رسائل في حال عدم وجود فواتير
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تطبيق الفلاتر: {ex.Message}", "خطأ في البحث", true);
            }
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnAdd.IsEnabled = false;
            btnSearch.IsEnabled = false;
            btnExport.IsEnabled = false;
            btnResetFilters.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnAdd.IsEnabled = true;
            btnSearch.IsEnabled = true;
            btnExport.IsEnabled = true;
            btnResetFilters.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private async void btnAdd_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                _firstLoad = false; // تعيين المتغير إلى false بعد تحميل الصفحة لأول مرة
                // التنقل إلى صفحة إضافة المبيعات والانتظار حتى إغلاقها
                await NavigateAction.NavigateToAndWaitAsync(new SalesView());
                // إعادة تحميل البيانات بعد الإضافة مع تطبيق الفلاتر الحالية
                await ApplyFiltersAsync();
            }
            finally
            {
                EnableAllButtons();
            }
        }


        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            // Ensure the ListView and GridView are valid, and there are enough columns
            if (list.View is GridView gridView && gridView.Columns.Count >= 10) // Changed to 10 columns
            {
                double actionColumnWidth = 100; // Fixed width for Operations
                double padding = 10;
                double scrollbarWidth = SystemParameters.VerticalScrollBarWidth;

                // Calculate the available width for the flexible columns
                double availableWidth = list.ActualWidth - actionColumnWidth - scrollbarWidth - padding;

                // Define weights for all flexible columns
                double indexWeight = 0.5;
                double invoiceIdWeight = 1.0;
                double dateWeight = 1.5;
                double clientWeight = 2.0;
                double warehouseWeight = 1.0;
                double totalWeight = 1.5;
                double paidWeight = 1.5;
                double remainingWeight = 1.5;
                double returnsWeight = 1.2; // Weight for returns column

                double totalFlexibleWeight = indexWeight + invoiceIdWeight + dateWeight +
                                             clientWeight + warehouseWeight + totalWeight +
                                             paidWeight + remainingWeight + returnsWeight;

                double unitWidth = availableWidth / totalFlexibleWeight;

                // Assign widths to all columns
                gridView.Columns[0].Width = unitWidth * indexWeight;       // Index
                gridView.Columns[1].Width = unitWidth * invoiceIdWeight;   // Invoice ID
                gridView.Columns[2].Width = unitWidth * dateWeight;        // Date
                gridView.Columns[3].Width = unitWidth * clientWeight;      // Client
                gridView.Columns[4].Width = unitWidth * warehouseWeight;   // Warehouse
                gridView.Columns[5].Width = unitWidth * totalWeight;       // Total
                gridView.Columns[6].Width = unitWidth * paidWeight;        // Paid
                gridView.Columns[7].Width = unitWidth * remainingWeight;   // Remaining
                gridView.Columns[8].Width = unitWidth * returnsWeight;     // Returns
                gridView.Columns[9].Width = actionColumnWidth;             // Operations (now at index 9)
            }
        }

        private async void btnExport_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (_sales.Count == 0)
                {
                    ErrorBox.Show("لا توجد بيانات لتصديرها", "تنبيه", false);
                    return;
                }

                // استخدام المساعد الجديد لتصدير المبيعات إلى Excel
                var exportHelper = new SalesExportHelper(_sales.ToList(), _clientId, _warehouseId);
                await exportHelper.StartExportProcess();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تصدير البيانات: {ex.Message}", "خطأ في التصدير", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnSearch_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                await ApplyFiltersAsync();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        // أحداث تغيير قيم الفلاتر

        private async void dpFromDate_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            _fromDate = dpFromDate.SelectedDate;
        }

        private async void dpToDate_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            _toDate = dpToDate.SelectedDate;
        }

        private async void cmbClients_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (cmbClients.SelectedValue != null && cmbClients.SelectedValue is int clientId && clientId != -1)
            {
                _clientId = clientId;
            }
            else
            {
                _clientId = null;
            }
        }

        private async void cmbWarehouses_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (cmbWarehouses.SelectedValue != null && cmbWarehouses.SelectedValue is int warehouseId && warehouseId != -1)
            {
                _warehouseId = warehouseId;
            }
            else
            {
                _warehouseId = null;
            }

            // إعادة تحميل العملاء حسب المخزن المختار
            await LoadClientsAsync();
        }

        private async void chkHasRemaining_CheckedChanged(object sender, RoutedEventArgs e)
        {
            _hasRemaining = chkHasRemaining.IsChecked;
        }

        private async void cmbReturnsFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (cmbReturnsFilter.SelectedItem is ComboBoxItem selectedItem)
            {
                _returnsFilter = selectedItem.Tag?.ToString();
            }
        }

        private async void chkAllPeriods_CheckedChanged(object sender, RoutedEventArgs e)
        {
            // تحديث حالة عناصر التاريخ
            UpdateDateControlsState();

            if (chkAllPeriods.IsChecked == true)
            {
                // إذا تم اختيار "كل الفترات"، نعين قيم التاريخ بـ null
                _fromDate = null;
                _toDate = null;
            }
            else
            {
                // إذا تم إلغاء اختيار "كل الفترات"، نعيد تعيين تاريخ اليوم كتاريخ بداية افتراضي
                _fromDate = DateTime.Today;
                dpFromDate.SelectedDate = _fromDate;
            }
        }

        private async void btnResetFilters_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // إعادة تعيين قيم الفلاتر
                _fromDate = DateTime.Today; // تعيين تاريخ اليوم
                _toDate = null;
                _clientId = null;
                _hasRemaining = null;
                _invoiceId = null;
                _warehouseId = null;
                _returnsFilter = "All";

                // إعادة تعيين عناصر واجهة المستخدم
                chkAllPeriods.IsChecked = false; // إلغاء اختيار "كل الفترات"
                dpFromDate.SelectedDate = _fromDate; // تعيين تاريخ اليوم
                dpToDate.SelectedDate = null;
                cmbClients.SelectedIndex = 0; // "الكل"
                cmbWarehouses.SelectedIndex = 0; // "الكل"
                chkHasRemaining.IsChecked = null;
                txtInvoiceNumber.Text = string.Empty;
                cmbReturnsFilter.SelectedIndex = 0; // "الكل"

                // تحديث حالة عناصر التاريخ
                UpdateDateControlsState();

                // تطبيق الفلاتر لعرض فواتير اليوم فقط
                await ApplyFiltersAsync();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void EditButton_Click(object sender, RoutedEventArgs e)
        {
            // التحقق من صلاحيات المستخدم
            bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditSalesRole");
            bool canView = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("SalesRole") || CurrentUser.HasRole("EditSalesRole") || CurrentUser.HasRole("DeleteSalesRole");

            // إذا لم يكن المستخدم يملك صلاحية العرض على الأقل، نمنع الوصول
            if (!canView)
            {
                ErrorBox.Show("لا تملك صلاحية عرض فواتير المبيعات", "خطأ في الصلاحيات", true);
                return;
            }

            // Get the sale associated with the button from the data context
            if (sender is Button button && button.DataContext is SaleViewModel selectedSale)
            {
                if (selectedSale == null)
                {
                    ErrorBox.Show("الرجاء اختيار فاتورة للعرض", "تنبيه", false);
                    return;
                }

                try
                {
                    _firstLoad = false;
                    // فتح صفحة عرض/تعديل المبيعات مع تمرير الفاتورة المحددة والانتظار حتى إغلاقها
                    await NavigateAction.NavigateToAndWaitAsync(new SalesView(selectedSale.Id));

                    // إعادة تحميل البيانات بعد العرض/التعديل مع تطبيق الفلاتر الحالية
                    await ApplyFiltersAsync();
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"حدث خطأ أثناء فتح صفحة الفاتورة: {ex.Message}", "خطأ في النظام", true);
                }
            }
        }

        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            // التحقق من صلاحية الحذف
            bool canDelete = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("DeleteSalesRole");
            if (!canDelete)
            {
                ErrorBox.Show("لا تملك صلاحية حذف فواتير المبيعات", "خطأ في الصلاحيات", true);
                return;
            }

            // Get the sale associated with the button from the data context
            if (sender is Button button && button.DataContext is SaleViewModel selectedSale)
            {
                if (selectedSale == null)
                {
                    ErrorBox.Show("الرجاء اختيار فاتورة للحذف", "تنبيه", false);
                    return;
                }

                var result = QuestionBox.Show("تأكيد الحذف", $"هل أنت متأكد من حذف الفاتورة رقم {selectedSale.Id}؟");
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // Call the DeleteSaleAsync method in the SaleService
                        var (success, message) = await _saleService.DeleteSaleAsync(selectedSale.Id);

                        if (success)
                        {
                            await ApplyFiltersAsync();
                            DialogBox.Show("تم بنجاح", "تم حذف الفاتورة بنجاح");
                        }
                        else
                        {
                            ErrorBox.Show(message, "خطأ في الحذف", false);
                        }
                    }
                    catch (Exception ex)
                    {
                        ErrorBox.Show($"حدث خطأ أثناء حذف الفاتورة: {ex.Message}", "خطأ في الحذف", true);
                    }
                }
            }
        }

        private void GridCheckBox_Toggle(object sender, MouseButtonEventArgs e)
        {
            // Make sure the click wasn't directly on a checkbox (to avoid double toggling)
            if (e.OriginalSource is CheckBox)
                return;

            if (sender is Grid grid)
            {
                // Find the first CheckBox inside the grid (you can adjust if you want a specific name or type)
                var checkBox = grid.Children.OfType<CheckBox>().FirstOrDefault();
                if (checkBox != null)
                {
                    checkBox.IsChecked = !checkBox.IsChecked;
                }
            }
        }

        private async void list_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            // التحقق من صلاحيات المستخدم
            bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditSalesRole");
            bool canView = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("SalesRole") || CurrentUser.HasRole("EditSalesRole") || CurrentUser.HasRole("DeleteSalesRole");

            // إذا لم يكن المستخدم يملك صلاحية العرض على الأقل، نمنع الوصول
            if (!canView)
            {
                ErrorBox.Show("لا تملك صلاحية عرض فواتير المبيعات", "خطأ في الصلاحيات", true);
                return;
            }

            // Get the selected sale
            if (list.SelectedItem is SaleViewModel selectedSale)
            {
                if (selectedSale == null)
                {
                    return;
                }

                try
                {
                    // فتح صفحة عرض/تعديل المبيعات مع تمرير الفاتورة المحددة والانتظار حتى إغلاقها
                    await NavigateAction.NavigateToAndWaitAsync(new SalesView(selectedSale.Id));

                    // إعادة تحميل البيانات بعد العرض/التعديل مع تطبيق الفلاتر الحالية
                    await ApplyFiltersAsync();
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"حدث خطأ أثناء فتح صفحة الفاتورة: {ex.Message}", "خطأ في النظام", true);
                }
            }
        }
    }
}
