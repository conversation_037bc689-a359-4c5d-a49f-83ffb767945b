﻿<Window
    x:Class="VisionPoint.UI.views.Pages.Settings.DiscountWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:VisionPoint.UI"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Settings"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils"
    Title="DiscountWindow"
    AllowsTransparency="True"
    Background="Transparent"
    FlowDirection="RightToLeft"
    Loaded="Window_Loaded"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">

    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">

        <Border
            Width="1920"
            Height="1080"
            Background="{StaticResource backgroundColor}"
            BorderBrush="LightGray"
            BorderThickness="1.5"
            CornerRadius="16">



            <Grid Margin="16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition />
                    <ColumnDefinition />
                    <ColumnDefinition />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>

                <TextBlock
                    Grid.ColumnSpan="8"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="24"
                    FontWeight="Bold"
                    TextAlignment="Center">
                    إدارة الخصومات
                </TextBlock>

                <Border
                    x:Name="btnclose"
                    Grid.ColumnSpan="4"
                    Width="24"
                    Height="24"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Top"
                    Background="Red"
                    CornerRadius="50"
                    MouseLeftButtonDown="btnclose_MouseLeftButtonDown" />

                <Grid
                    Grid.Row="1"
                    Grid.Column="2"
                    Grid.ColumnSpan="2"
                    Margin="8,0">


                    <TextBox
                        x:Name="txtDiscountPercentage"
                        Height="60"
                        VerticalAlignment="Stretch"
                        VerticalContentAlignment="Stretch"
                        BorderThickness="1"
                        FontSize="21"
                        Tag="نسبة الخصم %" />

                </Grid>

                <Grid
                    Grid.Row="1"
                    Grid.ColumnSpan="2"
                    Margin="16,0">


                    <ComboBox
                        x:Name="cmbItems"
                        Height="60"
                        Padding="42,0"
                        VerticalAlignment="Stretch"
                        VerticalContentAlignment="Stretch"
                        BorderThickness="1"
                        FontSize="21"
                        SelectionChanged="cmbItems_SelectionChanged"
                        Tag="اختر العناصر" />


                </Grid>


                <DatePicker
                    x:Name="dtpStartDate"
                    Grid.Row="2"
                    Grid.ColumnSpan="2"
                    Height="60"
                    Margin="8,0"
                    BorderBrush="{StaticResource PrimaryTextColor}"
                    BorderThickness="1"
                    FontSize="18"
                    SelectedDateFormat="Short">
                    <DatePicker.Resources>
                        <Style TargetType="{x:Type DatePickerTextBox}">
                            <Setter Property="Control.Template">
                                <Setter.Value>
                                    <ControlTemplate>
                                        <TextBox
                                            x:Name="PART_TextBox"
                                            VerticalAlignment="Stretch"
                                            BorderThickness="0"
                                            Foreground="{StaticResource PrimaryTextColor}"
                                            Style="{StaticResource txtDatePick}"
                                            Tag="تاريخ بداية الخصم"
                                            Text="{Binding Path=SelectedDate, StringFormat='dd/MM/yyyy', RelativeSource={RelativeSource AncestorType={x:Type DatePicker}}}" />
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </DatePicker.Resources>
                </DatePicker>

                <DatePicker
                    x:Name="dtpEndDate"
                    Grid.Row="2"
                    Grid.Column="2"
                    Grid.ColumnSpan="2"
                    Height="60"
                    Margin="8,0"
                    BorderBrush="{StaticResource PrimaryTextColor}"
                    BorderThickness="1"
                    FontSize="18"
                    SelectedDateFormat="Short">
                    <DatePicker.Resources>
                        <Style TargetType="{x:Type DatePickerTextBox}">
                            <Setter Property="Control.Template">
                                <Setter.Value>
                                    <ControlTemplate>
                                        <TextBox
                                            x:Name="PART_TextBox"
                                            VerticalAlignment="Stretch"
                                            BorderThickness="0"
                                            Foreground="{StaticResource PrimaryTextColor}"
                                            Style="{StaticResource txtDatePick}"
                                            Tag="تاريخ نهاية الخصم"
                                            Text="{Binding Path=SelectedDate, StringFormat='dd/MM/yyyy', RelativeSource={RelativeSource AncestorType={x:Type DatePicker}}}" />
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </DatePicker.Resources>
                </DatePicker>


                <Grid Grid.Row="3" Grid.ColumnSpan="4">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition />
                        <ColumnDefinition />
                        <ColumnDefinition />
                    </Grid.ColumnDefinitions>
                    <DockPanel>
                        <TextBlock
                            HorizontalAlignment="Center"
                            DockPanel.Dock="Top"
                            Text="تطبيق على جميع المنتجات" />
                        <CheckBox
                            x:Name="chkApplyToAllProducts"
                            Grid.Column="0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Checked="chkApplyToAllProducts_Checked"
                            DockPanel.Dock="Top"
                            Style="{StaticResource CircleCheckboxFL}"
                            Unchecked="chkApplyToAllProducts_Unchecked" />
                    </DockPanel>


                    <DockPanel Grid.Column="1">
                        <TextBlock
                            HorizontalAlignment="Center"
                            DockPanel.Dock="Top"
                            Text="تطبيق على جميع العدسات" />
                        <CheckBox
                            x:Name="chkApplyToAllLenses"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Checked="chkApplyToAllLenses_Checked"
                            DockPanel.Dock="Top"
                            Style="{StaticResource CircleCheckboxFL}"
                            Unchecked="chkApplyToAllLenses_Unchecked" />
                    </DockPanel>
                    <DockPanel Grid.Column="2">
                        <TextBlock
                            HorizontalAlignment="Center"
                            DockPanel.Dock="Top"
                            Text="تطبيق على جميع الخدمات" />
                        <CheckBox
                            x:Name="chkApplyToAllServices"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Checked="chkApplyToAllServices_Checked"
                            DockPanel.Dock="Top"
                            Style="{StaticResource CircleCheckboxFL}"
                            Unchecked="chkApplyToAllServices_Unchecked" />
                    </DockPanel>
                </Grid>

                <Grid
                    Grid.Row="4"
                    Grid.ColumnSpan="4"
                    Margin="0,10,0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition />
                        <ColumnDefinition />
                        <ColumnDefinition />
                    </Grid.ColumnDefinitions>

                    <RadioButton
                        x:Name="RdbProductSelect"
                        Grid.Column="0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Checked="RdbProduct_Checked"
                        Content="المنتجات"
                        GroupName="ItemSelect" />

                    <RadioButton
                        x:Name="RdbLensesSelect"
                        Grid.Column="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Checked="RdbLenses_Checked"
                        Content="العدسات"
                        GroupName="ItemSelect" />

                    <RadioButton
                        x:Name="RdbServiceSelect"
                        Grid.Column="2"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Checked="RdbService_Checked"
                        Content="الخدمات"
                        GroupName="ItemSelect" />
                </Grid>






                <Border
                    x:Name="btnSave"
                    Grid.Row="5"
                    Grid.Column="0"
                    MaxHeight="44"
                    Margin="8,0"
                    Background="{StaticResource PrimaryColor}"
                    CornerRadius="8"
                    Cursor="Hand"
                    MouseLeftButtonDown="btnSave_MouseLeftButtonDown">
                    <TextBlock
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        FontSize="18"
                        Foreground="White">
                        حفظ
                    </TextBlock>
                </Border>


                <Border
                    x:Name="btnNew"
                    Grid.Row="5"
                    Grid.Column="1"
                    MaxHeight="44"
                    Margin="8,0"
                    Background="Transparent"
                    BorderBrush="{StaticResource PrimaryColor}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Cursor="Hand"
                    MouseLeftButtonDown="btnNew_MouseLeftButtonDown">
                    <TextBlock
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        FontSize="18"
                        Foreground="{StaticResource PrimaryColor}">
                        جديد
                    </TextBlock>
                </Border>


                <Border
                    x:Name="btnDelete"
                    Grid.Row="5"
                    Grid.Column="2"
                    MaxHeight="44"
                    Margin="8,0"
                    Background="Transparent"
                    BorderBrush="{StaticResource errorColor}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Cursor="Hand"
                    MouseLeftButtonDown="btnDelete_MouseLeftButtonDown">
                    <TextBlock
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        FontSize="18"
                        Foreground="{StaticResource errorColor}">
                        حذف
                    </TextBlock>
                </Border>



                <Border
                    x:Name="btnSearch"
                    Grid.Row="5"
                    Grid.Column="3"
                    MaxHeight="44"
                    Margin="8,0"
                    Background="{StaticResource PrimaryColor}"
                    CornerRadius="8"
                    Cursor="Hand"
                    MouseLeftButtonDown="btnSearch_MouseLeftButtonDown">
                    <TextBlock
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        FontSize="18"
                        Foreground="White">
                        بحث
                    </TextBlock>
                </Border>

                <ListView
                    x:Name="list"
                    Grid.Row="6"
                    Grid.RowSpan="8"
                    Grid.ColumnSpan="4"
                    Background="{DynamicResource PageColor}"
                    BorderThickness="1"
                    FontFamily="pack://application:,,,/Assets/#Cairo"
                    ItemsSource="{Binding}"
                    MouseDoubleClick="list_MouseDoubleClick"
                    ScrollViewer.HorizontalScrollBarVisibility="Hidden"
                    SizeChanged="list_SizeChanged">
                    <ListView.BorderBrush>
                        <SolidColorBrush Opacity="0.42" Color="Black" />
                    </ListView.BorderBrush>

                    <ListView.ItemContainerStyle>
                        <Style TargetType="ListViewItem">
                            <Style.Triggers>
                                <Trigger Property="Control.IsMouseOver" Value="True">
                                    <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                                    <Setter Property="FontWeight" Value="Bold" />
                                    <Setter Property="Foreground" Value="Black" />
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                                    <Setter Property="Foreground" Value="Black" />
                                </Trigger>

                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="FontWeight" Value="Bold" />
                                    <Setter Property="Foreground" Value="Black" />
                                </Trigger>

                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="IsSelected" Value="False" />
                                        <Condition Property="IsMouseOver" Value="False" />
                                    </MultiTrigger.Conditions>
                                    <Setter Property="FontWeight" Value="Thin" />
                                    <Setter Property="Foreground" Value="{DynamicResource PrimaryTextColor}" />
                                </MultiTrigger>

                            </Style.Triggers>
                            <Setter Property="Control.Background" Value="{DynamicResource PageColor}" />
                            <Setter Property="Control.HorizontalContentAlignment" Value="Stretch" />
                        </Style>

                    </ListView.ItemContainerStyle>
                    <ListView.View>
                        <GridView AllowsColumnReorder="False">
                            <GridView.ColumnHeaderContainerStyle>
                                <Style BasedOn="{StaticResource ListViewHeader}" TargetType="{x:Type GridViewColumnHeader}">
                                    <Setter Property="IsEnabled" Value="False" />
                                    <Setter Property="Height" Value="60" />
                                    <Style.Triggers>
                                        <Trigger Property="IsEnabled" Value="False">
                                            <Setter Property="TextElement.Foreground" Value="Black" />
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </GridView.ColumnHeaderContainerStyle>
                            <GridViewColumn Width="Auto" Header="نسبة الخصم">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="45"
                                            MinWidth="35"
                                            HorizontalAlignment="Center"
                                            Background="Transparent"
                                            Text="{Binding FormattedPercentage, FallbackValue='0%', TargetNullValue='0%'}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>

                            <GridViewColumn Width="Auto" Header="تاريخ البداية">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="45"
                                            MinWidth="35"
                                            HorizontalAlignment="Center"
                                            Background="Transparent"
                                            Text="{Binding FormattedStartDate, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>

                            <GridViewColumn Width="Auto" Header="تاريخ النهاية">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="45"
                                            MinWidth="35"
                                            HorizontalAlignment="Center"
                                            Background="Transparent"
                                            Text="{Binding FormattedEndDate, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>

                            <GridViewColumn Width="Auto" Header="النوع">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="45"
                                            MinWidth="35"
                                            HorizontalAlignment="Center"
                                            Background="Transparent"
                                            Text="{Binding ItemType, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>

                            <GridViewColumn Width="Auto" Header="الحالة">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="45"
                                            MinWidth="35"
                                            HorizontalAlignment="Center"
                                            Background="Transparent"
                                            Text="{Binding Status, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>

                        </GridView>
                    </ListView.View>
                </ListView>

            </Grid>
        </Border>
    </Viewbox>
</Window>