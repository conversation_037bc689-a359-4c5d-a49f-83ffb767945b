﻿using ClosedXML.Excel;
using Microsoft.Win32;
using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Helper;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.Reports.client;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.Clients
{
    /// <summary>
    /// Interaction logic for ClientStatementWindow.xaml
    /// </summary>
    public partial class ClientStatementWindow : Window
    {
        private readonly ClientService _clientService;
        private readonly Client _client;
        private ObservableCollection<ClientStatementVM> _statementItems;
        private DateTime? _fromDate;
        private DateTime? _toDate;

        public ClientStatementWindow(Client client)
        {
            InitializeComponent();
            _client = client;
            _clientService = new ClientService();
            _statementItems = new ObservableCollection<ClientStatementVM>();
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // عرض معلومات العميل
            txtClientName.Text = _client.Name;
            txtClientBalance.Text = $"الرصيد الحالي: {_client.Balance:N3}";

            // تعيين حالة عناصر التاريخ
            chkAllPeriods.IsChecked = true;
            UpdateDateControlsState();

            // تحميل كشف الحساب
            await LoadClientStatementAsync();
        }

        /// <summary>
        /// تحديث حالة عناصر التاريخ بناءً على خيار "كل الفترات"
        /// </summary>
        private void UpdateDateControlsState()
        {
            bool isAllPeriods = chkAllPeriods.IsChecked == true;

            // تعطيل/تفعيل عناصر التاريخ
            dpFromDate.IsEnabled = !isAllPeriods;
            dpToDate.IsEnabled = !isAllPeriods;
        }

        private async void btnApplyFilter_Click(object sender, RoutedEventArgs e)
        {
            await LoadClientStatementAsync();
        }

        private async Task LoadClientStatementAsync()
        {
            try
            {
                // تحميل كشف الحساب مع تطبيق الفلاتر
                var (transactions, summary) = await _clientService.GetClientStatementAsync(_client.Id, _fromDate, _toDate);

                // تحديث القائمة
                _statementItems.Clear();
                foreach (var item in transactions)
                {
                    _statementItems.Add(item);
                }

                list.ItemsSource = _statementItems;

                // تحديث عنوان النافذة
                Title = $"كشف حساب {_client.Name} - {_statementItems.Count} عملية";

                // تحديث ملخص كشف الحساب
                txtClientBalance.Text = $"الرصيد الحالي: {_client.Balance:N3}";
                txtTotalDebit.Text = $"مجموع المدين: {summary.TotalDebit:N3}";
                txtTotalCredit.Text = $"مجموع الدائن: {summary.TotalCredit:N3}";
                txtFinalBalance.Text = $"الرصيد النهائي: {summary.FinalBalance:N3}";
                txtBalanceStatus.Text = $"حالة الرصيد: {summary.BalanceStatus}";
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل كشف الحساب: {ex.Message}", "خطأ في التحميل", true);
            }
        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (list.View is GridView gridView && gridView.Columns.Count >= 7)
            {
                double padding = 10;
                double scrollbarWidth = SystemParameters.VerticalScrollBarWidth;

                // حساب العرض المتاح للأعمدة الديناميكية
                double availableWidth = list.ActualWidth - scrollbarWidth - padding;

                // تعيين أوزان العرض النسبية للأعمدة
                double totalWeight = 1.5 + 2 + 1 + 3 + 1.5 + 1.5 + 1.5;
                double unitWidth = availableWidth / totalWeight;

                gridView.Columns[0].Width = unitWidth * 1.5; // التاريخ
                gridView.Columns[1].Width = unitWidth * 2;   // نوع العملية
                gridView.Columns[2].Width = unitWidth * 1;   // رقم العملية
                gridView.Columns[3].Width = unitWidth * 3;   // البيان
                gridView.Columns[4].Width = unitWidth * 1.5; // قبض
                gridView.Columns[5].Width = unitWidth * 1.5; // دفع
                gridView.Columns[6].Width = unitWidth * 1.5; // الرصيد
            }
        }

        private void chkAllPeriods_CheckedChanged(object sender, RoutedEventArgs e)
        {
            // تحديث حالة عناصر التاريخ
            UpdateDateControlsState();

            if (chkAllPeriods.IsChecked == true)
            {
                // إذا تم اختيار "كل الفترات"، نعين قيم التاريخ بـ null
                _fromDate = null;
                _toDate = null;
            }
            else
            {
                // إذا تم إلغاء اختيار "كل الفترات"، نستخدم القيم المحددة في عناصر التاريخ
                _fromDate = dpFromDate.SelectedDate;
                _toDate = dpToDate.SelectedDate;
            }
        }

        private void DatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            if (chkAllPeriods.IsChecked == false)
            {
                // تحديث قيم التاريخ فقط إذا لم يتم اختيار "كل الفترات"
                _fromDate = dpFromDate.SelectedDate;
                _toDate = dpToDate.SelectedDate;
            }
        }

        private void btnPrint_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء نسخة قابلة للطباعة من كشف الحساب
                PrintDialog printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // إنشاء مستند للطباعة
                    FlowDocument document = new FlowDocument();
                    document.PagePadding = new Thickness(50);
                    document.FontFamily = new FontFamily("Arial");
                    document.FontSize = 12;
                    document.TextAlignment = TextAlignment.Right;
                    document.FlowDirection = FlowDirection.RightToLeft;

                    // إضافة عنوان المستند
                    Paragraph title = new Paragraph(new Run($"كشف حساب {_client.Name}"));
                    title.FontSize = 20;
                    title.FontWeight = FontWeights.Bold;
                    title.TextAlignment = TextAlignment.Center;
                    document.Blocks.Add(title);

                    // إضافة معلومات العميل
                    Paragraph clientInfo = new Paragraph(new Run($"الرصيد الحالي: {_client.Balance:N3}"));
                    clientInfo.TextAlignment = TextAlignment.Center;
                    clientInfo.Margin = new Thickness(0, 0, 0, 20);
                    document.Blocks.Add(clientInfo);

                    // إضافة معلومات الفترة
                    string periodInfo = chkAllPeriods.IsChecked == true
                        ? "الفترة: كل الفترات"
                        : $"الفترة: من {_fromDate?.ToString("yyyy-MM-dd") ?? ""} إلى {_toDate?.ToString("yyyy-MM-dd") ?? ""}";
                    Paragraph period = new Paragraph(new Run(periodInfo));
                    period.TextAlignment = TextAlignment.Center;
                    period.Margin = new Thickness(0, 0, 0, 20);
                    document.Blocks.Add(period);

                    // إنشاء جدول للبيانات
                    Table table = new Table();
                    table.CellSpacing = 0;
                    table.BorderBrush = Brushes.Black;
                    table.BorderThickness = new Thickness(1);

                    // تعريف أعمدة الجدول
                    for (int i = 0; i < 7; i++)
                    {
                        table.Columns.Add(new TableColumn());
                    }

                    // إضافة رأس الجدول
                    TableRowGroup headerGroup = new TableRowGroup();
                    TableRow headerRow = new TableRow();
                    headerRow.Background = Brushes.LightGray;
                    headerRow.FontWeight = FontWeights.Bold;

                    string[] headers = { "التاريخ", "نوع العملية", "طريقة الدفع", "البيان", "قبض", "دفع", "الرصيد" };
                    foreach (string header in headers)
                    {
                        headerRow.Cells.Add(new TableCell(new Paragraph(new Run(header))));
                    }
                    headerGroup.Rows.Add(headerRow);
                    table.RowGroups.Add(headerGroup);

                    // إضافة بيانات الجدول
                    TableRowGroup dataGroup = new TableRowGroup();
                    foreach (var item in _statementItems)
                    {
                        TableRow row = new TableRow();

                        // التاريخ
                        row.Cells.Add(new TableCell(new Paragraph(new Run(item.Date.ToString("yyyy-MM-dd")))));

                        // نوع العملية
                        row.Cells.Add(new TableCell(new Paragraph(new Run(item.OperationType))));

                        // طريقة الدفع
                        row.Cells.Add(new TableCell(new Paragraph(new Run(item.PaymentMethod ?? "غير محدد"))));

                        // البيان
                        row.Cells.Add(new TableCell(new Paragraph(new Run(item.Description))));

                        // قبض
                        TableCell incomingCell = new TableCell(new Paragraph(new Run(item.Incoming.ToString("N3"))));
                        row.Cells.Add(incomingCell);

                        // دفع
                        TableCell outgoingCell = new TableCell(new Paragraph(new Run(item.Outgoing.ToString("N3"))));
                        row.Cells.Add(outgoingCell);

                        // الرصيد
                        TableCell balanceCell = new TableCell(new Paragraph(new Run(item.Balance.ToString("N3"))));
                        row.Cells.Add(balanceCell);

                        // تنسيق الصفوف الخاصة (الرصيد السابق والرصيد اللاحق)
                        if (item.IsSpecialRow)
                        {
                            // تنسيق خاص للصفوف الخاصة
                            row.Background = new SolidColorBrush(System.Windows.Media.Color.FromArgb(50, 0, 0, 255)); // أزرق شفاف
                            row.FontWeight = FontWeights.Bold;
                            row.FontStyle = FontStyles.Italic;

                            // تطبيق التنسيق على جميع الخلايا
                            foreach (var cell in row.Cells)
                            {
                                cell.Foreground = Brushes.Navy;
                            }
                        }
                        else
                        {
                            // تنسيق خلايا القبض والدفع والرصيد للصفوف العادية
                            if (item.Incoming > 0)
                            {
                                incomingCell.Foreground = Brushes.Green;
                                incomingCell.FontWeight = FontWeights.Bold;
                            }

                            if (item.Outgoing > 0)
                            {
                                outgoingCell.Foreground = Brushes.Red;
                                outgoingCell.FontWeight = FontWeights.Bold;
                            }

                            balanceCell.FontWeight = FontWeights.Bold;
                            if (item.Balance > 0)
                            {
                                // رصيد موجب (العميل مدين للمتجر) - أحمر
                                balanceCell.Foreground = Brushes.Red;
                            }
                            else if (item.Balance < 0)
                            {
                                // رصيد سالب (المتجر مدين للعميل) - أخضر
                                balanceCell.Foreground = Brushes.Green;
                            }
                        }

                        dataGroup.Rows.Add(row);
                    }
                    table.RowGroups.Add(dataGroup);

                    document.Blocks.Add(table);

                    // إضافة ملخص كشف الحساب
                    Paragraph summaryTitle = new Paragraph(new Run("ملخص كشف الحساب"));
                    summaryTitle.FontSize = 16;
                    summaryTitle.FontWeight = FontWeights.Bold;
                    summaryTitle.TextAlignment = TextAlignment.Center;
                    summaryTitle.Margin = new Thickness(0, 20, 0, 10);
                    document.Blocks.Add(summaryTitle);

                    // إنشاء جدول الملخص
                    Table summaryTable = new Table();
                    summaryTable.CellSpacing = 0;
                    summaryTable.BorderBrush = Brushes.Black;
                    summaryTable.BorderThickness = new Thickness(1);

                    // تعريف أعمدة الجدول
                    for (int i = 0; i < 3; i++)
                    {
                        summaryTable.Columns.Add(new TableColumn());
                    }

                    // إضافة رأس الجدول
                    TableRowGroup summaryHeaderGroup = new TableRowGroup();
                    TableRow summaryHeaderRow = new TableRow();
                    summaryHeaderRow.Background = Brushes.LightGray;
                    summaryHeaderRow.FontWeight = FontWeights.Bold;

                    summaryHeaderRow.Cells.Add(new TableCell(new Paragraph(new Run("مجموع المدين"))) { TextAlignment = TextAlignment.Center });
                    summaryHeaderRow.Cells.Add(new TableCell(new Paragraph(new Run("مجموع الدائن"))) { TextAlignment = TextAlignment.Center });
                    summaryHeaderRow.Cells.Add(new TableCell(new Paragraph(new Run("الرصيد النهائي"))) { TextAlignment = TextAlignment.Center });

                    summaryHeaderGroup.Rows.Add(summaryHeaderRow);
                    summaryTable.RowGroups.Add(summaryHeaderGroup);

                    // إضافة بيانات الملخص
                    TableRowGroup summaryDataGroup = new TableRowGroup();
                    TableRow summaryDataRow = new TableRow();

                    // استخراج قيم الملخص من عناصر واجهة المستخدم
                    string totalDebit = txtTotalDebit.Text.Replace("مجموع المدين: ", "");
                    string totalCredit = txtTotalCredit.Text.Replace("مجموع الدائن: ", "");
                    string finalBalance = txtFinalBalance.Text.Replace("الرصيد النهائي: ", "");

                    summaryDataRow.Cells.Add(new TableCell(new Paragraph(new Run(totalDebit))) { TextAlignment = TextAlignment.Center });
                    summaryDataRow.Cells.Add(new TableCell(new Paragraph(new Run(totalCredit))) { TextAlignment = TextAlignment.Center });
                    summaryDataRow.Cells.Add(new TableCell(new Paragraph(new Run(finalBalance))) { TextAlignment = TextAlignment.Center });

                    summaryDataGroup.Rows.Add(summaryDataRow);
                    summaryTable.RowGroups.Add(summaryDataGroup);

                    document.Blocks.Add(summaryTable);

                    // إضافة توضيح حالة الرصيد
                    string balanceStatus = txtBalanceStatus.Text;
                    Paragraph balanceStatusParagraph = new Paragraph(new Run(balanceStatus));
                    balanceStatusParagraph.TextAlignment = TextAlignment.Center;
                    balanceStatusParagraph.Margin = new Thickness(0, 10, 0, 0);
                    document.Blocks.Add(balanceStatusParagraph);

                    // طباعة المستند
                    printDialog.PrintDocument(((IDocumentPaginatorSource)document).DocumentPaginator, $"كشف حساب {_client.Name}");
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء الطباعة: {ex.Message}", "خطأ في الطباعة", true);
            }
        }

        private void btnExportExcel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء مربع حوار حفظ الملف
                SaveFileDialog saveFileDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx",
                    FileName = $"كشف_حساب_{_client.Name}_{DateTime.Now:yyyy-MM-dd}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    using (var workbook = new XLWorkbook())
                    {
                        var worksheet = workbook.Worksheets.Add("كشف الحساب");

                        // إضافة عنوان الورقة
                        worksheet.Cell("A1").Value = $"كشف حساب {_client.Name}";
                        worksheet.Range("A1:G1").Merge();
                        worksheet.Cell("A1").Style.Font.Bold = true;
                        worksheet.Cell("A1").Style.Font.FontSize = 16;
                        worksheet.Cell("A1").Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                        // إضافة معلومات العميل
                        worksheet.Cell("A2").Value = $"الرصيد الحالي: {_client.Balance:N3}";
                        worksheet.Range("A2:G2").Merge();
                        worksheet.Cell("A2").Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                        // إضافة معلومات الفترة
                        string periodInfo = chkAllPeriods.IsChecked == true
                            ? "الفترة: كل الفترات"
                            : $"الفترة: من {_fromDate?.ToString("yyyy-MM-dd") ?? ""} إلى {_toDate?.ToString("yyyy-MM-dd") ?? ""}";
                        worksheet.Cell("A3").Value = periodInfo;
                        worksheet.Range("A3:G3").Merge();
                        worksheet.Cell("A3").Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                        // إضافة رأس الجدول
                        string[] headers = { "التاريخ", "نوع العملية", "طريقة الدفع", "البيان", "قبض", "دفع", "الرصيد" };
                        for (int i = 0; i < headers.Length; i++)
                        {
                            worksheet.Cell(5, i + 1).Value = headers[i];
                            worksheet.Cell(5, i + 1).Style.Font.Bold = true;
                            worksheet.Cell(5, i + 1).Style.Fill.BackgroundColor = XLColor.LightGray;
                            worksheet.Cell(5, i + 1).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                        }

                        // إضافة بيانات الجدول
                        int row = 6;
                        foreach (var item in _statementItems)
                        {
                            worksheet.Cell(row, 1).Value = item.Date.ToString("yyyy-MM-dd");
                            worksheet.Cell(row, 2).Value = item.OperationType;
                            worksheet.Cell(row, 3).Value = item.PaymentMethod ?? "غير محدد";
                            worksheet.Cell(row, 4).Value = item.Description;
                            worksheet.Cell(row, 5).Value = item.Incoming;
                            worksheet.Cell(row, 6).Value = item.Outgoing;
                            worksheet.Cell(row, 7).Value = item.Balance;

                            // تنسيق الخلايا
                            for (int i = 1; i <= 7; i++)
                            {
                                worksheet.Cell(row, i).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                            }

                            // تنسيق الصفوف الخاصة (الرصيد السابق والرصيد اللاحق)
                            if (item.IsSpecialRow)
                            {
                                // تنسيق خاص للصفوف الخاصة
                                for (int i = 1; i <= 7; i++)
                                {
                                    worksheet.Cell(row, i).Style.Font.Bold = true;
                                    worksheet.Cell(row, i).Style.Font.Italic = true;
                                    worksheet.Cell(row, i).Style.Font.FontColor = XLColor.Navy;
                                    worksheet.Cell(row, i).Style.Fill.BackgroundColor = XLColor.FromArgb(230, 240, 255); // لون أزرق فاتح
                                }
                            }
                            else
                            {
                                // تنسيق خلايا القبض والدفع والرصيد للصفوف العادية
                                if (item.Incoming > 0)
                                {
                                    worksheet.Cell(row, 5).Style.Font.FontColor = XLColor.Green;
                                    worksheet.Cell(row, 5).Style.Font.Bold = true;
                                }

                                if (item.Outgoing > 0)
                                {
                                    worksheet.Cell(row, 6).Style.Font.FontColor = XLColor.Red;
                                    worksheet.Cell(row, 6).Style.Font.Bold = true;
                                }

                                worksheet.Cell(row, 7).Style.Font.Bold = true;
                                if (item.Balance > 0)
                                {
                                    // رصيد موجب (العميل مدين للمتجر) - أحمر
                                    worksheet.Cell(row, 7).Style.Font.FontColor = XLColor.Red;
                                }
                                else if (item.Balance < 0)
                                {
                                    // رصيد سالب (المتجر مدين للعميل) - أخضر
                                    worksheet.Cell(row, 7).Style.Font.FontColor = XLColor.Green;
                                }
                            }

                            row++;
                        }

                        // إضافة ملخص كشف الحساب
                        int summaryStartRow = row + 2;

                        // عنوان الملخص
                        worksheet.Cell(summaryStartRow, 1).Value = "ملخص كشف الحساب";
                        worksheet.Range(summaryStartRow, 1, summaryStartRow, 7).Merge();
                        worksheet.Cell(summaryStartRow, 1).Style.Font.Bold = true;
                        worksheet.Cell(summaryStartRow, 1).Style.Font.FontSize = 14;
                        worksheet.Cell(summaryStartRow, 1).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                        // رأس جدول الملخص
                        worksheet.Cell(summaryStartRow + 1, 3).Value = "مجموع المدين";
                        worksheet.Cell(summaryStartRow + 1, 4).Value = "مجموع الدائن";
                        worksheet.Cell(summaryStartRow + 1, 5).Value = "الرصيد النهائي";

                        // تنسيق رأس جدول الملخص
                        for (int i = 3; i <= 5; i++)
                        {
                            worksheet.Cell(summaryStartRow + 1, i).Style.Font.Bold = true;
                            worksheet.Cell(summaryStartRow + 1, i).Style.Fill.BackgroundColor = XLColor.LightGray;
                            worksheet.Cell(summaryStartRow + 1, i).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                            worksheet.Cell(summaryStartRow + 1, i).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                        }

                        // استخراج قيم الملخص من عناصر واجهة المستخدم
                        string totalDebit = txtTotalDebit.Text.Replace("مجموع المدين: ", "");
                        string totalCredit = txtTotalCredit.Text.Replace("مجموع الدائن: ", "");
                        string finalBalance = txtFinalBalance.Text.Replace("الرصيد النهائي: ", "");

                        // بيانات الملخص
                        worksheet.Cell(summaryStartRow + 2, 3).Value = totalDebit;
                        worksheet.Cell(summaryStartRow + 2, 4).Value = totalCredit;
                        worksheet.Cell(summaryStartRow + 2, 5).Value = finalBalance;

                        // تنسيق بيانات الملخص
                        for (int i = 3; i <= 5; i++)
                        {
                            worksheet.Cell(summaryStartRow + 2, i).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                            worksheet.Cell(summaryStartRow + 2, i).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                        }

                        // إضافة توضيح حالة الرصيد
                        string balanceStatus = txtBalanceStatus.Text;
                        worksheet.Cell(summaryStartRow + 3, 3).Value = balanceStatus;
                        worksheet.Range(summaryStartRow + 3, 3, summaryStartRow + 3, 5).Merge();
                        worksheet.Cell(summaryStartRow + 3, 3).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                        // تعديل عرض الأعمدة ليناسب المحتوى
                        worksheet.Columns().AdjustToContents();

                        // حفظ الملف
                        workbook.SaveAs(saveFileDialog.FileName);
                    }

                    DialogBox.Show("تم التصدير بنجاح", $"تم تصدير كشف الحساب إلى {saveFileDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تصدير البيانات: {ex.Message}", "خطأ في التصدير", true);
            }
        }

        private void btnclose_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            Close();
        }

        private void btnPrint_Click(object sender, MouseButtonEventArgs e)
        {
            var printerNames = GetDefaultPrinterName();
            var generator = new PdfGenerator<ClientStatementVM>
            {
                HeaderPage = new ClientReportHeader(
                    dpFromDate.SelectedDate.HasValue ? dpFromDate.SelectedDate.Value.ToString() : "N/A",
                    dpToDate.SelectedDate.HasValue ? dpToDate.SelectedDate.Value.ToString() : "N/A",
                    _client?.Phone ?? "N/A",
                    _client.Name),

                ListHeaderPage = new ClientReportListHeader(),
                FooterPage = new ClientReportFooter(
                    txtTotalDebit.Text,
                    txtTotalCredit.Text,
                    txtFinalBalance.Text,
                    txtBalanceStatus.Text),

                ListItemPageBuilder = (item, index) => new ClientReportList(item)
            };


            generator.ExportToTallPdf(_statementItems.ToList(), @"C:\PDFs", "ClientReport", printerNames, false);

        }

        public string GetDefaultPrinterName()
        {
            try
            {
                string defaultPrinter = Properties.Settings.Default.DefaultPrinter;
                return defaultPrinter;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على الطابعة الافتراضية: {ex.Message}");
                return string.Empty; // Return empty if error occurs
            }
        }
    }
}
