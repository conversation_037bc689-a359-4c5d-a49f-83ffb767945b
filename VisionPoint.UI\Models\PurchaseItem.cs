using Microsoft.EntityFrameworkCore;

namespace VisionPoint.UI.Models;

[Index(nameof(PurchaseId))]
[Index(nameof(ProductColorId))]
[Index(nameof(LensPrescriptionColorId))]
public class PurchaseItem : BaseEntity
{
    public int PurchaseId { get; set; }
    public Purchase Purchase { get; set; }
    public int? ProductColorId { get; set; }
    public ProductColor ProductColor { get; set; }
    public int? LensPrescriptionColorId { get; set; }
    public LensPrescriptionColor LensPrescriptionColor { get; set; }
    [Precision(18, 3)] public decimal Price { get; set; }
    public int Quantity { get; set; }
    public DateOnly? Exp { get; set; }
}
