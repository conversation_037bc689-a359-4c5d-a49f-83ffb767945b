﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;

namespace VisionPoint.UI.Converters
{
    public class BoolToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool booleanValue)
            {
                if (parameter != null && booleanValue)
                {
                    return parameter.ToString();
                }
                
                return booleanValue ? "نعم" : "";
            }
            return string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string strValue)
            {
                if (parameter != null)
                {
                    return strValue.Equals(parameter.ToString(), StringComparison.OrdinalIgnoreCase);
                }
                return strValue.Equals("نعم", StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }
    }
}
