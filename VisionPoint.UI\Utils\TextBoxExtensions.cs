using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace VisionPoint.UI.Utils
{
    public static class TextBoxExtensions
    {
        /// <summary>
        /// Applies numeric or decimal validation to TextBoxes based on their Tag property
        /// </summary>
        public static void ApplyNumericValidation(this TextBox textBox)
        {
            if (textBox == null || textBox.Tag == null)
                return;

            string tag = textBox.Tag.ToString().ToLower();

            // Check for monetary/currency tags that should support decimal values
            if (tag.Contains("سعر") || tag.Contains("القيمة") || tag.Contains("price") || 
                tag.Contains("قيمة") || tag.Contains("value") || tag.Contains("المدفوعة") ||
                tag.Contains("المتبقية") || tag.Contains("bc") || tag.Contains("axis") || 
                tag.Contains("dia"))
            {
                // For price/currency-related fields, apply decimal validation
                NumericInputControl.SetIsDecimalOnly(textBox, true);
                
                // Allow negative values for certain calculations
                if (tag.Contains("الفرق") || tag.Contains("difference") || 
                    tag.Contains("التغيير") || tag.Contains("change") ||
                    tag.Contains("التعديل") || tag.Contains("adjustment"))
                {
                    NumericInputControl.SetAllowNegative(textBox, true);
                }
            }
            // Check for quantity/count tags that should be integers
            else if (tag.Contains("كمية") || tag.Contains("عدد") || tag.Contains("quantity") ||
                    tag.Contains("count") || tag.Contains("رقم") || tag.Contains("no") ||
                    tag.Contains("number"))
            {
                // For quantity-related fields, apply integer validation
                NumericInputControl.SetIsNumericOnly(textBox, true);
            }
        }

        /// <summary>
        /// Recursively finds all TextBox controls and applies numeric/decimal validation based on their tags
        /// </summary>
        public static void ApplyNumericValidationToAllTextBoxes(this DependencyObject parent)
        {
            if (parent == null) return;

            // Process this element if it's a TextBox
            if (parent is TextBox textBox)
            {
                textBox.ApplyNumericValidation();
            }

            // Find all child elements
            int childCount = VisualTreeHelper.GetChildrenCount(parent);
            for (int i = 0; i < childCount; i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(parent, i);
                ApplyNumericValidationToAllTextBoxes(child);
            }
        }
        
        /// <summary>
        /// Apply validation to TextBoxes in the loaded UI when a Window or Page loads
        /// </summary>
        public static void ApplyNumericValidationToTextBoxes(this Window window)
        {
            window.Loaded += (sender, e) => {
                window.ApplyNumericValidationToAllTextBoxes();
            };
        }
        
        /// <summary>
        /// Apply validation to TextBoxes in the loaded UI when a Window or Page loads
        /// </summary>
        public static void ApplyNumericValidationToTextBoxes(this Page page)
        {
            page.Loaded += (sender, e) => {
                page.ApplyNumericValidationToAllTextBoxes();
            };
        }
    }
} 