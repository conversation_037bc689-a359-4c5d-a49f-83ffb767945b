﻿<Window
    x:Class="VisionPoint.UI.views.Dialogs.DeleteBox"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:VisionPoint.UI"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Dialogs"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="DeleteBox"
    AllowsTransparency="True"
    Background="Transparent"
    Closing="Window_Closing"
    FontFamily="Times New Roman"
    ResizeMode="NoResize"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">
        <Grid Width="1920" Height="1080">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Grid
                Grid.Row="1"
                Grid.Column="1"
                Width="auto"
                Height="auto"
                MinWidth="350">
                <Grid.RowDefinitions>
                    <RowDefinition Height="50" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>
                <Border
                    Grid.RowSpan="2"
                    Width="100"
                    Height="100"
                    VerticalAlignment="Top"
                    Panel.ZIndex="10"
                    Background="{StaticResource backgroundColor}"
                    BorderBrush="LightGray"
                    BorderThickness="4"
                    CornerRadius="50">
                    <Image Height="50" Source="{StaticResource AlertIcon}" />
                </Border>

                <Border
                    Grid.Row="1"
                    MinHeight="200"
                    VerticalAlignment="Stretch"
                    Background="{StaticResource backgroundColor}"
                    BorderThickness="1.5"
                    CornerRadius="20">
                    <Border.BorderBrush>
                        <SolidColorBrush Opacity="0.4" Color="Black" />
                    </Border.BorderBrush>
                    <DockPanel LastChildFill="False">
                        <Border
                            x:Name="btnClose"
                            Height="16"
                            Margin="20"
                            HorizontalAlignment="Right"
                            Cursor="Hand"
                            DockPanel.Dock="Top" />

                        <TextBlock
                            Margin="0,0,0,15"
                            HorizontalAlignment="Center"
                            DockPanel.Dock="Top"
                            FontSize="20"
                            FontWeight="Bold"
                            Foreground="#333333">
                            <TextBlock Foreground="#ED1C24">!!</TextBlock>
                            تأكيد الحذف
                            <TextBlock Foreground="#ED1C24">!!</TextBlock>
                        </TextBlock>

                        <ScrollViewer
                            MaxHeight="250"
                            DockPanel.Dock="Top"
                            HorizontalScrollBarVisibility="Disabled"
                            VerticalScrollBarVisibility="Auto">
                            <TextBlock
                                x:Name="txtDeleteHeader"
                                Margin="15,0,15,5"
                                Padding="5"
                                HorizontalAlignment="Center"
                                FontSize="18"
                                FontWeight="SemiBold"
                                Foreground="#64748B"
                                TextAlignment="Center"
                                TextWrapping="Wrap" />
                        </ScrollViewer>

                        <TextBlock
                            x:Name="txtName"
                            Margin="0,5,0,10"
                            HorizontalAlignment="Center"
                            DockPanel.Dock="Top"
                            FontSize="18"
                            FontWeight="Bold"
                            Foreground="#ED1C24" />

                        <DockPanel
                            Margin="14,15,14,20"
                            DockPanel.Dock="Bottom"
                            LastChildFill="False">
                            <Border
                                x:Name="btnYes"
                                Width="123"
                                Height="35"
                                Background="#5F9CE3"
                                CornerRadius="18"
                                Cursor="Hand"
                                DockPanel.Dock="Right"
                                MouseLeftButtonDown="btnYes_MouseLeftButtonUp">
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FontSize="17"
                                    FontWeight="Bold"
                                    Foreground="#fff">
                                    تأكيد
                                </TextBlock>
                            </Border>
                            <Border
                                x:Name="btnNo"
                                Width="123"
                                Height="35"
                                Background="#FC4B5D"
                                CornerRadius="18"
                                Cursor="Hand"
                                DockPanel.Dock="Left"
                                MouseLeftButtonDown="btnNo_MouseLeftButtonUp">
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FontSize="17"
                                    FontWeight="Bold"
                                    Foreground="#fff">
                                    الغاء
                                </TextBlock>
                            </Border>
                        </DockPanel>
                    </DockPanel>
                </Border>
            </Grid>
        </Grid>
    </Viewbox>
</Window>
