using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Data;
using System.Windows;
using System.Windows.Data;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.ProductsContent
{
    public partial class ColumnMappingWindow : Window
    {
        public Dictionary<string, string> ColumnMapping { get; private set; } = new Dictionary<string, string>();
        private ObservableCollection<FieldMappingItem> _fieldMappings = new ObservableCollection<FieldMappingItem>();

        public ColumnMappingWindow(DataColumnCollection excelColumns, Dictionary<string, FieldInfo> requiredFields)
        {
            InitializeComponent();

            var availableColumns = excelColumns.Cast<DataColumn>().Select(c => c.ColumnName).ToList();

            foreach (var field in requiredFields)
            {
                var item = new FieldMappingItem
                {
                    FieldName = field.Key,
                    FieldDisplayName = field.Value.DisplayName,
                    IsRequired = field.Value.IsRequired,
                    AvailableColumns = availableColumns,
                    IsEnabled = true
                };

                // Try to find a matching column based on possible names
                if (field.Value.PossibleColumnNames != null && field.Value.PossibleColumnNames.Any())
                {
                    var matchingColumn = field.Value.PossibleColumnNames.FirstOrDefault(name => availableColumns.Contains(name));
                    if (!string.IsNullOrEmpty(matchingColumn))
                    {
                        item.SelectedColumn = matchingColumn;
                    }
                }

                _fieldMappings.Add(item);
            }

            FieldMappingList.ItemsSource = _fieldMappings;
        }



        private void btnConfirm_Click(object sender, RoutedEventArgs e)
        {
            // Validate that all required fields have a selected column
            var missingRequiredFields = _fieldMappings
                .Where(f => f.IsRequired && string.IsNullOrEmpty(f.SelectedColumn))
                .Select(f => f.FieldDisplayName)
                .ToList();

            if (missingRequiredFields.Any())
            {
                ErrorBox.Show(
                    $"يرجى تحديد الأعمدة المقابلة للحقول المطلوبة التالية:\n{string.Join("\n", missingRequiredFields)}",
                    "حقول مطلوبة غير محددة");
                return;
            }

            // Create the column mapping dictionary
            ColumnMapping = _fieldMappings
                .Where(f => !string.IsNullOrEmpty(f.SelectedColumn))
                .ToDictionary(f => f.FieldName, f => f.SelectedColumn);

            DialogResult = true;
            Close();
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }


    }

    public class FieldMappingItem : INotifyPropertyChanged
    {
        private string _selectedColumn;

        public string FieldName { get; set; }
        public string FieldDisplayName { get; set; }
        public bool IsRequired { get; set; }
        public List<string> AvailableColumns { get; set; }
        public bool IsEnabled { get; set; }

        public string SelectedColumn
        {
            get => _selectedColumn;
            set
            {
                if (_selectedColumn != value)
                {
                    _selectedColumn = value;
                    OnPropertyChanged(nameof(SelectedColumn));
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class FieldInfo
    {
        public string DisplayName { get; set; }
        public bool IsRequired { get; set; }
        public string[] PossibleColumnNames { get; set; }
    }

    public class BoolToFontWeightConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value is bool isRequired && isRequired)
            {
                return FontWeights.Bold;
            }
            return FontWeights.Normal;
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
