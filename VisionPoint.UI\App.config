﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="VisionPoint.UI.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="VisionPoint.UI.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <userSettings>
        <VisionPoint.UI.Properties.Settings>
            <setting name="DefaultPrinter" serializeAs="String">
                <value>Microsoft Print to PDf</value>
            </setting>
            <setting name="ArabicAddress" serializeAs="String">
                <value>سوق الجمعة -عمارة مصرف الادخار مقبل كلية سوق الجمعة</value>
            </setting>
            <setting name="EnglishAddress" serializeAs="String">
                <value>Souq Al Juma'a - Al Idikhar Bank Building, In front of Souq Al Juma'a College</value>
            </setting>
            <setting name="PhoneNumber" serializeAs="String">
                <value>**********</value>
            </setting>
            <setting name="LicenseRemainingDays" serializeAs="String">
                <value>0</value>
            </setting>
            <setting name="AllowNegativeQuantities" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="BranchName" serializeAs="String">
                <value>سوق الجمعة</value>
            </setting>

            <setting name="AutoBackupEnabled" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="AutoBackupPath" serializeAs="String">
                <value />
            </setting>
            <setting name="AutoBackupMaxCount" serializeAs="String">
                <value>5</value>
            </setting>
            <setting name="LastAutoBackupDate" serializeAs="String">
                <value>2023-01-01</value>
            </setting>
            <setting name="EnableMinimumQuantityCheck" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="CompanyName" serializeAs="String">
                <value>مجموعة النمارق للبصريات</value>
            </setting>
            <setting name="CompanyNameEng" serializeAs="String">
                <value>Namariq Optics Group</value>
            </setting>
        </VisionPoint.UI.Properties.Settings>
    </userSettings>
    <applicationSettings>
        <VisionPoint.UI.Properties.Settings>
            <setting name="ConnectionString" serializeAs="String">
                <value>Server=.;Database=VisionPoint;Trusted_Connection=false;User id=user;password=***;TrustServerCertificate=True;MultipleActiveResultSets=true;</value>
            </setting>
        </VisionPoint.UI.Properties.Settings>
    </applicationSettings>
</configuration>