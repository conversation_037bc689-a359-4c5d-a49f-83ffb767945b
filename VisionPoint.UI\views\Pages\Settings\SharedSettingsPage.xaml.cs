﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using VisionPoint.UI.views.Dialogs;
using cc = VisionPoint.UI.Controls;

namespace VisionPoint.UI.views.Pages.Settings
{
    /// <summary>
    /// Interaction logic for SharedSettingsPage.xaml
    /// </summary>
    public partial class SharedSettingsPage : Page
    {
        public SharedSettingsPage()
        {
            InitializeComponent();
        }

        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            UpdateTabsVisibility();
            LoadDefaultPage();
        }

        private void LoadDefaultPage()
        {
            // تحديد الصفحة الافتراضية بناءً على صلاحيات المستخدم
            bool hasGeneralAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("SettingsRole") ||
                                  CurrentUser.HasRole("SystemConfigRole");
            bool hasBackupAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("BackupRole");
            // تبويب بيانات الشركة - يمكن تخصيص صلاحية له حسب الحاجة
            // لتفعيل الصلاحيات، قم بتغيير السطر التالي إلى:
            // bool hasBasicSettingsAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("BasicSettingsRole");
            bool hasBasicSettingsAccess = true; // متاح للجميع حالياً

            // إذا كان المستخدم يملك صلاحية الوصول للإعدادات العامة، يتم تحميلها أولاً
            if (hasGeneralAccess)
            {
                MoveToPage(new GeneralSettingsPage(), new[] { "Admin", "SettingsRole", "SystemConfigRole" }, btnGenral);
                SetActiveTab(btnGenral);
            }
            // إذا كان المستخدم يملك صلاحية النسخة الاحتياطية فقط، يتم تحميلها
            else if (hasBackupAccess)
            {
                MoveToPage(new BackupSettingsPage(), new[] { "Admin", "BackupRole" }, btnBackup);
                SetActiveTab(btnBackup);
            }
            // إذا كان لديه صلاحية بيانات الشركة فقط أو لم يكن لديه أي صلاحيات أخرى
            else if (hasBasicSettingsAccess)
            {
                MoveToPage(new BasicSettingsPage(), new string[0], btnBasicSettings);
                SetActiveTab(btnBasicSettings);
            }
            // إذا لم يكن لديه أي صلاحيات على الإطلاق
            else
            {
                ErrorBox.Show("لا تملك صلاحية الوصول لأي من صفحات الإعدادات", "خطأ في الصلاحيات", true);
            }
        }

        private void SetActiveTab(Button activeButton)
        {
            // إلغاء تحديد جميع التبويبات
            if (btnGenral.Content is cc.TabMenu generalTab)
                generalTab.IsSelected = false;
            if (btnBackup.Content is cc.TabMenu backupTab)
                backupTab.IsSelected = false;
            if (btnBasicSettings.Content is cc.TabMenu basicTab)
                basicTab.IsSelected = false;

            // تحديد التبويب النشط
            if (activeButton?.Content is cc.TabMenu activeTab)
                activeTab.IsSelected = true;
        }

        private void UpdateTabsVisibility()
        {
            // التحكم في ظهور التبويبات بناءً على صلاحيات المستخدم
            bool hasGeneralAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("SettingsRole") ||
                                  CurrentUser.HasRole("SystemConfigRole");

            bool hasBackupAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("BackupRole");

            // تبويب بيانات الشركة - يمكن تخصيص صلاحية له حسب الحاجة
            // لتفعيل الصلاحيات، قم بتغيير السطر التالي إلى:
            // bool hasBasicSettingsAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("BasicSettingsRole");
            bool hasBasicSettingsAccess = true; // متاح للجميع حالياً

            btnGenral.Visibility = hasGeneralAccess ? System.Windows.Visibility.Visible : System.Windows.Visibility.Collapsed;
            btnBackup.Visibility = hasBackupAccess ? System.Windows.Visibility.Visible : System.Windows.Visibility.Collapsed;
            btnBasicSettings.Visibility = hasBasicSettingsAccess ? System.Windows.Visibility.Visible : System.Windows.Visibility.Collapsed;
        }
        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnGenral.IsEnabled = false;
            btnBackup.IsEnabled = false;
            btnBasicSettings.IsEnabled = false;
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnGenral.IsEnabled = true;
            btnBackup.IsEnabled = true;
            btnBasicSettings.IsEnabled = true;
        }

        public void MoveToPage(Page page, string[] allowedRoles, Button senderButton)
        {
            // التحقق من الصلاحيات قبل الانتقال للصفحة
            if (allowedRoles != null && allowedRoles.Length > 0)
            {
                if (!CheckRole(allowedRoles))
                    return;
            }
            fContainer.Navigate(page);
        }

        private void btnGenral_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                MoveToPage(new GeneralSettingsPage(), new[] { "Admin", "SettingsRole", "SystemConfigRole" }, sender as Button);
                SetActiveTab(sender as Button);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnBackup_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                MoveToPage(new BackupSettingsPage(), new[] { "Admin", "BackupRole" }, sender as Button);
                SetActiveTab(sender as Button);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        bool CheckRole(string[] roles)
        {
            if (!CurrentUser.HasAnyRole(roles))
            {
                ErrorBox.Show("لا تملك صلاحية الوصول لهذه الصفحة", "خطأ في الصلاحيات", true);
                return false;
            }
            return true;
        }

        private void btnBasicSettings_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                MoveToPage(new BasicSettingsPage(), new string[0], sender as Button);
                SetActiveTab(sender as Button);
            }
            finally
            {
                EnableAllButtons();
            }
        }
    }
}
