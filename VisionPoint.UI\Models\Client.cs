﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace VisionPoint.UI.Models;
[Index(nameof(Name), IsUnique = true)]
[Index(nameof(Phone))]
[Index(nameof(IsCustomer))]
[Index(nameof(IsSupplier))]
[Index(nameof(WarehouseId))]
public class Client : BaseEntity
{
    [StringLength(100, ErrorMessage = "اسم العميل يجب ألا يتجاوز 100 حرف")]
    public string Name { get; set; }

    [StringLength(20, ErrorMessage = "رقم الهاتف يجب ألا يتجاوز 20 حرف")]
    public string? Phone { get; set; }
    [Precision(18, 3)] public decimal Balance { get; set; }
    /// <summary>
    /// الرصيد المسموح - إذا كان 0 فلا توجد قيود، إذا كان موجب فهو الحد الأقصى للمديونية، إذا كان سالب فهو الحد الأقصى للدائنية
    /// </summary>
    [Precision(18, 3)] public decimal AllowedBalance { get; set; } = 0;
    public bool IsCustomer { get; set; }
    public bool IsSupplier { get; set; }

    // ربط العميل بالمخزن - إذا كان null فالعميل يظهر في كل المخازن
    public int? WarehouseId { get; set; }
    public Warehouse? Warehouse { get; set; }
}
