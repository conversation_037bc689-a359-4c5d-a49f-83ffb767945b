using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace VisionPoint.UI.Converters
{
    /// <summary>
    /// محول يقوم بتحويل قيمة رصيد الموظف إلى لون مناسب
    /// </summary>
    public class EmployeeBalanceToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal balance)
            {
                if (balance > 0)
                {
                    // رصيد موجب (الموظف مدين للمتجر) - أحمر
                    return new SolidColorBrush(Colors.Red);
                }
                else if (balance < 0)
                {
                    // رصيد سالب (المتجر مدين للموظف) - أخضر
                    return new SolidColorBrush(Colors.Green);
                }
            }

            // رصيد صفر - أسود
            return new SolidColorBrush(Colors.Black);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
