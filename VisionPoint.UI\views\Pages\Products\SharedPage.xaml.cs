﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.Products
{
    /// <summary>
    /// Interaction logic for SharedPage.xaml
    /// </summary>
    public partial class SharedPage : Page
    {
        public SharedPage()
        {
            InitializeComponent();
            UpdateTabsVisibility();
        }

        private void UpdateTabsVisibility()
        {
            // التحكم في ظهور التبويبات بناءً على صلاحيات المستخدم

            // تبويب المنتجات العامة
            bool hasProductAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ProductRole") ||
                                   CurrentUser.HasRole("EditProductRole") || CurrentUser.HasRole("DeleteProductRole");

            // تبويب العدسات
            bool hasLensAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("LensRole") ||
                               CurrentUser.HasRole("EditLensRole") || CurrentUser.HasRole("DeleteLensRole");

            // تبويب أنواع العدسات
            bool hasLensCategoryAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("LensCategoryRole") ||
                                       CurrentUser.HasRole("EditLensCategoryRole") || CurrentUser.HasRole("DeleteLensCategoryRole");

            // تبويب الخدمات
            bool hasServiceAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ServiceRole") ||
                                  CurrentUser.HasRole("EditServiceRole") || CurrentUser.HasRole("DeleteServiceRole");

            // إخفاء الأزرار التي لا يملك المستخدم صلاحية الوصول إليها
            if (!hasProductAccess)
            {
                btnGenral.Visibility = Visibility.Collapsed;
            }

            if (!hasLensAccess)
            {
                btnLenses.Visibility = Visibility.Collapsed;
            }

            if (!hasLensCategoryAccess)
            {
                btnLensCategories.Visibility = Visibility.Collapsed;
            }

            if (!hasServiceAccess)
            {
                btnServices.Visibility = Visibility.Collapsed;
            }

            // إذا لم يبق أي زر مرئي، عرض رسالة خطأ
            bool hasAnyAccess = hasProductAccess || hasLensAccess || hasLensCategoryAccess || hasServiceAccess;
            if (!hasAnyAccess)
            {
                ErrorBox.Show("لا تملك صلاحية الوصول لأي من أقسام المنتجات", "خطأ في الصلاحيات", true);
            }
        }
        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            // تحديد الصفحة الافتراضية بناءً على الصلاحيات المتاحة
            bool hasProductAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ProductRole") ||
                                  CurrentUser.HasRole("EditProductRole") || CurrentUser.HasRole("DeleteProductRole");
            bool hasLensAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("LensRole") ||
                               CurrentUser.HasRole("EditLensRole") || CurrentUser.HasRole("DeleteLensRole");
            bool hasLensCategoryAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("LensCategoryRole") ||
                                       CurrentUser.HasRole("EditLensCategoryRole") || CurrentUser.HasRole("DeleteLensCategoryRole");
            bool hasServiceAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ServiceRole") ||
                                  CurrentUser.HasRole("EditServiceRole") || CurrentUser.HasRole("DeleteServiceRole");

            // اختيار أول صفحة متاحة
            if (hasProductAccess)
            {
                moveToPage("Views/Pages/Products/GenralProductPage.xaml");
            }
            else if (hasLensAccess)
            {
                moveToPage("Views/Pages/Products/LensesPage.xaml");
            }
            else if (hasLensCategoryAccess)
            {
                moveToPage("Views/Pages/ProductsContent/LensCategoryPage.xaml");
            }
            else if (hasServiceAccess)
            {
                moveToPage("Views/Pages/Products/ServicesPage.xaml");
            }
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnGenral.IsEnabled = false;
            btnLenses.IsEnabled = false;
            btnServices.IsEnabled = false;
            btnLensCategories.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnGenral.IsEnabled = true;
            btnLenses.IsEnabled = true;
            btnServices.IsEnabled = true;
            btnLensCategories.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        public void moveToPage(String pageUri)
        {
            fContainer.Navigate(new System.Uri(pageUri, UriKind.RelativeOrAbsolute));

        }
        private void btnLenses_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                bool hasAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("LensRole") ||
                               CurrentUser.HasRole("EditLensRole") || CurrentUser.HasRole("DeleteLensRole");
                if (!hasAccess)
                {
                    ErrorBox.Show("لا تملك صلاحية الوصول لصفحة العدسات", "خطأ في الصلاحيات", true);
                    return;
                }
                moveToPage("Views/Pages/Products/LensesPage.xaml");
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnGenral_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                bool hasAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ProductRole") ||
                               CurrentUser.HasRole("EditProductRole") || CurrentUser.HasRole("DeleteProductRole");
                if (!hasAccess)
                {
                    ErrorBox.Show("لا تملك صلاحية الوصول لصفحة المنتجات", "خطأ في الصلاحيات", true);
                    return;
                }
                moveToPage("Views/Pages/Products/GenralProductPage.xaml");
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnServices_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                bool hasAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ServiceRole") ||
                               CurrentUser.HasRole("EditServiceRole") || CurrentUser.HasRole("DeleteServiceRole");
                if (!hasAccess)
                {
                    ErrorBox.Show("لا تملك صلاحية الوصول لصفحة الخدمات", "خطأ في الصلاحيات", true);
                    return;
                }
                moveToPage("Views/Pages/Products/ServicesPage.xaml");
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnLensCategories_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                bool hasAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("LensCategoryRole") ||
                               CurrentUser.HasRole("EditLensCategoryRole") || CurrentUser.HasRole("DeleteLensCategoryRole");
                if (!hasAccess)
                {
                    ErrorBox.Show("لا تملك صلاحية الوصول لصفحة أنواع العدسات", "خطأ في الصلاحيات", true);
                    return;
                }
                moveToPage("Views/Pages/ProductsContent/LensCategoryPage.xaml");
            }
            finally
            {
                EnableAllButtons();
            }
        }
    }
}
