﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System.Data;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Models;

namespace VisionPoint.UI.PL
{
    public static class SeedData
    {
        public static async Task Initialize(AppDbContext context, UserManager<User> user, RoleManager<Role> role)
        {

            context.Database.Migrate();
            if (role.FindByNameAsync("Admin").GetAwaiter().GetResult() == null)
            {
                // الدور الرئيسي للمدير
                await role.CreateAsync(new() { Name = "Admin" });

                // أدوار خاصة بكل واجهة
                await role.CreateAsync(new() { Name = "SalesRole" });         // دور عرض وإضافة وبحث المبيعات
                await role.CreateAsync(new() { Name = "EditSalesRole" });     // دور تعديل المبيعات
                await role.CreateAsync(new() { Name = "DeleteSalesRole" });   // دور حذف المبيعات
                await role.CreateAsync(new() { Name = "ReturnSalesItemRole" }); // دور استرجاع أصناف المبيعات
                await role.CreateAsync(new() { Name = "PurchaseRole" });      // دور عرض وإضافة وبحث المشتريات
                await role.CreateAsync(new() { Name = "EditPurchaseRole" });  // دور تعديل المشتريات
                await role.CreateAsync(new() { Name = "DeletePurchaseRole" }); // دور حذف المشتريات

                // أدوار المنتجات
                await role.CreateAsync(new() { Name = "ProductRole" });       // دور عرض وإضافة المنتجات
                await role.CreateAsync(new() { Name = "EditProductRole" });   // دور تعديل المنتجات
                await role.CreateAsync(new() { Name = "DeleteProductRole" }); // دور حذف المنتجات

                // أدوار العدسات
                await role.CreateAsync(new() { Name = "LensRole" });          // دور عرض وإضافة العدسات
                await role.CreateAsync(new() { Name = "EditLensRole" });      // دور تعديل العدسات
                await role.CreateAsync(new() { Name = "DeleteLensRole" });    // دور حذف العدسات

                // أدوار أنواع العدسات
                await role.CreateAsync(new() { Name = "LensCategoryRole" });       // دور عرض وإضافة أنواع العدسات
                await role.CreateAsync(new() { Name = "EditLensCategoryRole" });   // دور تعديل أنواع العدسات
                await role.CreateAsync(new() { Name = "DeleteLensCategoryRole" }); // دور حذف أنواع العدسات

                // أدوار الخدمات
                await role.CreateAsync(new() { Name = "ServiceRole" });       // دور عرض وإضافة الخدمات
                await role.CreateAsync(new() { Name = "EditServiceRole" });   // دور تعديل الخدمات
                await role.CreateAsync(new() { Name = "DeleteServiceRole" }); // دور حذف الخدمات

                // أدوار العملاء
                await role.CreateAsync(new() { Name = "ClientRole" });        // دور عرض وإضافة العملاء
                await role.CreateAsync(new() { Name = "EditClientRole" });    // دور تعديل العملاء
                await role.CreateAsync(new() { Name = "DeleteClientRole" });  // دور حذف العملاء

                await role.CreateAsync(new() { Name = "ReceiptRole" });       // دور عرض وإضافة وبحث الإيصالات
                await role.CreateAsync(new() { Name = "EditReceiptRole" });   // دور تعديل الإيصالات
                await role.CreateAsync(new() { Name = "DeleteReceiptRole" }); // دور حذف الإيصالات

                // أدوار المصروفات
                await role.CreateAsync(new() { Name = "ExpenseRole" });       // دور عرض وإضافة المصروفات
                await role.CreateAsync(new() { Name = "EditExpenseRole" });   // دور تعديل المصروفات
                await role.CreateAsync(new() { Name = "DeleteExpenseRole" }); // دور حذف المصروفات

                // أدوار تغيير التواريخ
                await role.CreateAsync(new() { Name = "ChangePurchaseDateRole" }); // دور تغيير تاريخ فاتورة المشتريات
                await role.CreateAsync(new() { Name = "ChangeSaleDateRole" });     // دور تغيير تاريخ فاتورة المبيعات
                await role.CreateAsync(new() { Name = "ChangeReceiptDateRole" });  // دور تغيير تاريخ الإيصالات

                // أدوار طرق الدفع
                await role.CreateAsync(new() { Name = "TreasuryRole" });       // دور عرض وإضافة طرق الدفع
                await role.CreateAsync(new() { Name = "EditTreasuryRole" });   // دور تعديل طرق الدفع
                await role.CreateAsync(new() { Name = "DeleteTreasuryRole" }); // دور حذف طرق الدفع

                // أدوار المخازن
                await role.CreateAsync(new() { Name = "WarehouseRole" });       // دور عرض وإضافة المخازن
                await role.CreateAsync(new() { Name = "EditWarehouseRole" });   // دور تعديل المخازن
                await role.CreateAsync(new() { Name = "DeleteWarehouseRole" }); // دور حذف المخازن
                await role.CreateAsync(new() { Name = "ChangeWarehouseRole" }); // دور تغيير المخزن في الواجهات

                // أدوار العمليات المالية للموظفين
                await role.CreateAsync(new() { Name = "EmployeeFinancialRole" }); // دور سلف مرتب وأخذ ورد العهدة

                // أدوار إدارة المستخدمين
                await role.CreateAsync(new() { Name = "UserRole" });       // دور عرض وإضافة المستخدمين
                await role.CreateAsync(new() { Name = "EditUserRole" });   // دور تعديل المستخدمين
                await role.CreateAsync(new() { Name = "DeleteUserRole" }); // دور حذف المستخدمين

                await role.CreateAsync(new() { Name = "SettingsRole" });      // دور الإعدادات

                // أدوار خاصة بالإعدادات
                await role.CreateAsync(new() { Name = "BackupRole" });        // دور النسخ الاحتياطي
                await role.CreateAsync(new() { Name = "SystemConfigRole" });  // دور إعدادات النظام

                // دور تنبيهات انتهاء الصلاحية
                await role.CreateAsync(new() { Name = "ExpirationAlertRole" }); // دور عرض تنبيهات انتهاء الصلاحية

                // دور تنبيهات الحد الأدنى للكمية
                await role.CreateAsync(new() { Name = "MinimumQuantityAlertRole" }); // دور عرض تنبيهات الحد الأدنى للكمية
                                                                              // إضافة المخازن أولاً (مطلوبة للعلاقات الأخرى)
                if (!context.Warehouses.Any())
                {
                    var warehouses = new List<Warehouse>
                    {
                        new() { Name = "المخزن الرئيسي", Code = "A" }
                    };
                    context.Warehouses.AddRange(warehouses);
                  await  context.SaveWithTransactionAndBusy();
                }

                // الحصول على المخزن الرئيسي للربط
                var mainWarehouse = context.Warehouses.First();

                var admin = new User() { Name = "مدير النظام",Salary=100, UserName = "Admin", Email = "<EMAIL>",WarehouseId= mainWarehouse.Id };
                await user.CreateAsync(admin, "Admin123*");
                await user.AddToRoleAsync(admin, "Admin");
           
           

                // إضافة الوصفات الطبية
                if (!context.Prescriptions.Any())
                {
                    context.Prescriptions.AddRange(Enumerable.Range(-140, 281)
                        .Select(i => new Prescription { Value = Math.Round(i * 0.25m, 2) }).ToList());
                }

                // إضافة طرق الدفع مع ربطها بالمخزن الرئيسي
                if (!context.Treasuries.Any())
                {
                    context.Treasuries.Add(new Treasury
                    {
                        Name = "الخزينة النقدية",
                        WarehouseId = mainWarehouse.Id,
                        Balance = 0
                    });
                }

                // إضافة العملاء الافتراضيين (عامين - يظهرون في جميع المخازن)
                if (!context.Clients.Any())
                {
                    context.Clients.AddRange(new List<Client>
                    {
                        new() { Name = "زبون نقدي", IsCustomer = true, WarehouseId = null }, // عام
                        new() { Name = "مشتريات نقدي", IsSupplier = true, WarehouseId = null } // عام
                    });
                }

                // إضافة أنواع العمليات المالية
                if (!context.Financials.Any())
                {
                    context.Financials.AddRange(new List<Financial>
                    {
                        new() { Id = (byte)FinancialId.Client, Name = "عميل" },
                        new() { Id = (byte)FinancialId.Deposit, Name = "إيداع" },
                        new() { Id = (byte)FinancialId.Withdrawal, Name = "سحب" },
                        new() { Id = (byte)FinancialId.OpeningBalanceForClient, Name = "رصيد افتتاحي للعميل" },
                        new() { Id = (byte)FinancialId.OpeningBalanceForEmployee, Name = "رصيد افتتاحي للموظف" },
                        new() { Id = (byte)FinancialId.Employee, Name = "موظف" },
                        new() { Id = (byte)FinancialId.Sale, Name = "فاتورة مبيعات" },
                        new() { Id = (byte)FinancialId.Purchase, Name = "فاتورة مشتريات" },
                        new() { Id = (byte)FinancialId.Expense, Name = "مصروف" },
                        new() { Id = (byte)FinancialId.SalaryPayment, Name = "صرف راتب" },
                        new() { Id = (byte)FinancialId.AutomaticSalary, Name = "إضافة راتب تلقائي" },
                        new() { Id = (byte)FinancialId.Transfer, Name = "تحويل بين طرق الدفع" }
                    });
                }

                // إضافة الألوان الافتراضية
                if (!context.Colors.Any())
                {
                    context.Colors.Add(new Color { Name = "شفاف", HexCode = "#FFFFFF" });
                }

                // حفظ جميع التغييرات
              await  context.SaveWithTransactionAndBusy();
            }
        }
    }
}
public enum FinancialId
{
     Client ,
     Deposit ,
     Withdrawal ,
     OpeningBalanceForClient,
     OpeningBalanceForEmployee ,
     Employee ,
     Sale,
     Purchase,
     Expense ,
     SalaryPayment,
     AutomaticSalary, // إضافة راتب تلقائي
     Transfer, // تحويل بين طرق الدفع
}
