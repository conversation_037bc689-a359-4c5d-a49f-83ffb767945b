using VisionPoint.UI.Models;

namespace VisionPoint.UI.ViewModel
{
    public class PurchaseViewModel
    {
        public int Index { get; set; }
        public int Id { get; set; }
        /// <summary>
        /// رقم الفاتورة
        /// </summary>
        public int InvoiceNo { get; set; }

        /// <summary>
        /// رقم الفاتورة مع رمز المخزن (مثل: MAIN-001)
        /// </summary>
        public string? InvoiceNumber { get; set; }

        public DateTime PurchaseDate { get; set; }
        public string ClientName { get; set; }
        public string WarehouseName { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public Purchase Purchase { get; set; }
        public ICollection<PurchaseItemVM>? PurchaseItems { get; set; }
    }
}
