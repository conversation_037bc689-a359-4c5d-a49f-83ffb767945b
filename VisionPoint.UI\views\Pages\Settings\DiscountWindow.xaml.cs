﻿using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.Settings
{
    /// <summary>
    /// Interaction logic for DiscountWindow.xaml
    /// </summary>
    public partial class DiscountWindow : Window
    {
        private readonly DiscountManager _discountManager;
        private readonly ProductService _productService;
        private readonly LensService _lensService;
        private readonly ServiceService _serviceService;

        private ObservableCollection<DiscountVM> _discounts;
        private DiscountVM _selectedDiscount;
        private int _selectedDiscountId;

        private List<Product> _products;
        private List<Lens> _lenses;
        private List<Service> _services;

        private bool _isEditMode = false;

        public DiscountWindow()
        {
            InitializeComponent();

            // Initialize services using ServiceLocator
            _discountManager = ServiceLocator.GetService<DiscountManager>();
            _productService = ServiceLocator.GetService<ProductService>();
            _lensService = ServiceLocator.GetService<LensService>();
            _serviceService = ServiceLocator.GetService<ServiceService>();

            // Set default dates
            dtpStartDate.SelectedDate = DateTime.Today;
            dtpEndDate.SelectedDate = DateTime.Today.AddDays(30);
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnSave.IsEnabled = false;
            btnNew.IsEnabled = false;
            btnDelete.IsEnabled = false;
            btnSearch.IsEnabled = false;
            btnclose.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnSave.IsEnabled = true;
            btnNew.IsEnabled = true;
            btnDelete.IsEnabled = true;
            btnSearch.IsEnabled = true;
            btnclose.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private async void btnSearch_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                await LoadDiscounts();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnDelete_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (_selectedDiscount == null)
                {
                    ShowError("الرجاء اختيار خصم للحذف", "تنبيه", false);
                    return;
                }

                var result = ShowQuestion("تأكيد الحذف", "هل أنت متأكد من حذف هذا الخصم؟");
                if (result == MessageBoxResult.Yes)
                {
                    var (success, message) = await _discountManager.DeleteDiscount(_selectedDiscount.Id);
                    if (success)
                    {
                        ShowMessage("نجاح", message);
                        await LoadDiscounts();
                        ClearForm();
                    }
                    else
                    {
                        ShowError(message);
                    }
                }
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnclose_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                Close();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void ShowMessage(string title, string message)
        {
            DialogBox.Show(title, message);
        }

        private void ShowError(string message, string title = "خطأ", bool isError = true)
        {
            ErrorBox.Show(message, title, isError);
        }

        private MessageBoxResult ShowQuestion(string title, string message)
        {
            return QuestionBox.Show(title, message);
        }

        private void btnNew_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                ClearForm();
                _isEditMode = false;
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnSave_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (!ValidateForm())
                    return;

                // Create discount object
                var discount = new Discount
                {
                    StartDate = dtpStartDate.SelectedDate ?? DateTime.Today,
                    EndDate = dtpEndDate.SelectedDate ?? DateTime.Today.AddDays(30),
                    DiscountPercentage = decimal.Parse(txtDiscountPercentage.Text.Replace(',', '.'), System.Globalization.CultureInfo.InvariantCulture),
                    ApplyToAllProducts = chkApplyToAllProducts.IsChecked ?? false,
                    ApplyToAllLenses = chkApplyToAllLenses.IsChecked ?? false,
                    ApplyToAllServices = chkApplyToAllServices.IsChecked ?? false
                };

                // Get selected items
                List<int> productIds = new List<int>();
                List<int> lensIds = new List<int>();
                List<int> serviceIds = new List<int>();

                if (RdbProductSelect.IsChecked == true && !discount.ApplyToAllProducts)
                {
                    var selectedProduct = cmbItems.SelectedItem as Product;
                    if (selectedProduct != null)
                        productIds.Add(selectedProduct.Id);
                }

                if (RdbLensesSelect.IsChecked == true && !discount.ApplyToAllLenses)
                {
                    var selectedLens = cmbItems.SelectedItem as Lens;
                    if (selectedLens != null)
                        lensIds.Add(selectedLens.Id);
                }

                if (RdbServiceSelect.IsChecked == true && !discount.ApplyToAllServices)
                {
                    var selectedService = cmbItems.SelectedItem as Service;
                    if (selectedService != null)
                        serviceIds.Add(selectedService.Id);
                }

                // Save or update
                if (_isEditMode)
                {
                    discount.Id = _selectedDiscountId;
                    var (success, message) = await _discountManager.UpdateDiscount(discount, productIds, lensIds, serviceIds);
                    if (success)
                    {
                        ShowMessage("نجاح", message);
                        await LoadDiscounts();
                        ClearForm();
                    }
                    else
                    {
                        ShowError(message);
                    }
                }
                else
                {
                    var (success, message) = await _discountManager.CreateDiscount(discount, productIds, lensIds, serviceIds);
                    if (success)
                    {
                        ShowMessage("نجاح", message);
                        await LoadDiscounts();
                        ClearForm();
                    }
                    else
                    {
                        ShowError(message);
                    }
                }
            }
            catch (Exception)
            {

                throw;
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            // Adjust column widths if needed
            if (list.View is GridView gridView)
            {
                double width = list.ActualWidth / gridView.Columns.Count;
                foreach (var column in gridView.Columns)
                {
                    column.Width = width - 5;
                }
            }
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // Load initial data
            await LoadProducts();
            await LoadDiscounts();

            // Set default radio button
            RdbProductSelect.IsChecked = true;
        }

        private async void RdbLenses_Checked(object sender, RoutedEventArgs e)
        {
            if (IsLoaded)
            {
                await LoadLenses();
                cmbItems.ItemsSource = _lenses;
                cmbItems.DisplayMemberPath = "Name";

                // Enable the combo box by default
                cmbItems.IsEnabled = true;

                // If "Apply to All Lenses" is checked, disable the combo box
                if (chkApplyToAllLenses.IsChecked == true)
                {
                    cmbItems.IsEnabled = false;
                }

                // Uncheck other "Apply to All" checkboxes
                if (chkApplyToAllProducts.IsChecked == true)
                {
                    chkApplyToAllProducts.IsChecked = false;
                }

                if (chkApplyToAllServices.IsChecked == true)
                {
                    chkApplyToAllServices.IsChecked = false;
                }
            }
        }

        private async void RdbProduct_Checked(object sender, RoutedEventArgs e)
        {
            if (IsLoaded)
            {
                await LoadProducts();
                cmbItems.ItemsSource = _products;
                cmbItems.DisplayMemberPath = "Name";

                // Enable the combo box by default
                cmbItems.IsEnabled = true;

                // If "Apply to All Products" is checked, disable the combo box
                if (chkApplyToAllProducts.IsChecked == true)
                {
                    cmbItems.IsEnabled = false;
                }

                // Uncheck other "Apply to All" checkboxes
                if (chkApplyToAllLenses.IsChecked == true)
                {
                    chkApplyToAllLenses.IsChecked = false;
                }

                if (chkApplyToAllServices.IsChecked == true)
                {
                    chkApplyToAllServices.IsChecked = false;
                }
            }
        }

        private async void RdbService_Checked(object sender, RoutedEventArgs e)
        {
            if (IsLoaded)
            {
                await LoadServices();
                cmbItems.ItemsSource = _services;
                cmbItems.DisplayMemberPath = "Name";

                // Enable the combo box by default
                cmbItems.IsEnabled = true;

                // If "Apply to All Services" is checked, disable the combo box
                if (chkApplyToAllServices.IsChecked == true)
                {
                    cmbItems.IsEnabled = false;
                }

                // Uncheck other "Apply to All" checkboxes
                if (chkApplyToAllProducts.IsChecked == true)
                {
                    chkApplyToAllProducts.IsChecked = false;
                }

                if (chkApplyToAllLenses.IsChecked == true)
                {
                    chkApplyToAllLenses.IsChecked = false;
                }
            }
        }

        private void chkApplyToAllProducts_Checked(object sender, RoutedEventArgs e)
        {
            if (RdbProductSelect.IsChecked == true)
            {
                cmbItems.IsEnabled = false;
            }
            else
            {
                // If Products radio button is not checked, uncheck the checkbox
                chkApplyToAllProducts.IsChecked = false;
            }
        }

        private void chkApplyToAllProducts_Unchecked(object sender, RoutedEventArgs e)
        {
            if (RdbProductSelect.IsChecked == true)
            {
                cmbItems.IsEnabled = true;
            }
        }

        private void chkApplyToAllLenses_Checked(object sender, RoutedEventArgs e)
        {
            if (RdbLensesSelect.IsChecked == true)
            {
                cmbItems.IsEnabled = false;
            }
            else
            {
                // If Lenses radio button is not checked, uncheck the checkbox
                chkApplyToAllLenses.IsChecked = false;
            }
        }

        private void chkApplyToAllLenses_Unchecked(object sender, RoutedEventArgs e)
        {
            if (RdbLensesSelect.IsChecked == true)
            {
                cmbItems.IsEnabled = true;
            }
        }

        private void chkApplyToAllServices_Checked(object sender, RoutedEventArgs e)
        {
            if (RdbServiceSelect.IsChecked == true)
            {
                cmbItems.IsEnabled = false;
            }
            else
            {
                // If Services radio button is not checked, uncheck the checkbox
                chkApplyToAllServices.IsChecked = false;
            }
        }

        private void chkApplyToAllServices_Unchecked(object sender, RoutedEventArgs e)
        {
            if (RdbServiceSelect.IsChecked == true)
            {
                cmbItems.IsEnabled = true;
            }
        }

        private void cmbItems_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Handle selection change if needed
        }

        private async void list_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            _selectedDiscount = list.SelectedItem as DiscountVM;
            if (_selectedDiscount != null)
            {
                _selectedDiscountId = _selectedDiscount.Id;
                _isEditMode = true;

                // Fill form with selected discount data
                txtDiscountPercentage.Text = _selectedDiscount.DiscountPercentage.ToString(System.Globalization.CultureInfo.InvariantCulture);
                dtpStartDate.SelectedDate = _selectedDiscount.StartDate;
                dtpEndDate.SelectedDate = _selectedDiscount.EndDate;

                chkApplyToAllProducts.IsChecked = _selectedDiscount.ApplyToAllProducts;
                chkApplyToAllLenses.IsChecked = _selectedDiscount.ApplyToAllLenses;
                chkApplyToAllServices.IsChecked = _selectedDiscount.ApplyToAllServices;

                // Set appropriate radio button based on selected items
                if (_selectedDiscount.SelectedProductIds.Any())
                {
                    RdbProductSelect.IsChecked = true;

                    // Load products and set selected product in combo box
                    if (!chkApplyToAllProducts.IsChecked == true)
                    {
                        await LoadProducts();
                        cmbItems.ItemsSource = _products;
                        cmbItems.DisplayMemberPath = "Name";

                        // Find and select the product
                        var productId = _selectedDiscount.SelectedProductIds.FirstOrDefault();
                        var selectedProduct = _products.FirstOrDefault(p => p.Id == productId);
                        if (selectedProduct != null)
                        {
                            cmbItems.SelectedItem = selectedProduct;
                        }
                    }
                }
                else if (_selectedDiscount.SelectedLensIds.Any())
                {
                    RdbLensesSelect.IsChecked = true;

                    // Load lenses and set selected lens in combo box
                    if (!chkApplyToAllLenses.IsChecked == true)
                    {
                        await LoadLenses();
                        cmbItems.ItemsSource = _lenses;
                        cmbItems.DisplayMemberPath = "Name";

                        // Find and select the lens
                        var lensId = _selectedDiscount.SelectedLensIds.FirstOrDefault();
                        var selectedLens = _lenses.FirstOrDefault(l => l.Id == lensId);
                        if (selectedLens != null)
                        {
                            cmbItems.SelectedItem = selectedLens;
                        }
                    }
                }
                else if (_selectedDiscount.SelectedServiceIds.Any())
                {
                    RdbServiceSelect.IsChecked = true;

                    // Load services and set selected service in combo box
                    if (!chkApplyToAllServices.IsChecked == true)
                    {
                        await LoadServices();
                        cmbItems.ItemsSource = _services;
                        cmbItems.DisplayMemberPath = "Name";

                        // Find and select the service
                        var serviceId = _selectedDiscount.SelectedServiceIds.FirstOrDefault();
                        var selectedService = _services.FirstOrDefault(s => s.Id == serviceId);
                        if (selectedService != null)
                        {
                            cmbItems.SelectedItem = selectedService;
                        }
                    }
                }
            }
        }

        // Helper methods
        private async Task LoadDiscounts()
        {
            var discounts = await _discountManager.GetAllDiscountVMs();
            _discounts = new ObservableCollection<DiscountVM>(discounts);
            list.ItemsSource = _discounts;
        }

        private async Task LoadProducts()
        {
            _products = await _productService.GetAllProductsForSellAsync();
            cmbItems.ItemsSource = _products;
            cmbItems.DisplayMemberPath = "Name";
        }

        private async Task LoadLenses()
        {
            _lenses = await _lensService.GetAllLensesAsync();
            cmbItems.ItemsSource = _lenses;
            cmbItems.DisplayMemberPath = "Name";
        }

        private async Task LoadServices()
        {
            _services = await _serviceService.GetAllServices();
            cmbItems.ItemsSource = _services;
            cmbItems.DisplayMemberPath = "Name";
        }

        private void ClearForm()
        {
            txtDiscountPercentage.Text = string.Empty;
            dtpStartDate.SelectedDate = DateTime.Today;
            dtpEndDate.SelectedDate = DateTime.Today.AddDays(30);

            chkApplyToAllProducts.IsChecked = false;
            chkApplyToAllLenses.IsChecked = false;
            chkApplyToAllServices.IsChecked = false;

            cmbItems.SelectedItem = null;

            _isEditMode = false;
            _selectedDiscountId = 0;
            _selectedDiscount = null;

            list.SelectedItem = null;
        }

        private bool ValidateForm()
        {
            // Validate discount percentage
            if (string.IsNullOrWhiteSpace(txtDiscountPercentage.Text))
            {
                ShowError("الرجاء إدخال نسبة الخصم", "تنبيه", false);
                return false;
            }

            if (!decimal.TryParse(txtDiscountPercentage.Text.Replace(',', '.'), System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out decimal percentage))
            {
                ShowError("الرجاء إدخال نسبة الخصم بشكل صحيح", "تنبيه", false);
                return false;
            }

            if (percentage <= 0 || percentage > 100)
            {
                ShowError("نسبة الخصم يجب أن تكون بين 0 و 100", "تنبيه", false);
                return false;
            }

            // Validate dates
            if (dtpStartDate.SelectedDate == null)
            {
                ShowError("الرجاء اختيار تاريخ بداية الخصم", "تنبيه", false);
                return false;
            }

            if (dtpEndDate.SelectedDate == null)
            {
                ShowError("الرجاء اختيار تاريخ نهاية الخصم", "تنبيه", false);
                return false;
            }

            if (dtpStartDate.SelectedDate > dtpEndDate.SelectedDate)
            {
                ShowError("تاريخ بداية الخصم يجب أن يكون قبل تاريخ نهاية الخصم", "تنبيه", false);
                return false;
            }

            // Validate selection
            if (RdbProductSelect.IsChecked == true && !chkApplyToAllProducts.IsChecked == true && cmbItems.SelectedItem == null)
            {
                ShowError("الرجاء اختيار منتج أو تفعيل خيار تطبيق على جميع المنتجات", "تنبيه", false);
                return false;
            }

            if (RdbLensesSelect.IsChecked == true && !chkApplyToAllLenses.IsChecked == true && cmbItems.SelectedItem == null)
            {
                ShowError("الرجاء اختيار عدسة أو تفعيل خيار تطبيق على جميع العدسات", "تنبيه", false);
                return false;
            }

            if (RdbServiceSelect.IsChecked == true && !chkApplyToAllServices.IsChecked == true && cmbItems.SelectedItem == null)
            {
                ShowError("الرجاء اختيار خدمة أو تفعيل خيار تطبيق على جميع الخدمات", "تنبيه", false);
                return false;
            }

            return true;
        }
    }
}
