using Microsoft.Win32;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using VisionPoint.UI.Helper;
using VisionPoint.UI.Models;
using static VisionPoint.UI.Helper.ExportPreferencesHelper;
using VisionPoint.UI.PL;
using VisionPoint.UI.ViewModel;

namespace VisionPoint.UI.views.Dialogs
{
    /// <summary>
    /// نافذة معاينة تصدير المبيعات
    /// </summary>
    public partial class SalesExportPreviewWindow : Window
    {
        private readonly ExportPreviewModel _model;
        private readonly SaleService _saleService;
        private string _selectedSavePath = string.Empty;
        private bool _isExporting = false;

        public bool ExportCompleted { get; private set; } = false;

        public SalesExportPreviewWindow(List<SaleViewModel> salesData, int? clientId = null, int? warehouseId = null)
        {
            InitializeComponent();

            _saleService = new SaleService();
            _model = new ExportPreviewModel
            {
                SalesData = salesData,
                ClientId = clientId,
                WarehouseId = warehouseId,
                FileName = $"تقرير_المبيعات_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
            };

            DataContext = _model;

            // تهيئة النافذة
            InitializeWindow();
        }

        private async void InitializeWindow()
        {
            try
            {
                // تحميل بيانات الأصناف
                await LoadSaleItemsData();

                // تهيئة حقول التصدير
                InitializeExportFields();

                // تطبيق التفضيلات المحفوظة للمبيعات
                ExportPreferencesHelper.ApplyPreferences(_model, ExportType.Sales);

                // تحديث المعاينة
                UpdatePreview();

                // تحديث الملخص
                UpdateSummary();

                // تحديث حالة التبويبات
                UpdateTabsState();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تهيئة النافذة: {ex.Message}", "خطأ", true);
            }
        }

        private async Task LoadSaleItemsData()
        {
            try
            {
                // جلب جميع معرفات الفواتير
                var saleIds = _model.SalesData.Select(s => s.Id).ToList();

                // جلب جميع الأصناف في استعلام واحد محسن
                var allItems = await _saleService.GetSaleItemsBySaleIdsAsync(saleIds);

                _model.SalesItemsData = allItems;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل بيانات الأصناف: {ex.Message}", "خطأ", true);
            }
        }

        private void InitializeExportFields()
        {
            // حقول الفواتير
            _model.InvoiceFields = new List<ExportFieldOption>
            {
                new() { FieldKey = "Index", DefaultName = "الرقم التسلسلي", IsSelected = true, IsRequired = false },
                new() { FieldKey = "InvoiceNumber", DefaultName = "رقم الفاتورة", IsSelected = true, IsRequired = true },
                new() { FieldKey = "SaleDate", DefaultName = "تاريخ الفاتورة", IsSelected = true, IsRequired = false },
                new() { FieldKey = "ClientName", DefaultName = "العميل", IsSelected = true, IsRequired = false },
                new() { FieldKey = "WarehouseName", DefaultName = "المخزن", IsSelected = true, IsRequired = false },
                new() { FieldKey = "TotalBeforeDiscount", DefaultName = "الإجمالي قبل الخصم", IsSelected = true, IsRequired = false },
                new() { FieldKey = "TotalDiscount", DefaultName = "إجمالي الخصم", IsSelected = true, IsRequired = false },
                new() { FieldKey = "TotalAmount", DefaultName = "إجمالي الفاتورة", IsSelected = true, IsRequired = false },
                new() { FieldKey = "TotalReturned", DefaultName = "إجمالي المسترجعات", IsSelected = true, IsRequired = false },
                new() { FieldKey = "NetAmount", DefaultName = "الصافي", IsSelected = true, IsRequired = false },
                new() { FieldKey = "PaidAmount", DefaultName = "المدفوع", IsSelected = true, IsRequired = false },
                new() { FieldKey = "RemainingAmount", DefaultName = "المتبقي", IsSelected = true, IsRequired = false }
            };

            // حقول الأصناف
            _model.ItemFields = new List<ExportFieldOption>
            {
                new() { FieldKey = "Index", DefaultName = "الرقم التسلسلي", IsSelected = true, IsRequired = false },
                new() { FieldKey = "SaleId", DefaultName = "رقم الفاتورة", IsSelected = true, IsRequired = false },
                new() { FieldKey = "ClientName", DefaultName = "العميل", IsSelected = true, IsRequired = false },
                new() { FieldKey = "WarehouseName", DefaultName = "المخزن", IsSelected = true, IsRequired = false },
                new() { FieldKey = "Type", DefaultName = "نوع الصنف", IsSelected = true, IsRequired = false },
                new() { FieldKey = "Name", DefaultName = "اسم الصنف", IsSelected = true, IsRequired = true },
                new() { FieldKey = "ColorName", DefaultName = "اللون", IsSelected = true, IsRequired = false },
                new() { FieldKey = "Quantity", DefaultName = "الكمية", IsSelected = true, IsRequired = false },
                new() { FieldKey = "ReturnedQuantity", DefaultName = "الكمية المسترجعة", IsSelected = true, IsRequired = false },
                new() { FieldKey = "RemainingQuantity", DefaultName = "الكمية المتبقية", IsSelected = true, IsRequired = false },
                new() { FieldKey = "CostPrice", DefaultName = "سعر التكلفة", IsSelected = true, IsRequired = false },
                new() { FieldKey = "OriginalPrice", DefaultName = "السعر الأصلي", IsSelected = true, IsRequired = false },
                new() { FieldKey = "Discount", DefaultName = "الخصم", IsSelected = true, IsRequired = false },
                new() { FieldKey = "SellPrice", DefaultName = "سعر البيع", IsSelected = true, IsRequired = false }
            };

            // ربط البيانات
            invoiceFieldsList.ItemsSource = _model.InvoiceFields;
            itemFieldsList.ItemsSource = _model.ItemFields;

            // ربط أحداث تغيير الحقول
            foreach (var field in _model.InvoiceFields)
            {
                field.PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(ExportFieldOption.IsSelected))
                        UpdateInvoicesPreview();
                };
            }

            foreach (var field in _model.ItemFields)
            {
                field.PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(ExportFieldOption.IsSelected))
                        UpdateItemsPreview();
                };
            }
        }

        private void UpdatePreview()
        {
            // تحديث معاينة الفواتير
            if (_model.ExportInvoices)
            {
                UpdateInvoicesPreview();
            }

            // تحديث معاينة الأصناف
            if (_model.ExportItems)
            {
                UpdateItemsPreview();
            }
        }

        private void UpdateInvoicesPreview()
        {
            invoicesDataGrid.Columns.Clear();

            var selectedFields = _model.InvoiceFields.Where(f => f.IsSelected).ToList();

            // تعيين الرقم التسلسلي للفواتير
            for (int i = 0; i < _model.SalesData.Count; i++)
            {
                _model.SalesData[i].Index = i + 1;
            }

            foreach (var field in selectedFields)
            {
                var column = new DataGridTextColumn
                {
                    Header = field.CustomHeader,
                    Binding = new Binding(field.FieldKey)
                };

                invoicesDataGrid.Columns.Add(column);
            }

            invoicesDataGrid.ItemsSource = _model.SalesData;
        }

        private void UpdateItemsPreview()
        {
            itemsDataGrid.Columns.Clear();

            var selectedFields = _model.ItemFields.Where(f => f.IsSelected).ToList();

            // تعيين الرقم التسلسلي للأصناف
            for (int i = 0; i < _model.SalesItemsData.Count; i++)
            {
                _model.SalesItemsData[i].Index = i + 1;
            }

            foreach (var field in selectedFields)
            {
                var column = new DataGridTextColumn
                {
                    Header = field.CustomHeader,
                    Binding = new Binding(field.FieldKey)
                };

                itemsDataGrid.Columns.Add(column);
            }

            itemsDataGrid.ItemsSource = _model.SalesItemsData;
        }

        private void UpdateSummary()
        {
            var invoicesCount = _model.ExportInvoices ? _model.SalesData.Count : 0;
            var itemsCount = _model.ExportItems ? _model.SalesItemsData.Count : 0;

            txtSummary.Text = $"إجمالي الفواتير: {invoicesCount} | إجمالي الأصناف: {itemsCount}";
        }

        private void UpdateTabsState()
        {
            // تفعيل/تعطيل التبويبات بناءً على خيارات التصدير
            tabInvoices.IsEnabled = _model.ExportInvoices;
            tabItems.IsEnabled = _model.ExportItems;

            // تغيير شفافية التبويبات المعطلة
            tabInvoices.Opacity = _model.ExportInvoices ? 1.0 : 0.5;
            tabItems.Opacity = _model.ExportItems ? 1.0 : 0.5;
        }

        private void ExportOption_Changed(object sender, RoutedEventArgs e)
        {
            if (!IsLoaded) return;

            _model.ExportInvoices = chkExportInvoices.IsChecked == true;
            _model.ExportItems = chkExportItems.IsChecked == true;

            // التحقق من أن خيار واحد على الأقل مختار
            if (!_model.ExportInvoices && !_model.ExportItems)
            {
                ErrorBox.Show("يجب اختيار خيار واحد على الأقل للتصدير", "تنبيه", false);

                // إعادة تفعيل الخيار الذي تم إلغاؤه
                if (sender == chkExportInvoices)
                    chkExportInvoices.IsChecked = true;
                else
                    chkExportItems.IsChecked = true;

                return;
            }

            UpdatePreview();
            UpdateSummary();
            UpdateTabsState();
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnExportNow.IsEnabled = false;
            btnCancel.IsEnabled = false;
            btnSelectPath.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnExportNow.IsEnabled = true;
            btnCancel.IsEnabled = true;
            btnSelectPath.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private void btnSelectPath_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx",
                    FileName = _model.FileName,
                    Title = "حفظ تقرير المبيعات",
                    InitialDirectory = ExportPreferencesHelper.GetLastExportPath(ExportType.Sales)
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    _selectedSavePath = saveFileDialog.FileName;
                    txtSelectedPath.Text = _selectedSavePath;
                    _model.FileName = System.IO.Path.GetFileNameWithoutExtension(_selectedSavePath);

                    // حفظ مسار التصدير للاستخدام المستقبلي
                    ExportPreferencesHelper.SaveLastExportPath(_selectedSavePath, ExportType.Sales);
                }
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnExportNow_Click(object sender, RoutedEventArgs e)
        {
            if (_isExporting) return;
            DisableAllButtons();
            try
            {
                // التحقق من المتطلبات
                if (string.IsNullOrEmpty(_selectedSavePath))
                {
                    ErrorBox.Show("يرجى اختيار مسار حفظ الملف أولاً", "تنبيه", false);
                    return;
                }

                if (!_model.ExportInvoices && !_model.ExportItems)
                {
                    ErrorBox.Show("يجب اختيار خيار واحد على الأقل للتصدير", "تنبيه", false);
                    return;
                }

                // تأكيد التصدير
                var result = QuestionBox.Show("تأكيد التصدير", "هل أنت متأكد من بدء عملية التصدير؟");
                if (result != MessageBoxResult.Yes) return;

                await StartExportProcess();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء التصدير: {ex.Message}", "خطأ في التصدير", true);
            }
            finally
            {
                _isExporting = false;
                progressSection.Visibility = Visibility.Collapsed;
                btnExportNow.IsEnabled = true;
            }
        }

        private async Task StartExportProcess()
        {
            try
            {
                _isExporting = true;
                btnExportNow.IsEnabled = false;
                progressSection.Visibility = Visibility.Visible;

                var progress = new Progress<int>(percentage =>
                {
                    Dispatcher.Invoke(() =>
                    {
                        progressBar.Value = percentage;
                        txtProgress.Text = $"{percentage}%";
                    });
                });

                var exportService = new ExportService();

                // تحديد الحقول المختارة
                var selectedInvoiceFields = _model.InvoiceFields.Where(f => f.IsSelected).Select(f => f.FieldKey).ToList();
                var selectedItemFields = _model.ItemFields.Where(f => f.IsSelected).Select(f => f.FieldKey).ToList();

                // تحديد رؤوس الأعمدة المخصصة
                var invoiceHeaders = _model.InvoiceFields.Where(f => f.IsSelected).ToDictionary(f => f.FieldKey, f => f.CustomHeader);
                var itemHeaders = _model.ItemFields.Where(f => f.IsSelected).ToDictionary(f => f.FieldKey, f => f.CustomHeader);

                await exportService.ExportSalesWithPreviewAsync(
                    _selectedSavePath,
                    _model.SalesData,
                    _model.SalesItemsData,
                    _model.ExportInvoices,
                    _model.ExportItems,
                    selectedInvoiceFields,
                    selectedItemFields,
                    invoiceHeaders,
                    itemHeaders,
                    _model.ClientId,
                    _model.WarehouseId,
                    progress);

                // حفظ التفضيلات للاستخدام المستقبلي
                ExportPreferencesHelper.SavePreferences(_model, ExportType.Sales);

                ExportCompleted = true;
                DialogBox.Show("تم بنجاح", "تم تصدير البيانات بنجاح");
                Close();
            }
            catch (Exception)
            {

                throw;
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (_isExporting)
                {
                    var result = QuestionBox.Show("تأكيد الإلغاء", "هل أنت متأكد من إلغاء عملية التصدير؟");
                    if (result != MessageBoxResult.Yes) return;
                }

                ExportCompleted = false;
                Close();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // تطبيق تأثيرات التحميل إذا لزم الأمر
        }

        private void btnClose_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            btnCancel_Click(sender, e);
        }

        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            if (_isExporting)
            {
                var result = QuestionBox.Show("تأكيد الإغلاق", "عملية التصدير جارية. هل أنت متأكد من إغلاق النافذة؟");
                if (result != MessageBoxResult.Yes)
                {
                    e.Cancel = true;
                    return;
                }
            }

            base.OnClosing(e);
        }
    }
}
