using Microsoft.EntityFrameworkCore;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Models;

namespace VisionPoint.UI.PL;

public class ReceiptService : IDisposable
{
    private readonly AppDbContext _context ;
    private bool _disposed = false;

    public ReceiptService()
    {
        _context = ServiceLocator.GetService<AppDbContext>();
    }
    public async Task<List<Financial>> GetFinancials()
    {
        return await _context.Financials
            .Where(f => f.Id != (byte)FinancialId.AutomaticSalary) // استبعاد نوع إضافة راتب تلقائي
            .OrderBy(f => f.Name)
            .ToListAsyncWithBusy("GetFinancials");
    }

    public async Task<List<Receipt>> GetAllReceipts()
    {
        return await _context.Receipts
            .Where(r => r.FinancialId != (byte)FinancialId.AutomaticSalary) // استبعاد إيصالات إضافة راتب تلقائي
            .Include(r => r.Treasury)
            .Include(r => r.SourceTreasury)
            .Include(r => r.TargetTreasury)
            .Include(r => r.Client)
            .Include(r => r.Purchase)
            .Include(r => r.Sale)
            .Include(r => r.Employee)
            .Include(r => r.Financial)
            .Include(r => r.Warehouse)
            .OrderByDescending(r => r.Id)
            .ToListAsyncWithBusy("GetAllReceipts");
    }



    public async Task<Receipt> GetReceiptById(int id)
    {
        return await _context.Receipts
            .Include(r => r.Treasury)
            .Include(r => r.SourceTreasury)
            .Include(r => r.TargetTreasury)
            .Include(r => r.Client)
            .Include(r => r.Purchase)
            .Include(r => r.Sale)
            .Include(r => r.Employee)
            .Include(r => r.Financial)
            .FirstOrDefaultAsyncWithBusy(r => r.Id == id && r.FinancialId != (byte)FinancialId.AutomaticSalary, "GetReceiptById");
    }

    private async Task<(bool isValid, string errorMessage)> ValidateFinancialRules(Receipt receipt)
    {
        // التحقق من وجود نوع مالي
        if (!receipt.FinancialId.HasValue)
        {
            return (false, "يجب تحديد نوع العملية المالية");
        }

        // التحقق من القواعد حسب نوع العملية المالية
        FinancialId financialId = (FinancialId)receipt.FinancialId.Value;

        switch (financialId)
        {
                case FinancialId.Purchase:
                    // يجب أن يكون دائماً صرف
                    receipt.IsExchange = true;

                    // يجب أن يكون هناك معرف شراء ولا يتجاوز المبلغ المتبقي
                    if (!receipt.PurchaseId.HasValue)
                    {
                        return (false, "يجب تحديد فاتورة المشتريات");
                    }

                    var purchase = await _context.Purchases
                        .Include(p => p.Receipts)
                        .FirstOrDefaultAsyncWithBusy(p => p.Id == receipt.PurchaseId.Value, "GetPurchaseForValidation");

                    if (purchase == null)
                    {
                        return (false, "فاتورة المشتريات غير موجودة");
                    }

                    // يجب أن يتطابق العميل مع الفاتورة
                    receipt.ClientId = purchase.ClientId;

                    // التحقق من المبلغ المتبقي
                    decimal paidAmount = purchase.Receipts?.Where(r => r.Id != receipt.Id).Sum(r => r.Value) ?? 0;
                    decimal remainingAmount = purchase.TotalAmount - paidAmount;

                    if (receipt.Value > remainingAmount)
                    {
                        return (false, $"المبلغ المدخل ({receipt.Value:N2}) يتجاوز المبلغ المتبقي ({remainingAmount:N2})");
                    }
                    break;

                case FinancialId.Sale:
                    // يجب أن يكون دائماً قبض
                    receipt.IsExchange = false;

                    // يجب أن يكون هناك معرف بيع ولا يتجاوز المبلغ المتبقي
                    if (!receipt.SaleId.HasValue)
                    {
                        return (false, "يجب تحديد فاتورة المبيعات");
                    }

                    var sale = await _context.Sales
                        .Include(s => s.Receipts)
                        .FirstOrDefaultAsyncWithBusy(s => s.Id == receipt.SaleId.Value, "GetSaleForValidation");

                    if (sale == null)
                    {
                        return (false, "فاتورة المبيعات غير موجودة");
                    }

                    // يجب أن يتطابق العميل مع الفاتورة
                    receipt.ClientId = sale.ClientId;

                    // التحقق من المبلغ المتبقي
                    decimal saleAmountPaid = sale.Receipts?.Where(r => r.Id != receipt.Id).Sum(r => r.Value) ?? 0;
                    decimal saleRemainingAmount = sale.TotalAmount - saleAmountPaid;

                    if (receipt.Value > saleRemainingAmount)
                    {
                        return (false, $"المبلغ المدخل ({receipt.Value:N2}) يتجاوز المبلغ المتبقي ({saleRemainingAmount:N2})");
                    }
                    break;

                case FinancialId.Employee:
                    // يمكن أن يكون إيراد أو مصروف (تم تكوينه في واجهة المستخدم)
                    if (!receipt.EmployeeId.HasValue || receipt.EmployeeId.Value <= 0)
                    {
                        return (false, "يجب تحديد الموظف");
                    }
                    break;

                case FinancialId.OpeningBalanceForEmployee:
                    // ليس إيراد ولا مصروف، لكن يحتاج إلى تعديل الرصيد
                    if (!receipt.EmployeeId.HasValue || receipt.EmployeeId.Value <= 0)
                    {
                        return (false, "يجب تحديد الموظف للرصيد الافتتاحي");
                    }

                    // التحقق مما إذا كان هذا واصل جديد (إضافة)
                    if (receipt.Id == 0)
                    {
                        // التحقق مما إذا كان هناك واصلات أخرى للموظف
                        bool hasOtherReceipts = await HasOtherEmployeeReceipts(receipt.EmployeeId.Value);
                        if (hasOtherReceipts)
                        {
                            // لا يمكن إضافة رصيد افتتاحي إذا كان هناك واصلات أخرى
                            return (false, "لا يمكن إضافة رصيد افتتاحي للموظف لوجود إيصالات أخرى");
                        }

                        // التحقق مما إذا كان هناك رصيد افتتاحي موجود بالفعل
                        bool hasOpeningBalance = await HasOpeningBalanceForEmployee(receipt.EmployeeId.Value);
                        if (hasOpeningBalance)
                        {
                            // لا يمكن إضافة أكثر من رصيد افتتاحي واحد
                            return (false, "يوجد رصيد افتتاحي للموظف مسبقاً");
                        }
                    }

                    // لا يؤثر على IsExchange
                    receipt.IsExchange = null;
                    receipt.TreasuryId = null;

                    // تحديث رصيد الموظف
                    var employee = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId.Value);
                    if (employee != null)
                    {
                        employee.Balance = receipt.Value;
                    }
                    break;

                case FinancialId.OpeningBalanceForClient:
                    // ليس إيراد ولا مصروف، لكن يحتاج إلى تعديل الرصيد
                    if (!receipt.ClientId.HasValue || receipt.ClientId.Value <= 0)
                    {
                        return (false, "يجب تحديد العميل للرصيد الافتتاحي");
                    }

                    // التحقق مما إذا كان هذا واصل جديد (إضافة)
                    if (receipt.Id == 0)
                    {
                        // التحقق مما إذا كان هناك واصلات أخرى للعميل
                        bool hasOtherReceipts = await HasOtherClientReceipts(receipt.ClientId.Value);
                        if (hasOtherReceipts)
                        {
                            // لا يمكن إضافة رصيد افتتاحي إذا كان هناك واصلات أخرى
                            return (false, "لا يمكن إضافة رصيد افتتاحي للعميل لوجود إيصالات أخرى");
                        }

                        // التحقق مما إذا كان هناك رصيد افتتاحي موجود بالفعل
                        bool hasOpeningBalance = await HasOpeningBalanceForClient(receipt.ClientId.Value);
                        if (hasOpeningBalance)
                        {
                            // لا يمكن إضافة أكثر من رصيد افتتاحي واحد
                            return (false, "يوجد رصيد افتتاحي للعميل مسبقاً");
                        }
                    }

                    // لا يؤثر على IsExchange
                    receipt.IsExchange = null;
                    receipt.TreasuryId = null;

                    // تحديث رصيد العميل
                    var client = await _context.Clients.FindAsyncWithBusy(receipt.ClientId.Value);
                    if (client != null)
                    {
                        client.Balance = receipt.Value;
                    }
                    break;

                case FinancialId.Client:
                    // يمكن أن يكون إيراد أو مصروف (تم تكوينه في واجهة المستخدم)
                    if (!receipt.ClientId.HasValue || receipt.ClientId.Value <= 0)
                    {
                        return (false, "يجب تحديد العميل");
                    }
                    break;

                case FinancialId.Withdrawal:
                    // ليس إيراد ولا مصروف، لكن يؤثر على طريقة الدفع
                    if (!receipt.TreasuryId.HasValue)
                    {
                        return (false, "يجب تحديد طريقة الدفع للسحب");
                    }
                    // لا يؤثر على IsExchange
                    receipt.IsExchange = null;
                    break;

                case FinancialId.Deposit:
                    // ليس إيراد ولا مصروف، لكن يؤثر على طريقة الدفع
                    if (!receipt.TreasuryId.HasValue)
                    {
                        return (false, "يجب تحديد طريقة الدفع للإيداع");
                    }
                    // لا يؤثر على IsExchange
                    receipt.IsExchange = null;
                    break;

                case FinancialId.Expense:
                    // دائماً يكون مصروف (صرف) ولا يمكن تغييره
                    receipt.IsExchange = true;

                    // التحقق من وجود مصروف
                    if (receipt.ExpenseId.HasValue)
                    {
                        var expense = await _context.Expenses.FindAsyncWithBusy(receipt.ExpenseId.Value);
                        if (expense == null)
                        {
                            return (false, "المصروف المحدد غير موجود");
                        }
                    }
                    else
                    {
                        return (false, "يجب تحديد نوع المصروف");
                    }
                    break;

                case FinancialId.SalaryPayment:
                    // دائماً يكون مصروف (صرف) ولا يمكن تغييره
                    receipt.IsExchange = true;

                    // التحقق من وجود موظف
                    if (receipt.EmployeeId.HasValue)
                    {
                        var user = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId.Value);
                        if (user == null)
                        {
                            return (false, "الموظف المحدد غير موجود");
                        }
                    }
                    else
                    {
                        return (false, "يجب تحديد الموظف لصرف الراتب");
                    }
                    break;

                case FinancialId.AutomaticSalary:
                    // إضافة راتب تلقائي - لا يؤثر على الخزينة
                    receipt.IsExchange = null;
                    receipt.TreasuryId = null; // لا يوجد خزينة مرتبطة

                    // التحقق من وجود موظف
                    if (receipt.EmployeeId.HasValue)
                    {
                        var user = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId.Value);
                        if (user == null)
                        {
                            return (false, "الموظف المحدد غير موجود");
                        }
                    }
                    else
                    {
                        return (false, "يجب تحديد الموظف للراتب التلقائي");
                    }
                    break;

                case FinancialId.Transfer:
                    // التحقق من وجود طريقتي الدفع
                    if (!receipt.SourceTreasuryId.HasValue || !receipt.TargetTreasuryId.HasValue)
                    {
                        return (false, "يجب تحديد طريقتي الدفع للتحويل");
                    }

                    // التحقق من أن طريقتي الدفع مختلفتان
                    if (receipt.SourceTreasuryId == receipt.TargetTreasuryId)
                    {
                        return (false, "لا يمكن التحويل من وإلى نفس طريقة الدفع");
                    }

                    // التحقق من أن المبلغ موجب
                    if (receipt.Value <= 0)
                    {
                        return (false, "يجب أن يكون مبلغ التحويل أكبر من الصفر");
                    }

                    // التحقق من رصيد طريقة الدفع المصدر
                    var sourceTreasury = await _context.Treasuries.FindAsyncWithBusy(receipt.SourceTreasuryId.Value);
                    if (sourceTreasury == null)
                    {
                        return (false, "طريقة الدفع المصدر غير موجودة");
                    }

                    if (sourceTreasury.Balance < receipt.Value)
                    {
                        return (false, $"رصيد طريقة الدفع المصدر ({sourceTreasury.Balance:N2}) غير كافي للتحويل ({receipt.Value:N2})");
                    }

                    // تعيين خصائص الإيصال
                    receipt.IsExchange = null; // ليس قبض ولا صرف
                    receipt.TreasuryId = null; // لا نستخدم الحقل العادي
                    break;

        }

        return (true, "");
    }

    public async Task<(bool success, string message, int receiptId)> CreateReceipt(Receipt receipt)
    {
        try
        {
            // Validar reglas según tipo financiero
            var (isValid, errorMessage) = await ValidateFinancialRules(receipt);
            if (!isValid)
            {
                return (false, errorMessage, 0);
            }

            // تحديد المخزن بناءً على نوع العملية
            FinancialId financialType = (FinancialId)receipt.FinancialId.Value;

            if (financialType == FinancialId.OpeningBalanceForClient)
            {
                // للرصيد الافتتاحي للعميل، استخدم مخزن العميل
                if (receipt.ClientId.HasValue)
                {
                    var client = await _context.Clients
                        .Include(c => c.Warehouse)
                        .FirstOrDefaultAsyncWithBusy(c => c.Id == receipt.ClientId.Value, "GetClientForOpeningBalance");

                    if (client != null)
                    {
                        receipt.WarehouseId = client.WarehouseId;
                    }
                }
            }
            else if (financialType == FinancialId.OpeningBalanceForEmployee)
            {
                // للرصيد الافتتاحي للموظف، استخدم مخزن الموظف
                if (receipt.EmployeeId.HasValue)
                {
                    var employee = await _context.Users
                        .Include(u => u.Warehouse)
                        .FirstOrDefaultAsyncWithBusy(u => u.Id == receipt.EmployeeId.Value, "GetEmployeeForOpeningBalance");

                    if (employee != null)
                    {
                        receipt.WarehouseId = employee.WarehouseId;
                    }
                }
            }
            else if (financialType == FinancialId.Transfer)
            {
                // للتحويل بين طرق الدفع، استخدم مخزن طريقة الدفع المصدر
                if (receipt.SourceTreasuryId.HasValue)
                {
                    var sourceTreasury = await _context.Treasuries
                        .Include(t => t.Warehouse)
                        .FirstOrDefaultAsyncWithBusy(t => t.Id == receipt.SourceTreasuryId.Value, "GetSourceTreasuryWithWarehouse");

                    if (sourceTreasury != null)
                    {
                        receipt.WarehouseId = sourceTreasury.WarehouseId;
                    }
                }
            }
            else if (receipt.TreasuryId.HasValue)
            {
                // للعمليات الأخرى، استخدم مخزن طريقة الدفع
                var treasury = await _context.Treasuries
                    .Include(t => t.Warehouse)
                    .FirstOrDefaultAsyncWithBusy(t => t.Id == receipt.TreasuryId.Value, "GetTreasuryWithWarehouse");

                if (treasury != null)
                {
                    receipt.WarehouseId = treasury.WarehouseId;
                }
            }

            // Generar número de recibo بناءً على المخزن
            Receipt receiptNo;
            string warehouseCode = "";

            if (receipt.WarehouseId.HasValue)
            {
                // الحصول على رمز المخزن
                var warehouse = await _context.Warehouses
                    .FirstOrDefaultAsyncWithBusy(w => w.Id == receipt.WarehouseId.Value, "GetWarehouseForReceipt");
                warehouseCode = warehouse?.Code ?? "";

                // ترقيم منفصل لكل مخزن
                receiptNo = await _context.Receipts
                    .Where(r => r.WarehouseId == receipt.WarehouseId.Value)
                    .OrderByDescending(r => r.ReceiptNo)
                    .FirstOrDefaultAsyncWithBusy("GetLastReceiptNumberByWarehouse");
            }
            else
            {
                // ترقيم عام للواصلات بدون مخزن محدد
                receiptNo = await _context.Receipts
                    .Where(r => r.WarehouseId == null)
                    .OrderByDescending(r => r.ReceiptNo)
                    .FirstOrDefaultAsyncWithBusy("GetLastReceiptNumberGeneral");
                warehouseCode = "GEN"; // رمز عام للواصلات بدون مخزن
            }

            int nextReceiptNumber = (receiptNo?.ReceiptNo ?? 0) + 1;
            receipt.ReceiptNo = nextReceiptNumber;

            // إنشاء رقم الواصل مع رمز المخزن
            receipt.ReceiptNumber = $"{warehouseCode}-{nextReceiptNumber}";

                // Procesar según tipos de operaciones
                FinancialId financialId = (FinancialId)receipt.FinancialId.Value;

                // Aplicar cambios en los saldos según tipo de operación
                if (receipt.IsExchange == true) // صرف (Gasto)
                {
                    var treasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TreasuryId);
                    var user = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId);

                    if (treasury != null)
                        treasury.Balance -= receipt.Value;

                    // Para empleados، تحديث الرصيد
                    if (financialId == FinancialId.Employee)
                    {
                        if (user != null)
                        {
                            // التحقق من حدود الراتب قبل تحديث الرصيد
                            var (isEmployeeValid, validationMessage) = await ValidateEmployeeBalanceLimit(user.Id, receipt.Value);
                            if (!isEmployeeValid)
                            {
                                return (false, validationMessage, 0);
                            }
                            user.Balance += receipt.Value;
                        }
                    }
                    else if (financialId == FinancialId.SalaryPayment)
                    {
                        if (user != null)
                        {
                            // التحقق من حدود الراتب قبل تحديث الرصيد
                            var (isSalaryValid, validationMessage2) = await ValidateEmployeeBalanceLimit(user.Id, receipt.Value);
                            if (!isSalaryValid)
                            {
                                return (false, validationMessage2, 0);
                            }
                            user.Balance += receipt.Value; // زيادة رصيد الموظف (صرف المرتب)
                        }
                    }

                    // Para clientes، تحديث الرصيد
                    if (financialId == FinancialId.Client)
                    {
                        if (receipt.ClientId.HasValue)
                        {
                            var client = await _context.Clients.FindAsyncWithBusy(receipt.ClientId);
                            if (client != null)
                            {
                                // التحقق من الحد المسموح قبل تحديث الرصيد
                                var clientService = new ClientService();
                                var (isAllowed, clientMessage) = await clientService.CheckBalanceChangeAsync(receipt.ClientId.Value, receipt.Value);
                                if (!isAllowed)
                                {
                                    return (false, clientMessage, 0);
                                }

                                client.Balance += receipt.Value; // زيادة مديونية العميل (دفع للعميل)
                            }
                        }
                    }
                    else if (financialId == FinancialId.Purchase)
                    {
                        if (receipt.ClientId.HasValue)
                        {
                            var client = await _context.Clients.FindAsyncWithBusy(receipt.ClientId);
                            if (client != null)
                            {
                                // التحقق من الحد المسموح قبل تحديث الرصيد
                                var clientService = new ClientService();
                                var (isAllowed, clientMessage2) = await clientService.CheckBalanceChangeAsync(receipt.ClientId.Value, -receipt.Value);
                                if (!isAllowed)
                                {
                                    return (false, clientMessage2, 0);
                                }

                                client.Balance -= receipt.Value; // نقص مديونية العميل (دفع للمورد)
                            }
                        }
                    }
                }
                else if (receipt.IsExchange == false) // قبض (Ingreso)
                {
                    var treasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TreasuryId);
                    var user = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId);

                    if (treasury != null)
                        treasury.Balance += receipt.Value;

                    // Para empleados، تحديث الرصيد
                    if (financialId == FinancialId.Employee)
                    {
                        if (user != null)
                            user.Balance -= receipt.Value;
                    }

                    // Para clientes، تحديث الرصيد
                    if (financialId == FinancialId.Client)
                    {
                        if (receipt.ClientId.HasValue)
                        {
                            var client = await _context.Clients.FindAsyncWithBusy(receipt.ClientId);
                            if (client != null)
                            {
                                // التحقق من الحد المسموح قبل تحديث الرصيد
                                var clientService = new ClientService();
                                var (isAllowed, clientMessage3) = await clientService.CheckBalanceChangeAsync(receipt.ClientId.Value, -receipt.Value);
                                if (!isAllowed)
                                {
                                    return (false, clientMessage3, 0);
                                }

                                client.Balance -= receipt.Value; // نقص مديونية العميل (قبض من العميل)
                            }
                        }
                    }
                    else if (financialId == FinancialId.Sale)
                    {
                        if (receipt.ClientId.HasValue)
                        {
                            var client = await _context.Clients.FindAsyncWithBusy(receipt.ClientId);
                            if (client != null)
                            {
                                // التحقق من الحد المسموح قبل تحديث الرصيد
                                var clientService = new ClientService();
                                var (isAllowed, clientMessage4) = await clientService.CheckBalanceChangeAsync(receipt.ClientId.Value, -receipt.Value);
                                if (!isAllowed)
                                {
                                    return (false, clientMessage4, 0);
                                }

                                client.Balance -= receipt.Value; // نقص مديونية العميل (قبض من العميل لفاتورة مبيعات)
                            }
                        }
                    }
                }
                // Para operaciones especiales
                else if (financialId == FinancialId.Deposit) // إيداع
                {
                    // زيادة رصيد الخزينة
                    var treasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TreasuryId);
                    if (treasury != null)
                        treasury.Balance += receipt.Value;
                }
                else if (financialId == FinancialId.Withdrawal) // سحب
                {
                    // نقصان رصيد الخزينة
                    var treasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TreasuryId);
                    if (treasury != null)
                        treasury.Balance -= receipt.Value;
                }
                else if (financialId == FinancialId.OpeningBalanceForEmployee) // رصيد افتتاحي للموظف
                {
                    // تحديث رصيد الموظف
                    var user = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId);
                    if (user != null)
                        user.Balance = receipt.Value; // تعيين الرصيد الافتتاحي مباشرة (وليس إضافة)
                }
                else if (financialId == FinancialId.Transfer) // تحويل بين طرق الدفع
                {
                    // خصم من طريقة الدفع المصدر
                    var sourceTreasury = await _context.Treasuries.FindAsyncWithBusy(receipt.SourceTreasuryId);
                    if (sourceTreasury != null)
                        sourceTreasury.Balance -= receipt.Value;

                    // إضافة لطريقة الدفع الهدف
                    var targetTreasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TargetTreasuryId);
                    if (targetTreasury != null)
                        targetTreasury.Balance += receipt.Value;

                    // تعيين خصائص الإيصال
                    receipt.IsExchange = null; // ليس قبض ولا صرف
                    receipt.TreasuryId = null; // لا نستخدم الحقل العادي للخزينة
                }
                else if (financialId == FinancialId.OpeningBalanceForClient) // رصيد افتتاحي للعميل
                {
                    // تحديث رصيد العميل
                    var client = await _context.Clients.FindAsyncWithBusy(receipt.ClientId);
                    if (client != null)
                        client.Balance = receipt.Value; // تعيين الرصيد الافتتاحي مباشرة (وليس إضافة)
                }
                else if (financialId == FinancialId.SalaryPayment) // صرف مرتب
                {
                    // تحديث رصيد الموظف
                    var user = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId);
                    if (user != null)
                    {
                        // التحقق من حدود الراتب قبل تحديث الرصيد
                        var (isSalaryPaymentValid, validationMessage3) = await ValidateEmployeeBalanceLimit(user.Id, receipt.Value);
                        if (!isSalaryPaymentValid)
                        {
                            return (false, validationMessage3, 0);
                        }
                        user.Balance += receipt.Value; // زيادة رصيد الموظف (صرف المرتب)
                    }
                }
                else if (financialId == FinancialId.AutomaticSalary) // إضافة راتب تلقائي
                {
                    // تحديث رصيد الموظف
                    var user = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId);
                    if (user != null)
                        user.Balance -= receipt.Value; // نقص رصيد الموظف (إضافة المرتب التلقائي)
                }

                // Establecer metadatos
                receipt.CreatedById = CurrentUser.Id;
                receipt.ModifiedById = CurrentUser.Id;
                receipt.CreatedAt = DateTime.Now;
                receipt.UpdatedAt = DateTime.Now;
                receipt.Date = receipt.Date == default(DateTime) ? DateTime.Now : receipt.Date;

                await _context.Receipts.AddAsyncWithBusy(receipt);
                var (success, message) = await _context.SaveWithTransactionAndBusy("CreateReceipt");

                if (success)
                {
                    return (true, message, receipt.Id);
                }
                else
                {
                    return (false, message, 0);
                }
            }
            catch (Exception ex)
            {
            // Reverse any changes made before the exception
            _context.Reverse();
            return (false, $"خطأ في إنشاء الإيصال: {ex.Message}", 0);
        }
    }

    public async Task<(bool success, string message, int receiptId)> UpdateReceipt(Receipt receipt)
    {
        try
        {
            var existingReceipt = await _context.Receipts
                .Include(r => r.Purchase)
                .Include(r => r.Sale)
                .FirstOrDefaultAsyncWithBusy(r => r.Id == receipt.Id, "GetReceiptForUpdate");

                if (existingReceipt == null)
                {
                    return (false, "الإيصال غير موجود", 0);
                }

                // التحقق مما إذا كان الواصل من نوع رصيد افتتاحي
                if (existingReceipt.FinancialId == (byte)FinancialId.OpeningBalanceForClient)
                {
                    // التحقق مما إذا كان هناك واصلات أخرى للعميل
                    if (existingReceipt.ClientId.HasValue)
                    {
                        bool hasOtherReceipts = await HasOtherClientReceipts(existingReceipt.ClientId.Value, existingReceipt.Id);
                        if (hasOtherReceipts)
                        {
                            return (false, "لا يمكن تعديل الرصيد الافتتاحي للعميل لأنه توجد واصلات أخرى", 0);
                        }
                    }
                }
                else if (existingReceipt.FinancialId == (byte)FinancialId.OpeningBalanceForEmployee)
                {
                    // التحقق مما إذا كان هناك واصلات أخرى للموظف
                    if (existingReceipt.EmployeeId.HasValue)
                    {
                        bool hasOtherReceipts = await HasOtherEmployeeReceipts(existingReceipt.EmployeeId.Value, existingReceipt.Id);
                        if (hasOtherReceipts)
                        {
                            return (false, "لا يمكن تعديل الرصيد الافتتاحي للموظف لأنه توجد واصلات أخرى", 0);
                        }
                    }
                }

                // Validar reglas según tipo financiero
                var (isValid, errorMessage2) = await ValidateFinancialRules(receipt);
                if (!isValid)
                {
                    return (false, errorMessage2, 0);
                }

                // Revertir transacción anterior
                if (existingReceipt.IsExchange == true) // صرف (Gasto)
                {
                    var treasury = await _context.Treasuries.FindAsyncWithBusy(existingReceipt.TreasuryId);
                    var user = await _context.Users.FindAsyncWithBusy(existingReceipt.EmployeeId);

                    if (treasury != null)
                        treasury.Balance += existingReceipt.Value;

                    if (user != null && (existingReceipt.FinancialId == (byte)FinancialId.Employee ||
                                        existingReceipt.FinancialId == (byte)FinancialId.OpeningBalanceForEmployee))
                    {
                        user.Balance -= existingReceipt.Value;
                    }
                    else if (user != null && existingReceipt.FinancialId == (byte)FinancialId.SalaryPayment)
                    {
                        user.Balance -= existingReceipt.Value; // نقص رصيد الموظف (إلغاء صرف المرتب)
                    }
                    else if (user != null && existingReceipt.FinancialId == (byte)FinancialId.AutomaticSalary)
                    {
                        user.Balance += existingReceipt.Value; // زيادة رصيد الموظف (إلغاء إضافة المرتب التلقائي)
                    }

                    if (existingReceipt.ClientId.HasValue && (existingReceipt.FinancialId == (byte)FinancialId.Client ||
                                                            existingReceipt.FinancialId == (byte)FinancialId.OpeningBalanceForClient ||
                                                            existingReceipt.FinancialId == (byte)FinancialId.Purchase))
                    {
                        var client = await _context.Clients.FindAsyncWithBusy(existingReceipt.ClientId);
                        if (client != null)
                            client.Balance -= existingReceipt.Value;
                    }
                }
                else if (existingReceipt.IsExchange == false) // قبض (Ingreso)
                {
                    var treasury = await _context.Treasuries.FindAsyncWithBusy(existingReceipt.TreasuryId);
                    var user = await _context.Users.FindAsyncWithBusy(existingReceipt.EmployeeId);

                    if (treasury != null)
                        treasury.Balance -= existingReceipt.Value;

                    if (user != null && existingReceipt.FinancialId == (byte)FinancialId.Employee)
                    {
                        user.Balance += existingReceipt.Value;
                    }

                    if (existingReceipt.ClientId.HasValue && (existingReceipt.FinancialId == (byte)FinancialId.Client ||
                                                            existingReceipt.FinancialId == (byte)FinancialId.Sale))
                    {
                        var client = await _context.Clients.FindAsyncWithBusy(existingReceipt.ClientId);
                        if (client != null)
                            client.Balance += existingReceipt.Value;
                    }
                }
                // Para operaciones especiales
                else if (existingReceipt.FinancialId == (byte)FinancialId.Deposit) // إيداع
                {
                    var treasury = await _context.Treasuries.FindAsyncWithBusy(existingReceipt.TreasuryId);
                    if (treasury != null)
                        treasury.Balance -= existingReceipt.Value;
                }
                else if (existingReceipt.FinancialId == (byte)FinancialId.Withdrawal) // سحب
                {
                    var treasury = await _context.Treasuries.FindAsyncWithBusy(existingReceipt.TreasuryId);
                    if (treasury != null)
                        treasury.Balance += existingReceipt.Value;
                }
                else if (existingReceipt.FinancialId == (byte)FinancialId.Transfer) // تحويل بين طرق الدفع
                {
                    // إلغاء التحويل السابق
                    // إرجاع المبلغ لطريقة الدفع المصدر
                    var sourceTreasury = await _context.Treasuries.FindAsyncWithBusy(existingReceipt.SourceTreasuryId);
                    if (sourceTreasury != null)
                        sourceTreasury.Balance += existingReceipt.Value;

                    // خصم المبلغ من طريقة الدفع الهدف
                    var targetTreasury = await _context.Treasuries.FindAsyncWithBusy(existingReceipt.TargetTreasuryId);
                    if (targetTreasury != null)
                        targetTreasury.Balance -= existingReceipt.Value;
                }

                // Aplicar nueva transacción (misma lógica que en CreateReceipt)
                FinancialId financialId = (FinancialId)receipt.FinancialId.Value;

                if (receipt.IsExchange == true) // صرف (Gasto)
                {
                    var treasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TreasuryId);
                    var user = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId);

                    if (treasury != null)
                        treasury.Balance -= receipt.Value;

                    if (financialId == FinancialId.Employee)
                    {
                        if (user != null)
                        {
                            // التحقق من حدود الراتب قبل تحديث الرصيد (مع مراعاة القيمة السابقة في حالة التحديث)
                            var (isUpdateEmployeeValid, validationMessage4) = await ValidateEmployeeBalanceLimit(user.Id, receipt.Value, existingReceipt.Value);
                            if (!isUpdateEmployeeValid)
                            {
                                return (false, validationMessage4, 0);
                            }
                            user.Balance += receipt.Value;
                        }
                    }
                    else if (financialId == FinancialId.SalaryPayment)
                    {
                        if (user != null)
                        {
                            // التحقق من حدود الراتب قبل تحديث الرصيد (مع مراعاة القيمة السابقة في حالة التحديث)
                            var (isUpdateSalaryValid, validationMessage5) = await ValidateEmployeeBalanceLimit(user.Id, receipt.Value, existingReceipt.Value);
                            if (!isUpdateSalaryValid)
                            {
                                return (false, validationMessage5, 0);
                            }
                            user.Balance += receipt.Value; // زيادة رصيد الموظف (صرف المرتب)
                        }
                    }

                    if (financialId == FinancialId.Client)
                    {
                        if (receipt.ClientId.HasValue)
                        {
                            var client = await _context.Clients.FindAsyncWithBusy(receipt.ClientId);
                            if (client != null)
                                client.Balance += receipt.Value; // زيادة مديونية العميل (دفع للعميل)
                        }
                    }
                    else if (financialId == FinancialId.Purchase)
                    {
                        if (receipt.ClientId.HasValue)
                        {
                            var client = await _context.Clients.FindAsyncWithBusy(receipt.ClientId);
                            if (client != null)
                                client.Balance += receipt.Value; // زيادة رصيد العميل (المتجر يسدد دينه للعميل/المورد)
                        }
                    }
                }
                else if (receipt.IsExchange == false) // قبض (Ingreso)
                {
                    var treasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TreasuryId);
                    var user = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId);

                    if (treasury != null)
                        treasury.Balance += receipt.Value;

                    if (financialId == FinancialId.Employee)
                    {
                        if (user != null)
                            user.Balance -= receipt.Value;
                    }

                    if (financialId == FinancialId.Client)
                    {
                        if (receipt.ClientId.HasValue)
                        {
                            var client = await _context.Clients.FindAsyncWithBusy(receipt.ClientId);
                            if (client != null)
                                client.Balance -= receipt.Value; // نقص مديونية العميل (قبض من العميل)
                        }
                    }
                    else if (financialId == FinancialId.Sale)
                    {
                        if (receipt.ClientId.HasValue)
                        {
                            var client = await _context.Clients.FindAsyncWithBusy(receipt.ClientId);
                            if (client != null)
                                client.Balance -= receipt.Value; // نقص مديونية العميل (قبض من العميل لفاتورة مبيعات)
                        }
                    }
                }
                else if (financialId == FinancialId.Deposit) // إيداع
                {
                    var treasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TreasuryId);
                    if (treasury != null)
                        treasury.Balance += receipt.Value;
                }
                else if (financialId == FinancialId.Withdrawal) // سحب
                {
                    var treasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TreasuryId);
                    if (treasury != null)
                        treasury.Balance -= receipt.Value;
                }
                else if (financialId == FinancialId.OpeningBalanceForEmployee) // رصيد افتتاحي للموظف
                {
                    // تحديث رصيد الموظف
                    var user = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId);
                    if (user != null)
                        user.Balance = receipt.Value; // تعيين الرصيد الافتتاحي مباشرة (وليس إضافة)
                }
                else if (financialId == FinancialId.OpeningBalanceForClient) // رصيد افتتاحي للعميل
                {
                    // تحديث رصيد العميل
                    var client = await _context.Clients.FindAsyncWithBusy(receipt.ClientId);
                    if (client != null)
                        client.Balance = receipt.Value; // تعيين الرصيد الافتتاحي مباشرة (وليس إضافة)
                }
                else if (financialId == FinancialId.SalaryPayment) // صرف مرتب
                {
                    // تحديث رصيد الموظف
                    var user = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId);
                    if (user != null)
                    {
                        // التحقق من حدود الراتب قبل تحديث الرصيد (مع مراعاة القيمة السابقة في حالة التحديث)
                        var (isUpdateSalaryPaymentValid, validationMessage6) = await ValidateEmployeeBalanceLimit(user.Id, receipt.Value, existingReceipt.Value);
                        if (!isUpdateSalaryPaymentValid)
                        {
                            return (false, validationMessage6, 0);
                        }
                        user.Balance += receipt.Value; // زيادة رصيد الموظف (صرف المرتب)
                    }
                }
                else if (financialId == FinancialId.AutomaticSalary) // إضافة راتب تلقائي
                {
                    // تحديث رصيد الموظف
                    var user = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId);
                    if (user != null)
                        user.Balance -= receipt.Value; // نقص رصيد الموظف (إضافة المرتب التلقائي)
                }
                else if (financialId == FinancialId.Transfer) // تحويل بين طرق الدفع
                {
                    // خصم من طريقة الدفع المصدر
                    var sourceTreasury = await _context.Treasuries.FindAsyncWithBusy(receipt.SourceTreasuryId);
                    if (sourceTreasury != null)
                        sourceTreasury.Balance -= receipt.Value;

                    // إضافة لطريقة الدفع الهدف
                    var targetTreasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TargetTreasuryId);
                    if (targetTreasury != null)
                        targetTreasury.Balance += receipt.Value;

                    // تعيين خصائص الإيصال
                    receipt.IsExchange = null; // ليس قبض ولا صرف
                    receipt.TreasuryId = null; // لا نستخدم الحقل العادي للخزينة
                }

                // Establecer metadatos y actualizar
                receipt.ModifiedById = CurrentUser.Id;
                receipt.UpdatedAt = DateTime.Now;
                receipt.Date = receipt.Date == default(DateTime) ? DateTime.Now : receipt.Date;

                // Preserve the original receipt number
                receipt.ReceiptNo = existingReceipt.ReceiptNo;

                _context.Entry(existingReceipt).CurrentValues.SetValues(receipt);

                var (success, message) = await _context.SaveWithTransactionAndBusy("UpdateReceipt");

                if (success)
                {
                    return (true, message, receipt.Id);
                }
                else
                {
                    return (false, message, 0);
                }
            }
            catch (Exception ex)
            {
            // Reverse any changes made before the exception
            _context.Reverse();
            return (false, $"خطأ في تحديث الإيصال: {ex.Message}", 0);
        }
    }

    public async Task<(bool State, string Message)> DeleteReceipt(int id)
    {
        try
        {
                var receipt = await _context.Receipts.FindAsyncWithBusy(id);
                if (receipt != null)
                {
                    // التحقق مما إذا كان الواصل من نوع رصيد افتتاحي
                    if (receipt.FinancialId == (byte)FinancialId.OpeningBalanceForClient)
                    {
                        // التحقق مما إذا كان هناك واصلات أخرى للعميل
                        if (receipt.ClientId.HasValue)
                        {
                            bool hasOtherReceipts = await HasOtherClientReceipts(receipt.ClientId.Value, receipt.Id);
                            if (hasOtherReceipts)
                            {
                                return (false, "لا يمكن حذف الرصيد الافتتاحي للعميل لأنه توجد واصلات أخرى");
                            }
                        }
                    }
                    else if (receipt.FinancialId == (byte)FinancialId.OpeningBalanceForEmployee)
                    {
                        // التحقق مما إذا كان هناك واصلات أخرى للموظف
                        if (receipt.EmployeeId.HasValue)
                        {
                            bool hasOtherReceipts = await HasOtherEmployeeReceipts(receipt.EmployeeId.Value, receipt.Id);
                            if (hasOtherReceipts)
                            {
                                return (false, "لا يمكن حذف الرصيد الافتتاحي للموظف لأنه توجد واصلات أخرى");
                            }
                        }
                    }

                    // Revertir transacción según el tipo de operación financiera
                    if (receipt.FinancialId.HasValue)
                    {
                        FinancialId financialId = (FinancialId)receipt.FinancialId.Value;

                        if (receipt.IsExchange == true) // صرف (Gasto)
                        {
                            var treasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TreasuryId);
                            var user = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId);

                            if (treasury != null)
                                treasury.Balance += receipt.Value;

                            if (user != null)
                            {
                                if (financialId == FinancialId.Employee)
                                {
                                    user.Balance -= receipt.Value; // نقص رصيد الموظف (إلغاء الدفع للموظف)
                                }
                                else if (financialId == FinancialId.OpeningBalanceForEmployee)
                                {
                                    // لا نقوم بأي تغيير عند إلغاء الرصيد الافتتاحي، لأن هذا سيؤدي إلى تعيين الرصيد إلى صفر
                                    // يجب على المستخدم إنشاء رصيد افتتاحي جديد إذا أراد تغيير الرصيد
                                }
                                else if (financialId == FinancialId.SalaryPayment)
                                {
                                    // Cuando eliminamos un recibo de tipo SalaryPayment, debemos revertir la operación original
                                    // La operación original aumentaba el saldo del usuario, por lo que ahora debemos disminuirlo
                                    user.Balance -= receipt.Value; // نقص رصيد الموظف (إلغاء صرف المرتب)
                                }
                                else if (financialId == FinancialId.AutomaticSalary)
                                {
                                    user.Balance += receipt.Value; // زيادة رصيد الموظف (إلغاء إضافة المرتب التلقائي)
                                }
                            }

                            if (receipt.ClientId.HasValue)
                            {
                                var client = await _context.Clients.FindAsyncWithBusy(receipt.ClientId);
                                if (client != null)
                                {
                                    if (financialId == FinancialId.Client)
                                    {
                                        client.Balance -= receipt.Value; // نقص مديونية العميل (إلغاء الدفع للعميل)
                                    }
                                    else if (financialId == FinancialId.OpeningBalanceForClient)
                                    {
                                        // لا نقوم بأي تغيير عند إلغاء الرصيد الافتتاحي، لأن هذا سيؤدي إلى تعيين الرصيد إلى صفر
                                        // يجب على المستخدم إنشاء رصيد افتتاحي جديد إذا أراد تغيير الرصيد
                                    }
                                    else if (financialId == FinancialId.Purchase)
                                    {
                                        client.Balance -= receipt.Value; // نقص رصيد العميل (إلغاء دفع المتجر للعميل/المورد)
                                    }
                                }
                            }
                        }
                        else if (receipt.IsExchange == false) // قبض (Ingreso)
                        {
                            var treasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TreasuryId);
                            var user = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId);

                            if (treasury != null)
                                treasury.Balance -= receipt.Value;

                            if (user != null && financialId == FinancialId.Employee)
                            {
                                user.Balance += receipt.Value;
                            }

                            if (receipt.ClientId.HasValue)
                            {
                                var client = await _context.Clients.FindAsyncWithBusy(receipt.ClientId);
                                if (client != null)
                                {
                                    if (financialId == FinancialId.Client || financialId == FinancialId.Sale)
                                    {
                                        client.Balance += receipt.Value; // زيادة مديونية العميل (إلغاء القبض من العميل)
                                    }
                                }
                            }
                        }
                        else if (financialId == FinancialId.Deposit) // إيداع
                        {
                            var treasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TreasuryId);
                            if (treasury != null)
                                treasury.Balance -= receipt.Value;
                        }
                        else if (financialId == FinancialId.Withdrawal) // سحب
                        {
                            var treasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TreasuryId);
                            if (treasury != null)
                                treasury.Balance += receipt.Value;
                        }
                        else if (financialId == FinancialId.Transfer) // تحويل بين طرق الدفع
                        {
                            // إلغاء التحويل
                            // إرجاع المبلغ لطريقة الدفع المصدر
                            var sourceTreasury = await _context.Treasuries.FindAsyncWithBusy(receipt.SourceTreasuryId);
                            if (sourceTreasury != null)
                                sourceTreasury.Balance += receipt.Value;

                            // خصم المبلغ من طريقة الدفع الهدف
                            var targetTreasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TargetTreasuryId);
                            if (targetTreasury != null)
                                targetTreasury.Balance -= receipt.Value;
                        }
                    }

                    _context.Receipts.RemoveWithBusy(receipt);
                    return await _context.SaveWithTransactionAndBusy("DeleteReceipt");
                }
                return (false, "الإيصال غير موجود");
            }
            catch (Exception ex)
            {
            // Reverse any changes made before the exception
            _context.Reverse();
            return (false, $"خطأ في حذف الإيصال: {ex.Message}");
        }
    }

    public async Task<List<Receipt>> GetUserBorrows(int userId)
    {
        return await _context.Receipts
            .Where(r => r.EmployeeId == userId &&
                  ((r.FinancialId == (byte)FinancialId.Employee ||
                    r.FinancialId == (byte)FinancialId.SalaryPayment ||
                    r.FinancialId == (byte)FinancialId.AutomaticSalary) )) // دعم جميع أنواع المعاملات المتعلقة بالموظف
            .OrderByDescending(r => r.Date)
            .Include(r => r.Treasury)
            .Include(r => r.Employee)
            .Include(r => r.Financial)
            .ToListAsyncWithBusy("GetUserBorrows");
    }

    // التحقق من وجود واصلات أخرى للعميل
    private async Task<bool> HasOtherClientReceipts(int clientId, int? excludeReceiptId = null)
    {
        var query = _context.Receipts.Where(r => r.ClientId == clientId);

        if (excludeReceiptId.HasValue)
        {
            query = query.Where(r => r.Id != excludeReceiptId.Value);
        }

        return await query.AnyAsyncWithBusy("HasOtherClientReceipts");
    }

    // التحقق من وجود واصلات أخرى للموظف
    private async Task<bool> HasOtherEmployeeReceipts(int employeeId, int? excludeReceiptId = null)
    {
        var query = _context.Receipts.Where(r => r.EmployeeId == employeeId);

        if (excludeReceiptId.HasValue)
        {
            query = query.Where(r => r.Id != excludeReceiptId.Value);
        }

        return await query.AnyAsyncWithBusy("HasOtherEmployeeReceipts");
    }

    // التحقق مما إذا كان هناك رصيد افتتاحي موجود للعميل
    private async Task<bool> HasOpeningBalanceForClient(int clientId, int? excludeReceiptId = null)
    {
        var query = _context.Receipts
            .Where(r => r.ClientId == clientId && r.FinancialId == (byte)FinancialId.OpeningBalanceForClient);

        if (excludeReceiptId.HasValue)
        {
            query = query.Where(r => r.Id != excludeReceiptId.Value);
        }

        return await query.AnyAsyncWithBusy("HasOpeningBalanceForClient");
    }

    // التحقق مما إذا كان هناك رصيد افتتاحي موجود للموظف
    private async Task<bool> HasOpeningBalanceForEmployee(int employeeId, int? excludeReceiptId = null)
    {
        var query = _context.Receipts
            .Where(r => r.EmployeeId == employeeId && r.FinancialId == (byte)FinancialId.OpeningBalanceForEmployee);

        if (excludeReceiptId.HasValue)
        {
            query = query.Where(r => r.Id != excludeReceiptId.Value);
        }

        return await query.AnyAsyncWithBusy("HasOpeningBalanceForEmployee");
    }

    // التحقق من أن رصيد الموظف لا يتجاوز قيمة المرتب عند إصدار الواصلات
    private async Task<(bool isValid, string message)> ValidateEmployeeBalanceLimit(int employeeId, decimal balanceChange, decimal? previousBalanceChange = null)
    {
        var user = await _context.Users.FindAsyncWithBusy(employeeId);
        if (user == null)
        {
            return (false, "الموظف غير موجود");
        }

        // إذا كان المرتب 0 أو أقل، لا نطبق أي قيود
        if (user.Salary <= 0)
        {
            return (true, string.Empty);
        }

        // إذا كان مسموح للموظف بتجاوز حد الرصيد، لا نطبق أي قيود
        if (user.AllowBalanceOverride)
        {
            return (true, string.Empty);
        }

        // حساب الرصيد الجديد المتوقع
        decimal currentBalance = user.Balance;

        // في حالة التحديث، نحتاج لإلغاء التأثير السابق أولاً
        if (previousBalanceChange.HasValue)
        {
            currentBalance -= previousBalanceChange.Value;
        }

        decimal newBalance = currentBalance + balanceChange;

        // التحقق من أن الرصيد الجديد لا يتجاوز قيمة المرتب
        if (newBalance > user.Salary)
        {
            return (false, $"لا يمكن أن يتجاوز رصيد الموظف قيمة المرتب ({user.Salary:N3}). الرصيد الحالي: {currentBalance:N3}، المبلغ المطلوب: {balanceChange:N3}");
        }

        return (true, string.Empty);
    }

    // التحقق مما إذا كان الواصل هو أول واصل للعميل
    private async Task<bool> IsFirstReceiptForClient(int clientId, int? excludeReceiptId = null)
    {
        var query = _context.Receipts.Where(r => r.ClientId == clientId);

        if (excludeReceiptId.HasValue)
        {
            query = query.Where(r => r.Id != excludeReceiptId.Value);
        }

        return !await query.AnyAsyncWithBusy("IsFirstReceiptForClient");
    }

    // التحقق مما إذا كان الواصل هو أول واصل للموظف
    private async Task<bool> IsFirstReceiptForEmployee(int employeeId, int? excludeReceiptId = null)
    {
        var query = _context.Receipts.Where(r => r.EmployeeId == employeeId);

        if (excludeReceiptId.HasValue)
        {
            query = query.Where(r => r.Id != excludeReceiptId.Value);
        }

        return !await query.AnyAsyncWithBusy("IsFirstReceiptForEmployee");
    }

    /// <summary>
    /// إنشاء إيصالات متعددة في معاملة واحدة
    /// </summary>
    public async Task<(bool success, string message, List<int> receiptIds)> CreateMultipleReceipts(List<Receipt> receipts)
    {
        try
        {
            var createdReceiptIds = new List<int>();

            // التحقق من القواعد المالية لجميع الإيصالات أولاً (محسنة)
            foreach (var receipt in receipts)
            {
                var (isValid, errorMessage) = await ValidateFinancialRules(receipt);
                if (!isValid)
                {
                    return (false, errorMessage, new List<int>());
                }
            }

            // معالجة جميع الإيصالات بشكل مجمع (محسنة)
            var currentTime = DateTime.Now;
            foreach (var receipt in receipts)
            {
                // تحديد المخزن بناءً على نوع العملية
                await SetReceiptWarehouse(receipt);

                // توليد رقم الإيصال
                await GenerateReceiptNumber(receipt);

                // تطبيق التغييرات المالية
                await ApplyFinancialChanges(receipt);

                // تعيين البيانات الأساسية
                receipt.CreatedById = CurrentUser.Id;
                receipt.ModifiedById = CurrentUser.Id;
                receipt.CreatedAt = currentTime;
                receipt.UpdatedAt = currentTime;
                receipt.Date = receipt.Date == default(DateTime) ? currentTime : receipt.Date;
            }

            // إدراج جميع الإيصالات دفعة واحدة (محسنة)
            await _context.Receipts.AddRangeAsyncWithBusy(receipts);

            var (success, message) = await _context.SaveWithTransactionAndBusy("CreateMultipleReceipts");

            if (success)
            {
                createdReceiptIds = receipts.Select(r => r.Id).ToList();
                return (true, message, createdReceiptIds);
            }
            else
            {
                return (false, message, new List<int>());
            }
        }
        catch (Exception ex)
        {
            _context.Reverse();
            return (false, $"خطأ في إنشاء الإيصالات: {ex.Message}", new List<int>());
        }
    }

    public async Task<List<Receipt>> SearchReceipts(
        int? receiptNo = null,
        byte? financialId = null,
        DateTime? date = null,
        int? clientId = null,
        int? employeeId = null,
        byte? treasuryId = null,
        bool? isExchange = null,
        string statement = null,
        int? purchaseId = null,
        int? saleId = null,
        int? expenseId = null)
    {
        // بناء الاستعلام
        var query = _context.Receipts
            .Include(r => r.Treasury)
            .Include(r => r.Client)
            .Include(r => r.Purchase)
            .Include(r => r.Sale)
            .Include(r => r.Employee)
            .Include(r => r.Financial)
            .Include(r => r.Expense)
            // استبعاد الإيصالات من نوع إضافة راتب تلقائي
            .Where(r => r.FinancialId != (byte)FinancialId.AutomaticSalary)
            .AsQueryable();

            // تطبيق تصفية البيانات حسب المعايير المحددة فقط
            if (receiptNo.HasValue)
                query = query.Where(r => r.ReceiptNo == receiptNo.Value);

            if (financialId.HasValue)
                query = query.Where(r => r.FinancialId == financialId.Value);

            if (date.HasValue)
                query = query.Where(r => r.Date.Date == date.Value.Date);

            if (clientId.HasValue)
                query = query.Where(r => r.ClientId == clientId.Value);

            if (employeeId.HasValue)
                query = query.Where(r => r.EmployeeId == employeeId.Value);

            if (treasuryId.HasValue)
                query = query.Where(r => r.TreasuryId == treasuryId.Value);

            if (isExchange.HasValue)
                query = query.Where(r => r.IsExchange == isExchange.Value);

            if (!string.IsNullOrEmpty(statement))
                query = query.Where(r => r.Statement.Contains(statement));

            if (purchaseId.HasValue)
                query = query.Where(r => r.PurchaseId == purchaseId.Value);

            if (saleId.HasValue)
                query = query.Where(r => r.SaleId == saleId.Value);

            if (expenseId.HasValue)
                query = query.Where(r => r.ExpenseId == expenseId.Value);

        // ترتيب النتائج حسب الرقم التسلسلي تنازلياً
        return await query.OrderByDescending(r => r.Id).ToListAsyncWithBusy("SearchReceipts");
    }

    // Implement IDisposable pattern
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // Dispose managed resources
                _context?.Dispose();
            }

            // Free unmanaged resources
            _disposed = true;
        }
    }

    /// <summary>
    /// تحديد المخزن للإيصال بناءً على نوع العملية
    /// </summary>
    private async Task SetReceiptWarehouse(Receipt receipt)
    {
        FinancialId financialType = (FinancialId)receipt.FinancialId.Value;

        if (financialType == FinancialId.OpeningBalanceForClient)
        {
            if (receipt.ClientId.HasValue)
            {
                var client = await _context.Clients
                    .Include(c => c.Warehouse)
                    .FirstOrDefaultAsyncWithBusy(c => c.Id == receipt.ClientId.Value, "GetClientForOpeningBalance");

                if (client != null)
                {
                    receipt.WarehouseId = client.WarehouseId;
                }
            }
        }
        else if (financialType == FinancialId.OpeningBalanceForEmployee)
        {
            if (receipt.EmployeeId.HasValue)
            {
                var employee = await _context.Users
                    .Include(u => u.Warehouse)
                    .FirstOrDefaultAsyncWithBusy(u => u.Id == receipt.EmployeeId.Value, "GetEmployeeForOpeningBalance");

                if (employee != null)
                {
                    receipt.WarehouseId = employee.WarehouseId;
                }
            }
        }
        else if (financialType == FinancialId.Transfer)
        {
            if (receipt.SourceTreasuryId.HasValue)
            {
                var sourceTreasury = await _context.Treasuries
                    .Include(t => t.Warehouse)
                    .FirstOrDefaultAsyncWithBusy(t => t.Id == receipt.SourceTreasuryId.Value, "GetSourceTreasuryWithWarehouse");

                if (sourceTreasury != null)
                {
                    receipt.WarehouseId = sourceTreasury.WarehouseId;
                }
            }
        }
        else if (receipt.TreasuryId.HasValue)
        {
            var treasury = await _context.Treasuries
                .Include(t => t.Warehouse)
                .FirstOrDefaultAsyncWithBusy(t => t.Id == receipt.TreasuryId.Value, "GetTreasuryWithWarehouse");

            if (treasury != null)
            {
                receipt.WarehouseId = treasury.WarehouseId;
            }
        }
    }

    /// <summary>
    /// توليد رقم الإيصال
    /// </summary>
    private async Task GenerateReceiptNumber(Receipt receipt)
    {
        Receipt receiptNo;
        string warehouseCode = "";

        if (receipt.WarehouseId.HasValue)
        {
            var warehouse = await _context.Warehouses
                .FirstOrDefaultAsyncWithBusy(w => w.Id == receipt.WarehouseId.Value, "GetWarehouseForReceipt");
            warehouseCode = warehouse?.Code ?? "";

            receiptNo = await _context.Receipts
                .Where(r => r.WarehouseId == receipt.WarehouseId.Value)
                .OrderByDescending(r => r.ReceiptNo)
                .FirstOrDefaultAsyncWithBusy("GetLastReceiptNumberByWarehouse");
        }
        else
        {
            receiptNo = await _context.Receipts
                .Where(r => r.WarehouseId == null)
                .OrderByDescending(r => r.ReceiptNo)
                .FirstOrDefaultAsyncWithBusy("GetLastReceiptNumberGeneral");
            warehouseCode = "GEN";
        }

        int nextReceiptNumber = (receiptNo?.ReceiptNo ?? 0) + 1;
        receipt.ReceiptNo = nextReceiptNumber;
        receipt.ReceiptNumber = $"{warehouseCode}-{nextReceiptNumber}";
    }

    /// <summary>
    /// تطبيق التغييرات المالية
    /// </summary>
    private async Task ApplyFinancialChanges(Receipt receipt)
    {
        FinancialId financialId = (FinancialId)receipt.FinancialId.Value;

        if (receipt.IsExchange == true) // صرف
        {
            var treasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TreasuryId);
            var user = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId);

            if (treasury != null)
                treasury.Balance -= receipt.Value;

            if (financialId == FinancialId.Employee)
            {
                if (user != null)
                {
                    var (isEmployeeValid, validationMessage) = await ValidateEmployeeBalanceLimit(user.Id, receipt.Value);
                    if (!isEmployeeValid)
                    {
                        throw new InvalidOperationException(validationMessage);
                    }
                    user.Balance += receipt.Value;
                }
            }
            else if (financialId == FinancialId.SalaryPayment)
            {
                if (user != null)
                {
                    var (isSalaryValid, validationMessage2) = await ValidateEmployeeBalanceLimit(user.Id, receipt.Value);
                    if (!isSalaryValid)
                    {
                        throw new InvalidOperationException(validationMessage2);
                    }
                    user.Balance += receipt.Value;
                }
            }

            if (financialId == FinancialId.Client)
            {
                if (receipt.ClientId.HasValue)
                {
                    var client = await _context.Clients.FindAsyncWithBusy(receipt.ClientId);
                    if (client != null)
                    {
                        var clientService = new ClientService();
                        var (isAllowed, clientMessage) = await clientService.CheckBalanceChangeAsync(receipt.ClientId.Value, receipt.Value);
                        if (!isAllowed)
                        {
                            throw new InvalidOperationException(clientMessage);
                        }
                        client.Balance += receipt.Value;
                    }
                }
            }
            else if (financialId == FinancialId.Purchase)
            {
                if (receipt.ClientId.HasValue)
                {
                    var client = await _context.Clients.FindAsyncWithBusy(receipt.ClientId);
                    if (client != null)
                    {
                        var clientService = new ClientService();
                        var (isAllowed, clientMessage2) = await clientService.CheckBalanceChangeAsync(receipt.ClientId.Value, -receipt.Value);
                        if (!isAllowed)
                        {
                            throw new InvalidOperationException(clientMessage2);
                        }
                        client.Balance -= receipt.Value;
                    }
                }
            }
        }
        else if (receipt.IsExchange == false) // قبض
        {
            var treasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TreasuryId);
            var user = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId);

            if (treasury != null)
                treasury.Balance += receipt.Value;

            if (financialId == FinancialId.Employee)
            {
                if (user != null)
                    user.Balance -= receipt.Value;
            }

            if (financialId == FinancialId.Client)
            {
                if (receipt.ClientId.HasValue)
                {
                    var client = await _context.Clients.FindAsyncWithBusy(receipt.ClientId);
                    if (client != null)
                    {
                        var clientService = new ClientService();
                        var (isAllowed, clientMessage3) = await clientService.CheckBalanceChangeAsync(receipt.ClientId.Value, -receipt.Value);
                        if (!isAllowed)
                        {
                            throw new InvalidOperationException(clientMessage3);
                        }
                        client.Balance -= receipt.Value;
                    }
                }
            }
            else if (financialId == FinancialId.Sale)
            {
                if (receipt.ClientId.HasValue)
                {
                    var client = await _context.Clients.FindAsyncWithBusy(receipt.ClientId);
                    if (client != null)
                    {
                        var clientService = new ClientService();
                        var (isAllowed, clientMessage4) = await clientService.CheckBalanceChangeAsync(receipt.ClientId.Value, -receipt.Value);
                        if (!isAllowed)
                        {
                            throw new InvalidOperationException(clientMessage4);
                        }
                        client.Balance -= receipt.Value;
                    }
                }
            }
        }
        // معالجة العمليات الخاصة الأخرى...
        else if (financialId == FinancialId.Deposit)
        {
            var treasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TreasuryId);
            if (treasury != null)
                treasury.Balance += receipt.Value;
        }
        else if (financialId == FinancialId.Withdrawal)
        {
            var treasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TreasuryId);
            if (treasury != null)
                treasury.Balance -= receipt.Value;
        }
        else if (financialId == FinancialId.OpeningBalanceForEmployee)
        {
            var user = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId);
            if (user != null)
                user.Balance = receipt.Value;
        }
        else if (financialId == FinancialId.Transfer)
        {
            var sourceTreasury = await _context.Treasuries.FindAsyncWithBusy(receipt.SourceTreasuryId);
            if (sourceTreasury != null)
                sourceTreasury.Balance -= receipt.Value;

            var targetTreasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TargetTreasuryId);
            if (targetTreasury != null)
                targetTreasury.Balance += receipt.Value;

            receipt.IsExchange = null;
            receipt.TreasuryId = null;
        }
        else if (financialId == FinancialId.OpeningBalanceForClient)
        {
            var client = await _context.Clients.FindAsyncWithBusy(receipt.ClientId);
            if (client != null)
                client.Balance = receipt.Value;
        }
        else if (financialId == FinancialId.SalaryPayment)
        {
            var user = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId);
            if (user != null)
            {
                var (isSalaryPaymentValid, validationMessage3) = await ValidateEmployeeBalanceLimit(user.Id, receipt.Value);
                if (!isSalaryPaymentValid)
                {
                    throw new InvalidOperationException(validationMessage3);
                }
                user.Balance += receipt.Value;
            }
        }
        else if (financialId == FinancialId.AutomaticSalary)
        {
            var user = await _context.Users.FindAsyncWithBusy(receipt.EmployeeId);
            if (user != null)
                user.Balance -= receipt.Value;
        }
    }

    // Destructor
    ~ReceiptService()
    {
        Dispose(false);
    }
}
