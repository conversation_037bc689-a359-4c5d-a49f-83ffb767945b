using Microsoft.EntityFrameworkCore;
using System;

namespace VisionPoint.UI.Models;

[Index(nameof(SaleId))]
[Index(nameof(ProductQuantityId))]
[Index(nameof(LensQuantityRightId))]
[Index(nameof(LensQuantityLeftId))]
[Index(nameof(ServiceId))]
[Index(nameof(PairId))]
public class SaleItem : BaseEntity
{
    public int SaleId { get; set; }
    public Sale Sale { get; set; }
    public int? ProductQuantityId { get; set; }
    public ProductQuantity? ProductQuantity { get; set; }
    public int? LensQuantityRightId { get; set; }
    public LensQuantity? LensQuantityRight { get; set; }
    public int? LensQuantityLeftId { get; set; }
    public LensQuantity? LensQuantityLeft { get; set; }
    public int? ServiceId { get; set; }
    public Service? Service { get; set; }
    /// <summary>
    /// معرف فريد للربط بين العدسة اليمنى واليسرى المضافة معًا
    /// يكون null إذا تمت إضافة العدسة بشكل منفرد
    /// </summary>
    public Guid? PairId { get; set; }
    [Precision(18, 3)]
    public decimal CostPrice { get; set; }
    [Precision(18, 3)]
    public decimal OriginalPrice { get; set; }
    [Precision(18, 3)]
    public decimal Discount { get; set; }
    [Precision(18, 3)]
    public decimal SellPrice { get; set; }
    public int Quantity { get; set; }
    /// <summary>
    /// الكمية المسترجعة
    /// </summary>
    public int ReturnedQuantity { get; set; } = 0;
    [Precision(18, 3)] public decimal? Axis { get; set; }
}
