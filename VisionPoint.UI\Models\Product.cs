﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace VisionPoint.UI.Models;
[Index(nameof(Name), IsUnique = true)]
public class Product : BaseEntity
{
    [StringLength(100, ErrorMessage = "اسم المنتج يجب ألا يتجاوز 100 حرف")]
    public string Name { get; set; }
    [Precision(18, 3)] public decimal CostPrice { get; set; } = decimal.Zero;
    [Precision(18, 3)] public decimal SellPrice { get; set; } = decimal.Zero;
    public bool Color { get; set; }
    public bool Exp { get; set; }
    /// <summary>
    /// الحد الأدنى للكمية
    /// </summary>
    public int MinimumQuantity { get; set; } = 0;
    public ICollection<ProductColor>? ProductColors { get; set; } = new List<ProductColor>();
}
