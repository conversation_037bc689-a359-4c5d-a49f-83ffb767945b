using ClosedXML.Excel;
using Microsoft.Win32;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using VisionPoint.UI.PL;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.Treasury
{
    /// <summary>
    /// Interaction logic for TreasuryStatementWindow.xaml
    /// </summary>
    public partial class TreasuryStatementWindow : Window
    {
        private readonly TreasuryService _treasuryService;
        private readonly Models.Treasury _treasury;
        private ObservableCollection<TreasuryStatementVM> _statementItems;
        private TreasuryStatementSummaryVM _summaryData;
        private DateTime? _fromDate;
        private DateTime? _toDate;

        public TreasuryStatementWindow(Models.Treasury treasury)
        {
            InitializeComponent();
            _treasury = treasury;
            _treasuryService = new TreasuryService();
            _statementItems = new ObservableCollection<TreasuryStatementVM>();
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // عرض معلومات الخزينة
            txtTreasuryName.Text = _treasury.Name;
            txtTreasuryBalance.Text = $"الرصيد الحالي: {_treasury.Balance:N3}";

            // تعيين حالة عناصر التاريخ
            chkAllPeriods.IsChecked = true;
            UpdateDateControlsState();

            // تحميل كشف الحساب
            await LoadTreasuryStatementAsync();
        }

        /// <summary>
        /// تحديث حالة عناصر التاريخ بناءً على خيار "كل الفترات"
        /// </summary>
        private void UpdateDateControlsState()
        {
            // التحقق من أن العناصر تم تهيئتها
            if (dpFromDate == null || dpToDate == null || chkAllPeriods == null)
                return;

            bool isAllPeriods = chkAllPeriods.IsChecked == true;

            // تعطيل/تفعيل عناصر التاريخ
            dpFromDate.IsEnabled = !isAllPeriods;
            dpToDate.IsEnabled = !isAllPeriods;
        }

        private void chkAllPeriods_CheckedChanged(object sender, RoutedEventArgs e)
        {
            // تحديث حالة عناصر التاريخ
            UpdateDateControlsState();

            if (chkAllPeriods?.IsChecked == true)
            {
                // إذا تم اختيار "كل الفترات"، قم بمسح التواريخ
                _fromDate = null;
                _toDate = null;
            }
            else
            {
                // إذا تم إلغاء اختيار "كل الفترات"، استخدم التواريخ المحددة
                _fromDate = dpFromDate?.SelectedDate;
                _toDate = dpToDate?.SelectedDate;
            }
        }

        private void DatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            if (chkAllPeriods?.IsChecked == false)
            {
                // تحديث قيم التاريخ فقط إذا لم يتم اختيار "كل الفترات"
                _fromDate = dpFromDate?.SelectedDate;
                _toDate = dpToDate?.SelectedDate;
            }
        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (list.View is GridView gridView && gridView.Columns.Count >= 8)
            {
                double scrollbarWidth = SystemParameters.VerticalScrollBarWidth;
                double availableWidth = list.ActualWidth  - scrollbarWidth;

                if (availableWidth > 0)
                {
                    // Assign proportional weights to each column
                    // Columns: Date, OperationType, OperationNumber, ClientName, Description, Incoming, Outgoing, Balance
                    double[] weights = new double[] { 1.2, 1.5, 1.2, 1.5, 2.0, 1.3, 1.3, 1.5 };
                    double totalWeight = weights.Sum();
                    double unitWidth = availableWidth / totalWeight;

                    for (int i = 0; i < weights.Length; i++)
                    {
                        gridView.Columns[i].Width = unitWidth * weights[i];
                    }
                }
            }
        }


        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnApplyFilter.IsEnabled = false;
            btnPrint.IsEnabled = false;
            btnExportExcel.IsEnabled = false;
            btnclose.IsEnabled = false;
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnApplyFilter.IsEnabled = true;
            btnPrint.IsEnabled = true;
            btnExportExcel.IsEnabled = true;
            btnclose.IsEnabled = true;
        }

        private void btnclose_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                Close();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnExportExcel_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                // إنشاء مربع حوار حفظ الملف
                SaveFileDialog saveFileDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx",
                    Title = "حفظ كشف حساب الخزينة",
                    FileName = $"كشف_حساب_الخزينة_{_treasury.Name}_{DateTime.Now:yyyy-MM-dd}.xlsx"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // تصدير البيانات إلى Excel
                    ExportToExcel(saveFileDialog.FileName);
                    DialogBox.Show("نجح التصدير", "تم تصدير البيانات بنجاح!");
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تصدير البيانات: {ex.Message}", "خطأ في التصدير", true);
            }
        }

        private async void btnApplyFilter_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تحديث التواريخ من عناصر التحكم
                if (chkAllPeriods?.IsChecked != true)
                {
                    _fromDate = dpFromDate?.SelectedDate;
                    _toDate = dpToDate?.SelectedDate;
                }
                else
                {
                    _fromDate = null;
                    _toDate = null;
                }

                await LoadTreasuryStatementAsync();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تطبيق الفلتر: {ex.Message}", "خطأ", true);
            }
        }

        private async Task LoadTreasuryStatementAsync()
        {
            try
            {
                // تحميل كشف الحساب مع تطبيق الفلاتر
                var (transactions, summary) = await _treasuryService.GetTreasuryStatementAsync(_treasury.Id, _fromDate, _toDate);

                // تحديث القائمة
                _statementItems.Clear();
                foreach (var item in transactions)
                {
                    _statementItems.Add(item);
                }

                list.ItemsSource = _statementItems;

                // تحديث عنوان النافذة
                Title = $"كشف حساب {_treasury.Name} - {_statementItems.Count} عملية";

                // حفظ بيانات الملخص للاستخدام في التصدير
                _summaryData = summary;

                // تحديث ملخص كشف الحساب
                txtTreasuryBalance.Text = $"الرصيد الحالي: {_treasury.Balance:N3}";
                txtTotalIncoming.Text = $"مجموع القبض: {summary.TotalIncoming:N3}";
                txtTotalOutgoing.Text = $"مجموع الصرف: {summary.TotalOutgoing:N3}";
                txtFinalBalance.Text = $"الرصيد النهائي: {summary.FinalBalance:N3}";
                txtBalanceStatus.Text = $"حالة الرصيد: {summary.BalanceStatus}";
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل كشف الحساب: {ex.Message}", "خطأ في التحميل", true);
            }
        }

        private void btnPrint_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                // إنشاء نسخة قابلة للطباعة من كشف الحساب
                PrintDialog printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // إنشاء مستند للطباعة
                    FlowDocument document = new FlowDocument();
                    document.PagePadding = new Thickness(20); // تقليل المسافات أكثر
                    document.FontFamily = new FontFamily("Arial");
                    document.FontSize = 9; // تصغير الخط أكثر
                    document.PageWidth = 794; // عرض A4 مع هوامش أقل
                    document.PageHeight = 1123; // ارتفاع A4 مع هوامش أقل
                    document.ColumnWidth = double.PositiveInfinity;
                    document.IsOptimalParagraphEnabled = false;
                    document.IsHyphenationEnabled = false;
                    document.TextAlignment = TextAlignment.Right;
                    document.FlowDirection = FlowDirection.RightToLeft;

                    // إضافة عنوان المستند
                    Paragraph title = new Paragraph(new Run($"كشف حساب {_treasury.Name}"));
                    title.FontSize = 20;
                    title.FontWeight = FontWeights.Bold;
                    title.TextAlignment = TextAlignment.Center;
                    document.Blocks.Add(title);

                    // إضافة معلومات الخزينة
                    Paragraph treasuryInfo = new Paragraph(new Run($"الرصيد الحالي: {_treasury.Balance:N3}"));
                    treasuryInfo.TextAlignment = TextAlignment.Center;
                    treasuryInfo.Margin = new Thickness(0, 0, 0, 20);
                    document.Blocks.Add(treasuryInfo);

                    // إضافة معلومات الفترة
                    string periodInfo = chkAllPeriods.IsChecked == true
                        ? "الفترة: كل الفترات"
                        : $"الفترة: من {_fromDate?.ToString("yyyy-MM-dd") ?? ""} إلى {_toDate?.ToString("yyyy-MM-dd") ?? ""}";
                    Paragraph period = new Paragraph(new Run(periodInfo));
                    period.TextAlignment = TextAlignment.Center;
                    period.Margin = new Thickness(0, 0, 0, 20);
                    document.Blocks.Add(period);

                    // إنشاء جدول العمليات
                    Table table = new Table();
                    table.CellSpacing = 0;
                    table.BorderBrush = Brushes.Black;
                    table.BorderThickness = new Thickness(1);
                    table.FontSize = 10; // تصغير حجم الخط للطباعة
                    table.FontFamily = new FontFamily("Arial");

                    // تعريف الأعمدة بعرض محسن للطباعة
                    table.Columns.Add(new TableColumn { Width = new GridLength(40) }); // الرقم المتسلسل
                    table.Columns.Add(new TableColumn { Width = new GridLength(75) }); // التاريخ
                    table.Columns.Add(new TableColumn { Width = new GridLength(95) }); // نوع العملية
                    table.Columns.Add(new TableColumn { Width = new GridLength(70) }); // رقم العملية
                    table.Columns.Add(new TableColumn { Width = new GridLength(100) }); // الطرف المتعامل
                    table.Columns.Add(new TableColumn { Width = new GridLength(150) }); // البيان
                    table.Columns.Add(new TableColumn { Width = new GridLength(75) }); // قبض
                    table.Columns.Add(new TableColumn { Width = new GridLength(75) }); // صرف
                    table.Columns.Add(new TableColumn { Width = new GridLength(75) }); // الرصيد

                    // إضافة رأس الجدول
                    TableRowGroup headerGroup = new TableRowGroup();
                    TableRow headerRow = new TableRow();
                    headerRow.Background = Brushes.LightGray;

                    string[] headers = { "الرقم المتسلسل", "التاريخ", "نوع العملية", "رقم العملية", "الطرف المتعامل", "البيان", "قبض", "صرف", "الرصيد" };
                    foreach (string header in headers)
                    {
                        TableCell cell = new TableCell(new Paragraph(new Run(header)));
                        cell.BorderBrush = Brushes.Black;
                        cell.BorderThickness = new Thickness(1);
                        cell.Padding = new Thickness(3); // تقليل المسافة الداخلية
                        cell.TextAlignment = TextAlignment.Center;
                        cell.FontWeight = FontWeights.Bold;
                        cell.FontSize = 9; // حجم خط أصغر للرؤوس
                        headerRow.Cells.Add(cell);
                    }

                    headerGroup.Rows.Add(headerRow);
                    table.RowGroups.Add(headerGroup);

                    // إضافة بيانات العمليات
                    TableRowGroup dataGroup = new TableRowGroup();
                    int serialNumber = 1; // متغير للرقم المتسلسل
                    foreach (var item in _statementItems)
                    {
                        // تخطي الصفوف الخاصة إذا كان الرصيد صفر
                        if (item.IsSpecialRow && item.Balance == 0)
                            continue;

                        TableRow dataRow = new TableRow();

                        // تلوين الصفوف الخاصة
                        if (item.IsSpecialRow)
                        {
                            dataRow.Background = Brushes.LightYellow;
                        }

                        string[] cellData = {
                            serialNumber.ToString(), // استخدام الرقم المتسلسل بدلاً من ID
                            item.Date?.ToString("yyyy-MM-dd") ?? "لا يوجد",
                            item.OperationType,
                            item.OperationNumber?.ToString() ?? "لا يوجد",
                            item.ClientName ?? "",
                            item.Description,
                            item.Incoming.ToString("N3"),
                            item.Outgoing.ToString("N3"),
                            item.Balance.ToString("N3")
                        };

                        foreach (string data in cellData)
                        {
                            var run = new Run(data);

                            // تنسيق خاص للصفوف الخاصة
                            if (item.IsSpecialRow)
                            {
                                run.FontWeight = FontWeights.Bold;
                                run.FontStyle = FontStyles.Italic;
                                run.Foreground = Brushes.DarkBlue;
                            }

                            TableCell cell = new TableCell(new Paragraph(run));
                            cell.BorderBrush = Brushes.Black;
                            cell.BorderThickness = new Thickness(1);
                            cell.Padding = new Thickness(2); // تقليل المسافة الداخلية
                            cell.TextAlignment = TextAlignment.Center;
                            cell.FontSize = 8; // حجم خط أصغر للبيانات
                            dataRow.Cells.Add(cell);
                        }

                        dataGroup.Rows.Add(dataRow);
                        serialNumber++; // زيادة الرقم المتسلسل
                    }

                    table.RowGroups.Add(dataGroup);
                    document.Blocks.Add(table);

                    // إضافة ملخص كشف الحساب
                    Table summaryTable = new Table();
                    summaryTable.Margin = new Thickness(0, 20, 0, 0);
                    summaryTable.CellSpacing = 0;
                    summaryTable.BorderBrush = Brushes.Black;
                    summaryTable.BorderThickness = new Thickness(1);
                    summaryTable.FontSize = 10;

                    summaryTable.Columns.Add(new TableColumn { Width = new GridLength(180) });
                    summaryTable.Columns.Add(new TableColumn { Width = new GridLength(180) });
                    summaryTable.Columns.Add(new TableColumn { Width = new GridLength(180) });

                    TableRowGroup summaryDataGroup = new TableRowGroup();
                    TableRow summaryDataRow = new TableRow();

                    string[] summaryData = {
                        txtTotalIncoming.Text,
                        txtTotalOutgoing.Text,
                        txtFinalBalance.Text
                    };

                    foreach (string data in summaryData)
                    {
                        TableCell cell = new TableCell(new Paragraph(new Run(data)));
                        cell.BorderBrush = Brushes.Black;
                        cell.BorderThickness = new Thickness(1);
                        cell.Padding = new Thickness(5); // تقليل المسافة الداخلية
                        cell.FontSize = 9; // تصغير حجم الخط
                        cell.TextAlignment = TextAlignment.Center;
                        cell.Background = Brushes.LightBlue;
                        summaryDataRow.Cells.Add(cell);
                    }

                    summaryDataGroup.Rows.Add(summaryDataRow);
                    summaryTable.RowGroups.Add(summaryDataGroup);

                    document.Blocks.Add(summaryTable);

                    // إضافة توضيح حالة الرصيد
                    string balanceStatus = txtBalanceStatus.Text;
                    Paragraph balanceStatusParagraph = new Paragraph(new Run(balanceStatus));
                    balanceStatusParagraph.TextAlignment = TextAlignment.Center;
                    balanceStatusParagraph.Margin = new Thickness(0, 10, 0, 0);
                    document.Blocks.Add(balanceStatusParagraph);

                    // طباعة المستند
                    printDialog.PrintDocument(((IDocumentPaginatorSource)document).DocumentPaginator, $"كشف حساب {_treasury.Name}");
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء الطباعة: {ex.Message}", "خطأ في الطباعة", true);
            }
        }

        /// <summary>
        /// تصدير كشف الحساب إلى ملف Excel
        /// </summary>
        private void ExportToExcel(string filePath)
        {
            using (var workbook = new XLWorkbook())
            {
                var worksheet = workbook.Worksheets.Add("كشف حساب الخزينة");

                // إعداد اتجاه الصفحة من اليمين لليسار
                worksheet.RightToLeft = true;

                // إعداد رأس التقرير
                int currentRow = 1;

                // عنوان التقرير
                worksheet.Cell(currentRow, 1).Value = "كشف حساب الخزينة";
                worksheet.Range(currentRow, 1, currentRow, 9).Merge();
                worksheet.Cell(currentRow, 1).Style.Font.Bold = true;
                worksheet.Cell(currentRow, 1).Style.Font.FontSize = 16;
                worksheet.Cell(currentRow, 1).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                currentRow += 2;

                // معلومات الخزينة
                worksheet.Cell(currentRow, 1).Value = "اسم الخزينة:";
                worksheet.Cell(currentRow, 2).Value = _treasury.Name;
                worksheet.Cell(currentRow, 1).Style.Font.Bold = true;
                currentRow++;

                worksheet.Cell(currentRow, 1).Value = "الرصيد الحالي:";
                worksheet.Cell(currentRow, 2).Value = _treasury.Balance.ToString("N3");
                worksheet.Cell(currentRow, 1).Style.Font.Bold = true;
                currentRow++;

                // فترة التقرير
                if (_fromDate.HasValue && _toDate.HasValue)
                {
                    worksheet.Cell(currentRow, 1).Value = "فترة التقرير:";
                    worksheet.Cell(currentRow, 2).Value = $"من {_fromDate.Value:yyyy-MM-dd} إلى {_toDate.Value:yyyy-MM-dd}";
                    worksheet.Cell(currentRow, 1).Style.Font.Bold = true;
                }
                else
                {
                    worksheet.Cell(currentRow, 1).Value = "فترة التقرير:";
                    worksheet.Cell(currentRow, 2).Value = "كل الفترات";
                    worksheet.Cell(currentRow, 1).Style.Font.Bold = true;
                }
                currentRow += 2;

                // رؤوس الأعمدة
                string[] headers = { "الرقم المتسلسل", "التاريخ", "نوع العملية", "رقم العملية", "الطرف المتعامل", "البيان", "قبض", "صرف", "الرصيد" };
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cell(currentRow, i + 1).Value = headers[i];
                    worksheet.Cell(currentRow, i + 1).Style.Font.Bold = true;
                    worksheet.Cell(currentRow, i + 1).Style.Fill.BackgroundColor = XLColor.LightGray;
                    worksheet.Cell(currentRow, i + 1).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                }
                currentRow++;

                // بيانات العمليات
                if (_statementItems != null)
                {
                    int excelSerialNumber = 1; // رقم متسلسل للإكسل
                    foreach (var item in _statementItems)
                    {
                        // تخطي الصفوف الخاصة إذا كان الرصيد صفر
                        if (item.IsSpecialRow && item.Balance == 0)
                            continue;

                        worksheet.Cell(currentRow, 1).Value = excelSerialNumber; // الرقم المتسلسل
                        worksheet.Cell(currentRow, 2).Value = item.Date?.ToString("yyyy-MM-dd") ?? "لا يوجد";
                        worksheet.Cell(currentRow, 3).Value = item.OperationType ?? "";
                        worksheet.Cell(currentRow, 4).Value = item.OperationNumber?.ToString() ?? "لا يوجد";
                        worksheet.Cell(currentRow, 5).Value = item.ClientName ?? "";
                        worksheet.Cell(currentRow, 6).Value = item.Description ?? "";
                        worksheet.Cell(currentRow, 7).Value = item.Incoming;
                        worksheet.Cell(currentRow, 8).Value = item.Outgoing;
                        worksheet.Cell(currentRow, 9).Value = item.Balance;

                        // تنسيق الأرقام
                        worksheet.Cell(currentRow, 7).Style.NumberFormat.Format = "#,##0.000";
                        worksheet.Cell(currentRow, 8).Style.NumberFormat.Format = "#,##0.000";
                        worksheet.Cell(currentRow, 9).Style.NumberFormat.Format = "#,##0.000";

                        // تنسيق خاص للصفوف الخاصة (الرصيد السابق والرصيد اللاحق)
                        if (item.IsSpecialRow)
                        {
                            for (int col = 1; col <= 9; col++)
                            {
                                worksheet.Cell(currentRow, col).Style.Font.Bold = true;
                                worksheet.Cell(currentRow, col).Style.Font.Italic = true;
                                worksheet.Cell(currentRow, col).Style.Fill.BackgroundColor = XLColor.LightBlue;
                                worksheet.Cell(currentRow, col).Style.Font.FontColor = XLColor.DarkBlue;
                            }
                        }

                        // تنسيق الحدود
                        for (int col = 1; col <= 9; col++)
                        {
                            worksheet.Cell(currentRow, col).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                        }

                        currentRow++;
                        excelSerialNumber++; // زيادة الرقم المتسلسل
                    }
                }

                // ملخص كشف الحساب
                currentRow += 2;
                if (_summaryData != null)
                {
                    worksheet.Cell(currentRow, 1).Value = "ملخص كشف الحساب";
                    worksheet.Range(currentRow, 1, currentRow, 9).Merge();
                    worksheet.Cell(currentRow, 1).Style.Font.Bold = true;
                    worksheet.Cell(currentRow, 1).Style.Font.FontSize = 14;
                    worksheet.Cell(currentRow, 1).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                    currentRow += 2;

                    worksheet.Cell(currentRow, 1).Value = "مجموع القبض:";
                    worksheet.Cell(currentRow, 2).Value = _summaryData.TotalIncoming;
                    worksheet.Cell(currentRow, 2).Style.NumberFormat.Format = "#,##0.000";
                    worksheet.Cell(currentRow, 1).Style.Font.Bold = true;
                    currentRow++;

                    worksheet.Cell(currentRow, 1).Value = "مجموع الصرف:";
                    worksheet.Cell(currentRow, 2).Value = _summaryData.TotalOutgoing;
                    worksheet.Cell(currentRow, 2).Style.NumberFormat.Format = "#,##0.000";
                    worksheet.Cell(currentRow, 1).Style.Font.Bold = true;
                    currentRow++;

                    worksheet.Cell(currentRow, 1).Value = "الرصيد النهائي:";
                    worksheet.Cell(currentRow, 2).Value = _summaryData.FinalBalance;
                    worksheet.Cell(currentRow, 2).Style.NumberFormat.Format = "#,##0.000";
                    worksheet.Cell(currentRow, 1).Style.Font.Bold = true;
                    currentRow++;

                    worksheet.Cell(currentRow, 1).Value = "حالة الرصيد:";
                    worksheet.Cell(currentRow, 2).Value = _summaryData.BalanceStatus;
                    worksheet.Cell(currentRow, 1).Style.Font.Bold = true;
                }

                // تعديل عرض الأعمدة
                worksheet.ColumnsUsed().AdjustToContents();

                // حفظ الملف
                workbook.SaveAs(filePath);
            }
        }
    }
}
