﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.ProductsContent
{
    /// <summary>
    /// Interaction logic for ColorsPage.xaml
    /// </summary>
    public partial class ColorsPage : Window
    {
        private readonly ColorService _colorService;
        private Models.Color _selectedColor = new();

        public ColorsPage(ColorService colorService)
        {
            InitializeComponent();
            _colorService = colorService;
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadData();
        }

        private async Task LoadData()
        {
            try
            {
                // The BusyService is automatically set by the ColorService
                var colors = await _colorService.GetAllColorsAsync();
                list.ItemsSource = colors;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ: {ex.Message}", "خطأ", true);
            }
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnSave.IsEnabled = false;
            btnNew.IsEnabled = false;
            btnDelete.IsEnabled = false;
            btnclose.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnSave.IsEnabled = true;
            btnNew.IsEnabled = true;
            btnDelete.IsEnabled = true;
            btnclose.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private async void btnSave_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (string.IsNullOrEmpty(txtName.Text.Trim()))
                {
                    ErrorBox.Show("الرجاء إدخال اسم اللون", "خطأ", true);
                    txtName.Focus();
                    return;
                }
                if (string.IsNullOrEmpty(txtHex.Text.Trim()))
                {
                    ErrorBox.Show("الرجاء إدخال كود اللون", "خطأ", true);
                    txtHex.Focus();
                    return;
                }
                _selectedColor.Name = txtName.Text.Trim();
                _selectedColor.HexCode = txtHex.Text.Trim();

                var result = _selectedColor.Id == 0
                    ? await _colorService.AddColorAsync(_selectedColor)
                    : await _colorService.UpdateColorAsync(_selectedColor);

                if (result.State)
                {
                    DialogBox.Show(result.Message, "نجاح");
                    btnNew_MouseLeftButtonDown(sender, e);
                    await LoadData();
                }
                else
                {
                    ErrorBox.Show(result.Message, "خطأ", true);
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnNew_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                _selectedColor = new Models.Color();
                txtName.Clear();
                txtHex.Clear();
                list.SelectedItem = null;
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnDelete_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (_selectedColor.Id == 0)
                {
                    ErrorBox.Show("الرجاء اختيار لون للحذف", "تنبيه", false);
                    return;
                }

                var result = DeleteBox.Show("هل انت متاكد من حذف لون", _selectedColor.Name);

                if (result == true)
                {
                    var deleteResult = await _colorService.DeleteColorAsync(_selectedColor.Id);

                    if (deleteResult.State)
                    {
                        DialogBox.Show("تم الحذف", "نجاح");
                        btnNew_MouseLeftButtonDown(sender, e);
                        await LoadData();
                    }
                    else
                    {
                        ErrorBox.Show(deleteResult.Message, "خطأ", true);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }
        private void btnclose_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                Close();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            try
            {
                ListView listView = sender as ListView;
                if (listView != null)
                {
                    GridView gView = listView.View as GridView;
                    if (gView != null)
                    {
                        var workingWidth = listView.ActualWidth - SystemParameters.VerticalScrollBarWidth;
                        var col1 = 0.1;  // numbering column
                        var col2 = 0.45; // name column
                        var col3 = 0.45; // hex code column
                        gView.Columns[0].Width = workingWidth * col1;
                        gView.Columns[1].Width = workingWidth * col2;
                    }
                }
            }
            catch
            {
                return;
            }
        }

        private void list_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (list.SelectedItem is Models.Color selectedColor)
            {
                _selectedColor = selectedColor;
                txtName.Text = selectedColor.Name;
                txtHex.Text = selectedColor.HexCode;
            }
            else
            {
                ErrorBox.Show("الرجاء اختيار لون", "تنبيه", false);
            }
        }
    }

}
