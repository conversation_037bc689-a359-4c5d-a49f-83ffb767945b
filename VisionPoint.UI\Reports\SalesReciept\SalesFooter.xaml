﻿<Page
    x:Class="VisionPoint.UI.Reports.SalesReciept.SalesFooter"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.Reports.SalesReciept"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="SalesFooter"
    Width="800"
    Height="250"
    Background="White"
    FlowDirection="RightToLeft"
    mc:Ignorable="d">

    <Grid Margin="32,0">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>


        <DockPanel
            Grid.RowSpan="2"
            Grid.ColumnSpan="8"
            Margin="16,0"
            LastChildFill="False">
            <TextBlock
                VerticalAlignment="Center"
                FontSize="18"
                Text="الاجمالي  :  " />
            <TextBlock
                x:Name="txtTotal"
                VerticalAlignment="Center"
                FontSize="18"
                Text="24/4/2025" />
            <TextBlock
                Margin="16,0,0,0"
                VerticalAlignment="Center"
                FontSize="18"
                Text="المدفوع  :  " />
            <TextBlock
                x:Name="txtPaid"
                VerticalAlignment="Center"
                FontSize="18"
                Text="06:00" />

            <TextBlock
                Margin="16,0,0,0"
                VerticalAlignment="Center"
                FontSize="18"
                Text="الباقي  :  " />
            <TextBlock
                x:Name="txtBaqi"
                VerticalAlignment="Center"
                FontSize="18"
                Text="06:00" />
        </DockPanel>

        <DockPanel
            Grid.RowSpan="2"
            Grid.Column="2"
            Grid.ColumnSpan="2"
            Margin="16,0"
            LastChildFill="False" />


        <DockPanel
            Grid.RowSpan="2"
            Grid.Column="4"
            Grid.ColumnSpan="2"
            Margin="16,0"
            LastChildFill="False" />

        <DockPanel
            Grid.Row="4"
            Grid.Column="0"
            Grid.ColumnSpan="3"
            Margin="16,0,0,0"
            HorizontalAlignment="Stretch"
            LastChildFill="False">
            <TextBlock
                VerticalAlignment="Center"
                FontSize="18"
                Text="رقم الهاتف  :  " />
            <TextBlock
                x:Name="txtPhone"
                VerticalAlignment="Center"
                d:Text="090000000"
                FontSize="18" />
        </DockPanel>


        <DockPanel
            Grid.Row="2"
            Grid.RowSpan="2"
            Grid.Column="0"
            Grid.ColumnSpan="6"
            Margin="16,0,0,0"
            HorizontalAlignment="Stretch"
            LastChildFill="True">
            <TextBlock
                VerticalAlignment="Stretch"
                FontSize="18"
                Text=" ملاحظة  :  " />
            <TextBlock
                x:Name="txtNote"
                VerticalAlignment="Stretch"
                d:Text="090000000"
                FontSize="18" />
        </DockPanel>

    </Grid>
</Page>
