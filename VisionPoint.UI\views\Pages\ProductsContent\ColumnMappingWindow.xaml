﻿<Window
    x:Class="VisionPoint.UI.views.Pages.ProductsContent.ColumnMappingWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:VisionPoint.UI"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.ProductsContent"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="تخطيط الأعمدة"
    AllowsTransparency="True"
    Background="Transparent"
    FlowDirection="RightToLeft"
    WindowState="Maximized"
    WindowStartupLocation="CenterScreen"
    WindowStyle="None"
    mc:Ignorable="d">
    <Window.Resources>
        <local:BoolToFontWeightConverter x:Key="BoolToFontWeightConverter" />
    </Window.Resources>
    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">

        <Grid Width="1920" Height="1080">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="5*" />
                <ColumnDefinition Width="6*" />
                <ColumnDefinition Width="5*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="1*" />
                <RowDefinition Height="6*" />
                <RowDefinition Height="1*" />
            </Grid.RowDefinitions>
            <Border
                Grid.Row="1"
                Grid.Column="1"
                Padding="24"
                Background="{StaticResource backgroundColor}"
                BorderBrush="{StaticResource PrimaryColor}"
                BorderThickness="1"
                CornerRadius="24">


                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="40" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="60" />
                    </Grid.RowDefinitions>


                    <TextBlock
                        Grid.Row="0"
                        FontSize="28"
                        FontWeight="Bold"
                        Text="يرجى تحديد الأعمدة لكل حقل في النظام" />

                    <ScrollViewer
                        Grid.Row="1"
                        Margin="0,16"
                        Padding="8,0"
                        VerticalScrollBarVisibility="Auto">
                        <ItemsControl x:Name="FieldMappingList">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0,8">
                                        <ComboBox
                                            Grid.Column="1"
                                            MinHeight="60"
                                            DisplayMemberPath="."
                                            FontSize="21"
                                            IsEditable="True"
                                            IsEnabled="{Binding IsEnabled}"
                                            ItemsSource="{Binding AvailableColumns}"
                                            SelectedItem="{Binding SelectedColumn, Mode=TwoWay}"
                                            Style="{StaticResource ComboBoxFloatingLabelAlwaysUpStyle}"
                                            Tag="{Binding FieldDisplayName}" />
                                    </Grid>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>

                    <Grid Grid.Row="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <Border
                            x:Name="btnConfirm"
                            Height="44"
                            Margin="8,0"
                            Background="{StaticResource PrimaryColor}"
                            CornerRadius="16"
                            MouseLeftButtonDown="btnConfirm_Click">
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="16"
                                Foreground="White"
                                Text="تأكيد" />
                        </Border>

                        <Border
                            x:Name="btnCancel"
                            Grid.Column="1"
                            Height="44"
                            Margin="8,0"
                            Background="Transparent"
                            BorderBrush="{StaticResource errorColor}"
                            BorderThickness="1"
                            CornerRadius="16"
                            MouseLeftButtonDown="btnCancel_Click">
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="16"
                                Foreground="{StaticResource errorColor}"
                                Text="إلغاء" />
                        </Border>
                    </Grid>
                </Grid>

            </Border>
        </Grid>
    </Viewbox>
</Window>