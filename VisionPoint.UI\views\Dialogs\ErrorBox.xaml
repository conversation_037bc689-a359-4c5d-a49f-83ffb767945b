﻿<Window
    x:Class="VisionPoint.UI.views.Dialogs.ErrorBox"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:VisionPoint.UI"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Dialogs"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="ErrorBox"
    AllowsTransparency="True"
    Background="Transparent"
    Closing="Window_Closing"
    FontFamily="Times New Roman"
    ResizeMode="NoResize"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">
        <Grid Width="1920" Height="1080">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Grid
                Grid.Row="1"
                Grid.Column="1"
                Width="auto"
                Height="auto"
                MinWidth="350">
                <Grid.RowDefinitions>
                    <RowDefinition Height="50" />
                    <RowDefinition Height="*" />

                </Grid.RowDefinitions>
                <Border
                    Grid.RowSpan="2"
                    Width="100"
                    Height="100"
                    VerticalAlignment="Top"
                    Panel.ZIndex="10"
                    Background="{StaticResource backgroundColor}"
                    BorderBrush="LightGray"
                    BorderThickness="4"
                    CornerRadius="50">
                    <Image
                        x:Name="iconBox"
                        Height="50"
                        Source="{StaticResource errorIcon}" />
                </Border>
                <Border
                    Grid.Row="1"
                    MinHeight="200"
                    Background="{StaticResource backgroundColor}"
                    BorderThickness="1.5"
                    CornerRadius="20">
                    <Border.BorderBrush>
                        <SolidColorBrush Opacity="0.4" Color="Black" />
                    </Border.BorderBrush>
                    <DockPanel LastChildFill="False">

                        <TextBlock
                            x:Name="txtHeader"
                            Margin="8,64,8,24"
                            HorizontalAlignment="Center"
                            DockPanel.Dock="Top"
                            FontSize="20"
                            FontWeight="Bold"
                            Foreground="#333333" />

                        <ScrollViewer
                            MaxHeight="350"
                            DockPanel.Dock="Top"
                            HorizontalScrollBarVisibility="Disabled"
                            VerticalScrollBarVisibility="Auto">
                            <TextBlock
                                x:Name="txtMessage"
                                Margin="15,0,15,15"
                                Padding="5"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Top"
                                FontSize="18"
                                FontWeight="SemiBold"
                                Foreground="#64748B"
                                TextAlignment="Center"
                                TextWrapping="Wrap" />
                        </ScrollViewer>

                        <DockPanel
                            Margin="12,15,12,20"
                            DockPanel.Dock="Bottom"
                            LastChildFill="True">
                            <Border
                                x:Name="btnYes"
                                Height="35"
                                MinWidth="120"
                                Background="#5F9CE3"
                                CornerRadius="18"
                                Cursor="Hand"
                                DockPanel.Dock="Right"
                                MouseLeftButtonDown="btnYes_MouseLeftButtonUp">
                                <TextBlock
                                    Margin="15,0"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FontSize="17"
                                    FontWeight="Bold"
                                    Foreground="#fff">
                                    موافق
                                </TextBlock>
                            </Border>
                        </DockPanel>
                    </DockPanel>
                </Border>
            </Grid>
        </Grid>
    </Viewbox>
</Window>
