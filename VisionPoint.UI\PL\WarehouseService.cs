using Microsoft.EntityFrameworkCore;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Models;

namespace VisionPoint.UI.PL;

public class WarehouseService : IDisposable
{
    private readonly AppDbContext _context = ServiceLocator.GetService<AppDbContext>();
    private bool _disposed = false;

    public WarehouseService()
    {
    }

    public async Task<List<Warehouse>> GetAllWarehousesAsync()
    {
        return await _context.Warehouses.ToListAsyncWithBusy("GetAllWarehouses");
    }

    public async Task<Warehouse?> GetWarehouseByIdAsync(int id)
    {
        return await _context.Warehouses.FindAsyncWithBusy(id);
    }

    public async Task<Warehouse?> GetWarehouseByCodeAsync(string code)
    {
        return await _context.Warehouses
            .FirstOrDefaultAsyncWithBusy(w => w.Code == code, "GetWarehouseByCode");
    }

    public async Task<(bool State, string Message, Warehouse Warehouse)> AddWarehouseAsync(Warehouse warehouse)
    {
        warehouse.CreatedAt = DateTime.Now;
        warehouse.UpdatedAt = DateTime.Now;
        await _context.Warehouses.AddAsyncWithBusy(warehouse);
        var result = await _context.SaveWithTransactionAndBusy("AddWarehouse");
        return (result.State, result.Message, warehouse);
    }

    public async Task<(bool State, string Message)> UpdateWarehouseAsync(Warehouse warehouse)
    {
        warehouse.UpdatedAt = DateTime.Now;
        _context.Warehouses.UpdateWithBusy(warehouse);
        return await _context.SaveWithTransactionAndBusy("UpdateWarehouse");
    }

    public async Task<(bool State, string Message)> DeleteWarehouseAsync(int id)
    {
        var warehouse = await _context.Warehouses.FindAsyncWithBusy(id);
        if (warehouse == null)
            return (false, "المخزن غير موجود");

        // التحقق من وجود منتجات مرتبطة بالمخزن
        // يمكن إضافة هذا التحقق لاحقاً عند ربط المنتجات بالمخازن

        _context.Warehouses.RemoveWithBusy(warehouse);
        return await _context.SaveWithTransactionAndBusy("DeleteWarehouse");
    }

    public async Task<List<Warehouse>> SearchWarehousesAsync(string searchTerm)
    {
        return await _context.Warehouses
            .Where(w => w.Name.Contains(searchTerm) || w.Code.Contains(searchTerm))
            .ToListAsyncWithBusy("SearchWarehouses");
    }

    public async Task<bool> IsCodeExistsAsync(string code, int? excludeId = null)
    {
        var query = _context.Warehouses.Where(w => w.Code == code);

        if (excludeId.HasValue)
            query = query.Where(w => w.Id != excludeId.Value);

        return await query.AnyAsyncWithBusy("CheckWarehouseCodeExists");
    }

    public async Task<bool> IsNameExistsAsync(string name, int? excludeId = null)
    {
        var query = _context.Warehouses.Where(w => w.Name == name);

        if (excludeId.HasValue)
            query = query.Where(w => w.Id != excludeId.Value);

        return await query.AnyAsyncWithBusy("CheckWarehouseNameExists");
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                _context?.Dispose();
            }
            _disposed = true;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}
