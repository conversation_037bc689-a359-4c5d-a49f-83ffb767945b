﻿<Page
    x:Class="VisionPoint.UI.views.Pages.Settings.BackupSettingsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Settings"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils"
    Title="BackupSettingsPage"
    d:Background="White"
    d:Height="1080"
    d:Width="1570"
    FlowDirection="RightToLeft"
    mc:Ignorable="d">

    <Grid Margin="32">
        <Grid.ColumnDefinitions>
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
        </Grid.RowDefinitions>

        <Border
            x:Name="btnBackup"
            Grid.Row="3"
            Grid.Column="0"
            MaxHeight="44"
            Margin="16,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnBackup_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White"
                Text="نسخة احتياطية" />
        </Border>

        <Border
            x:Name="btnRestore"
            Grid.Row="3"
            Grid.Column="1"
            MaxHeight="44"
            Margin="16,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnRestore_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}"
                Text="استعادة النسخة" />
        </Border>

        <TextBlock
            Grid.ColumnSpan="1"
            Margin="16,0,0,0"
            VerticalAlignment="Center"
            FontSize="18"
            FontWeight="Bold"
            Text="إعدادات النسخ الاحتياطية" />

        <Grid
            Grid.Row="1"
            Grid.Column="0"
            Margin="8,0">
            <DockPanel VerticalAlignment="Center" LastChildFill="False">
                <TextBlock
                    Margin="0,8"
                    HorizontalAlignment="Center"
                    DockPanel.Dock="Top"
                    Text="تفعيل النسخ التلقائي" />
                <CheckBox
                    x:Name="chkAutoBackupEnabled"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Checked="chkAutoBackupEnabled_Checked"
                    DockPanel.Dock="Top"
                    Style="{StaticResource CircleCheckboxFL}"
                    Unchecked="chkAutoBackupEnabled_Unchecked" />
            </DockPanel>
        </Grid>

        <Grid
            Grid.Row="2"
            Grid.Column="0"
            Grid.ColumnSpan="2"
            Margin="8,0">
            <TextBox
                x:Name="txtAutoBackupMaxCount"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                utils:NumericInputControl.IsNumericOnly="True"
                BorderThickness="1"
                FontSize="21"
                Tag="عدد النسخ الاحتياطية"
                TextChanged="txtAutoBackupMaxCount_TextChanged" />
        </Grid>

        <Grid
            Grid.Row="2"
            Grid.Column="2"
            Grid.ColumnSpan="2"
            Margin="8,0">
            <TextBox
                x:Name="txtAutoBackupPath"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                IsReadOnly="True"
                Tag="مسار النسخ الاحتياطي التلقائي" />
        </Grid>

        <Border
            x:Name="btnBrowseAutoBackupPath"
            Grid.Row="2"
            Grid.Column="4"
            MaxHeight="44"
            Margin="16,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnBrowseAutoBackupPath_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White"
                Text="تعديل المسار" />
        </Border>
    </Grid>
</Page>
