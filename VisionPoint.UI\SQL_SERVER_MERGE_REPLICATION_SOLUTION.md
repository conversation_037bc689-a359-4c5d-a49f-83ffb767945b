# حل مشكلة SQL Server Merge Replication مع EF Core

## المشكلة
بعد إضافة SQL Server Merge Replication إلى قاعدة البيانات، أصبح التطبيق غير قادر على تنفيذ أي عمليات إضافة أو تحديث أو حذف باستخدام EF Core.

### أسباب المشكلة
1. **أعمدة rowguid مفقودة**: SQL Server Merge Replication يضيف أعمدة `rowguid` إلى جميع الجداول، لكن EF Core لم يكن يعرف عنها
2. **مشكلة OUTPUT clause**: Merge Replication ينشئ triggers على الجداول، و EF Core يستخدم OUTPUT clause الذي لا يتوافق مع هذه triggers
3. **مشكلة rowguidcol**: أعمدة rowguid لها خاصية ROWGUIDCOL ولا يمكن تحديثها مباشرة
4. **عدم تطابق Schema**: عدم تطابق بين schema قاعدة البيانات الفعلية وschema النماذج في EF Core

### رسائل الخطأ الشائعة
- `Could not save changes because the target table has database triggers`
- `The target table 'AspNetRoles' of the DML statement cannot have any enabled triggers if the statement contains an OUTPUT clause without INTO clause`
- `Updating columns with the rowguidcol property is not allowed`
- `The insert failed. It conflicted with an identity range check constraint in database 'VisionPoint', replicated table 'dbo.Prescriptions', column 'Id'`

## الحل المطبق

### 1. تحديث BaseEntity
تم إضافة خاصية `RowGuid` إلى `BaseEntity.cs`:
```csharp
/// <summary>
/// Row GUID for SQL Server Merge Replication - managed by database
/// </summary>
[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
public Guid RowGuid { get; set; }
```

### 2. تحديث النماذج التي لا ترث من BaseEntity
تم إضافة خاصية `RowGuid` إلى جميع النماذج التي لا ترث من BaseEntity:
- `Color.cs`
- `Prescription.cs`
- `User.cs`
- `Role.cs`
- `UserRole.cs` - نموذج علاقة المستخدمين والأدوار (AspNetUserRoles)
- `Treasury.cs`
- `Warehouse.cs`
- `LensPrescription.cs`
- `LensPrescriptionColor.cs`
- `LensQuantity.cs`
- `ProductColor.cs`
- `ProductQuantity.cs`
- `DiscountLens.cs`
- `DiscountProduct.cs`
- `DiscountService.cs`

### 3. تحديث AppDbContext
تم إضافة دالة `ConfigureRowGuidColumns` في `AppDbContext.cs` لتكوين أعمدة rowguid:
```csharp
/// <summary>
/// تكوين أعمدة rowguid لـ SQL Server Merge Replication
/// </summary>
private void ConfigureRowGuidColumns(ModelBuilder modelBuilder)
{
    // تكوين أعمدة rowguid لجميع الكيانات
    foreach (var entityType in modelBuilder.Model.GetEntityTypes())
    {
        var rowGuidProperty = entityType.FindProperty("RowGuid");
        if (rowGuidProperty != null)
        {
            modelBuilder.Entity(entityType.ClrType)
                .Property("RowGuid")
                .HasColumnName("rowguid")
                .HasDefaultValueSql("newsequentialid()")
                .ValueGeneratedOnAdd()
                .IsRequired();
        }
    }
}
```

### 4. حل مشكلة rowguidcol
تم إضافة تكوين خاص لمنع تحديث أعمدة rowguid:
```csharp
/// <summary>
/// تجاهل تحديث أعمدة rowguid لتجنب خطأ "Updating columns with the rowguidcol property is not allowed"
/// </summary>
private void IgnoreRowGuidUpdates()
{
    foreach (var entry in ChangeTracker.Entries())
    {
        if (entry.State == EntityState.Modified)
        {
            // البحث عن خاصية RowGuid وتجاهل تحديثها
            var rowGuidProperty = entry.Properties.FirstOrDefault(p => p.Metadata.Name == "RowGuid");
            if (rowGuidProperty != null)
            {
                rowGuidProperty.IsModified = false;
            }
        }
    }
}
```

وتم تكوين خاصية RowGuid لتجاهل التحديث بعد الحفظ:
```csharp
.Metadata.SetAfterSaveBehavior(PropertySaveBehavior.Ignore); // تجاهل التحديث بعد الحفظ
```

### 5. حل مشكلة OUTPUT clause مع Triggers
تم إضافة تكوين خاص في `AppDbContext.cs` لحل مشكلة OUTPUT clause:
```csharp
/// <summary>
/// Override SaveChanges لحل مشكلة SQL Server Merge Replication triggers
/// </summary>
public override int SaveChanges()
{
    try
    {
        // تجاهل تحديث أعمدة rowguid قبل الحفظ
        IgnoreRowGuidUpdates();
        return base.SaveChanges();
    }
    catch (DbUpdateException ex) when (ex.InnerException?.Message?.Contains("OUTPUT clause") == true ||
                                      ex.InnerException?.Message?.Contains("rowguidcol") == true)
    {
        // إعادة المحاولة بدون OUTPUT clause
        return SaveChangesWithoutOutputClause();
    }
}
```

### 6. تكوين Triggers في ModelBuilder
تم إضافة دالة `ConfigureMergeReplicationCompatibility` لتكوين triggers:
```csharp
private void ConfigureMergeReplicationCompatibility(ModelBuilder modelBuilder)
{
    // تكوين جداول Identity لتجنب مشكلة OUTPUT clause مع triggers
    foreach (var entityType in modelBuilder.Model.GetEntityTypes())
    {
        modelBuilder.Entity(entityType.ClrType)
            .ToTable(tb => tb.HasTrigger($"MSmerge_ins_{entityType.GetTableName()}"))
            .ToTable(tb => tb.HasTrigger($"MSmerge_upd_{entityType.GetTableName()}"))
            .ToTable(tb => tb.HasTrigger($"MSmerge_del_{entityType.GetTableName()}"));
    }
}
```

### 7. تكوين خاص لجدول AspNetUserRoles
تم إنشاء نموذج مخصص `UserRole` وتكوينه:
```csharp
public class UserRole : IdentityUserRole<int>
{
    /// <summary>
    /// Row GUID for SQL Server Merge Replication - managed by database
    /// </summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid RowGuid { get; set; }
}
```

وتم تحديث `AppDbContext` لاستخدام النموذج المخصص:
```csharp
public class AppDbContext : IdentityDbContext<User, Role, int, IdentityUserClaim<int>, UserRole, IdentityUserLogin<int>, IdentityRoleClaim<int>, IdentityUserToken<int>>
```

وإضافة تكوين خاص لجدول AspNetUserRoles:
```csharp
modelBuilder.Entity<UserRole>()
    .ToTable("AspNetUserRoles", tb =>
    {
        tb.HasTrigger("MSmerge_ins_AspNetUserRoles");
        tb.HasTrigger("MSmerge_upd_AspNetUserRoles");
        tb.HasTrigger("MSmerge_del_AspNetUserRoles");
    });

// تكوين عمود RowGuid لجدول AspNetUserRoles
modelBuilder.Entity<UserRole>()
    .Property(ur => ur.RowGuid)
    .HasColumnName("rowguid")
    .HasDefaultValueSql("newsequentialid()")
    .ValueGeneratedOnAdd()
    .IsRequired();
```

### 8. إنشاء Migration
تم إنشاء Migration جديدة `SyncRowGuidWithExistingDatabase` لمزامنة النموذج مع قاعدة البيانات الموجودة:
- Migration فارغة لأن أعمدة rowguid موجودة بالفعل في قاعدة البيانات
- الهدف هو فقط تحديث Model Snapshot ليتطابق مع قاعدة البيانات

تم أيضاً إنشاء Migration إضافية `AddRowGuidToAspNetUserRoles` لجدول AspNetUserRoles:
- Migration فارغة لأن عمود rowguid موجود بالفعل في الجدول
- الهدف هو مزامنة النموذج المخصص UserRole مع قاعدة البيانات

## النتيجة
- ✅ تم حل مشكلة عدم التطابق بين schema قاعدة البيانات والنماذج
- ✅ تم حل مشكلة OUTPUT clause مع triggers
- ✅ تم حل مشكلة rowguidcol - لا يمكن تحديث أعمدة rowguid
- ✅ تم إضافة دعم كامل لجدول AspNetUserRoles مع RowGuid
- ✅ التطبيق الآن قادر على تنفيذ جميع العمليات (إضافة، تحديث، حذف) بنجاح
- ✅ أعمدة rowguid يتم إدارتها بواسطة قاعدة البيانات تلقائياً ولا يتم تحديثها بواسطة EF Core
- ✅ SQL Server Merge Replication يعمل بشكل طبيعي
- ✅ لا توجد رسائل خطأ متعلقة بـ triggers أو OUTPUT clause أو rowguidcol
- ✅ جميع جداول ASP.NET Identity متوافقة مع Merge Replication

## ملاحظات مهمة
1. **لا تحذف أعمدة rowguid**: هذه الأعمدة مطلوبة لـ SQL Server Merge Replication
2. **لا تحذف triggers**: triggers المنشأة بواسطة Merge Replication مطلوبة للمزامنة
3. **القيم تُولد تلقائياً**: قاعدة البيانات تولد قيم rowguid تلقائياً باستخدام `newsequentialid()`
4. **معالجة تلقائية للأخطاء**: النظام يتعامل تلقائياً مع مشاكل OUTPUT clause
5. **لا تحتاج لتعديل الكود**: EF Core يتعامل مع هذه الأعمدة والمشاكل تلقائياً
6. **Migration آمنة**: Migration لا تحاول إضافة أو حذف أعمدة موجودة
7. **AspNetUserRoles متوافق**: جدول AspNetUserRoles يدعم Merge Replication بالكامل
8. **نموذج مخصص**: تم إنشاء نموذج UserRole مخصص لدعم RowGuid في AspNetUserRoles

## اختبار الحل
تم اختبار الحل بنجاح:
- بناء التطبيق بدون أخطاء
- تطبيق Migration بنجاح
- جميع العمليات تعمل بشكل طبيعي

## التوافق
هذا الحل متوافق مع:
- SQL Server Merge Replication
- Entity Framework Core
- جميع عمليات CRUD
- النسخ الاحتياطي والاستعادة
- المزامنة بين قواعد البيانات المختلفة

## الحل النهائي

تم حل جميع المشاكل المتعلقة بـ SQL Server Merge Replication من خلال:

1. **إضافة حقول rowguid لجميع الجداول**
2. **إنشاء triggers للحفاظ على قيم rowguid**
3. **تحديث DbContext لتجاهل حقول rowguid**
4. **إضافة identity range management**
5. **تحديث SaveChanges لتجنب مشاكل OUTPUT clause**
6. **إضافة identity range constraints للجداول المطلوبة**

### ملاحظات مهمة للحل النهائي
- تم إضافة identity range constraints لجدول Prescriptions لحل مشكلة تضارب الهوية
- جميع الجداول تحتوي على حقول rowguid مع triggers للحفاظ على القيم
- DbContext محدث لتجاهل حقول rowguid أثناء العمليات
- SaveChanges محدث لتجنب مشاكل OUTPUT clause مع triggers
- النظام الآن يدعم SQL Server Merge Replication بشكل كامل ويمكن استخدامه في بيئة موزعة مع عدة مواقع

## الخلاصة

تم تطبيق جميع التغييرات المطلوبة لدعم SQL Server Merge Replication في نظام VisionPoint. النظام الآن جاهز للعمل في بيئة موزعة مع إمكانية المزامنة بين عدة مواقع.
