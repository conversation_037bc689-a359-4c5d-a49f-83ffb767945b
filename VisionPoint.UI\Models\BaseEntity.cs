﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VisionPoint.UI.Models;

public class BaseEntity
{
    [Key]
    public int Id { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    public DateTime UpdatedAt { get; set; } = DateTime.Now;
    public int? CreatedById { get; set; }
    public User? CreatedBy { get; set; }
    public int? ModifiedById { get; set; }
    public User? ModifiedBy { get; set; }

    /// <summary>
    /// Row GUID for SQL Server Merge Replication - managed by database
    /// </summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid RowGuid { get; set; }
}
