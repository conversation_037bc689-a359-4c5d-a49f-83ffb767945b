﻿using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.Receipts
{
    /// <summary>
    /// Interaction logic for AddReceiptWindow.xaml
    /// </summary>
    public partial class AddReceiptWindow : Window
    {
        private readonly TreasuryService _treasuryService;
        private readonly WarehouseService _warehouseService;
        private List<Models.Treasury> _treasuries;
        private List<Models.Warehouse> _warehouses;
        int? _saleId = null, _purchaseId = null; decimal _totalPaid;
        public AddReceiptWindow(decimal totalPaid, int? saleId = null, int? purchaseId = null)
        {
            InitializeComponent();
            _treasuryService = new TreasuryService();
            _warehouseService = new WarehouseService();
            _saleId = saleId;
            _purchaseId = purchaseId;
            _totalPaid = totalPaid;
        }

        private void btnclose_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                this.DialogResult = false;
                Close();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnSave.IsEnabled = false;
            btnclose.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnSave.IsEnabled = true;
            btnclose.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private async void btnSave_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (cmbWarehouse.SelectedValue == null)
                {
                    ErrorBox.Show("يجب اختيار المخزن", "خطأ");
                    return;
                }
                decimal.TryParse(txtPaid.Text, out decimal paid);
                if (paid <= 0)
                {
                    ErrorBox.Show("المبلغ المدفوع يجب أن يكون أكبر من 0", "خطأ");
                    return;
                }
                if (cmbTreasury.SelectedIndex == -1)
                {
                    ErrorBox.Show("يجب اختيار طريقة الدفع", "خطأ");
                    return;
                }
                ReceiptService receiptService = new ReceiptService();
                if (_saleId != null)
                {
                    SaleService saleService = new SaleService();
                    var receipt = new Receipt
                    {
                        SaleId = _saleId,
                        Value = paid,
                        ClientId = await saleService.GetClientBySaleId(_saleId.Value),
                        TreasuryId = (byte)cmbTreasury.SelectedValue,
                        Date = DateTime.Now,
                        IsExchange = false,
                        Statement = "فاتورة بيع رقم " + _saleId,
                        FinancialId = (byte)FinancialId.Sale,
                    };
                    var (success, message, receiptId) = await receiptService.CreateReceipt(receipt);
                    if (success)
                    {
                        DialogBox.Show("نجاح", "تمت إضافة الوصل بنجاح");
                        this.DialogResult = true;
                        this.Close();
                    }
                    else
                    {
                        ErrorBox.Show(message, "خطأ");
                    }
                }
                else if (_purchaseId != null)
                {
                    PurchaseService purchaseService = new PurchaseService();
                    var receipt = new Receipt
                    {
                        PurchaseId = _purchaseId,
                        Value = paid,
                        ClientId = await purchaseService.GetClientByPurchaseId(_purchaseId.Value),
                        TreasuryId = (byte)cmbTreasury.SelectedValue,
                        Date = DateTime.Now,
                        IsExchange = true,
                        Statement = "فاتورة مشتريات رقم " + _purchaseId,
                        FinancialId = (byte)FinancialId.Purchase,
                    };
                    var (success, message, receiptId) = await receiptService.CreateReceipt(receipt);
                    if (success)
                    {
                        DialogBox.Show("نجاح", "تمت إضافة الوصل بنجاح");
                        this.DialogResult = true;
                        this.Close();
                    }
                    else
                    {
                        ErrorBox.Show(message, "خطأ");
                    }
                }
            }
            catch (Exception)
            {

                throw;
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void txtPaid_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (decimal.TryParse(txtPaid.Text, out decimal paid))
            {
                if (paid > _totalPaid)
                {
                    txtPaid.Text = _totalPaid.ToString();
                    txtPaid.CaretIndex = txtPaid.Text.Length;
                    ErrorBox.Show("المبلغ المدفوع أكبر من المبلغ المطلوب", "خطأ");
                }
            }
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                txtPaid.Text = "0.00";

                // تحميل المخازن
                await LoadWarehousesAsync();


            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ");
            }
        }

        /// <summary>
        /// تحميل المخازن
        /// </summary>
        private async Task LoadWarehousesAsync()
        {
            try
            {
                _warehouses = await _warehouseService.GetAllWarehousesAsync();

                // تكوين كومبوبوكس المخازن
                cmbWarehouse.ItemsSource = _warehouses;
                cmbWarehouse.DisplayMemberPath = "Name";
                cmbWarehouse.SelectedValuePath = "Id";

                // تحديد المخزن الافتراضي بناءً على المستخدم الحالي
                if (CurrentUser.WarehouseId.HasValue && _warehouses.Any(w => w.Id == CurrentUser.WarehouseId.Value))
                {
                    // اختيار مخزن المستخدم الحالي
                    cmbWarehouse.SelectedValue = CurrentUser.WarehouseId.Value;
                }
                else if (_warehouses.Count > 0)
                {
                    // اختيار أول مخزن إذا لم يكن للمستخدم مخزن محدد
                    cmbWarehouse.SelectedIndex = 0;
                }

                // تطبيق منطق الصلاحيات لتغيير المخزن
                // إذا لم يكن المستخدم مديراً أو لا يملك صلاحية تغيير المخزن، يتم تعطيل الكومبو
                cmbWarehouse.IsEnabled = CurrentUser.CanChangeWarehouse;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل المخازن: {ex.Message}", "خطأ");
            }
        }

        /// <summary>
        /// تحميل طرق الدفع بناءً على المخزن المختار
        /// </summary>
        private async Task LoadTreasuriesAsync()
        {
            try
            {
                if (cmbWarehouse.SelectedValue != null)
                {
                    int warehouseId = (int)cmbWarehouse.SelectedValue;
                    _treasuries = await _treasuryService.GetTreasuriesByWarehouseAsync(warehouseId);
                }
                else
                {
                    _treasuries = new List<Models.Treasury>();
                }

                // تكوين كومبوبوكس طرق الدفع
                cmbTreasury.ItemsSource = _treasuries;
                cmbTreasury.DisplayMemberPath = "Name";
                cmbTreasury.SelectedValuePath = "Id";

                // اختيار أول طريقة دفع إذا كانت متاحة
                if (_treasuries.Count > 0)
                {
                    cmbTreasury.SelectedIndex = 0;
                }
                else
                {
                    cmbTreasury.SelectedIndex = -1;
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل طرق الدفع: {ex.Message}", "خطأ");
            }
        }

        /// <summary>
        /// حدث تغيير المخزن المختار
        /// </summary>
        private async void cmbWarehouse_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (!this.IsLoaded) return; // تجنب التنفيذ أثناء التحميل الأولي

            try
            {
                await LoadTreasuriesAsync();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تغيير المخزن: {ex.Message}", "خطأ");
            }
        }
    }
}
