using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.MinimumQuantity
{
    /// <summary>
    /// Interaction logic for MinimumQuantityWindow.xaml
    /// </summary>
    public partial class MinimumQuantityWindow : Window
    {
        private ObservableCollection<MinimumQuantityItemViewModel> _items;
        private int? _selectedWarehouseId; // nullable لدعم "جميع المخازن"
        private bool _isProductsSelected = true;
        private bool _isLoaded = false;
        private bool _isInitializing = false;

        public MinimumQuantityWindow(bool isProductsSelected)
        {
            _items = new ObservableCollection<MinimumQuantityItemViewModel>();
            InitializeComponent();
            _isLoaded = true;
            list.ItemsSource = _items;
            _isProductsSelected = isProductsSelected;
        }

        // Create a new ProductService with a shared DbContext
        private ProductService CreateProductService()
        {
            return new ProductService();
        }

        // Create a new LensService with a shared DbContext
        private LensService CreateLensService()
        {
            return new LensService();
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            _isInitializing = true;
            try
            {
                // Load warehouses
                await LoadWarehousesAsync();
                RdbLensesSelect.IsChecked = !_isProductsSelected;
                RdbProductSelect.IsChecked = _isProductsSelected;
                Rdb_Checked(sender, e); // Load initial data based on selected type
            }
            finally
            {
                _isInitializing = false;
            }
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnclose.IsEnabled = false;
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnclose.IsEnabled = true;
        }

        private void btnclose_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                Close();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async Task LoadWarehousesAsync()
        {
            try
            {
                using var warehouseService = new WarehouseService();
                var warehouses = await warehouseService.GetAllWarehousesAsync();

                // Check user permissions for warehouse access
                cmbWarehouse.IsEnabled = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ChangeWarehouseRole");
                // User can see all warehouses - add "All Warehouses" option
                var allWarehouses = new List<Warehouse>
                    {
                        new Warehouse { Id = 0, Name = "جميع المخازن" }
                    };
                allWarehouses.AddRange(warehouses);

                cmbWarehouse.ItemsSource = allWarehouses;
                cmbWarehouse.SelectedValue = CurrentUser.WarehouseId;
                _selectedWarehouseId = CurrentUser.WarehouseId;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل المخازن: {ex.Message}", "خطأ", true);
            }
        }

        private async void cmbWarehouse_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isInitializing || cmbWarehouse.SelectedValue == null)
                return;

            try
            {
                // التعامل مع خيار "جميع المخازن" (Id = 0) مقابل مخزن محدد
                int selectedId = (int)cmbWarehouse.SelectedValue;
                _selectedWarehouseId = selectedId == 0 ? null : selectedId;

                // إعادة تحميل البيانات للمخزن المحدد
                if (RdbProductSelect.IsChecked == true)
                {
                    await LoadProductsBelowMinimumQuantity();
                }
                else if (RdbLensesSelect.IsChecked == true)
                {
                    await LoadLensesBelowMinimumQuantity();
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تغيير المخزن: {ex.Message}", "خطأ", true);
            }
        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (list.View is GridView gridView && gridView.Columns.Count >= 5)
            {
                double scrollbarWidth = SystemParameters.VerticalScrollBarWidth;

                double availableWidth = list.ActualWidth - scrollbarWidth;

                double totalWeight = 3.0 + 1.0 + 1.0 + 1.0 + 1.5; // Name, CurrentQuantity, MinimumQuantity, ItemType, Warehouse
                double unitWidth = availableWidth / totalWeight;

                gridView.Columns[0].Width = unitWidth * 3.0; // Full Name (Name + Color + Details)
                gridView.Columns[1].Width = unitWidth * 1.0; // Current Quantity
                gridView.Columns[2].Width = unitWidth * 1.0; // Minimum Quantity
                gridView.Columns[3].Width = unitWidth * 1.0; // Item Type
                gridView.Columns[4].Width = unitWidth * 1.5; // Warehouse
            }
        }

        private async void Rdb_Checked(object sender, RoutedEventArgs e)
        {
            if (!_isLoaded) return;
            if (RdbLensesSelect.IsChecked == true) await LoadLensesBelowMinimumQuantity();
            else if (RdbProductSelect.IsChecked == true) await LoadProductsBelowMinimumQuantity();
        }

        private async Task LoadProductsBelowMinimumQuantity()
        {
            try
            {
                _items.Clear();

                using (var productService = CreateProductService())
                {
                    _items = new(await productService.GetMinimumQuantityProductsViewModelAsync(_selectedWarehouseId));
                    list.ItemsSource = _items;
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل المنتجات التي وصلت للحد الأدنى: {ex.Message}", "خطأ", true);
            }
        }

        private async Task LoadLensesBelowMinimumQuantity()
        {
            try
            {
                _items.Clear();

                using (var lensService = CreateLensService())
                {
                    _items = new(await lensService.GetMinimumQuantityLensesViewModelAsync(_selectedWarehouseId));
                    list.ItemsSource = _items;
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل العدسات التي وصلت للحد الأدنى: {ex.Message}", "خطأ", true);
            }
        }
    }

    // View model for minimum quantity items
    public class MinimumQuantityItemViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public int CurrentQuantity { get; set; }
        public int MinimumQuantity { get; set; }
        public string ItemType { get; set; } = string.Empty;
        public string ColorName { get; set; } = string.Empty;
        public string Details { get; set; } = string.Empty; // For lens numbers (SPH, CYL, POW)
        public string WarehouseName { get; set; } = string.Empty; // اسم المخزن
        public string FullName => $"{Name} {(string.IsNullOrEmpty(ColorName) ? "" : $"- {ColorName}")} {(string.IsNullOrEmpty(Details) ? "" : $"- {Details}")}";

        // Override Equals and GetHashCode to help with duplicate detection
        public override bool Equals(object obj)
        {
            if (obj is not MinimumQuantityItemViewModel other)
                return false;

            // Consider items equal if they have the same ID, name, color, and details
            return Id == other.Id &&
                   Name == other.Name &&
                   ColorName == other.ColorName &&
                   Details == other.Details;
        }

        public override int GetHashCode()
        {
            // Create a hash code based on the properties we're comparing
            return HashCode.Combine(Id, Name, ColorName, Details);
        }
    }
}
