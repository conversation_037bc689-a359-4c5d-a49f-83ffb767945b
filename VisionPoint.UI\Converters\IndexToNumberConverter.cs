﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows.Data;

namespace VisionPoint.UI.Converters;

public class IndexToNumberConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var item = value;
        var itemsControl = parameter as ItemsControl;

        if (itemsControl != null && item != null)
        {
            int index = itemsControl.Items.IndexOf(item);
            return index >= 0 ? index + 1 : "-";
        }

        return "-";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

