﻿using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows;

namespace VisionPoint.UI.Converters;

// إضافة محول لحساب الإجمالي
public class PriceMultiplierConverter : IMultiValueConverter
{
    public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
    {
        if (values.Length == 2 && values[0] is decimal price && values[1] is int quantity)
        {
            return (price * quantity).ToString("N3");
        }
        return "0";
    }

    public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}