using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VisionPoint.UI.Controls;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.Helper.ImportExport
{
    /// <summary>
    /// مساعد تصدير الإيصالات
    /// </summary>
    public class ReceiptsExportHelper
    {
        private readonly List<Receipt> _receipts;
        private readonly int? _warehouseId;

        /// <summary>
        /// إنشاء مساعد تصدير الإيصالات
        /// </summary>
        /// <param name="receipts">قائمة الإيصالات المراد تصديرها</param>
        /// <param name="warehouseId">معرف المخزن (اختياري)</param>
        public ReceiptsExportHelper(List<Receipt> receipts, int? warehouseId = null)
        {
            _receipts = receipts ?? new List<Receipt>();
            _warehouseId = warehouseId;
        }

        /// <summary>
        /// بدء عملية تصدير الإيصالات
        /// </summary>
        /// <returns>قيمة تشير إلى نجاح العملية</returns>
        public async Task<bool> StartExportProcess()
        {
            try
            {
                if (_receipts == null || _receipts.Count == 0)
                {
                    ErrorBox.Show("لا توجد بيانات لتصديرها", "تنبيه", false);
                    return false;
                }

                // إنشاء خدمة تصدير
                var exportService = new ExportService();

                // عرض نافذة حفظ الملف
                var savePath = SelectSaveLocation();
                if (string.IsNullOrEmpty(savePath))
                {
                    return false; // تم إلغاء العملية
                }

                // تنفيذ عملية التصدير
                var progress = new Progress<int>(percentage =>
                {
                    // يمكن إضافة تحديث لشريط التقدم هنا إذا لزم الأمر
                });

                await exportService.ExportReceiptsToExcel(savePath, _receipts, _warehouseId, progress);

                DialogBox.Show("تم بنجاح", "تم تصدير البيانات بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تصدير البيانات: {ex.Message}", "خطأ في التصدير", true);
                return false;
            }
        }

        /// <summary>
        /// اختيار مكان حفظ الملف
        /// </summary>
        /// <returns>مسار الملف المختار أو سلسلة فارغة إذا تم الإلغاء</returns>
        private string SelectSaveLocation()
        {
            SaveFileDialog saveFileDialog = new SaveFileDialog
            {
                Filter = "Excel Files|*.xlsx",
                FileName = $"تقرير_الإيصالات_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx",
                Title = "حفظ تقرير الإيصالات"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                return saveFileDialog.FileName;
            }

            return string.Empty;
        }
    }
}
