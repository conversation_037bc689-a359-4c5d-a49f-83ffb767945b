﻿using Microsoft.EntityFrameworkCore;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.ProductsContent
{
    /// <summary>
    /// Interaction logic for LensPrescriptionPage.xaml
    /// </summary>
    public partial class LensPrescriptionPage : Window
    {
        private readonly LensService _lensService;
        private readonly ColorService _colorService;
        private readonly WarehouseService _warehouseService;
        private LensPrescriptionColorVM _selectedPrescriptionColor = new();
        private LensVM _selectedLens;
        private int? _selectedWarehouseId = null;

        public LensPrescriptionPage(LensVM lens)
        {
            InitializeComponent();
            _lensService = new LensService();
            _colorService = new ColorService();
            _warehouseService = new WarehouseService();
            _selectedLens = lens;
        }
        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // Basic lens info
            cmbLensesName.Text = _selectedLens.Name;

            // Set visibility based on null values
            (txtAxis.Parent as Grid).Visibility = _selectedLens.Axis.HasValue ? Visibility.Visible : Visibility.Collapsed;
            (txtAdd.Parent as Grid).Visibility = _selectedLens.Addtion.HasValue ? Visibility.Visible : Visibility.Collapsed;
            (txtBC.Parent as Grid).Visibility = _selectedLens.BC.HasValue ? Visibility.Visible : Visibility.Collapsed;
            (TxtDia.Parent as Grid).Visibility = _selectedLens.Dia.HasValue ? Visibility.Visible : Visibility.Collapsed;

            // Set values if they exist
            if (_selectedLens.Axis.HasValue) txtAxis.Text = _selectedLens.Axis.ToString();
            if (_selectedLens.Addtion.HasValue) txtAdd.Text = _selectedLens.Addtion.ToString();
            if (_selectedLens.BC.HasValue) txtBC.Text = _selectedLens.BC.ToString();
            if (_selectedLens.Dia.HasValue) TxtDia.Text = _selectedLens.Dia.ToString();
            // Show/hide prescription fields
            (cmbSph.Parent as Grid).Visibility = _selectedLens.Sphere ? Visibility.Visible : Visibility.Collapsed;
            (cmbCyl.Parent as Grid).Visibility = _selectedLens.Cylinder ? Visibility.Visible : Visibility.Collapsed;
            (cmbPow.Parent as Grid).Visibility = _selectedLens.Power ? Visibility.Visible : Visibility.Collapsed;

            // Load warehouses first
            await LoadWarehouses();
            // Load prescriptions data
            await LoadPrescription();
            // Load colors
            await LoadColors();
        }



        private async Task LoadWarehouses()
        {
            try
            {
                var warehouses = await _warehouseService.GetAllWarehousesAsync();

                // إضافة خيار "جميع المخازن" في البداية
                var allWarehouses = new List<Warehouse>
                {
                    new Warehouse { Id = 0, Name = "جميع المخازن" }
                };
                allWarehouses.AddRange(warehouses);

                cmbWarehouse.ItemsSource = allWarehouses;

                // تحديد المخزن الافتراضي للمستخدم الحالي
                if (CurrentUser.WarehouseId.HasValue)
                {
                    // اختيار مخزن المستخدم الحالي
                    cmbWarehouse.SelectedValue = CurrentUser.WarehouseId.Value;
                    _selectedWarehouseId = CurrentUser.WarehouseId.Value;
                }
                else
                {
                    // إذا لم يكن له مخزن محدد، نختار "جميع المخازن"
                    cmbWarehouse.SelectedIndex = 0;
                    _selectedWarehouseId = null;
                }

                // تطبيق منطق الصلاحيات لتغيير المخزن
                cmbWarehouse.IsEnabled = CurrentUser.CanChangeWarehouse;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل المخازن: {ex.Message}", "خطأ", true);
            }
        }

        private async Task LoadColors()
        {
            var colors = await _colorService.GetAllColorsAsync();
            cmbColor.ItemsSource = colors;
            cmbColor.DisplayMemberPath = "Name";
        }
        private async Task LoadPrescriptionColors()
        {
            if ((cmbSph.Parent as Grid).Visibility == Visibility.Visible && cmbSph.SelectedItem == null)
            {
                ErrorBox.Show("الرجاء اختيار قيمة SPH لمواصفات العدسة", "قيم ناقصة", true);
                return;
            }

            if ((cmbCyl.Parent as Grid).Visibility == Visibility.Visible && cmbCyl.SelectedItem == null)
            {
                ErrorBox.Show("الرجاء اختيار قيمة CYL لمواصفات العدسة", "قيم ناقصة", true);
                return;
            }

            if ((cmbPow.Parent as Grid).Visibility == Visibility.Visible && cmbPow.SelectedItem == null)
            {
                ErrorBox.Show("الرجاء اختيار قيمة POW لمواصفات العدسة", "قيم ناقصة", true);
                return;
            }
            list.ItemsSource = await _lensService.GetLensPrescriptionColorsAsync(
                _selectedLens.Id,
                cmbSph.SelectedItem == null ? null : (short)cmbSph.SelectedValue,
                cmbCyl.SelectedItem == null ? null : (short)cmbCyl.SelectedValue,
                cmbPow.SelectedItem == null ? null : (short)cmbPow.SelectedValue,
                _selectedWarehouseId);
        }

        private async void cmbWarehouse_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // التحقق من أن المستخدم لديه صلاحية تغيير المخزن أو أن هذا هو التحديد الأولي
            if (cmbWarehouse.SelectedValue != null && (CurrentUser.CanChangeWarehouse || _selectedWarehouseId == null))
            {
                var selectedId = (int)cmbWarehouse.SelectedValue;
                _selectedWarehouseId = selectedId == 0 ? null : selectedId;

                // إعادة تحميل البيانات إذا كانت الوصفات محددة
                if (list.ItemsSource != null)
                {
                    await LoadPrescriptionColors();
                }
            }
        }
        private async Task LoadPrescription()
        {
            var prescriptions = await _lensService.GetAllPrescriptionsAsync();
            if (_selectedLens.Sphere)
                cmbSph.ItemsSource = prescriptions;
            if (_selectedLens.Cylinder)
                cmbCyl.ItemsSource = prescriptions;
            if (_selectedLens.Power)
                cmbPow.ItemsSource = prescriptions;

            // Configure ComboBox display
            cmbSph.DisplayMemberPath = "Value";
            cmbCyl.DisplayMemberPath = "Value";
            cmbPow.DisplayMemberPath = "Value";
            cmbSph.SelectedValuePath = "Id";
            cmbCyl.SelectedValuePath = "Id";
            cmbPow.SelectedValuePath = "Id";
        }

        private void btnclose_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            Close();
        }
        private async void btnDelete_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (_selectedPrescriptionColor.Id == 0)
            {
                ErrorBox.Show("الرجاء اختيار عنصر من القائمة للحذف", "تحديد غير مكتمل", true);
                return;
            }

            var result = DeleteBox.Show("هل انت متاكد من حذف لون العدسة", _selectedPrescriptionColor.Color?.Name);
            if (result == true)
            {
                var deleteResult = await _lensService.DeleteLensPrescriptionColorAsync(_selectedPrescriptionColor.Id);
                if (deleteResult.State)
                {
                    DialogBox.Show("تم حذف لون العدسة بنجاح", "عملية ناجحة");
                    Clear();
                    await LoadPrescriptionColors();
                }
                else
                {
                    ErrorBox.Show(deleteResult.Message, "خطأ في الحذف", true);
                }
            }
        }


        private void btnNew_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            Clear();
        }

        private async void btnSave_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (string.IsNullOrEmpty(cmbPow.Text?.Trim()) && _selectedLens.Power != false)
            {
                ErrorBox.Show("الرجاء إدخال قيمة Pow", "بيانات ناقصة", true);
                return;
            }
            if (cmbColor.SelectedItem == null)
            {
                ErrorBox.Show("الرجاء اختيار اللون", "بيانات ناقصة", true);
                return;
            }

            if (string.IsNullOrEmpty(txtSellPrice.Text?.Trim()))
            {
                ErrorBox.Show("الرجاء إدخال سعر العدسة", "بيانات ناقصة", true);
                return;
            }

            try
            {
                // Get prescription values based on selected items
                short? sphereId = null;
                short? cylinderId = null;
                short? powId = null;

                if (_selectedLens.Sphere && cmbSph.SelectedItem is Prescription selectedSph)
                {
                    sphereId = selectedSph.Id;
                }

                if (_selectedLens.Cylinder && cmbCyl.SelectedItem is Prescription selectedCyl)
                {
                    cylinderId = selectedCyl.Id;
                }

                if (_selectedLens.Power && cmbPow.SelectedItem is Prescription selectedPow)
                {
                    powId = selectedPow.Id;
                }

                // Call service method
                var result = await _lensService.SaveLensPrescriptionColorAsync(
                    _selectedLens.Id,
                    sphereId,
                    cylinderId,
                    powId,
                    decimal.Parse(txtSellPrice.Text),
                    string.IsNullOrWhiteSpace(txtBarcode.Text) ? null : txtBarcode.Text.Trim(),
                    ((Models.Color)cmbColor.SelectedItem).Id,
                    _selectedPrescriptionColor.Id
                );

                if (result.State)
                {
                    DialogBox.Show("تم حفظ بيانات لون العدسة بنجاح", "عملية ناجحة");
                    Clear();
                    await LoadPrescriptionColors();
                }
                else
                {
                    ErrorBox.Show(result.Message, "خطأ في الحفظ", true);
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ في النظام", true);
            }
        }

        // Add Clear method if it doesn't exist
        private void Clear()
        {
            _selectedPrescriptionColor = new LensPrescriptionColorVM();
            txtBarcode.Clear();
            cmbColor.SelectedIndex = -1;
            txtSellPrice.Clear();
            txtCostPrice.Clear();
        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            try
            {
                if (sender is ListView listView && listView.View is GridView gView)
                {
                    var workingWidth = listView.ActualWidth - SystemParameters.VerticalScrollBarWidth;

                    var col0 = 0.25; // Barcode
                    var col1 = 0.25; // Color
                    var col2 = 0.25; // Quantity
                    var col3 = 0.25; // Additional column

                    for (int i = 0; i < gView.Columns.Count; i++)
                    {
                        var width = i switch
                        {
                            0 => col0,
                            1 => col1,
                            2 => col2,
                            3 => col3,
                            _ => 0.25
                        };
                        gView.Columns[i].Width = workingWidth * width;
                    }
                }
            }
            catch
            {
                return;
            }
        }

        private async void btnShow_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            await LoadPrescriptionColors();
        }

        private async void btnAddColor_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            var colorsPage = new ColorsPage(_colorService);
            colorsPage.ShowDialog();
            await LoadColors();
        }

        private void list_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (list.SelectedItem is LensPrescriptionColorVM selectedPrescriptionColor)
            {
                _selectedPrescriptionColor = selectedPrescriptionColor;
                txtBarcode.Text = selectedPrescriptionColor.Barcode;
                cmbColor.SelectedItem = selectedPrescriptionColor.Color;

                // If there's a price field in your form, you would set it here
                if (selectedPrescriptionColor.LensPrescription != null)
                {
                    txtSellPrice.Text = selectedPrescriptionColor.LensPrescription.SellPrice.ToString();
                    txtCostPrice.Text = selectedPrescriptionColor.LensPrescription.CostPrice.ToString();
                }
            }
            else
            {
                ErrorBox.Show("الرجاء اختيار عنصر من القائمة للتعديل", "تحديد غير مكتمل", true);
            }
        }

        private void cmbPow_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (cmbPow.SelectedIndex == -1)
            {
                list.ItemsSource = null;
                Clear();
            }
        }

        private async void cmbPow_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                if (cmbPow.SelectedItem != null)
                {
                    list.ItemsSource = null;
                    await LoadPrescriptionColors();
                }
            }
        }
    }
}
