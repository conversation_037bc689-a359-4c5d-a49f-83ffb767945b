﻿using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using VisionPoint.UI.Helper.ImportExport;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Dialogs;
using VisionPoint.UI.views.Pages.ImportExport;
using VisionPoint.UI.views.Pages.ProductsContent;

namespace VisionPoint.UI.views.Pages.Products
{
    /// <summary>
    /// Interaction logic for LensesPage.xaml
    /// </summary>
    public partial class LensesPage : Page
    {
        private readonly LensService? _lensService;
        private readonly LensCategoryService _categoryService;
        private readonly WarehouseService _warehouseService;
        private LensVM _selectedLens = new();
        private int? _selectedWarehouseId = null;

        public LensesPage()
        {
            InitializeComponent();
            _lensService = new LensService();
            _categoryService = new LensCategoryService();
            _warehouseService = new WarehouseService();
        }
        private async void Page_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadWarehouses();
            await LoadCategories();
        }

        private async Task LoadWarehouses()
        {
            try
            {
                var warehouses = await _warehouseService.GetAllWarehousesAsync();

                // إضافة خيار "جميع المخازن" في البداية
                var allWarehouses = new List<Warehouse>
                {
                    new Warehouse { Id = 0, Name = "جميع المخازن" }
                };
                allWarehouses.AddRange(warehouses);

                cmbWarehouse.ItemsSource = allWarehouses;

                // تحديد المخزن الافتراضي للمستخدم الحالي
                if (CurrentUser.WarehouseId.HasValue)
                {
                    // اختيار مخزن المستخدم الحالي
                    cmbWarehouse.SelectedValue = CurrentUser.WarehouseId.Value;
                    _selectedWarehouseId = CurrentUser.WarehouseId.Value;
                }
                else
                {
                    // إذا لم يكن له مخزن محدد، نختار "جميع المخازن"
                    cmbWarehouse.SelectedIndex = 0;
                    _selectedWarehouseId = null;
                }

                // تطبيق منطق الصلاحيات لتغيير المخزن
                // إذا لم يكن المستخدم مديراً أو لا يملك صلاحية تغيير المخزن، يتم تعطيل الكومبو
                cmbWarehouse.IsEnabled = CurrentUser.CanChangeWarehouse;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل المخازن: {ex.Message}", "خطأ", true);
            }
        }

        private async Task LoadData()
        {
            try
            {
                // The BusyService is automatically set by the LensService
                list.ItemsSource = await _lensService.GetAllVMAsync(_selectedWarehouseId);
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ: {ex.Message}", "خطأ", true);
            }
        }

        private async void cmbWarehouse_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // التحقق من أن المستخدم لديه صلاحية تغيير المخزن أو أن هذا هو التحديد الأولي
            if (cmbWarehouse.SelectedValue != null && (CurrentUser.CanChangeWarehouse || _selectedWarehouseId == null))
            {
                var selectedId = (int)cmbWarehouse.SelectedValue;
                _selectedWarehouseId = selectedId == 0 ? null : selectedId;
                await LoadData();
            }
        }

        private async Task LoadCategories()
        {
            try
            {
                var categories = await _categoryService.GetAllCategoriesAsync();
                cmbCategory.ItemsSource = categories;

                // Si hay una categoría seleccionada, mantenerla
                if (_selectedLens.CategoryId.HasValue)
                {
                    cmbCategory.SelectedValue = _selectedLens.CategoryId;
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل أنواع العدسات: {ex.Message}", "خطأ", true);
            }
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnSave.IsEnabled = false;
            btnNew.IsEnabled = false;
            btnDelete.IsEnabled = false;
            btnSearch.IsEnabled = false;
            btnAddValues.IsEnabled = false;
            btnImport.IsEnabled = false;
            btnExport.IsEnabled = false;
            btnExportInventory.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnSave.IsEnabled = true;
            btnNew.IsEnabled = true;
            btnDelete.IsEnabled = true;
            btnSearch.IsEnabled = true;
            btnAddValues.IsEnabled = true;
            btnImport.IsEnabled = true;
            btnExport.IsEnabled = true;
            btnExportInventory.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private async void btnSave_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق من صلاحيات المستخدم
                bool canAdd = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("LensRole");
                bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditLensRole");

                // إذا كانت عدسة جديدة، نحتاج صلاحية الإضافة
                if (_selectedLens.Id == 0 && !canAdd)
                {
                    ErrorBox.Show("لا تملك صلاحية إضافة العدسات", "خطأ في الصلاحيات", true);
                    return;
                }

                // إذا كان تعديل عدسة موجودة، نحتاج صلاحية التعديل
                if (_selectedLens.Id != 0 && !canEdit)
                {
                    ErrorBox.Show("لا تملك صلاحية تعديل العدسات", "خطأ في الصلاحيات", true);
                    return;
                }

                if (string.IsNullOrEmpty(txtName.Text.Trim()))
                {
                    ErrorBox.Show("الرجاء إدخال اسم العدسة", "خطأ", true);
                    return;
                }

                if (cmbCategory.SelectedValue == null)
                {
                    ErrorBox.Show("الرجاء اختيار نوع العدسة", "خطأ", true);
                    return;
                }

                // Prepare lens data
                _selectedLens.Name = txtName.Text.Trim();
                _selectedLens.Sphere = chkSph.IsChecked ?? false;
                _selectedLens.Cylinder = chkCyl.IsChecked ?? false;
                _selectedLens.Power = chkPow.IsChecked ?? false;
                _selectedLens.Axis = chkAxis.IsChecked == true ? decimal.Parse(txtExis.Text) : null;
                _selectedLens.Dia = chkDia.IsChecked == true ? decimal.Parse(txtDia.Text) : null;
                _selectedLens.BC = chkBC.IsChecked == true ? decimal.Parse(txtBC.Text) : null;
                _selectedLens.Addtion = chkAdd.IsChecked == true ? decimal.Parse(txtAdd.Text) : null;
                _selectedLens.Exp = chkExpire.IsChecked ?? false;
                _selectedLens.MinimumQuantity = !string.IsNullOrEmpty(txtMinimumQuantity.Text) ? int.Parse(txtMinimumQuantity.Text) : 0;
                _selectedLens.CategoryId = cmbCategory.SelectedValue as int?;

                var result = _selectedLens.Id == 0
                    ? await _lensService.AddAsync(_selectedLens)
                    : await _lensService.UpdateAsync(_selectedLens);

                if (result.State)
                {
                    DialogBox.Show(result.Message, "نجاح");
                    // Clear form
                    btnNew_MouseLeftButtonDown(sender, e);
                    await LoadData();
                }
                else
                {
                    ErrorBox.Show(result.Message, "خطأ", true);
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }
        private void btnNew_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            _selectedLens = new LensVM();
            txtName.Clear();
            chkSph.IsChecked = false;
            chkCyl.IsChecked = false;
            chkPow.IsChecked = false;
            chkAxis.IsChecked = false;
            chkDia.IsChecked = false;
            chkBC.IsChecked = false;
            chkAdd.IsChecked = false;
            txtExis.Clear();
            txtDia.Clear();
            txtBC.Clear();
            txtAdd.Clear();
            txtMinimumQuantity.Clear();
            chkExpire.IsChecked = false;
            cmbCategory.SelectedItem = null;
            list.SelectedItem = null;
            chkExpire.IsEnabled = true;
        }
        private async void btnDelete_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق من صلاحية الحذف
                bool canDelete = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("DeleteLensRole");
                if (!canDelete)
                {
                    ErrorBox.Show("لا تملك صلاحية حذف العدسات", "خطأ في الصلاحيات", true);
                    return;
                }

                if (_selectedLens.Id == 0)
                {
                    ErrorBox.Show("الرجاء اختيار عدسة للحذف", "تنبيه", false);
                    return;
                }

                var result = DeleteBox.Show("هل انت متاكد من حذف عدسة", _selectedLens.Name);

                if (result == true)
                {
                    var deleteResult = await _lensService.DeleteAsync(_selectedLens.Id);

                    if (deleteResult.State)
                    {
                        DialogBox.Show("تم الحذف", "نجاح");
                        btnNew_MouseLeftButtonDown(sender, e);
                        await LoadData();
                    }
                    else
                    {
                        ErrorBox.Show(deleteResult.Message, "خطأ", true);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء حذف العدسة: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }
        private async void btnSearch_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                var searchTerm = txtName.Text.Trim();
                if (string.IsNullOrEmpty(searchTerm))
                {
                    await LoadData();
                    return;
                }

                var searchResults = await _lensService.SearchByNameAsync(searchTerm, _selectedWarehouseId);
                list.ItemsSource = searchResults;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }


        private void btnAddValues_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (_selectedLens == null || _selectedLens.Id == 0)
                {
                    ErrorBox.Show("الرجاء اختيار عدسة قبل العرض", "تنبيه", false);
                    return;
                }

                LensPrescriptionPage treasuryPage = new LensPrescriptionPage(_selectedLens);
                treasuryPage.ShowDialog();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnImport_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // استخدام المساعد الجديد لعملية الاستيراد
                var importHelper = new LensImportHelper();
                var result = await importHelper.StartImportProcess();

                if (result)
                {
                    // إذا تمت العملية بنجاح، نقوم بتحديث البيانات
                    await LoadData();
                    await LoadCategories();
                    btnNew_MouseLeftButtonDown(sender, e);
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء استيراد البيانات: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }
        private async void btnExport_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق من وجود بيانات للتصدير
                if (list.ItemsSource is not IEnumerable<LensVM> lensesSource || !lensesSource.Any())
                {
                    ErrorBox.Show("لا توجد بيانات عدسات للتصدير", "تحذير", true);
                    return;
                }

                // جلب بيانات العدسات المسطحة للمعاينة مع تطبيق التصفية الحالية
                var lensService = new LensService();
                var lensesData = await lensService.GetLensesForExportAsync(_selectedWarehouseId);

                if (!lensesData.Any())
                {
                    ErrorBox.Show("لا توجد بيانات عدسات للتصدير في المخزن المحدد", "تحذير", true);
                    return;
                }

                // فتح نافذة معاينة التصدير مع البيانات الحالية والتصفية المطبقة
                var exportPreviewWindow = new LensExportPreviewWindow(lensesData, _selectedWarehouseId);
                exportPreviewWindow.ShowDialog();

                // يمكن إضافة أي إجراءات إضافية بعد إغلاق نافذة التصدير إذا لزم الأمر
                if (exportPreviewWindow.ExportCompleted)
                {
                    // تم التصدير بنجاح
                    // يمكن إضافة أي منطق إضافي هنا إذا لزم الأمر
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تصدير البيانات: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnExportInventory_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق من اختيار عدسة محددة
                if (list.SelectedItem == null)
                {
                    ErrorBox.Show("يجب اختيار عدسة محددة لتصدير الجرد", "تنبيه", true);
                    return;
                }

                var selectedLensVM = (LensVM)list.SelectedItem;


                if (selectedLensVM == null)
                {
                    ErrorBox.Show("العدسة المحددة غير موجودة", "خطأ", true);
                    return;
                }

                // عرض نافذة اختيار نطاق التصدير
                var exportDialog = new LensInventoryExportDialog(selectedLensVM);

                // عرض النافذة
                exportDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء فتح نافذة التصدير: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }


        private void list_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (list.SelectedItem is LensVM selectedLens)
            {
                _selectedLens = selectedLens;
                txtName.Text = selectedLens.Name;
                chkSph.IsChecked = selectedLens.Sphere;
                chkCyl.IsChecked = selectedLens.Cylinder;
                chkPow.IsChecked = selectedLens.Power;
                chkAxis.IsChecked = selectedLens.Axis.HasValue;
                txtExis.Text = selectedLens.Axis?.ToString();
                chkDia.IsChecked = selectedLens.Dia.HasValue;
                txtDia.Text = selectedLens.Dia?.ToString();
                chkBC.IsChecked = selectedLens.BC.HasValue;
                txtBC.Text = selectedLens.BC?.ToString();
                chkAdd.IsChecked = selectedLens.Addtion.HasValue;
                txtAdd.Text = selectedLens.Addtion?.ToString();
                chkExpire.IsChecked = selectedLens.Exp;
                txtMinimumQuantity.Text = selectedLens.MinimumQuantity.ToString();
                cmbCategory.SelectedValue = selectedLens.CategoryId;
                chkExpire.IsEnabled = selectedLens.IsAbleToEditExp;
            }
            else
            {
                btnNew_MouseLeftButtonDown(sender, e);
            }
        }
        private void chkAxis_Checked(object sender, RoutedEventArgs e)
        {
            txtExis.Clear();
        }

        private void chkDia_Checked(object sender, RoutedEventArgs e)
        {
            txtDia.Clear();
        }

        private void chkBC_Checked(object sender, RoutedEventArgs e)
        {
            txtBC.Clear();
        }

        private void chkAdd_Checked(object sender, RoutedEventArgs e)
        {
            txtAdd.Clear();
        }
        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (list.View is GridView gridView && gridView.Columns.Count >= 8)
            {
                double padding = 10;
                double scrollbarWidth = SystemParameters.VerticalScrollBarWidth;

                double availableWidth = list.ActualWidth - scrollbarWidth - padding;

                double totalWeight = 0.3 + 2.0 + 2.0 + 0.4 + 0.4 + 0.4 + 0.4 + 0.4 + 0.4 + 0.4 + 1.0;
                double unitWidth = availableWidth / totalWeight;

                gridView.Columns[0].Width = unitWidth * 0.3; // Index
                gridView.Columns[1].Width = unitWidth * 2.0; // Invoice ID
                gridView.Columns[2].Width = unitWidth * 2.0; // Date
                gridView.Columns[3].Width = unitWidth * 0.4; // Client
                gridView.Columns[4].Width = unitWidth * 0.4; // Total
                gridView.Columns[5].Width = unitWidth * 0.4; // Paid
                gridView.Columns[6].Width = unitWidth * 0.4; // Remaining
                gridView.Columns[7].Width = unitWidth * 0.4; // Operations
                gridView.Columns[8].Width = unitWidth * 0.4; // Operations
                gridView.Columns[9].Width = unitWidth * 0.4; // Operations
                gridView.Columns[10].Width = unitWidth * 1.0; // Operations
            }
        }


        private void TextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                textBox.SelectAll();
            }
        }


    }
}

