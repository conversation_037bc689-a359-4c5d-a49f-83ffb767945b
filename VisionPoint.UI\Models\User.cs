﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VisionPoint.UI.Models;

public class User : IdentityUser<int>
{
    [StringLength(100, ErrorMessage = "اسم المستخدم يجب ألا يتجاوز 100 حرف")]
    public string Name { get; set; }
    [Precision(18, 3)]
    public decimal Balance { get; set; }
    /// <summary>
    /// المرتب الشهري للمستخدم
    /// </summary>
    [Precision(18, 3)]
    public decimal Salary { get; set; } = 0;

    /// <summary>
    /// السماح بتجاوز حد الرصيد (القيمة الافتراضية: لا)
    /// إذا كانت true، يمكن للموظف تجاوز حد المرتب في الرصيد
    /// إذا كانت false، لا يمكن للموظف تجاوز حد المرتب في الرصيد
    /// </summary>
    public bool AllowBalanceOverride { get; set; } = false;

    /// <summary>
    /// معرف المخزن المرتبط بالمستخدم (اختياري)
    /// </summary>
    public int? WarehouseId { get; set; }

    /// <summary>
    /// المخزن المرتبط بالمستخدم
    /// </summary>
    [ForeignKey("WarehouseId")]
    public virtual Warehouse? Warehouse { get; set; }

    /// <summary>
    /// Row GUID for SQL Server Merge Replication - managed by database
    /// </summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid RowGuid { get; set; }
}
public class Role : IdentityRole<int>
{
    /// <summary>
    /// Row GUID for SQL Server Merge Replication - managed by database
    /// </summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid RowGuid { get; set; }
}

public class UserRole : IdentityUserRole<int>
{
    /// <summary>
    /// Row GUID for SQL Server Merge Replication - managed by database
    /// </summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid RowGuid { get; set; }
}
