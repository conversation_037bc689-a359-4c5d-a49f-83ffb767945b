﻿<Page
    x:Class="VisionPoint.UI.views.Pages.Products.SharedPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:cc="clr-namespace:VisionPoint.UI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Products"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="SharedPage"
    d:Background="White"
    d:Height="1080"
    d:Width="1570"
    FlowDirection="RightToLeft"
    Loaded="Page_Loaded"
    mc:Ignorable="d">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="72" />
            <RowDefinition />
        </Grid.RowDefinitions>

        <Border
            Margin="0,0,-10000,0"
            BorderThickness="0,0,0,0.5"
            DockPanel.Dock="Top">
            <Border.BorderBrush>
                <SolidColorBrush Opacity="0.6" Color="Gray" />
            </Border.BorderBrush>
            <DockPanel Margin="12,0,12,0" LastChildFill="False">
                <Button
                    x:Name="btnGenral"
                    Margin="0,0,0,0"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Bottom"
                    HorizontalContentAlignment="Left"
                    Background="Transparent"
                    BorderThickness="0"
                    Click="btnGenral_Click"
                    DockPanel.Dock="Left"
                    Style="{DynamicResource noHover}">
                    <cc:TabMenu
                        VerticalAlignment="Bottom"
                        GroupName="SharedItems"
                        IsSelected="True"
                        Paddings="16 0"
                        Text="منتجات" />
                </Button>
                <Button
                    x:Name="btnLenses"
                    Margin="0,0,0,0"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Bottom"
                    HorizontalContentAlignment="Left"
                    Background="Transparent"
                    BorderThickness="0"
                    Click="btnLenses_Click"
                    DockPanel.Dock="Left"
                    Style="{DynamicResource noHover}">
                    <cc:TabMenu
                        VerticalAlignment="Bottom"
                        GroupName="SharedItems"
                        IsSelected="False"
                        Paddings="16 0"
                        Text="عدسات" />
                </Button>



                <Button
                    x:Name="btnServices"
                    Margin="0,0,0,0"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Bottom"
                    HorizontalContentAlignment="Left"
                    Background="Transparent"
                    BorderThickness="0"
                    Click="btnServices_Click"
                    DockPanel.Dock="Left"
                    Style="{DynamicResource noHover}">
                    <cc:TabMenu
                        VerticalAlignment="Bottom"
                        GroupName="SharedItems"
                        IsSelected="False"
                        Paddings="16 0"
                        Text="الخدمات" />
                </Button>

                <Button
                    x:Name="btnLensCategories"
                    Margin="0,0,0,0"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Bottom"
                    HorizontalContentAlignment="Left"
                    Background="Transparent"
                    BorderThickness="0"
                    Click="btnLensCategories_Click"
                    DockPanel.Dock="Left"
                    Style="{DynamicResource noHover}">
                    <cc:TabMenu
                        VerticalAlignment="Bottom"
                        GroupName="SharedItems"
                        IsSelected="False"
                        Paddings="16 0"
                        Text="أنواع العدسات" />
                </Button>
            </DockPanel>
        </Border>

        <Border Grid.Row="1">
            <Frame
                Name="fContainer"
                Grid.Column="1"
                Margin="0"
                NavigationUIVisibility="Hidden" />
        </Border>

    </Grid>
</Page>
