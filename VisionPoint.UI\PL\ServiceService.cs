using Microsoft.EntityFrameworkCore;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Models;

namespace VisionPoint.UI.PL;

public class ServiceService : IDisposable
{
    private readonly AppDbContext _context = ServiceLocator.GetService<AppDbContext>();
    private bool _disposed = false;

    public ServiceService()
    {
    }
    public async Task<List<Service>> GetAllServices()
    {
        return await _context.Services.ToListAsyncWithBusy("GetAllServices");
    }

    public async Task<Service?> GetServiceById(int id)
    {
        return await _context.Services.FindAsyncWithBusy(id);
    }

    public async Task<(bool State, string Message)> CreateService(Service service)
    {
        service.CreatedById = CurrentUser.Id;
        service.ModifiedById = CurrentUser.Id;
        service.CreatedAt = DateTime.Now;
        service.UpdatedAt = DateTime.Now;
        await _context.Services.AddAsyncWithBusy(service);
        return await _context.SaveWithTransactionAndBusy("CreateService");
    }

    public async Task<(bool State, string Message)> UpdateService(Service service)
    {
        service.ModifiedById = CurrentUser.Id;
        service.UpdatedAt = DateTime.Now;
        _context.Services.UpdateWithBusy(service);
        return await _context.SaveWithTransactionAndBusy("UpdateService");
    }

    public async Task<(bool State, string Message)> DeleteService(int id)
    {
        var service = await _context.Services.FindAsyncWithBusy(id);
        if (service == null)
        {
            return (false, "الخدمة غير موجودة");
        }

        // التحقق من وجود عناصر مبيعات مرتبطة بالخدمة
        bool hasSaleItems = await _context.SaleItems.AnyAsyncWithBusy(si => si.ServiceId == id, "CheckServiceSaleItems");

        if (hasSaleItems)
        {
            return (false, "لا يمكن حذف الخدمة لأنها مرتبطة بعناصر مبيعات");
        }

        // التحقق من وجود خصومات مرتبطة بالخدمة
        bool hasDiscountServices = await _context.DiscountServices.AnyAsyncWithBusy(ds => ds.ServiceId == id, "CheckServiceDiscounts");

        if (hasDiscountServices)
        {
            return (false, "لا يمكن حذف الخدمة لأنها مرتبطة بخصومات");
        }

        _context.Services.RemoveWithBusy(service);
        return await _context.SaveWithTransactionAndBusy("DeleteService");
    }

    // Implement IDisposable pattern
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // Dispose managed resources
                _context?.Dispose();
            }

            // Free unmanaged resources
            _disposed = true;
        }
    }

    // Destructor
    ~ServiceService()
    {
        Dispose(false);
    }
}