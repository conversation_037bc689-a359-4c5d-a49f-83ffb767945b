﻿﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Models;
using VisionPoint.UI.Services;

namespace VisionPoint.UI.Extensions
{
    /// <summary>
    /// Extension methods for AppDbContext to integrate with BusyService.
    /// </summary>
    public static class DbContextExtensions
    {
        private static BusyService _busyService => ServiceLocator.GetService<BusyService>();

        /// <summary>
        /// Executes a query while setting the busy state.
        /// </summary>
        /// <typeparam name="T">The type of the result.</typeparam>
        /// <param name="query">The query to execute.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The result of the query.</returns>
        public static async Task<List<T>> ToListAsyncWithBusy<T>(this IQueryable<T> query, string operationName = null)
        {
            operationName ??= $"Query_{typeof(T).Name}";
            return await _busyService.ExecuteWhileBusyAsync(() => query.ToListAsync(), operationName);
        }

        /// <summary>
        /// Finds an entity by its key while setting the busy state.
        /// </summary>
        /// <typeparam name="T">The type of the entity.</typeparam>
        /// <param name="dbSet">The DbSet to search.</param>
        /// <param name="keyValues">The key values to find.</param>
        /// <returns>The found entity or null.</returns>
        public static async Task<T> FindAsyncWithBusy<T>(this DbSet<T> dbSet, params object[] keyValues) where T : class
        {
            string operationName = $"Find_{typeof(T).Name}";
            return await _busyService.ExecuteWhileBusyAsync(() => dbSet.FindAsync(keyValues).AsTask(), operationName);
        }

        /// <summary>
        /// Adds an entity to the database while setting the busy state.
        /// </summary>
        /// <typeparam name="T">The type of the entity.</typeparam>
        /// <param name="dbSet">The DbSet to add to.</param>
        /// <param name="entity">The entity to add.</param>
        /// <returns>The added entity.</returns>
        public static async Task<T> AddAsyncWithBusy<T>(this DbSet<T> dbSet, T entity) where T : class
        {
            string operationName = $"Add_{typeof(T).Name}";
            return await _busyService.ExecuteWhileBusyAsync(async () =>
            {
                var entry = await dbSet.AddAsync(entity);
                return entry.Entity;
            }, operationName);
        }

        /// <summary>
        /// Saves changes with transaction while setting the busy state.
        /// </summary>
        /// <param name="context">The database context.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>A tuple indicating success and a message.</returns>
        public static async Task<(bool State, string Message)> SaveWithTransactionAndBusy(this AppDbContext context, string operationName = null)
        {
            operationName ??= "SaveChanges";
            return await _busyService.ExecuteWhileBusyAsync(() => context.SaveWithTransaction(), operationName);
        }

        /// <summary>
        /// Updates an entity while setting the busy state.
        /// </summary>
        /// <typeparam name="T">The type of the entity.</typeparam>
        /// <param name="dbSet">The DbSet to update.</param>
        /// <param name="entity">The entity to update.</param>
        /// <returns>The updated entity.</returns>
        public static T UpdateWithBusy<T>(this DbSet<T> dbSet, T entity) where T : class
        {
            string operationName = $"Update_{typeof(T).Name}";
            return _busyService.ExecuteWhileBusy(() =>
            {
                var entry = dbSet.Update(entity);
                T result = entry.Entity;
                return result;
            }, operationName);
        }

        /// <summary>
        /// Removes an entity while setting the busy state.
        /// </summary>
        /// <typeparam name="T">The type of the entity.</typeparam>
        /// <param name="dbSet">The DbSet to remove from.</param>
        /// <param name="entity">The entity to remove.</param>
        /// <returns>The removed entity.</returns>
        public static T RemoveWithBusy<T>(this DbSet<T> dbSet, T entity) where T : class
        {
            string operationName = $"Remove_{typeof(T).Name}";
            return _busyService.ExecuteWhileBusy(() =>
            {
                var entry = dbSet.Remove(entity);
                T result = entry.Entity;
                return result;
            }, operationName);
        }

        /// <summary>
        /// Executes FirstOrDefaultAsync while setting the busy state.
        /// </summary>
        /// <typeparam name="T">The type of the result.</typeparam>
        /// <param name="query">The query to execute.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The first entity or null.</returns>
        public static async Task<T?> FirstOrDefaultAsyncWithBusy<T>(this IQueryable<T> query, string operationName = null)
        {
            operationName ??= $"FirstOrDefault_{typeof(T).Name}";
            return await _busyService.ExecuteWhileBusyAsync(() => query.FirstOrDefaultAsync(), operationName);
        }

        /// <summary>
        /// Executes FirstOrDefaultAsync with predicate while setting the busy state.
        /// </summary>
        /// <typeparam name="T">The type of the result.</typeparam>
        /// <param name="query">The query to execute.</param>
        /// <param name="predicate">The predicate to filter by.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The first entity or null.</returns>
        public static async Task<T?> FirstOrDefaultAsyncWithBusy<T>(this IQueryable<T> query, System.Linq.Expressions.Expression<System.Func<T, bool>> predicate, string operationName = null)
        {
            operationName ??= $"FirstOrDefault_{typeof(T).Name}";
            return await _busyService.ExecuteWhileBusyAsync(() => query.FirstOrDefaultAsync(predicate), operationName);
        }

        /// <summary>
        /// Executes AnyAsync while setting the busy state.
        /// </summary>
        /// <typeparam name="T">The type of the entity.</typeparam>
        /// <param name="query">The query to execute.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>True if any entity matches the condition.</returns>
        public static async Task<bool> AnyAsyncWithBusy<T>(this IQueryable<T> query, string operationName = null)
        {
            operationName ??= $"Any_{typeof(T).Name}";
            return await _busyService.ExecuteWhileBusyAsync(() => query.AnyAsync(), operationName);
        }

        /// <summary>
        /// Executes AnyAsync with predicate while setting the busy state.
        /// </summary>
        /// <typeparam name="T">The type of the entity.</typeparam>
        /// <param name="query">The query to execute.</param>
        /// <param name="predicate">The predicate to filter by.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>True if any entity matches the condition.</returns>
        public static async Task<bool> AnyAsyncWithBusy<T>(this IQueryable<T> query, System.Linq.Expressions.Expression<System.Func<T, bool>> predicate, string operationName = null)
        {
            operationName ??= $"Any_{typeof(T).Name}";
            return await _busyService.ExecuteWhileBusyAsync(() => query.AnyAsync(predicate), operationName);
        }

        /// <summary>
        /// Executes SumAsync while setting the busy state.
        /// </summary>
        /// <param name="query">The query to execute.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The sum of the values.</returns>
        public static async Task<decimal> SumAsyncWithBusy(this IQueryable<decimal> query, string operationName = null)
        {
            operationName ??= "Sum_Decimal";
            return await _busyService.ExecuteWhileBusyAsync(() => query.SumAsync(), operationName);
        }

        /// <summary>
        /// Executes SumAsync for nullable decimal while setting the busy state.
        /// </summary>
        /// <param name="query">The query to execute.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The sum of the values or null if no values.</returns>
        public static async Task<decimal?> SumAsyncWithBusy(this IQueryable<decimal?> query, string operationName = null)
        {
            operationName ??= "Sum_NullableDecimal";
            return await _busyService.ExecuteWhileBusyAsync(() => query.SumAsync(), operationName);
        }

        /// <summary>
        /// Executes SumAsync for int while setting the busy state.
        /// </summary>
        /// <param name="query">The query to execute.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The sum of the values.</returns>
        public static async Task<int> SumAsyncWithBusy(this IQueryable<int> query, string operationName = null)
        {
            operationName ??= "Sum_Int";
            return await _busyService.ExecuteWhileBusyAsync(() => query.SumAsync(), operationName);
        }

        /// <summary>
        /// Executes SumAsync for nullable int while setting the busy state.
        /// </summary>
        /// <param name="query">The query to execute.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The sum of the values or null if no values.</returns>
        public static async Task<int?> SumAsyncWithBusy(this IQueryable<int?> query, string operationName = null)
        {
            operationName ??= "Sum_NullableInt";
            return await _busyService.ExecuteWhileBusyAsync(() => query.SumAsync(), operationName);
        }

        /// <summary>
        /// Executes SumAsync for long while setting the busy state.
        /// </summary>
        /// <param name="query">The query to execute.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The sum of the values.</returns>
        public static async Task<long> SumAsyncWithBusy(this IQueryable<long> query, string operationName = null)
        {
            operationName ??= "Sum_Long";
            return await _busyService.ExecuteWhileBusyAsync(() => query.SumAsync(), operationName);
        }

        /// <summary>
        /// Executes SumAsync for nullable long while setting the busy state.
        /// </summary>
        /// <param name="query">The query to execute.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The sum of the values or null if no values.</returns>
        public static async Task<long?> SumAsyncWithBusy(this IQueryable<long?> query, string operationName = null)
        {
            operationName ??= "Sum_NullableLong";
            return await _busyService.ExecuteWhileBusyAsync(() => query.SumAsync(), operationName);
        }

        /// <summary>
        /// Executes SumAsync for double while setting the busy state.
        /// </summary>
        /// <param name="query">The query to execute.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The sum of the values.</returns>
        public static async Task<double> SumAsyncWithBusy(this IQueryable<double> query, string operationName = null)
        {
            operationName ??= "Sum_Double";
            return await _busyService.ExecuteWhileBusyAsync(() => query.SumAsync(), operationName);
        }

        /// <summary>
        /// Executes SumAsync for nullable double while setting the busy state.
        /// </summary>
        /// <param name="query">The query to execute.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The sum of the values or null if no values.</returns>
        public static async Task<double?> SumAsyncWithBusy(this IQueryable<double?> query, string operationName = null)
        {
            operationName ??= "Sum_NullableDouble";
            return await _busyService.ExecuteWhileBusyAsync(() => query.SumAsync(), operationName);
        }

        /// <summary>
        /// Executes SumAsync for float while setting the busy state.
        /// </summary>
        /// <param name="query">The query to execute.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The sum of the values.</returns>
        public static async Task<float> SumAsyncWithBusy(this IQueryable<float> query, string operationName = null)
        {
            operationName ??= "Sum_Float";
            return await _busyService.ExecuteWhileBusyAsync(() => query.SumAsync(), operationName);
        }

        /// <summary>
        /// Executes SumAsync for nullable float while setting the busy state.
        /// </summary>
        /// <param name="query">The query to execute.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The sum of the values or null if no values.</returns>
        public static async Task<float?> SumAsyncWithBusy(this IQueryable<float?> query, string operationName = null)
        {
            operationName ??= "Sum_NullableFloat";
            return await _busyService.ExecuteWhileBusyAsync(() => query.SumAsync(), operationName);
        }

        /// <summary>
        /// Executes CountAsync while setting the busy state.
        /// </summary>
        /// <typeparam name="T">The type of the entity.</typeparam>
        /// <param name="query">The query to execute.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The count of entities.</returns>
        public static async Task<int> CountAsyncWithBusy<T>(this IQueryable<T> query, string operationName = null)
        {
            operationName ??= $"Count_{typeof(T).Name}";
            return await _busyService.ExecuteWhileBusyAsync(() => query.CountAsync(), operationName);
        }

        /// <summary>
        /// Executes CountAsync with predicate while setting the busy state.
        /// </summary>
        /// <typeparam name="T">The type of the entity.</typeparam>
        /// <param name="query">The query to execute.</param>
        /// <param name="predicate">The predicate to filter by.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The count of entities matching the predicate.</returns>
        public static async Task<int> CountAsyncWithBusy<T>(this IQueryable<T> query, System.Linq.Expressions.Expression<System.Func<T, bool>> predicate, string operationName = null)
        {
            operationName ??= $"Count_{typeof(T).Name}";
            return await _busyService.ExecuteWhileBusyAsync(() => query.CountAsync(predicate), operationName);
        }

        /// <summary>
        /// Executes MaxAsync while setting the busy state.
        /// </summary>
        /// <typeparam name="T">The type of the entity.</typeparam>
        /// <typeparam name="TResult">The type of the result.</typeparam>
        /// <param name="query">The query to execute.</param>
        /// <param name="selector">The selector expression.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The maximum value.</returns>
        public static async Task<TResult> MaxAsyncWithBusy<T, TResult>(this IQueryable<T> query, System.Linq.Expressions.Expression<System.Func<T, TResult>> selector, string operationName = null)
        {
            operationName ??= $"Max_{typeof(T).Name}";
            return await _busyService.ExecuteWhileBusyAsync(() => query.MaxAsync(selector), operationName);
        }

        /// <summary>
        /// Executes MinAsync while setting the busy state.
        /// </summary>
        /// <typeparam name="T">The type of the entity.</typeparam>
        /// <typeparam name="TResult">The type of the result.</typeparam>
        /// <param name="query">The query to execute.</param>
        /// <param name="selector">The selector expression.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The minimum value.</returns>
        public static async Task<TResult> MinAsyncWithBusy<T, TResult>(this IQueryable<T> query, System.Linq.Expressions.Expression<System.Func<T, TResult>> selector, string operationName = null)
        {
            operationName ??= $"Min_{typeof(T).Name}";
            return await _busyService.ExecuteWhileBusyAsync(() => query.MinAsync(selector), operationName);
        }

        /// <summary>
        /// Executes AverageAsync while setting the busy state.
        /// </summary>
        /// <param name="query">The query to execute.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The average of the values.</returns>
        public static async Task<decimal> AverageAsyncWithBusy(this IQueryable<decimal> query, string operationName = null)
        {
            operationName ??= "Average_Decimal";
            return await _busyService.ExecuteWhileBusyAsync(() => query.AverageAsync(), operationName);
        }

        /// <summary>
        /// Executes AverageAsync for nullable decimal while setting the busy state.
        /// </summary>
        /// <param name="query">The query to execute.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The average of the values or null if no values.</returns>
        public static async Task<decimal?> AverageAsyncWithBusy(this IQueryable<decimal?> query, string operationName = null)
        {
            operationName ??= "Average_NullableDecimal";
            return await _busyService.ExecuteWhileBusyAsync(() => query.AverageAsync(), operationName);
        }

        /// <summary>
        /// Executes ToDictionaryAsync while setting the busy state.
        /// </summary>
        /// <typeparam name="TSource">The type of the source elements.</typeparam>
        /// <typeparam name="TKey">The type of the key.</typeparam>
        /// <param name="query">The query to execute.</param>
        /// <param name="keySelector">A function to extract a key from each element.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>A dictionary containing the elements.</returns>
        public static async Task<Dictionary<TKey, TSource>> ToDictionaryAsyncWithBusy<TSource, TKey>(this IQueryable<TSource> query, System.Linq.Expressions.Expression<System.Func<TSource, TKey>> keySelector, string operationName = null) where TKey : notnull
        {
            operationName ??= $"ToDictionary_{typeof(TSource).Name}";
            return await _busyService.ExecuteWhileBusyAsync(() => query.ToDictionaryAsync(keySelector.Compile()), operationName);
        }

        /// <summary>
        /// Adds an entity synchronously while setting the busy state.
        /// </summary>
        /// <typeparam name="T">The type of the entity.</typeparam>
        /// <param name="dbSet">The DbSet to add to.</param>
        /// <param name="entity">The entity to add.</param>
        /// <returns>The added entity.</returns>
        public static T AddWithBusy<T>(this DbSet<T> dbSet, T entity) where T : class
        {
            string operationName = $"Add_{typeof(T).Name}";
            return _busyService.ExecuteWhileBusy(() =>
            {
                var entry = dbSet.Add(entity);
                return entry.Entity;
            }, operationName);
        }

        /// <summary>
        /// Saves changes synchronously while setting the busy state.
        /// </summary>
        /// <param name="context">The database context.</param>
        /// <param name="operationName">A name for the operation (for logging and tracking).</param>
        /// <returns>The number of state entries written to the database.</returns>
        public static int SaveChangesWithBusy(this AppDbContext context, string operationName = null)
        {
            operationName ??= "SaveChanges";
            return _busyService.ExecuteWhileBusy(() => context.SaveChanges(), operationName);
        }

        /// <summary>
        /// Adds multiple entities to the database while setting the busy state.
        /// </summary>
        /// <typeparam name="T">The type of the entities.</typeparam>
        /// <param name="dbSet">The DbSet to add to.</param>
        /// <param name="entities">The entities to add.</param>
        /// <returns>Task representing the async operation.</returns>
        public static async Task AddRangeAsyncWithBusy<T>(this DbSet<T> dbSet, IEnumerable<T> entities) where T : class
        {
            string operationName = $"AddRange_{typeof(T).Name}";
            await _busyService.ExecuteWhileBusyAsync(async () =>
            {
                await dbSet.AddRangeAsync(entities);
            }, operationName);
        }

        /// <summary>
        /// Removes multiple entities from the database while setting the busy state.
        /// </summary>
        /// <typeparam name="T">The type of the entities.</typeparam>
        /// <param name="dbSet">The DbSet to remove from.</param>
        /// <param name="entities">The entities to remove.</param>
        public static void RemoveRangeWithBusy<T>(this DbSet<T> dbSet, IEnumerable<T> entities) where T : class
        {
            string operationName = $"RemoveRange_{typeof(T).Name}";
            _busyService.ExecuteWhileBusy(() =>
            {
                dbSet.RemoveRange(entities);
            }, operationName);
        }

    }
}
