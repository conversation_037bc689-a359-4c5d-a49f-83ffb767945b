﻿﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;

namespace VisionPoint.UI.Services
{
    /// <summary>
    /// Service for managing the global busy state of the application.
    /// This service is used to indicate when the application is performing operations
    /// that should block user interaction.
    /// </summary>
    public class BusyService : INotifyPropertyChanged
    {
        private bool _isBusy;
        private readonly Dictionary<string, OperationInfo> _busyOperations = new Dictionary<string, OperationInfo>();
        private readonly object _lock = new object();
        private static readonly Dispatcher _dispatcher = Application.Current.Dispatcher;
        private string _currentMessage = "جاري تنفيذ العملية...";

        /// <summary>
        /// Gets or sets a value indicating whether the application is busy.
        /// </summary>
        public bool IsBusy
        {
            get => _isBusy;
            private set
            {
                if (_isBusy != value)
                {
                    _isBusy = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Gets the current operation message.
        /// </summary>
        public string CurrentMessage
        {
            get => _currentMessage;
            private set
            {
                if (_currentMessage != value)
                {
                    _currentMessage = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Class to store information about an operation.
        /// </summary>
        private class OperationInfo
        {
            public string Message { get; set; } = "جاري تنفيذ العملية...";
            public DateTime StartTime { get; set; } = DateTime.Now;
        }

        /// <summary>
        /// Sets the busy state for a specific operation.
        /// </summary>
        /// <param name="operationKey">A unique key identifying the operation.</param>
        /// <param name="isBusy">Whether the operation is busy.</param>
        /// <param name="message">Optional message describing the operation.</param>
        public void SetBusy(string operationKey, bool isBusy, string message = null)
        {
            lock (_lock)
            {
                if (isBusy)
                {
                    _busyOperations[operationKey] = new OperationInfo
                    {
                        Message = message ?? "جاري تنفيذ العملية...",
                        StartTime = DateTime.Now
                    };
                }
                else if (_busyOperations.ContainsKey(operationKey))
                {
                    _busyOperations.Remove(operationKey);
                }

                // Update the global busy state
                IsBusy = _busyOperations.Count > 0;

                // Update the current message to the most recent operation
                if (_busyOperations.Count > 0)
                {
                    // Get the most recent operation
                    var mostRecent = _busyOperations.OrderByDescending(o => o.Value.StartTime).First();
                    CurrentMessage = mostRecent.Value.Message;
                }
                else
                {
                    CurrentMessage = "جاري تنفيذ العملية...";
                }
            }
        }

        /// <summary>
        /// Executes an action while setting the busy state.
        /// </summary>
        /// <param name="action">The action to execute.</param>
        /// <param name="operationKey">A unique key identifying the operation.</param>
        /// <param name="message">Optional message describing the operation.</param>
        public void ExecuteWhileBusy(Action action, string operationKey = null, string message = null)
        {
            operationKey ??= Guid.NewGuid().ToString();

            try
            {
                SetBusy(operationKey, true, message);
                action();
            }
            finally
            {
                SetBusy(operationKey, false);
            }
        }

        /// <summary>
        /// Executes a function while setting the busy state.
        /// </summary>
        /// <typeparam name="T">The return type of the function.</typeparam>
        /// <param name="func">The function to execute.</param>
        /// <param name="operationKey">A unique key identifying the operation.</param>
        /// <param name="message">Optional message describing the operation.</param>
        /// <returns>The result of the function.</returns>
        public T ExecuteWhileBusy<T>(Func<T> func, string operationKey = null, string message = null)
        {
            operationKey ??= Guid.NewGuid().ToString();

            try
            {
                SetBusy(operationKey, true, message);
                return func();
            }
            finally
            {
                SetBusy(operationKey, false);
            }
        }

        /// <summary>
        /// Executes an async function while setting the busy state.
        /// </summary>
        /// <typeparam name="T">The return type of the function.</typeparam>
        /// <param name="func">The async function to execute.</param>
        /// <param name="operationKey">A unique key identifying the operation.</param>
        /// <param name="message">Optional message describing the operation.</param>
        /// <returns>A task representing the async operation.</returns>
        public async Task<T> ExecuteWhileBusyAsync<T>(Func<Task<T>> func, string operationKey = null, string message = null)
        {
            operationKey ??= Guid.NewGuid().ToString();

            try
            {
                SetBusy(operationKey, true, message);
                return await func();
            }
            finally
            {
                SetBusy(operationKey, false);
            }
        }

        /// <summary>
        /// Executes an async action while setting the busy state.
        /// </summary>
        /// <param name="func">The async action to execute.</param>
        /// <param name="operationKey">A unique key identifying the operation.</param>
        /// <param name="message">Optional message describing the operation.</param>
        /// <returns>A task representing the async operation.</returns>
        public async Task ExecuteWhileBusyAsync(Func<Task> func, string operationKey = null, string message = null)
        {
            operationKey ??= Guid.NewGuid().ToString();

            try
            {
                SetBusy(operationKey, true, message);
                await func();
            }
            finally
            {
                SetBusy(operationKey, false);
            }
        }

        /// <summary>
        /// Clears all busy operations and resets the busy state.
        /// </summary>
        public void ClearAllBusyOperations()
        {
            lock (_lock)
            {
                _busyOperations.Clear();
                IsBusy = false;
                CurrentMessage = "جاري تنفيذ العملية...";
            }
        }

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
