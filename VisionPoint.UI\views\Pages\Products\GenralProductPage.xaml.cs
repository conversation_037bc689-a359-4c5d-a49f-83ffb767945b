﻿using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using VisionPoint.UI.Helper.ImportExport;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Dialogs;
using VisionPoint.UI.views.Pages.ProductsContent;
using VisionPoint.UI.views.Pages.Warehouses;

namespace VisionPoint.UI.views.Pages.Products
{
    /// <summary>
    /// Interaction logic for GenralProductPage.xaml
    /// </summary>
    public partial class GenralProductPage : Page
    {
        private readonly ProductService _productService;
        private readonly WarehouseService _warehouseService;
        private ProductVM _selectedProduct = new();
        private int? _selectedWarehouseId = null;
        public GenralProductPage()
        {
            InitializeComponent();
            _productService = new ProductService();
            _warehouseService = new WarehouseService();
        }

        private async void Page_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadWarehouses();
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnSave.IsEnabled = false;
            btnNew.IsEnabled = false;
            btnDelete.IsEnabled = false;
            btnSearch.IsEnabled = false;
            btnAddValues.IsEnabled = false;
            btnImport.IsEnabled = false;
            btnExport.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnSave.IsEnabled = true;
            btnNew.IsEnabled = true;
            btnDelete.IsEnabled = true;
            btnSearch.IsEnabled = true;
            btnAddValues.IsEnabled = true;
            btnImport.IsEnabled = true;
            btnExport.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private async void btnSave_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق من صلاحيات المستخدم
                bool canAdd = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ProductRole");
                bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditProductRole");

            // إذا كان منتج جديد، نحتاج صلاحية الإضافة
            if (_selectedProduct.Id == 0 && !canAdd)
            {
                ErrorBox.Show("لا تملك صلاحية إضافة المنتجات", "خطأ في الصلاحيات", true);
                return;
            }

            // إذا كان تعديل منتج موجود، نحتاج صلاحية التعديل
            if (_selectedProduct.Id != 0 && !canEdit)
            {
                ErrorBox.Show("لا تملك صلاحية تعديل المنتجات", "خطأ في الصلاحيات", true);
                return;
            }

            if (string.IsNullOrEmpty(txtName.Text.Trim()))
            {
                ErrorBox.Show("الرجاء إدخال اسم المنتج", "خطأ", true);
                return;
            }
            if (string.IsNullOrEmpty(txtSellPrice.Text.Trim()))
            {
                ErrorBox.Show("الرجاء إدخال سعر البيع", "خطأ", true);
                return;
            }

            if (_selectedProduct.Id != 0 && _selectedProduct.Color != chkColor.IsChecked)
            {
                if (QuestionBox.Show($"سيتم حذف {(_selectedProduct.Color ? "جميع الألوان" : "الباركود")} هل انت متاكد من تغيير حالة اللون؟", "تنبيه") == MessageBoxResult.No)
                    return;
            }

                _selectedProduct.Name = txtName.Text.Trim();
                _selectedProduct.SellPrice = decimal.Parse(txtSellPrice.Text);
                _selectedProduct.Exp = chkExpire.IsChecked ?? false;
                _selectedProduct.Color = chkColor.IsChecked ?? false;
                _selectedProduct.MinimumQuantity = !string.IsNullOrEmpty(txtMinimumQuantity.Text) ? int.Parse(txtMinimumQuantity.Text) : 0;

                var result = _selectedProduct.Id == 0
                    ? await _productService.AddProductAsync(_selectedProduct)
                    : await _productService.UpdateProductAsync(_selectedProduct);

                if (result.State)
                {
                    DialogBox.Show(result.Message, "نجاح");
                    btnNew_MouseLeftButtonDown(sender, e);
                    await LoadData();
                }
                else
                {
                    ErrorBox.Show(result.Message, "خطأ", true);
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnNew_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                _selectedProduct = new();
                txtName.Clear();
                txtCostPrice.Clear();
                txtSellPrice.Clear();
                txtMinimumQuantity.Clear();
                chkExpire.IsChecked = false;
                chkColor.IsChecked = false;
                list.SelectedItem = null;
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnDelete_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق من صلاحية الحذف
                bool canDelete = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("DeleteProductRole");
                if (!canDelete)
                {
                    ErrorBox.Show("لا تملك صلاحية حذف المنتجات", "خطأ في الصلاحيات", true);
                    return;
                }

                if (_selectedProduct.Id == 0)
                {
                    ErrorBox.Show("الرجاء اختيار منتج للحذف", "تنبيه", false);
                    return;
                }

                var result = DeleteBox.Show("هل انت متاكد من حذف منتج", _selectedProduct.Name);
                if (result == true)
                {
                    var deleteResult = await _productService.DeleteProductAsync(_selectedProduct.Id);
                    if (deleteResult.State)
                    {
                        DialogBox.Show("تم الحذف", "نجاح");
                        btnNew_MouseLeftButtonDown(sender, e);
                        await LoadData();
                    }
                    else
                    {
                        ErrorBox.Show(deleteResult.Message, "خطأ", true);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء حذف المنتج: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async Task LoadWarehouses()
        {
            try
            {
                var warehouses = await _warehouseService.GetAllWarehousesAsync();

                // إضافة خيار "جميع المخازن" في البداية
                var allWarehouses = new List<Warehouse>
                {
                    new Warehouse { Id = 0, Name = "جميع المخازن" }
                };
                allWarehouses.AddRange(warehouses);

                cmbWarehouse.ItemsSource = allWarehouses;

                // تحديد المخزن الافتراضي للمستخدم الحالي
                if (CurrentUser.WarehouseId.HasValue)
                {
                    // اختيار مخزن المستخدم الحالي
                    cmbWarehouse.SelectedValue = CurrentUser.WarehouseId.Value;
                    _selectedWarehouseId = CurrentUser.WarehouseId.Value;
                }
                else
                {
                    // إذا لم يكن له مخزن محدد، نختار "جميع المخازن"
                    cmbWarehouse.SelectedIndex = 0;
                    _selectedWarehouseId = null;
                }

                // تطبيق منطق الصلاحيات لتغيير المخزن
                // إذا لم يكن المستخدم مديراً أو لا يملك صلاحية تغيير المخزن، يتم تعطيل الكومبو
                cmbWarehouse.IsEnabled = CurrentUser.CanChangeWarehouse;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل المخازن: {ex.Message}", "خطأ", true);
            }
        }

        private async Task LoadData()
        {
            try
            {
                // The BusyService is automatically set by the ProductService
                var products = await _productService.GetAllProductVMsAsync(_selectedWarehouseId);
                list.ItemsSource = products;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", true);
            }
        }

        private async void cmbWarehouse_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // التحقق من أن المستخدم لديه صلاحية تغيير المخزن أو أن هذا هو التحديد الأولي
            if (cmbWarehouse.SelectedValue != null && (CurrentUser.CanChangeWarehouse || _selectedWarehouseId == null))
            {
                var selectedId = (int)cmbWarehouse.SelectedValue;
                _selectedWarehouseId = selectedId == 0 ? null : selectedId;
                await LoadData();
            }
        }


        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (list.View is GridView gridView && gridView.Columns.Count >= 5)
            {
                double totalWeight = 0.5 + 2.5 + 1.5 + 1.5 + 1.0;
                double scrollbarWidth = SystemParameters.VerticalScrollBarWidth;
                double padding = 10;
                double availableWidth = list.ActualWidth - scrollbarWidth - padding;

                double unitWidth = availableWidth / totalWeight;

                gridView.Columns[0].Width = unitWidth * 0.5; // Index
                gridView.Columns[1].Width = unitWidth * 2.5; // Product Name
                gridView.Columns[2].Width = unitWidth * 1.5; // Cost Price
                gridView.Columns[3].Width = unitWidth * 1.5; // Sell Price
                gridView.Columns[4].Width = unitWidth * 1.0; // Quantity
            }
        }





        private void btnAddValues_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (_selectedProduct.Id == 0)
                {
                    ErrorBox.Show("الرجاء اختيار منتج أولاً", "تنبيه", false);
                    return;
                }

                GenralProductPrescriptionPage prescriptionPage = new(_selectedProduct);
                prescriptionPage.ShowDialog();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnImport_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // استخدام المساعد الجديد لعملية الاستيراد
                var importHelper = new ProductImportHelper();
                var result = await importHelper.StartImportProcess();

                if (result)
                {
                    // إذا تمت العملية بنجاح، نقوم بتحديث البيانات
                    await LoadData();
                    btnNew_MouseLeftButtonDown(sender, e);
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء استيراد البيانات: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnExport_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق من وجود بيانات للتصدير
                if (list.ItemsSource is not List<ProductVM> products || !products.Any())
                {
                    ErrorBox.Show("لا توجد بيانات منتجات للتصدير", "تحذير", true);
                    return;
                }
              var models=  await _productService.GetProductsDetailedForExportAsync(_selectedWarehouseId);
                // عرض شاشة التحميل أثناء تحضير البيانات
                await ShowExportLoadingAndPrepareData(models);
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تصدير البيانات: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        /// <summary>
        /// عرض شاشة التحميل وتحضير البيانات للتصدير
        /// </summary>
        private async Task ShowExportLoadingAndPrepareData(List<ProductExportVM> products)
        {
            try
            {
                // فتح نافذة معاينة التصدير مع البيانات المحضرة
                var exportPreviewWindow = new ProductExportPreviewWindow(products, _selectedWarehouseId, cmbWarehouse.Text);
                exportPreviewWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                throw; // إعادة رفع الاستثناء ليتم التعامل معه في الدالة الأصلية
            }
        }

        private async void btnSearch_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (string.IsNullOrEmpty(txtSearch.Text.Trim()))
                {
                    await LoadData();
                    return;
                }

                var products = await _productService.SearchVMByNameAsync(txtSearch.Text.Trim(), _selectedWarehouseId);
                list.ItemsSource = products;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }
        private void list_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (list.SelectedItem is ProductVM selectedProduct)
            {
                _selectedProduct = selectedProduct;
                txtName.Text = selectedProduct.Name;
                txtSellPrice.Text = selectedProduct.SellPrice.ToString();
                txtCostPrice.Text = selectedProduct.CostPrice.ToString();
                txtMinimumQuantity.Text = selectedProduct.MinimumQuantity.ToString();
                chkExpire.IsChecked = selectedProduct.Exp;
                chkColor.IsChecked = selectedProduct.Color;
            }
            else
            {
                btnNew_MouseLeftButtonDown(sender, e);
            }
        }

        private void TextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                textBox.SelectAll();
            }
        }
    }
}
