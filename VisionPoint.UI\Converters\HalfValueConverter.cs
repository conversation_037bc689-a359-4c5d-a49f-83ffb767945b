﻿using System;
using System.Globalization;
using System.Windows.Data;

namespace VisionPoint.UI.Converters
{
    public class HalfValueConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double doubleValue)
            {
                return doubleValue / 2.0;
            }
            // Return a default value or throw an exception for unsupported types
            return 0.0;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // This converter is used for one-way binding, so ConvertBack is not needed.
            throw new NotImplementedException();
        }
    }
}
