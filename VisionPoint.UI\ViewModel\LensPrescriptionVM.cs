﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VisionPoint.UI.Models;

namespace VisionPoint.UI.ViewModel;

public class LensPrescriptionVM
{
    public int Id { get; set; }
    [Precision(18, 3)] public decimal CostPrice { get; set; } = decimal.Zero;
    [Precision(18, 3)] public decimal SellPrice { get; set; } = decimal.Zero;
    public ICollection<LensPrescriptionColorVM>? LensPrescriptionColors { get; set; } = new List<LensPrescriptionColorVM>();
}
