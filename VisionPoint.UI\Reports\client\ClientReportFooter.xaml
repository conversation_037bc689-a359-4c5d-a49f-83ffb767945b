﻿<Page
    x:Class="VisionPoint.UI.Reports.client.ClientReportFooter"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.Reports.client"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="ClientReportFooter"
    Width="1080"
    Height="250"
    Background="White"
    FlowDirection="RightToLeft"
    mc:Ignorable="d">

    <Grid Margin="32,0">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <Border
            Grid.RowSpan="2"
            Grid.ColumnSpan="8"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1">

            <Grid Margin="16,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  First row with light gray background  -->
                <Border Grid.ColumnSpan="3" Background="LightGray">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <TextBlock
                            Grid.Column="0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="18"
                            Text="مجموع الدين" />
                        <TextBlock
                            Grid.Column="1"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="18"
                            Text="مجموع الدائن" />
                        <TextBlock
                            Grid.Column="2"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="18"
                            Text="الرصيد النهائي" />
                    </Grid>
                </Border>

                <!--  Second row with updated names  -->
                <TextBlock
                    x:Name="txtDebtTotal"
                    Grid.Row="1"
                    Grid.Column="0"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="18"
                    Text="24/4/2025" />
                <TextBlock
                    x:Name="txtCreditorTotal"
                    Grid.Row="1"
                    Grid.Column="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="18"
                    Text="06:00" />
                <TextBlock
                    x:Name="txtFinalBalance"
                    Grid.Row="1"
                    Grid.Column="2"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="18"
                    Text="06:00" />
            </Grid>



        </Border>

        <DockPanel
            Grid.RowSpan="2"
            Grid.Column="2"
            Grid.ColumnSpan="2"
            Margin="16,0"
            LastChildFill="False" />


        <DockPanel
            Grid.RowSpan="2"
            Grid.Column="4"
            Grid.ColumnSpan="2"
            Margin="16,0"
            LastChildFill="False" />






        <TextBlock
            x:Name="txtNote"
            Grid.Row="2"
            Grid.ColumnSpan="10"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            d:Text="حالة الرصيد"
            FontSize="18"
            TextAlignment="Center" />

        <TextBlock
            Grid.Row="3"
            Grid.ColumnSpan="2"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            d:Text="توقيع المحاسب"
            FontSize="18"
            TextAlignment="Center" />
        <TextBlock
            Grid.Row="4"
            Grid.ColumnSpan="2"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            d:Text="............."
            FontSize="18"
            TextAlignment="Center" />
    </Grid>
</Page>

