﻿﻿using System;
using System.Threading;
using System.Threading.Tasks;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.Services
{
    /// <summary>
    /// خدمة لتنفيذ المهام في الخلفية بشكل دوري
    /// </summary>
    public class BackgroundTaskService : IDisposable
    {
        private readonly SalaryService _salaryService;
        private Timer _salaryTimer;
        private bool _isProcessing = false;

        public BackgroundTaskService(SalaryService salaryService)
        {
            _salaryService = salaryService;
        }

        /// <summary>
        /// بدء تشغيل خدمة المهام الخلفية
        /// </summary>
        public void Start()
        {
            // تحقق من المرتبات عند بدء التشغيل
            Task.Run(async () => await CheckAndProcessSalaries());

            // إعداد المؤقت للتحقق من المرتبات كل يوم
            // سيتم التحقق كل 24 ساعة (86400000 مللي ثانية)
            _salaryTimer = new Timer(async _ => await CheckAndProcessSalaries(), null, TimeSpan.FromHours(24), TimeSpan.FromHours(24));
        }

        /// <summary>
        /// التحقق من المرتبات ومعالجتها إذا لزم الأمر
        /// </summary>
        private async Task CheckAndProcessSalaries()
        {
            // تجنب تنفيذ العملية إذا كانت قيد التنفيذ بالفعل
            if (_isProcessing)
                return;

            try
            {
                _isProcessing = true;

                // الحصول على التاريخ الحالي
                DateTime now = DateTime.Now;
                int currentMonth = now.Month;
                int currentYear = now.Year;
                int currentDay = now.Day;

                // التحقق مما إذا كان اليوم هو اليوم الأول من الشهر
                // يمكن تغيير هذا الشرط حسب متطلبات العمل
                if (currentDay <=14)
                {
                    // معالجة المرتبات للشهر الحالي
                    var result = await _salaryService.ProcessMonthlySalariesAsync(currentMonth, currentYear);

                    // تسجيل النتيجة (يمكن إضافة إشعار للمستخدم إذا لزم الأمر)
                    if (result.success && result.processedCount > 0)
                    {
                        Console.WriteLine($"تم إضافة المرتبات بنجاح: {result.message}");
                    }
                    else if (!result.success)
                    {
                        Console.WriteLine($"فشل في إضافة المرتبات: {result.message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ أثناء معالجة المرتبات: {ex.Message}");
            }
            finally
            {
                _isProcessing = false;
            }
        }

        /// <summary>
        /// إيقاف خدمة المهام الخلفية
        /// </summary>
        public void Stop()
        {
            _salaryTimer?.Change(Timeout.Infinite, Timeout.Infinite);
        }

        /// <summary>
        /// التخلص من الموارد
        /// </summary>
        public void Dispose()
        {
            _salaryTimer?.Dispose();
        }
    }
}
