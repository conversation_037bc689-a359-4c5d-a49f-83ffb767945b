using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.Services;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.ImportExport
{
    /// <summary>
    /// نافذة اختيار المخزن للتصدير
    /// </summary>
    public partial class WarehouseSelectionWindow : Window
    {
        private readonly WarehouseService _warehouseService;
        private List<object> _warehouseItems;

        public int? SelectedWarehouseId { get; private set; }
        public bool IsAllWarehousesSelected { get; private set; }
        public bool DialogResult { get; private set; }

        public WarehouseSelectionWindow()
        {
            InitializeComponent();
            _warehouseService = new WarehouseService();
            _warehouseItems = new List<object>();
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadWarehouses();
        }

        private async System.Threading.Tasks.Task LoadWarehouses()
        {
            try
            {
                var warehouses = await _warehouseService.GetAllWarehousesAsync();
                _warehouseItems.Clear();

                // إضافة خيار "الكل" في البداية
                _warehouseItems.Add(new { Id = -1, Name = "جميع المخازن" });

                // إضافة المخازن
                foreach (var warehouse in warehouses)
                {
                    _warehouseItems.Add(new { Id = warehouse.Id, Name = warehouse.Name });
                }

                // تكوين ComboBox
                cmbWarehouse.ItemsSource = _warehouseItems;

                // تحديد المخزن الافتراضي بناءً على المستخدم الحالي
                if (CurrentUser.WarehouseId.HasValue && warehouses.Any(w => w.Id == CurrentUser.WarehouseId.Value))
                {
                    // اختيار مخزن المستخدم الحالي
                    cmbWarehouse.SelectedValue = CurrentUser.WarehouseId.Value;
                    SelectedWarehouseId = CurrentUser.WarehouseId.Value;
                    IsAllWarehousesSelected = false;
                }
                else
                {
                    // اختيار "جميع المخازن" افتراضياً
                    cmbWarehouse.SelectedIndex = 0;
                    SelectedWarehouseId = null;
                    IsAllWarehousesSelected = true;
                }

                // تطبيق منطق الصلاحيات لتغيير المخزن
                // إذا لم يكن المستخدم مديراً أو لا يملك صلاحية تغيير المخزن، يتم تعطيل الكومبو
                cmbWarehouse.IsEnabled = CurrentUser.CanChangeWarehouse;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل المخازن: {ex.Message}", "خطأ", true);
            }
        }

        private void cmbWarehouse_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (cmbWarehouse.SelectedValue != null)
            {
                int selectedId = (int)cmbWarehouse.SelectedValue;

                if (selectedId == -1)
                {
                    // تم اختيار "جميع المخازن"
                    SelectedWarehouseId = null;
                    IsAllWarehousesSelected = true;
                }
                else
                {
                    // تم اختيار مخزن محدد
                    SelectedWarehouseId = selectedId;
                    IsAllWarehousesSelected = false;
                }
            }
        }

        private void btnConfirm_Click(object sender, MouseButtonEventArgs e)
        {
            // التحقق من أن المستخدم اختار شيئاً
            if (cmbWarehouse.SelectedValue == null)
            {
                ErrorBox.Show("يرجى اختيار مخزن", "تنبيه", false);
                return;
            }

            DialogResult = true;
            Close();
        }

        private void btnCancel_Click(object sender, MouseButtonEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
