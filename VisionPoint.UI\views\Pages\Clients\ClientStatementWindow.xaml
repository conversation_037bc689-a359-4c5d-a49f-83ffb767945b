﻿<Window
    x:Class="VisionPoint.UI.views.Pages.Clients.ClientStatementWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converter="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:VisionPoint.UI"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Clients"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils"
    Title="كشف حساب العميل"
    Background="Transparent"
    FlowDirection="RightToLeft"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <converter:NonZeroToBoldConverter x:Key="NonZeroToBoldConverter" />
            <converter:BalanceToColorConverter x:Key="BalanceToColorConverter" />
        </ResourceDictionary>
    </Window.Resources>
    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">
        <Border
            Width="1920"
            Height="1080"
            Padding="24"
            Background="{StaticResource backgroundColor}"
            CornerRadius="24">


            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="1.3*" />
                </Grid.RowDefinitions>

                <Border
                    x:Name="btnclose"
                    Width="24"
                    Height="24"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Top"
                    Background="Red"
                    CornerRadius="50"
                    MouseLeftButtonDown="btnclose_MouseLeftButtonDown" />

                <!--  معلومات العميل  -->
                <StackPanel>
                    <TextBlock
                        x:Name="txtClientName"
                        HorizontalAlignment="Center"
                        d:Text="زبون نقدي"
                        FontSize="24"
                        FontWeight="Bold" />
                    <TextBlock
                        x:Name="txtClientBalance"
                        HorizontalAlignment="Center"
                        d:Text="زبون نقدي"
                        FontSize="18" />
                </StackPanel>

                <!--  فلاتر التاريخ  -->
                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>




                    <DatePicker
                        x:Name="dpFromDate"
                        Grid.ColumnSpan="2"
                        Height="60"
                        Margin="8,0"
                        BorderBrush="{StaticResource PrimaryTextColor}"
                        BorderThickness="1"
                        DisplayDateStart="2000-01-01"
                        FontSize="18"
                        SelectedDateChanged="DatePicker_SelectedDateChanged"
                        SelectedDateFormat="Short">
                        <DatePicker.Resources>
                            <Style TargetType="{x:Type DatePickerTextBox}">
                                <Setter Property="Control.Template">
                                    <Setter.Value>
                                        <ControlTemplate>
                                            <TextBox
                                                x:Name="PART_TextBox"
                                                VerticalAlignment="Stretch"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                Foreground="{StaticResource PrimaryTextColor}"
                                                IsReadOnly="True"
                                                Style="{StaticResource txtDatePick}"
                                                Tag="من تاريخ"
                                                Text="{Binding Path=SelectedDate, StringFormat='yyyy-MM-dd', RelativeSource={RelativeSource AncestorType={x:Type DatePicker}}}" />
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </DatePicker.Resources>
                    </DatePicker>


                    <DatePicker
                        x:Name="dpToDate"
                        Grid.Column="2"
                        Grid.ColumnSpan="2"
                        Height="60"
                        Margin="8,0"
                        BorderBrush="{StaticResource PrimaryTextColor}"
                        BorderThickness="1"
                        DisplayDateStart="2000-01-01"
                        FontSize="18"
                        SelectedDateChanged="DatePicker_SelectedDateChanged"
                        SelectedDateFormat="Short">
                        <DatePicker.Resources>
                            <Style TargetType="{x:Type DatePickerTextBox}">
                                <Setter Property="Control.Template">
                                    <Setter.Value>
                                        <ControlTemplate>
                                            <TextBox
                                                x:Name="PART_TextBox"
                                                VerticalAlignment="Stretch"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                Foreground="{StaticResource PrimaryTextColor}"
                                                IsReadOnly="True"
                                                Style="{StaticResource txtDatePick}"
                                                Tag="إلى تاريخ"
                                                Text="{Binding Path=SelectedDate, StringFormat='yyyy-MM-dd', RelativeSource={RelativeSource AncestorType={x:Type DatePicker}}}" />
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </DatePicker.Resources>
                    </DatePicker>

                    <Grid Grid.Column="4">
                        <TextBlock VerticalAlignment="Top" TextAlignment="Center">
                            كل الفترات
                        </TextBlock>
                        <CheckBox
                            x:Name="chkAllPeriods"
                            VerticalAlignment="Stretch"
                            Checked="chkAllPeriods_CheckedChanged"
                            Style="{StaticResource CircleCheckboxFL}"
                            Unchecked="chkAllPeriods_CheckedChanged" />
                    </Grid>




                    <Border
                        x:Name="btnApplyFilter"
                        Grid.Column="5"
                        MaxHeight="60"
                        Margin="8,0"
                        Background="{DynamicResource PrimaryColor}"
                        CornerRadius="16"
                        Cursor="Hand"
                        MouseLeftButtonDown="btnApplyFilter_Click">
                        <Label
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            Content="تحديث البيانات"
                            FontSize="16"
                            FontWeight="Bold"
                            Foreground="White" />
                    </Border>

                    <Border
                        x:Name="btnPrint"
                        Grid.Column="6"
                        MaxHeight="60"
                        Margin="8,0"
                        Background="Transparent"
                        BorderBrush="{DynamicResource PrimaryColor}"
                        BorderThickness="1"
                        CornerRadius="16"
                        Cursor="Hand"
                        MouseLeftButtonDown="btnPrint_Click">
                        <Label
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            Content="طباعة"
                            FontSize="16"
                            FontWeight="Bold"
                            Foreground="{StaticResource PrimaryColor}" />
                    </Border>

                    <Border
                        x:Name="btnExportExcel"
                        Grid.Column="7"
                        MaxHeight="60"
                        Margin="8,0"
                        Background="Transparent"
                        BorderBrush="{DynamicResource PrimaryColor}"
                        BorderThickness="1"
                        CornerRadius="16"
                        Cursor="Hand"
                        MouseLeftButtonDown="btnExportExcel_Click">
                        <Label
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            Content="تصدير إلى إكسل"
                            FontSize="16"
                            FontWeight="Bold"
                            Foreground="{StaticResource PrimaryColor}" />
                    </Border>
                </Grid>

                <!--  جدول كشف الحساب  -->
                <ListView
                    x:Name="list"
                    Grid.Row="3"
                    Grid.RowSpan="6"
                    Background="{DynamicResource PageColor}"
                    BorderThickness="1"
                    FontFamily="pack://application:,,,/Assets/#Cairo"
                    ItemsSource="{Binding}"
                    ScrollViewer.HorizontalScrollBarVisibility="Hidden"
                    SizeChanged="list_SizeChanged">
                    <ListView.BorderBrush>
                        <SolidColorBrush Opacity="0.42" Color="Black" />
                    </ListView.BorderBrush>

                    <ListView.ItemContainerStyle>
                        <Style TargetType="ListViewItem">
                            <Style.Triggers>
                                <!--  تنسيق الصفوف الخاصة (الرصيد السابق والرصيد اللاحق)  -->
                                <DataTrigger Binding="{Binding IsSpecialRow}" Value="True">
                                    <Setter Property="Control.Background" Value="#E6F0FF" />
                                    <Setter Property="FontWeight" Value="Bold" />
                                    <Setter Property="FontStyle" Value="Italic" />
                                    <Setter Property="Foreground" Value="Navy" />
                                </DataTrigger>

                                <Trigger Property="Control.IsMouseOver" Value="True">
                                    <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                                    <Setter Property="FontWeight" Value="Bold" />
                                    <Setter Property="Foreground" Value="Black" />
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                                    <Setter Property="Foreground" Value="Black" />
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="FontWeight" Value="Bold" />
                                    <Setter Property="Foreground" Value="Black" />
                                </Trigger>
                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="IsSelected" Value="False" />
                                        <Condition Property="IsMouseOver" Value="False" />
                                    </MultiTrigger.Conditions>
                                    <Setter Property="FontWeight" Value="Thin" />
                                    <Setter Property="Foreground" Value="{DynamicResource PrimaryTextColor}" />
                                </MultiTrigger>
                            </Style.Triggers>
                            <Setter Property="Control.Background" Value="{DynamicResource PageColor}" />
                            <Setter Property="Control.HorizontalContentAlignment" Value="Stretch" />
                        </Style>
                    </ListView.ItemContainerStyle>

                    <ListView.View>
                        <GridView AllowsColumnReorder="False">
                            <GridView.ColumnHeaderContainerStyle>
                                <Style BasedOn="{StaticResource ListViewHeader}" TargetType="{x:Type GridViewColumnHeader}">
                                    <Setter Property="IsEnabled" Value="False" />
                                    <Setter Property="Height" Value="60" />
                                    <Style.Triggers>
                                        <Trigger Property="IsEnabled" Value="False">
                                            <Setter Property="TextElement.Foreground" Value="Black" />
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </GridView.ColumnHeaderContainerStyle>

                            <!--  تاريخ العملية  -->
                            <GridViewColumn Width="Auto" Header="التاريخ">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="45"
                                            MinWidth="35"
                                            HorizontalAlignment="Center"
                                            Background="Transparent"
                                            Text="{Binding Date, StringFormat='{}{0:yyyy-MM-dd}', FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>

                            <!--  نوع العملية  -->
                            <GridViewColumn Width="Auto" Header="نوع العملية">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="45"
                                            MinWidth="35"
                                            HorizontalAlignment="Center"
                                            Background="Transparent"
                                            Text="{Binding OperationType, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>

                            <!--  طريقة الدفع  -->
                            <GridViewColumn Width="Auto" Header="طريقة الدفع">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="45"
                                            MinWidth="35"
                                            HorizontalAlignment="Center"
                                            Background="Transparent"
                                            Text="{Binding PaymentMethod, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>

                            <!--  البيان  -->
                            <GridViewColumn Width="Auto" Header="البيان">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="45"
                                            MinWidth="35"
                                            HorizontalAlignment="Center"
                                            Background="Transparent"
                                            Text="{Binding Description, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>

                            <!--  قبض (مبلغ داخل للعميل)  -->
                            <GridViewColumn Width="Auto" Header="قبض">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="45"
                                            MinWidth="35"
                                            HorizontalAlignment="Center"
                                            Background="Transparent"
                                            FontWeight="{Binding Incoming, Converter={StaticResource NonZeroToBoldConverter}}"
                                            Foreground="Green"
                                            Text="{Binding Incoming, StringFormat='{}{0:N3}', FallbackValue='0.000', TargetNullValue='0.000'}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>

                            <!--  صرف (مبلغ خارج من العميل)  -->
                            <GridViewColumn Width="Auto" Header="صرف">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="45"
                                            MinWidth="35"
                                            HorizontalAlignment="Center"
                                            Background="Transparent"
                                            FontWeight="{Binding Outgoing, Converter={StaticResource NonZeroToBoldConverter}}"
                                            Foreground="Red"
                                            Text="{Binding Outgoing, StringFormat='{}{0:N3}', FallbackValue='0.000', TargetNullValue='0.000'}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>

                            <!--  الرصيد  -->
                            <GridViewColumn Width="Auto" Header="الرصيد">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="45"
                                            MinWidth="35"
                                            HorizontalAlignment="Center"
                                            Background="Transparent"
                                            FontWeight="Bold"
                                            Foreground="{Binding Balance, Converter={StaticResource BalanceToColorConverter}}"
                                            Text="{Binding Balance, StringFormat='{}{0:N3}', FallbackValue='0.000', TargetNullValue='0.000'}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                        </GridView>
                    </ListView.View>
                </ListView>

                <!--  ملخص كشف الحساب  -->
                <Border
                    Grid.Row="9"
                    Padding="16"
                    Background="#F5F5F5"
                    BorderBrush="{DynamicResource PrimaryColor}"
                    BorderThickness="1"
                    CornerRadius="5">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>

                        <!--  العنوان  -->
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="0"
                            Grid.ColumnSpan="3"
                            Margin="0,0,0,16"
                            HorizontalAlignment="Center"
                            FontSize="21"
                            FontWeight="Bold"
                            Text="ملخص كشف الحساب" />

                        <!--  مجموع المدين  -->
                        <TextBlock
                            x:Name="txtTotalDebit"
                            Grid.Row="1"
                            Grid.Column="0"
                            HorizontalAlignment="Center"
                            FontSize="18"
                            Foreground="Red"
                            Text="مجموع المدين: 0.000" />

                        <!--  مجموع الدائن  -->
                        <TextBlock
                            x:Name="txtTotalCredit"
                            Grid.Row="1"
                            Grid.Column="1"
                            HorizontalAlignment="Center"
                            FontSize="18"
                            Foreground="Green"
                            Text="مجموع الدائن: 0.000" />

                        <!--  الرصيد النهائي  -->
                        <StackPanel
                            Grid.RowSpan="2"
                            Grid.Column="2"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center">
                            <TextBlock
                                x:Name="txtFinalBalance"
                                HorizontalAlignment="Center"
                                FontSize="18"
                                FontWeight="Bold"
                                Text="الرصيد النهائي: 0.000" />
                            <TextBlock
                                x:Name="txtBalanceStatus"
                                Margin="0,8,0,0"
                                HorizontalAlignment="Center"
                                FontSize="18"
                                Text="حالة الرصيد: متوازن" />
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </Border>
    </Viewbox>
</Window>
