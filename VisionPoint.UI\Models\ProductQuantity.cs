﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VisionPoint.UI.Models;

[Index(nameof(ProductColorId))]
[Index(nameof(Exp))]
[Index(nameof(WarehouseId))]
[Index(nameof(ProductColorId), nameof(Exp))]
[Index(nameof(ProductColorId), nameof(WarehouseId))]
[Index(nameof(ProductColorId), nameof(WarehouseId), nameof(Exp))]
public class ProductQuantity
{
    [Key]
    public int Id { get; set; }
    public int? ProductColorId { get; set; }
    public ProductColor? ProductColor { get; set; }
    public int WarehouseId { get; set; }
    public Warehouse Warehouse { get; set; } = null!;
    public DateOnly? Exp { get; set; }
    public int Quantity { get; set; }

    /// <summary>
    /// Row GUID for SQL Server Merge Replication - managed by database
    /// </summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid RowGuid { get; set; }
}
