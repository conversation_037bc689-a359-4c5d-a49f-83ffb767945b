using Microsoft.EntityFrameworkCore;
using System;

namespace VisionPoint.UI.ViewModel;

/// <summary>
/// نموذج عرض كشف حساب المستخدم
/// </summary>
public class UserStatementVM
{
    /// <summary>
    /// معرف العملية
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// تاريخ العملية
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// نوع العملية (إيصال، مرتب، إلخ)
    /// </summary>
    public string OperationType { get; set; }

    /// <summary>
    /// رقم العملية (رقم الإيصال)
    /// </summary>
    public int OperationNumber { get; set; }

    /// <summary>
    /// طريقة الدفع (اسم الخزينة)
    /// </summary>
    public string? PaymentMethod { get; set; }

    /// <summary>
    /// البيان أو الوصف
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// قيمة القبض (المبلغ الداخل للمستخدم)
    /// </summary>
    [Precision(18, 3)] public decimal Incoming { get; set; } = 0;

    /// <summary>
    /// قيمة الدفع (المبلغ الخارج من المستخدم)
    /// </summary>
    [Precision(18, 3)] public decimal Outgoing { get; set; } = 0;

    /// <summary>
    /// الرصيد بعد العملية
    /// </summary>
    [Precision(18, 3)] public decimal Balance { get; set; } = 0;

    /// <summary>
    /// يشير إلى ما إذا كان هذا الصف خاصًا (مثل الرصيد السابق أو الرصيد اللاحق)
    /// </summary>
    public bool IsSpecialRow { get; set; } = false;
}
