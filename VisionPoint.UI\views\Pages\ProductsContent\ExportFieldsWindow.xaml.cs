using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.ProductsContent
{
    public partial class ExportFieldsWindow : Window
    {
        public List<string> SelectedFields { get; private set; } = new List<string>();
        private ObservableCollection<ExportFieldItem> _fields = new ObservableCollection<ExportFieldItem>();

        public ExportFieldsWindow(Dictionary<string, ExportFieldInfo> availableFields)
        {
            InitializeComponent();

            foreach (var field in availableFields)
            {
                var item = new ExportFieldItem
                {
                    FieldName = field.Key,
                    DisplayName = field.Value.DisplayName,
                    IsSelected = field.Value.IsSelectedByDefault,
                    IsEnabled = !field.Value.IsRequired
                };

                // If the field is required, it should always be selected and disabled
                if (field.Value.IsRequired)
                {
                    item.IsSelected = true;
                }

                _fields.Add(item);
            }

            FieldsList.ItemsSource = _fields;
        }

        private void btnSelectAll_Click(object sender, RoutedEventArgs e)
        {
            foreach (var field in _fields)
            {
                field.IsSelected = true;
            }
        }

        private void btnDeselectAll_Click(object sender, RoutedEventArgs e)
        {
            foreach (var field in _fields)
            {
                // Don't deselect required fields
                if (field.IsEnabled)
                {
                    field.IsSelected = false;
                }
            }
        }

        private void btnConfirm_Click(object sender, RoutedEventArgs e)
        {
            // Get all selected fields
            SelectedFields = _fields
                .Where(f => f.IsSelected)
                .Select(f => f.FieldName)
                .ToList();

            if (SelectedFields.Count == 0)
            {
                ErrorBox.Show(
                    "يجب تحديد حقل واحد على الأقل للتصدير.",
                    "لم يتم تحديد حقول");
                return;
            }

            DialogResult = true;
            Close();
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void btnclose_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            Close();
        }

        private void Grid_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            // Get the clicked Grid
            Grid clickedGrid = sender as Grid;

            if (clickedGrid != null)
            {
                // Get the data item bound to the Grid
                // Replace 'YourDataItemClass' with the actual type of your data item
                if (clickedGrid.DataContext is ExportFieldItem dataItem)
                {
                    // Toggle the IsSelected property
                    dataItem.IsSelected = !dataItem.IsSelected;

                    // Mark the event as handled to prevent it from bubbling up further
                    e.Handled = true;
                }
            }
        }


    }

    public class ExportFieldItem : INotifyPropertyChanged
    {
        private bool _isSelected;

        public string FieldName { get; set; }
        public string DisplayName { get; set; }
        public bool IsEnabled { get; set; } = true;

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged(nameof(IsSelected));
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class ExportFieldInfo
    {
        public string DisplayName { get; set; }
        public bool IsRequired { get; set; }
        public bool IsSelectedByDefault { get; set; } = true;
    }
}
