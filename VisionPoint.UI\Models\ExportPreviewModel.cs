using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using VisionPoint.UI.ViewModel;

namespace VisionPoint.UI.Models
{
    /// <summary>
    /// نموذج بيانات معاينة التصدير
    /// </summary>
    public class ExportPreviewModel : INotifyPropertyChanged
    {
        private bool _exportInvoices = true;
        private bool _exportItems = true;
        private List<ExportFieldOption> _invoiceFields = new();
        private List<ExportFieldOption> _itemFields = new();
        private string _fileName = string.Empty;

        /// <summary>
        /// تصدير الفواتير
        /// </summary>
        public bool ExportInvoices
        {
            get => _exportInvoices;
            set => SetProperty(ref _exportInvoices, value);
        }

        /// <summary>
        /// تصدير الأصناف
        /// </summary>
        public bool ExportItems
        {
            get => _exportItems;
            set => SetProperty(ref _exportItems, value);
        }

        /// <summary>
        /// حقول الفواتير المتاحة للتصدير
        /// </summary>
        public List<ExportFieldOption> InvoiceFields
        {
            get => _invoiceFields;
            set => SetProperty(ref _invoiceFields, value);
        }

        /// <summary>
        /// حقول الأصناف المتاحة للتصدير
        /// </summary>
        public List<ExportFieldOption> ItemFields
        {
            get => _itemFields;
            set => SetProperty(ref _itemFields, value);
        }

        /// <summary>
        /// اسم الملف
        /// </summary>
        public string FileName
        {
            get => _fileName;
            set => SetProperty(ref _fileName, value);
        }

        /// <summary>
        /// بيانات الفواتير للمعاينة (المبيعات)
        /// </summary>
        public List<SaleViewModel> SalesData { get; set; } = new();

        /// <summary>
        /// بيانات الأصناف للمعاينة (المبيعات)
        /// </summary>
        public List<SaleItemVM> SalesItemsData { get; set; } = new();

        /// <summary>
        /// بيانات الفواتير للمعاينة (المشتريات)
        /// </summary>
        public List<PurchaseViewModel> PurchasesData { get; set; } = new();

        /// <summary>
        /// بيانات الأصناف للمعاينة (المشتريات)
        /// </summary>
        public List<PurchaseItemVM> PurchaseItemsData { get; set; } = new();

        /// <summary>
        /// بيانات المنتجات للمعاينة
        /// </summary>
        public List<ProductVM> ProductsData { get; set; } = new();

        /// <summary>
        /// بيانات العدسات المسطحة للمعاينة
        /// </summary>
        public List<LensExportItemVM> LensesData { get; set; } = new();

        /// <summary>
        /// معرف العميل المحدد (للفلترة)
        /// </summary>
        public int? ClientId { get; set; }

        /// <summary>
        /// معرف المخزن المحدد (للفلترة)
        /// </summary>
        public int? WarehouseId { get; set; }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    /// <summary>
    /// خيار حقل التصدير
    /// </summary>
    public class ExportFieldOption : INotifyPropertyChanged
    {
        private bool _isSelected;
        private string _customHeader = string.Empty;

        /// <summary>
        /// مفتاح الحقل
        /// </summary>
        public string FieldKey { get; set; } = string.Empty;

        /// <summary>
        /// اسم الحقل الافتراضي
        /// </summary>
        public string DefaultName { get; set; } = string.Empty;

        /// <summary>
        /// هل الحقل مختار للتصدير
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }

        /// <summary>
        /// رأس مخصص للحقل في ملف Excel
        /// </summary>
        public string CustomHeader
        {
            get => string.IsNullOrEmpty(_customHeader) ? DefaultName : _customHeader;
            set => SetProperty(ref _customHeader, value);
        }

        /// <summary>
        /// هل الحقل مطلوب (لا يمكن إلغاء تحديده)
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// وصف الحقل
        /// </summary>
        public string Description { get; set; } = string.Empty;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}
