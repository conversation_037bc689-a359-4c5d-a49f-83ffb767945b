<Page
    x:Class="VisionPoint.UI.views.Pages.Clients.ClientPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converter="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Clients"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils"
    Title="ClientPage"
    d:Background="White"
    d:Height="1080"
    d:Width="1570"
    FlowDirection="RightToLeft"
    Loaded="Window_Loaded"
    mc:Ignorable="d">
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <converter:BoolToStringConverter x:Key="BoolToStringConverter" />
            <converter:ClientTypeConverter x:Key="ClientTypeConverter" />
        </ResourceDictionary>
    </Page.Resources>

    <Border>



        <Grid Grid.Row="1" Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition />
                <RowDefinition />
                <RowDefinition />
                <RowDefinition />
                <RowDefinition />
                <RowDefinition />
                <RowDefinition />
            </Grid.RowDefinitions>


            <Grid Grid.ColumnSpan="2" Margin="16,0">


                <TextBox
                    x:Name="txtName"
                    Height="60"
                    VerticalAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    BorderThickness="1"
                    FontSize="21"
                    GotFocus="TextBox_GotFocus"
                    Tag="الاسم" />

            </Grid>

            <Grid
                Grid.Column="2"
                Grid.ColumnSpan="2"
                Margin="16,0">

                <TextBox
                    x:Name="txtPhoneNumber"
                    Height="60"
                    VerticalAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    utils:NumericInputControl.IsNumericOnly="True"
                    BorderThickness="1"
                    FontSize="21"
                    GotFocus="TextBox_GotFocus"
                    Tag="رقم الهاتف" />

            </Grid>

            <!-- نوع العميل: زبون -->
            <Grid Grid.Row="1" Grid.Column="0" HorizontalAlignment="Center" VerticalAlignment="Center">
                <CheckBox
                    x:Name="chkCustomer"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FlowDirection="LeftToRight"
                    IsChecked="True"
                    Style="{StaticResource CircleCheckboxFL}"
                    FontSize="18"
                    FontWeight="Bold"
                    Foreground="{StaticResource PrimaryColor}"
                    Content="زبون"
                    ToolTip="تحديد ما إذا كان العميل زبوناً (يشتري من المتجر)" />
            </Grid>

            <!-- نوع العميل: مورد -->
            <Grid Grid.Row="1" Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                <CheckBox
                    x:Name="chkSupplier"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FlowDirection="LeftToRight"
                    IsChecked="False"
                    Style="{StaticResource CircleCheckboxFL}"
                    FontSize="18"
                    FontWeight="Bold"
                    Foreground="{StaticResource PrimaryColor}"
                    Content="مورد"
                    ToolTip="تحديد ما إذا كان العميل مورداً (يبيع للمتجر)" />
            </Grid>

            <Grid
                Grid.Row="1"
                Grid.Column="2"
                Grid.ColumnSpan="2"
                Margin="16,0">
                <TextBox
                    x:Name="txtAllowedBalance"
                    Height="60"
                    VerticalAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    utils:NumericInputControl.AllowNegative="True"
                    utils:NumericInputControl.IsDecimalOnly="True"
                    BorderThickness="1"
                    FontSize="21"
                    GotFocus="TextBox_GotFocus"
                    Tag="الرصيد المسموح (0 = بلا قيود)" />
            </Grid>


            <Border
                Grid.Row="1"
                Grid.Column="4"
                Width="40"
                Height="40"
                HorizontalAlignment="Left"
                Background="Transparent"
                BorderBrush="{StaticResource PrimaryColor}"
                BorderThickness="2"
                CornerRadius="100"
                Cursor="Hand"
                MouseLeftButtonDown="DataInfo_MouseLeftButtonDown">
                <TextBlock
                    Margin="0,4,0,0"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="28"
                    FontWeight="Bold"
                    Foreground="{StaticResource PrimaryColor}"
                    Text="?"
                    TextAlignment="Center" />
            </Border>

            <!--  اختيار المخزن  -->
            <Grid Grid.Column="4" Grid.ColumnSpan="2">
                <ComboBox
                    x:Name="cmbWarehouse"
                    Height="60"
                    VerticalAlignment="Stretch"
                    VerticalContentAlignment="Center"
                    BorderThickness="1"
                    DisplayMemberPath="Name"
                    FontSize="18"
                    SelectedValuePath="Id"
                    Tag="المخزن" />

            </Grid>


            <Border
                x:Name="btnSave"
                Grid.Row="2"
                Grid.Column="0"
                MaxHeight="44"
                Margin="16,0"
                Background="{StaticResource PrimaryColor}"
                CornerRadius="8"
                Cursor="Hand"
                MouseLeftButtonDown="btnSave_MouseLeftButtonDown">
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="18"
                    Foreground="White">
                    حفظ
                </TextBlock>
            </Border>

            <Border
                x:Name="btnNew"
                Grid.Row="2"
                Grid.Column="1"
                MaxHeight="44"
                Margin="16,0"
                Background="Transparent"
                BorderBrush="{StaticResource PrimaryColor}"
                BorderThickness="1"
                CornerRadius="8"
                Cursor="Hand"
                MouseLeftButtonDown="btnNew_MouseLeftButtonDown">
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="18"
                    Foreground="{StaticResource PrimaryColor}">
                    جديد
                </TextBlock>
            </Border>


            <Border
                x:Name="btmSearch"
                Grid.Row="2"
                Grid.Column="2"
                MaxHeight="44"
                Margin="16,0"
                Background="{StaticResource PrimaryColor}"
                CornerRadius="8"
                Cursor="Hand"
                MouseLeftButtonDown="btnSearch_MouseLeftButtonDown">
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="18"
                    Foreground="White">
                    بحث
                </TextBlock>
            </Border>

            <Border
                x:Name="btnStatement"
                Grid.Row="2"
                Grid.Column="3"
                MaxHeight="44"
                Margin="16,0"
                Background="Transparent"
                BorderBrush="{StaticResource PrimaryColor}"
                BorderThickness="1"
                CornerRadius="8"
                Cursor="Hand"
                MouseLeftButtonDown="btnStatement_MouseLeftButtonDown">
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="18"
                    Foreground="{StaticResource PrimaryColor}">
                    كشف حساب
                </TextBlock>
            </Border>




            <ListView
                x:Name="list"
                Grid.Row="3"
                Grid.RowSpan="6"
                Grid.ColumnSpan="8"
                Background="{DynamicResource PageColor}"
                BorderThickness="1"
                FontFamily="pack://application:,,,/Assets/#Cairo"
                ItemsSource="{Binding}"
                MouseDoubleClick="list_MouseDoubleClick"
                ScrollViewer.HorizontalScrollBarVisibility="Hidden"
                SizeChanged="list_SizeChanged">
                <ListView.BorderBrush>
                    <SolidColorBrush Opacity="0.42" Color="Black" />
                </ListView.BorderBrush>
                <!--  SelectionChanged="ListView_SelectionChanged"  -->

                <ListView.ItemContainerStyle>
                    <Style TargetType="ListViewItem">
                        <Style.Triggers>
                            <Trigger Property="Control.IsMouseOver" Value="True">
                                <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                                <Setter Property="FontWeight" Value="Bold" />
                                <Setter Property="Foreground" Value="Black" />
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                                <Setter Property="Foreground" Value="Black" />
                            </Trigger>

                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="FontWeight" Value="Bold" />
                                <Setter Property="Foreground" Value="Black" />
                            </Trigger>

                            <MultiTrigger>
                                <MultiTrigger.Conditions>
                                    <Condition Property="IsSelected" Value="False" />
                                    <Condition Property="IsMouseOver" Value="False" />
                                </MultiTrigger.Conditions>
                                <Setter Property="FontWeight" Value="Thin" />
                                <Setter Property="Foreground" Value="{DynamicResource PrimaryTextColor}" />
                            </MultiTrigger>

                        </Style.Triggers>
                        <Setter Property="Control.Background" Value="{DynamicResource PageColor}" />
                        <Setter Property="Control.HorizontalContentAlignment" Value="Stretch" />
                    </Style>

                </ListView.ItemContainerStyle>
                <ListView.View>
                    <GridView AllowsColumnReorder="False">
                        <GridView.ColumnHeaderContainerStyle>
                            <Style BasedOn="{StaticResource ListViewHeader}" TargetType="{x:Type GridViewColumnHeader}">
                                <Setter Property="IsEnabled" Value="False" />
                                <Setter Property="Height" Value="60" />
                                <Style.Triggers>
                                    <Trigger Property="IsEnabled" Value="False">
                                        <Setter Property="TextElement.Foreground" Value="Black" />
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </GridView.ColumnHeaderContainerStyle>
                        <GridViewColumn Width="Auto" Header="الاسم">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock
                                        Height="45"
                                        MinWidth="35"
                                        HorizontalAlignment="Center"
                                        Background="Transparent"
                                        Text="{Binding Name, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>

                        <GridViewColumn Width="Auto" Header="رقم الهاتف">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock
                                        Height="45"
                                        MinWidth="35"
                                        HorizontalAlignment="Center"
                                        Background="Transparent"
                                        Text="{Binding Phone, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>

                        <GridViewColumn Width="Auto" Header="النوع">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock
                                        Height="45"
                                        MinWidth="35"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="Transparent"
                                        FontWeight="Medium"
                                        FontSize="14"
                                        Foreground="{StaticResource PrimaryColor}">
                                        <TextBlock.Text>
                                            <MultiBinding Converter="{StaticResource ClientTypeConverter}">
                                                <Binding Path="IsCustomer" />
                                                <Binding Path="IsSupplier" />
                                            </MultiBinding>
                                        </TextBlock.Text>
                                    </TextBlock>
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>

                        <GridViewColumn Width="Auto" Header="الرصيد">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock
                                        Height="45"
                                        MinWidth="35"
                                        HorizontalAlignment="Center"
                                        Background="Transparent"
                                        Text="{Binding Balance, StringFormat='{}{0:N3}', FallbackValue='0.000', TargetNullValue='0.000'}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>

                        <GridViewColumn Width="Auto" Header="الرصيد المسموح">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock
                                        Height="45"
                                        MinWidth="35"
                                        HorizontalAlignment="Center"
                                        Background="Transparent">
                                        <TextBlock.Text>
                                            <Binding Path="AllowedBalance" StringFormat="{}{0:N3}">
                                                <Binding.TargetNullValue>0.000</Binding.TargetNullValue>
                                                <Binding.FallbackValue>0.000</Binding.FallbackValue>
                                            </Binding>
                                        </TextBlock.Text>
                                        <TextBlock.ToolTip>
                                            <StackPanel MaxWidth="400">
                                                <TextBlock
                                                    Margin="0,0,0,5"
                                                    FontSize="14"
                                                    FontWeight="Bold">
                                                    <Run Text="الرصيد المسموح: " />
                                                    <Run Text="{Binding AllowedBalance, StringFormat='{}{0:N3}'}" />
                                                </TextBlock>

                                                <Separator Margin="0,5" />

                                                <TextBlock Margin="0,5" TextWrapping="Wrap">
                                                    <Run FontWeight="Bold" Text="• " />
                                                    <Run
                                                        FontWeight="Bold"
                                                        Foreground="Green"
                                                        Text="0 = بلا قيود" />
                                                    <LineBreak />
                                                    <Run Text="  لا توجد حدود على رصيد العميل" />
                                                </TextBlock>

                                                <TextBlock Margin="0,5" TextWrapping="Wrap">
                                                    <Run FontWeight="Bold" Text="• " />
                                                    <Run
                                                        FontWeight="Bold"
                                                        Foreground="Red"
                                                        Text="قيمة موجبة = حد أقصى للمديونية" />
                                                    <LineBreak />
                                                    <Run Text="  العميل مدين للمتجر (العميل يدين المتجر)" />
                                                    <LineBreak />
                                                    <Run Text="  مثال: 1000 = العميل يمكن أن يدين المتجر حتى 1000" />
                                                </TextBlock>

                                                <TextBlock Margin="0,5" TextWrapping="Wrap">
                                                    <Run FontWeight="Bold" Text="• " />
                                                    <Run
                                                        FontWeight="Bold"
                                                        Foreground="Blue"
                                                        Text="قيمة سالبة = حد أقصى للدائنية" />
                                                    <LineBreak />
                                                    <Run Text="  المتجر مدين للعميل (المتجر يدين العميل)" />
                                                    <LineBreak />
                                                    <Run Text="  مثال: -500 = المتجر يمكن أن يدين العميل حتى 500" />
                                                </TextBlock>
                                            </StackPanel>
                                        </TextBlock.ToolTip>
                                    </TextBlock>
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>

                        <GridViewColumn Width="Auto" Header="المخزن">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock
                                        Height="45"
                                        MinWidth="35"
                                        HorizontalAlignment="Center"
                                        Background="Transparent"
                                        Text="{Binding Warehouse.Name, FallbackValue='عام', TargetNullValue='عام'}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>

                        <GridViewColumn Width="Auto" Header="العمليات">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                        <!--  زر التعديل  -->
                                        <Button
                                            Width="30"
                                            Height="30"
                                            Margin="5,0"
                                            Background="Transparent"
                                            BorderBrush="Transparent"
                                            Click="EditButton_Click"
                                            Cursor="Hand"
                                            ToolTip="تعديل العميل">
                                            <Path
                                                Width="16"
                                                Height="16"
                                                Data="M16.84,2.73C16.45,2.73 16.07,2.88 15.77,3.17L13.35,5.59L18.41,10.65L20.83,8.23C21.42,7.64 21.42,6.69 20.83,6.1L17.9,3.17C17.6,2.88 17.22,2.73 16.84,2.73M12.94,6L4.83,14.11L4.5,14.44L4.5,19.5H9.56L9.89,19.17L18,11.06L12.94,6Z"
                                                Fill="{StaticResource PrimaryColor}"
                                                Stretch="Uniform" />
                                        </Button>

                                        <!--  زر الحذف  -->
                                        <Button
                                            Width="30"
                                            Height="30"
                                            Margin="5,0"
                                            Background="Transparent"
                                            BorderBrush="Transparent"
                                            Click="DeleteButton_Click"
                                            Cursor="Hand"
                                            ToolTip="حذف العميل">
                                            <Path
                                                Width="16"
                                                Height="16"
                                                Data="{StaticResource Trash}"
                                                Fill="{StaticResource errorColor}"
                                                Stretch="Uniform" />
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                    </GridView>
                </ListView.View>
            </ListView>


        </Grid>
    </Border>
</Page>
