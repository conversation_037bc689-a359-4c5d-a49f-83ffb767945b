﻿using System.IO;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using VisionPoint.UI.views.Dialogs;
using ZXing;
using ZXing.Windows.Compatibility;

namespace VisionPoint.UI.Reports.SalesReciept
{
    /// <summary>
    /// Interaction logic for SalesHeader.xaml
    /// </summary>
    public partial class SalesHeader : Page
    {
        public SalesHeader(int id, string Date, string phone, string ClientName)
        {
            InitializeComponent();
            txtDate.Text = Date;
            txtPhone.Text = phone;
            txtName.Text = ClientName;
            ReceiptNumber.Text = "رقم (" + id.ToString() + ")";
            CNameAr.Text = Properties.Settings.Default.CompanyName;
            GenerateBarcode(id.ToString());
            LoadInitialLogo(); // Call this here
        }
        private void LoadInitialLogo()
        {
            string initialLogoPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Assets", "company_logo.png");

            if (File.Exists(initialLogoPath))
            {
                try
                {
                    BitmapImage bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri(initialLogoPath);
                    bitmap.CacheOption = BitmapCacheOption.OnLoad; // Important for releasing file lock
                    bitmap.EndInit();
                    LogoImageElement.Source = bitmap; // Assuming LogoImageElement is your Image's x:Name
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"Error loading initial logo: {ex.Message}", "خطأ");
                }
            }
        }
        private void GenerateBarcode(string content)
        {
            var writer = new BarcodeWriter<System.Drawing.Bitmap>
            {
                Renderer = new BitmapRenderer(), // Now available
                Format = BarcodeFormat.CODE_128,
                Options = new ZXing.Common.EncodingOptions
                {
                    Width = 300,
                    Height = 100,
                    Margin = 10
                }
            };



            // Generate the bitmap image
            var bitmap = writer.Write(content);

            // Convert the Bitmap to BitmapImage
            using (MemoryStream memory = new MemoryStream())
            {
                bitmap.Save(memory, System.Drawing.Imaging.ImageFormat.Bmp);
                memory.Position = 0;

                BitmapImage bitmapImage = new BitmapImage();
                bitmapImage.BeginInit();
                bitmapImage.StreamSource = memory;
                bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                bitmapImage.EndInit();

                imgBarcode.Source = bitmapImage; // Image control in XAML
            }
        }
        // OR, you could call LoadAndCacheImage from the Loaded event:
        // private void Page_Loaded(object sender, RoutedEventArgs e)
        // {
        //     LoadAndCacheImage();
        // }

    }
}
