using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.Clients
{
    /// <summary>
    /// Interaction logic for ClientPage.xaml
    /// </summary>
    public partial class ClientPage : Page
    {
        private readonly ClientService _clientService;
        private readonly WarehouseService _warehouseService;
        private ObservableCollection<Client> _clients;
        private ObservableCollection<Warehouse> _warehouses;
        private Client _selectedClient;
        private bool _isSearchMode = false;
        public ClientPage()
        {
            InitializeComponent();
            _clientService = new ClientService();
            _warehouseService = new WarehouseService();
            _clients = new ObservableCollection<Client>();
            _warehouses = new ObservableCollection<Warehouse>();
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadWarehousesAsync();
            await LoadClientsAsync();
        }

        private async Task LoadWarehousesAsync()
        {
            try
            {
                _warehouses.Clear();
                var warehouses = await _warehouseService.GetAllWarehousesAsync();

                // إضافة خيار "عام" في البداية
                _warehouses.Add(new Warehouse { Id = 0, Name = "عام (جميع المخازن)", Code = "ALL" });

                foreach (var warehouse in warehouses)
                {
                    _warehouses.Add(warehouse);
                }

                cmbWarehouse.ItemsSource = _warehouses;

                // تحديد المخزن الافتراضي بناءً على المستخدم الحالي
                if (CurrentUser.WarehouseId.HasValue && warehouses.Any(w => w.Id == CurrentUser.WarehouseId.Value))
                {
                    // اختيار مخزن المستخدم الحالي
                    cmbWarehouse.SelectedValue = CurrentUser.WarehouseId.Value;
                }
                else
                {
                    // اختيار "عام" افتراضياً
                    cmbWarehouse.SelectedIndex = 0;
                }

                // تطبيق منطق الصلاحيات لتغيير المخزن
                // إذا لم يكن المستخدم مديراً أو لا يملك صلاحية تغيير المخزن، يتم تعطيل الكومبو
                cmbWarehouse.IsEnabled = CurrentUser.CanChangeWarehouse;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل المخازن: {ex.Message}", "خطأ في التحميل", true);
            }
        }

        private async Task LoadClientsAsync()
        {
            try
            {
                _clients.Clear();

                // تحديد المخزن المختار
                int? selectedWarehouseId = null;
                if (cmbWarehouse.SelectedValue != null && cmbWarehouse.SelectedValue is int warehouseId && warehouseId > 0)
                {
                    selectedWarehouseId = warehouseId;
                }
                _clients = new(await _clientService.GetClientsByWarehouseAsync(selectedWarehouseId));

                list.ItemsSource = _clients;

            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ في التحميل", true);
            }
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnSave.IsEnabled = false;
            btnNew.IsEnabled = false;
            btnStatement.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnSave.IsEnabled = true;
            btnNew.IsEnabled = true;
            btnStatement.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private async void btnSave_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                DisableAllButtons();

                // التحقق من صلاحيات المستخدم
                bool canAdd = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ClientRole");
                bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditClientRole");

                // إذا كان عميل جديد، نحتاج صلاحية الإضافة
                if (_selectedClient == null && !canAdd)
                {
                    ErrorBox.Show("لا تملك صلاحية إضافة العملاء", "خطأ في الصلاحيات", true);
                    return;
                }

                // إذا كان تعديل عميل موجود،
                if (_selectedClient != null)
                {
                    if (_selectedClient.Id is 1 or 2) // لا يمكن تعديل العملاء الافتراضيين
                    {
                        ErrorBox.Show("لا يمكن تعديل هذا العميل من النظام", "خطأ في الصلاحيات", true);
                        return;
                    }
                    if (!canEdit) //نحتاج صلاحية التعديل
                    {

                        ErrorBox.Show("لا تملك صلاحية تعديل العملاء", "خطأ في الصلاحيات", true);
                        return;
                    }
                }

                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    ErrorBox.Show("الرجاء إدخال اسم العميل", "بيانات ناقصة", false);
                    txtName.Focus();
                    return;
                }
                if (_clients.Any(c => c.Name == txtName.Text && c.Id != _selectedClient?.Id))
                {
                    ErrorBox.Show("اسم العميل موجود مسبقاً", "خطأ في البيانات", false);
                    txtName.Focus();
                    return;
                }

                // التحقق من أن العميل يجب أن يكون زبوناً أو مورداً أو كلاهما
                if (chkCustomer.IsChecked != true && chkSupplier.IsChecked != true)
                {
                    ErrorBox.Show("يجب تحديد نوع العميل (زبون أو مورد أو كلاهما)", "خطأ في البيانات", false);
                    chkCustomer.Focus();
                    return;
                }


                // تعديل العميل أو إضافة عميل جديد
                if (_selectedClient != null)
                {
                    // تحديث العميل الموجود
                    _selectedClient.Name = txtName.Text;
                    _selectedClient.Phone = txtPhoneNumber.Text;
                    _selectedClient.IsCustomer = chkCustomer.IsChecked ?? false;
                    _selectedClient.IsSupplier = chkSupplier.IsChecked ?? false;
                    _selectedClient.AllowedBalance = decimal.TryParse(txtAllowedBalance.Text, out decimal allowedBalance) ? allowedBalance : 0;

                    // تحديد المخزن - إذا كان المحدد هو "عام" (Id = 0) فنضع null
                    _selectedClient.WarehouseId = cmbWarehouse.SelectedValue != null && (int)cmbWarehouse.SelectedValue > 0
                        ? (int)cmbWarehouse.SelectedValue
                        : null;

                    try
                    {
                        await _clientService.UpdateClientAsync(_selectedClient);
                        DialogBox.Show("تم بنجاح", $"تم تحديث العميل {_selectedClient.Name} بنجاح");
                    }
                    catch (Exception ex)
                    {
                        ErrorBox.Show($"حدث خطأ أثناء تحديث العميل: {ex.Message}", "خطأ في التحديث", true);
                        return;
                    }
                }
                else
                {
                    // إضافة عميل جديد
                    var newClient = new Client
                    {
                        Name = txtName.Text,
                        Phone = txtPhoneNumber.Text,
                        IsCustomer = chkCustomer.IsChecked ?? false,
                        IsSupplier = chkSupplier.IsChecked ?? false,
                        AllowedBalance = decimal.TryParse(txtAllowedBalance.Text, out decimal allowedBalance) ? allowedBalance : 0,

                        // تحديد المخزن - إذا كان المحدد هو "عام" (Id = 0) فنضع null
                        WarehouseId = cmbWarehouse.SelectedValue != null && (int)cmbWarehouse.SelectedValue > 0
                            ? (int)cmbWarehouse.SelectedValue
                            : null
                    };

                    try
                    {
                        var addedClient = await _clientService.AddClientAsync(newClient);
                        DialogBox.Show("تم بنجاح", $"تم إضافة العميل {newClient.Name} بنجاح");
                    }
                    catch (Exception ex)
                    {
                        ErrorBox.Show($"حدث خطأ أثناء إضافة العميل: {ex.Message}", "خطأ في الإضافة", true);
                        return;
                    }
                }

                // إعادة تحميل القائمة وتنظيف الحقول
                await LoadClientsAsync();
                ClearForm();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ: {ex.Message}", "خطأ في النظام", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnNew_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                ClearForm();

                // If we're in search mode, clear the filter and reload all clients
                if (_isSearchMode)
                {
                    _isSearchMode = false;
                    await LoadClientsAsync();
                }
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnDelete_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                await DeleteSelectedClient();
            }
            finally
            {
                EnableAllButtons();
            }
        }


        private void list_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (list.SelectedItem is Client selectedClient)
            {
                _selectedClient = selectedClient;
                DisplayClientDetails();
            }
            else
            {
                _selectedClient = null;
            }
        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (list.View is GridView gridView && gridView.Columns.Count >= 6)
            {
                double actionColumnWidth = 80; // Fixed width for buttons
                double padding = 10;
                double scrollbarWidth = SystemParameters.VerticalScrollBarWidth;

                // Calculate available width for dynamic columns
                double availableWidth = list.ActualWidth - actionColumnWidth - scrollbarWidth - padding;

                double totalWeight = 2.0 + 1.2 + 1.0 + 1.2 + 1.4 + 1.0; // إضافة وزن عمود المخزن
                double unitWidth = availableWidth / totalWeight;

                gridView.Columns[0].Width = unitWidth * 2.0; // Name
                gridView.Columns[1].Width = unitWidth * 1.2; // Phone
                gridView.Columns[2].Width = unitWidth * 1.0; // Type
                gridView.Columns[3].Width = unitWidth * 1.2; // Balance
                gridView.Columns[4].Width = unitWidth * 1.4; // AllowedBalance
                gridView.Columns[5].Width = unitWidth * 1.0; // Warehouse
                gridView.Columns[6].Width = actionColumnWidth; // Actions
            }
        }


        // عرض بيانات العميل المحدد في الحقول
        private void DisplayClientDetails()
        {
            if (_selectedClient != null)
            {
                txtName.Text = _selectedClient.Name;
                txtPhoneNumber.Text = _selectedClient.Phone;
                txtAllowedBalance.Text = _selectedClient.AllowedBalance.ToString("F3");
                chkCustomer.IsChecked = _selectedClient.IsCustomer;
                chkSupplier.IsChecked = _selectedClient.IsSupplier;

                // تحديد المخزن في ComboBox
                if (_selectedClient.WarehouseId.HasValue)
                {
                    cmbWarehouse.SelectedValue = _selectedClient.WarehouseId.Value;
                }
                else
                {
                    cmbWarehouse.SelectedIndex = 0; // اختيار "عام"
                }
            }
        }

        // مسح الحقول وإعادة تعيين العميل المحدد
        private void ClearForm()
        {
            txtName.Clear();
            txtPhoneNumber.Clear();
            txtAllowedBalance.Text = "0.000";
            chkCustomer.IsChecked = true;
            chkSupplier.IsChecked = false;

            // تحديد المخزن الافتراضي بناءً على المستخدم الحالي
            if (CurrentUser.WarehouseId.HasValue && _warehouses.Any(w => w.Id == CurrentUser.WarehouseId.Value))
            {
                // اختيار مخزن المستخدم الحالي
                cmbWarehouse.SelectedValue = CurrentUser.WarehouseId.Value;
            }
            else
            {
                // اختيار "عام" افتراضياً
                cmbWarehouse.SelectedIndex = 0;
            }

            _selectedClient = null;
        }

        // معالج النقر على زر التعديل داخل القائمة
        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is Client client)
            {
                _selectedClient = client;
                DisplayClientDetails();
            }
        }

        // معالج النقر على زر الحذف داخل القائمة
        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is Client client)
            {
                _selectedClient = client;
                await DeleteSelectedClient();
            }
        }

        // حذف العميل المحدد حالياً
        private async Task DeleteSelectedClient()
        {
            // التحقق من صلاحية الحذف
            bool canDelete = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("DeleteClientRole");
            if (!canDelete)
            {
                ErrorBox.Show("لا تملك صلاحية حذف العملاء", "خطأ في الصلاحيات", true);
                return;
            }

            if (_selectedClient == null)
            {
                ErrorBox.Show("الرجاء اختيار عميل للحذف", "تنبيه", false);
                return;
            }
            if (_selectedClient.Id is 1 or 2)
            {
                ErrorBox.Show("لا يمكن حذف هذا العميل من النظام", "تنبيه", false);
                return;
            }
            var clientName = _selectedClient.Name;

            var result = DeleteBox.Show("هل انت متاكد من حذف العميل", clientName);
            if (result == true)
            {
                (bool State, string Message) deleteResult = (false, "");
                try
                {
                    // استدعاء دالة الحذف والتحقق من النتيجة
                    deleteResult = await _clientService.DeleteClientAsync(_selectedClient.Id);

                    if (deleteResult.State)
                    {
                        // إزالة العميل من القائمة المحلية
                        _clients.Remove(_selectedClient);

                        // تحديث واجهة المستخدم
                        ClearForm();
                        list.Items.Refresh();
                        Title = $"العملاء - {_clients.Count}";

                        DialogBox.Show("تم بنجاح", "تم حذف العميل بنجاح");
                        _selectedClient = null;
                    }
                    else
                    {
                        // عرض رسالة الخطأ التي أعادتها دالة الحذف
                        ErrorBox.Show(deleteResult.Message, "لا يمكن حذف العميل", true);
                    }
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"حدث خطأ أثناء حذف العميل: {ex.Message}", "خطأ في الحذف", true);
                }
            }
        }
        private void TextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                textBox.SelectAll();
            }
        }

        private async void btnSearch_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                // Get search criteria from the input fields
                string? name = txtName.Text.Trim() == "" ? null : txtName.Text.Trim();
                string? phone = txtPhoneNumber.Text.Trim() == "" ? null : txtPhoneNumber.Text.Trim();

                // تحديد معايير البحث بناءً على حالة CheckBox
                bool isCustomer = chkCustomer.IsChecked == true;
                bool isSupplier = chkSupplier.IsChecked == true;
                // If all fields are empty and no type is selected, load all clients
                if (name is null && name is null && !isCustomer && !isSupplier)
                {
                    await LoadClientsAsync();
                    _isSearchMode = false;
                    return;
                }
                // تحديد المخزن المختار
                int? selectedWarehouseId = null;
                if (cmbWarehouse.SelectedValue != null && cmbWarehouse.SelectedValue is int warehouseId && warehouseId > 0)
                {
                    selectedWarehouseId = warehouseId;
                }
                // Perform the search
                _clients = new(await _clientService.SearchClientsAsync(selectedWarehouseId, name, phone, isCustomer, isSupplier));
                list.ItemsSource = _clients;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ في البحث", true);
            }
        }

        private void btnStatement_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // التحقق من اختيار عميل
            if (_selectedClient == null)
            {
                // إذا لم يتم اختيار عميل، نتحقق مما إذا كان هناك عميل محدد في القائمة
                if (list.SelectedItem is Client selectedClient)
                {
                    _selectedClient = selectedClient;
                }
                else
                {
                    ErrorBox.Show("الرجاء اختيار عميل لعرض كشف حسابه", "تنبيه", false);
                    return;
                }
            }

            try
            {
                // فتح نافذة كشف الحساب
                var statementWindow = new ClientStatementWindow(_selectedClient);
                statementWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء فتح كشف الحساب: {ex.Message}", "خطأ", true);
            }
        }

        private void DataInfo_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DataInfo dataInfoWindow = new DataInfo();
            dataInfoWindow.ShowDialog();
        }


    }
}
