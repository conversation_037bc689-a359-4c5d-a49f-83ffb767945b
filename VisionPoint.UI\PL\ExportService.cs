using ClosedXML.Excel;
using Microsoft.EntityFrameworkCore;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Models;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Pages.ProductsContent;

namespace VisionPoint.UI.PL
{
    public class ExportService : IDisposable
    {
        private readonly AppDbContext _context = ServiceLocator.GetService<AppDbContext>();
        private bool _disposed = false;

        public ExportService()
        {
        }

        // تعريف الحقول المتاحة لتصدير العدسات
        public Dictionary<string, ExportFieldInfo> GetLensExportFields()
        {
            return new Dictionary<string, ExportFieldInfo>
            {
                { "Name", new ExportFieldInfo { DisplayName = "اسم العدسة", IsRequired = true } },
                { "BC", new ExportFieldInfo { DisplayName = "BC", IsRequired = false } },
                { "DIA", new ExportFieldInfo { DisplayName = "DIA", IsRequired = false } },
                { "ADD", new ExportFieldInfo { DisplayName = "ADD", IsRequired = false } },
                { "AXIS", new ExportFieldInfo { DisplayName = "AXIS", IsRequired = false } },
                { "SPH", new ExportFieldInfo { DisplayName = "SPH", IsRequired = false } },
                { "CYL", new ExportFieldInfo { DisplayName = "CYL", IsRequired = false } },
                { "POW", new ExportFieldInfo { DisplayName = "POW", IsRequired = false } },
                { "HexCode", new ExportFieldInfo { DisplayName = "كود اللون", IsRequired = false } },
                { "ColorName", new ExportFieldInfo { DisplayName = "اسم اللون", IsRequired = false } },
                { "Barcode", new ExportFieldInfo { DisplayName = "الباركود", IsRequired = false } },
                { "Cost", new ExportFieldInfo { DisplayName = "سعر التكلفة", IsRequired = false } },
                { "Price", new ExportFieldInfo { DisplayName = "سعر البيع", IsRequired = false } },
                { "Quantity", new ExportFieldInfo { DisplayName = "الكمية", IsRequired = false } },
                { "Expiration", new ExportFieldInfo { DisplayName = "تاريخ الصلاحية", IsRequired = false } },
                { "Category", new ExportFieldInfo { DisplayName = "فئة العدسة", IsRequired = false } },
                { "Warehouse", new ExportFieldInfo { DisplayName = "اسم المخزن", IsRequired = false } }
            };
        }

        // تعريف الحقول المتاحة لتصدير المنتجات المفصل
        public Dictionary<string, ExportFieldInfo> GetProductExportFields()
        {
            return new Dictionary<string, ExportFieldInfo>
            {
                { "Name", new ExportFieldInfo { DisplayName = "اسم المنتج", IsRequired = true } },
                { "ColorName", new ExportFieldInfo { DisplayName = "اسم اللون", IsRequired = false } },
                { "ColorCode", new ExportFieldInfo { DisplayName = "كود اللون", IsRequired = false } },
                { "Barcode", new ExportFieldInfo { DisplayName = "الباركود", IsRequired = false } },
                { "CostPrice", new ExportFieldInfo { DisplayName = "سعر التكلفة", IsRequired = false } },
                { "SellPrice", new ExportFieldInfo { DisplayName = "سعر البيع", IsRequired = false } },
                { "Quantity", new ExportFieldInfo { DisplayName = "الكمية", IsRequired = false } },
                { "WarehouseName", new ExportFieldInfo { DisplayName = "اسم المخزن", IsRequired = false } },
                { "ExpiryDate", new ExportFieldInfo { DisplayName = "تاريخ الصلاحية", IsRequired = false } },
                { "MinimumQuantity", new ExportFieldInfo { DisplayName = "الحد الأدنى للكمية", IsRequired = false } },
                { "HasColor", new ExportFieldInfo { DisplayName = "يحتوي على ألوان", IsRequired = false } },
                { "HasExpiry", new ExportFieldInfo { DisplayName = "يحتوي على تاريخ صلاحية", IsRequired = false } }
            };
        }

        // تصدير العدسات إلى ملف Excel
        public async Task ExportLensesToExcel(string filePath, List<string> selectedFields, int? warehouseId = null, IProgress<int> progress = null)
        {
            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add("Lenses");

            // إضافة رأس الجدول
            int col = 1;
            var fields = GetLensExportFields();
            var headerMapping = new Dictionary<string, int>();

            // إنشاء headers بشكل مجمع (محسنة)
            var validFields = selectedFields.Where(field => fields.ContainsKey(field)).ToList();

            foreach (var field in validFields)
            {
                worksheet.Cell(1, col).Value = fields[field].DisplayName;
                headerMapping.Add(field, col);
                col++;
            }

            // الحصول على بيانات العدسات
            var lensesQuery = _context.Lenses
                .Include(l => l.Category)
                .Include(l => l.LensPrescriptions)
                .ThenInclude(lp => lp.Sphere)
                .Include(l => l.LensPrescriptions)
                .ThenInclude(lp => lp.Cylinder)
                .Include(l => l.LensPrescriptions)
                .ThenInclude(lp => lp.Pow)
                .Include(l => l.LensPrescriptions)
                .ThenInclude(lp => lp.LensPrescriptionColors)
                .ThenInclude(lpc => lpc.Color)
                .Include(l => l.LensPrescriptions)
                .ThenInclude(lp => lp.LensPrescriptionColors)
                .ThenInclude(lpc => lpc.LensQuantity)
                .ThenInclude(lq => lq.Warehouse);

            // تطبيق تصفية المخزن إذا كان محدداً
            IQueryable<Lens> filteredLensesQuery = lensesQuery;
            if (warehouseId.HasValue)
            {
                filteredLensesQuery = lensesQuery.Where(l => l.LensPrescriptions.Any(lp =>
                    lp.LensPrescriptionColors.Any(lpc =>
                        lpc.LensQuantity.Any(lq => lq.WarehouseId == warehouseId.Value))));
            }

            var lenses = await filteredLensesQuery.ToListAsyncWithBusy("GetLensesForExport");

            int totalItems = lenses.Sum(l =>
                l.LensPrescriptions.Sum(lp =>
                    lp.LensPrescriptionColors.Sum(lpc =>
                        lpc.LensQuantity.Count)));

            int processedItems = 0;
            int row = 2;

            // إضافة بيانات العدسات (محسنة - تسطيح البيانات أولاً)
            var flattenedLensData = lenses
                .SelectMany(lens => lens.LensPrescriptions
                    .SelectMany(prescription => prescription.LensPrescriptionColors
                        .SelectMany(color =>
                        {
                            // تطبيق تصفية المخزن على الكميات
                            var quantities = warehouseId.HasValue
                                ? color.LensQuantity.Where(lq => lq.WarehouseId == warehouseId.Value)
                                : color.LensQuantity;

                            return quantities.Select(quantity => new
                            {
                                Lens = lens,
                                Prescription = prescription,
                                Color = color,
                                Quantity = quantity
                            });
                        })))
                .ToList();

            // معالجة البيانات المسطحة بكفاءة
            foreach (var item in flattenedLensData)
            {
                foreach (var field in selectedFields)
                {
                    if (headerMapping.TryGetValue(field, out int colIndex))
                    {
                        switch (field)
                        {
                            case "Name":
                                worksheet.Cell(row, colIndex).Value = item.Lens.Name;
                                break;
                            case "BC":
                                worksheet.Cell(row, colIndex).Value = item.Lens.BC;
                                break;
                            case "DIA":
                                worksheet.Cell(row, colIndex).Value = item.Lens.Dia;
                                break;
                            case "ADD":
                                worksheet.Cell(row, colIndex).Value = item.Lens.Addtion;
                                break;
                            case "AXIS":
                                worksheet.Cell(row, colIndex).Value = item.Lens.Axis;
                                break;
                            case "SPH":
                                worksheet.Cell(row, colIndex).Value = item.Prescription.Sphere?.Value;
                                break;
                            case "CYL":
                                worksheet.Cell(row, colIndex).Value = item.Prescription.Cylinder?.Value;
                                break;
                            case "POW":
                                worksheet.Cell(row, colIndex).Value = item.Prescription.Pow?.Value;
                                break;
                            case "HexCode":
                                worksheet.Cell(row, colIndex).Value = item.Color.Color?.HexCode;
                                break;
                            case "ColorName":
                                worksheet.Cell(row, colIndex).Value = item.Color.Color?.Name;
                                break;
                            case "Barcode":
                                worksheet.Cell(row, colIndex).Value = item.Color.Barcode;
                                break;
                            case "Cost":
                                worksheet.Cell(row, colIndex).Value = item.Prescription.CostPrice;
                                break;
                            case "Price":
                                worksheet.Cell(row, colIndex).Value = item.Prescription.SellPrice;
                                break;
                            case "Quantity":
                                worksheet.Cell(row, colIndex).Value = item.Quantity.Quantity;
                                break;
                            case "Expiration":
                                worksheet.Cell(row, colIndex).Value = item.Quantity.Exp.HasValue ? item.Quantity.Exp.Value.ToString("yyyy-MM-dd") : "";
                                break;
                            case "Category":
                                worksheet.Cell(row, colIndex).Value = item.Lens.Category?.Name;
                                break;
                            case "Warehouse":
                                worksheet.Cell(row, colIndex).Value = item.Quantity.Warehouse?.Name;
                                break;
                        }
                    }
                }

                row++;
                processedItems++;

                // تحديث التقدم
                if (processedItems % 10 == 0 || processedItems == totalItems)
                {
                    int percentage = (int)((processedItems * 100.0) / totalItems);
                    progress?.Report(percentage);
                    await Task.Delay(1); // للسماح بتحديث واجهة المستخدم
                }
            }

            // ضبط عرض الأعمدة
            worksheet.Columns().AdjustToContents();
            workbook.SaveAs(filePath);
        }

        // تصدير المنتجات إلى ملف Excel
        public async Task ExportProductsToExcel(string filePath, List<string> selectedFields, int? warehouseId = null, IProgress<int> progress = null)
        {
            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add("Products");

            // إضافة رأس الجدول
            int col = 1;
            var fields = GetProductExportFields();
            var headerMapping = new Dictionary<string, int>();

            // إنشاء headers بشكل مجمع (محسنة)
            var validFields = selectedFields.Where(field => fields.ContainsKey(field)).ToList();

            foreach (var field in validFields)
            {
                worksheet.Cell(1, col).Value = fields[field].DisplayName;
                headerMapping.Add(field, col);
                col++;
            }

            // الحصول على بيانات المنتجات
            var productsQuery = _context.Products
                .Include(p => p.ProductColors)
                .ThenInclude(pc => pc.Color)
                .Include(p => p.ProductColors)
                .ThenInclude(pc => pc.ProductQuantity)
                .ThenInclude(pq => pq.Warehouse);

            // تطبيق تصفية المخزن إذا كان محدداً
            IQueryable<Product> filteredProductsQuery = productsQuery;
            if (warehouseId.HasValue)
            {
                filteredProductsQuery = productsQuery.Where(p => p.ProductColors.Any(pc =>
                    pc.ProductQuantity.Any(pq => pq.WarehouseId == warehouseId.Value)));
            }

            var products = await filteredProductsQuery.ToListAsyncWithBusy("GetProductsForExport");

            int totalItems = products.Sum(p =>
                p.ProductColors.Sum(pc =>
                    pc.ProductQuantity.Count));

            int processedItems = 0;
            int row = 2;

            // إضافة بيانات المنتجات (محسنة - تسطيح البيانات أولاً)
            var flattenedProductData = products
                .SelectMany(product => product.ProductColors
                    .SelectMany(color =>
                    {
                        // تطبيق تصفية المخزن على الكميات
                        var quantities = warehouseId.HasValue
                            ? color.ProductQuantity.Where(pq => pq.WarehouseId == warehouseId.Value)
                            : color.ProductQuantity;

                        return quantities.Select(quantity => new
                        {
                            Product = product,
                            Color = color,
                            Quantity = quantity
                        });
                    }))
                .ToList();

            // معالجة البيانات المسطحة بكفاءة
            foreach (var item in flattenedProductData)
            {
                foreach (var field in selectedFields)
                {
                    if (headerMapping.TryGetValue(field, out int colIndex))
                    {
                        switch (field)
                        {
                            case "Name":
                                worksheet.Cell(row, colIndex).Value = item.Product.Name;
                                break;
                            case "Barcode":
                                worksheet.Cell(row, colIndex).Value = item.Color.Barcode;
                                break;
                            case "ColorCode":
                                worksheet.Cell(row, colIndex).Value = item.Color.Color?.HexCode;
                                break;
                            case "ColorName":
                                worksheet.Cell(row, colIndex).Value = item.Color.Color?.Name;
                                break;
                            case "CostPrice":
                                worksheet.Cell(row, colIndex).Value = item.Product.CostPrice;
                                break;
                            case "SellPrice":
                                worksheet.Cell(row, colIndex).Value = item.Product.SellPrice;
                                break;
                            case "Quantity":
                                worksheet.Cell(row, colIndex).Value = item.Quantity.Quantity;
                                break;
                            case "Expiration":
                                worksheet.Cell(row, colIndex).Value = item.Quantity.Exp.HasValue ? item.Quantity.Exp.Value.ToString("yyyy-MM-dd") : "";
                                break;
                            case "Warehouse":
                                worksheet.Cell(row, colIndex).Value = item.Quantity.Warehouse?.Name;
                                break;
                        }
                    }
                }

                row++;
                processedItems++;

                // تحديث التقدم
                if (processedItems % 10 == 0 || processedItems == totalItems)
                {
                    int percentage = (int)((processedItems * 100.0) / totalItems);
                    progress?.Report(percentage);
                    await Task.Delay(1); // للسماح بتحديث واجهة المستخدم
                }
            }

            // ضبط عرض الأعمدة
            worksheet.Columns().AdjustToContents();
            workbook.SaveAs(filePath);
        }
        // تعريف الحقول المتاحة لتصدير المبيعات
        public Dictionary<string, ExportFieldInfo> GetSalesExportFields()
        {
            return new Dictionary<string, ExportFieldInfo>
            {
                { "Index", new ExportFieldInfo { DisplayName = "الترقيم", IsRequired = true } },
                { "InvoiceNo", new ExportFieldInfo { DisplayName = "رقم الفاتورة", IsRequired = true } },
                { "SaleDate", new ExportFieldInfo { DisplayName = "تاريخ الفاتورة", IsRequired = true } },
                { "ClientName", new ExportFieldInfo { DisplayName = "العميل", IsRequired = true } },
                { "TotalBeforeDiscount", new ExportFieldInfo { DisplayName = "الإجمالي قبل الخصم", IsRequired = false } },
                { "TotalDiscount", new ExportFieldInfo { DisplayName = "قيمة الخصم", IsRequired = false } },
                { "TotalAmount", new ExportFieldInfo { DisplayName = "قيمة الفاتورة", IsRequired = true } },
                { "TotalReturned", new ExportFieldInfo { DisplayName = "قيمة المرتجع", IsRequired = false } },
                { "PaidAmount", new ExportFieldInfo { DisplayName = "القيمة المدفوعة", IsRequired = true } },
                { "RemainingAmount", new ExportFieldInfo { DisplayName = "المتبقي", IsRequired = true } }
            };
        }

        // تعريف الحقول المتاحة لتصدير المشتريات
        public Dictionary<string, ExportFieldInfo> GetPurchasesExportFields()
        {
            return new Dictionary<string, ExportFieldInfo>
            {
                { "Index", new ExportFieldInfo { DisplayName = "الترقيم", IsRequired = true } },
                { "InvoiceNo", new ExportFieldInfo { DisplayName = "رقم الفاتورة", IsRequired = true } },
                { "PurchaseDate", new ExportFieldInfo { DisplayName = "تاريخ الفاتورة", IsRequired = true } },
                { "ClientName", new ExportFieldInfo { DisplayName = "المورد", IsRequired = true } },
                { "TotalAmount", new ExportFieldInfo { DisplayName = "قيمة الفاتورة", IsRequired = true } },
                { "PaidAmount", new ExportFieldInfo { DisplayName = "القيمة المدفوعة", IsRequired = true } },
                { "RemainingAmount", new ExportFieldInfo { DisplayName = "المتبقي", IsRequired = true } }
            };
        }

        // تصدير المبيعات مع المعاينة إلى ملف Excel
        public async Task ExportSalesWithPreviewAsync(
            string filePath,
            List<SaleViewModel> sales,
            List<SaleItemVM> items,
            bool exportInvoices,
            bool exportItems,
            List<string> selectedInvoiceFields,
            List<string> selectedItemFields,
            Dictionary<string, string> invoiceHeaders,
            Dictionary<string, string> itemHeaders,
            int? clientId = null,
            int? warehouseId = null,
            IProgress<int> progress = null)
        {
            using var workbook = new XLWorkbook();

            int totalSteps = 0;
            int currentStep = 0;

            // حساب إجمالي الخطوات
            if (exportInvoices) totalSteps++;
            if (exportItems) totalSteps++;

            // إنشاء ورقة الفواتير إذا كانت مطلوبة
            if (exportInvoices && sales.Any())
            {
                var invoicesSheet = workbook.Worksheets.Add("الفواتير");
                await CreateInvoicesSheetWithPreview(invoicesSheet, sales, selectedInvoiceFields, invoiceHeaders, clientId, warehouseId);

                currentStep++;
                progress?.Report((currentStep * 100) / totalSteps);
            }

            // إنشاء ورقة الأصناف إذا كانت مطلوبة
            if (exportItems && items.Any())
            {
                var itemsSheet = workbook.Worksheets.Add("الأصناف");
                await CreateItemsSheetWithPreview(itemsSheet, items, selectedItemFields, itemHeaders, clientId, warehouseId);

                currentStep++;
                progress?.Report((currentStep * 100) / totalSteps);
            }

            // حفظ الملف
            await Task.Run(() => workbook.SaveAs(filePath));
            progress?.Report(100);
        }

        private async Task CreateInvoicesSheetWithPreview(
            IXLWorksheet sheet,
            List<SaleViewModel> sales,
            List<string> selectedFields,
            Dictionary<string, string> headers,
            int? clientId = null,
            int? warehouseId = null)
        {
            // تطبيق إعدادات عامة للورقة
            sheet.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center);
            sheet.Style.Font.SetFontName("Tahoma"); // خط أفضل للعربية
            sheet.Style.Font.SetFontSize(11);

            // تعيين اتجاه الكتابة من اليمين لليسار
            sheet.RightToLeft = true;

            // إعدادات إضافية لتحسين دعم RTL
            sheet.PageSetup.PageOrientation = XLPageOrientation.Portrait;
            sheet.PageSetup.PaperSize = XLPaperSize.A4Paper;

            // إضافة العنوان والمعلومات
            int startRow = await AddReportHeader(sheet, "تقرير فواتير المبيعات", clientId, warehouseId, selectedFields.Count);

            // إضافة رؤوس الأعمدة
            int col = 1;
            foreach (var field in selectedFields)
            {
                var header = headers.ContainsKey(field) ? headers[field] : field;
                sheet.Cell(startRow, col).Value = header;
                sheet.Cell(startRow, col).Style.Font.SetBold(true);
                sheet.Cell(startRow, col).Style.Font.SetFontName("Tahoma");
                sheet.Cell(startRow, col).Style.Font.SetFontSize(12);
                sheet.Cell(startRow, col).Style.Fill.SetBackgroundColor(XLColor.FromHtml("#134074"));
                sheet.Cell(startRow, col).Style.Font.FontColor = XLColor.White;
                sheet.Cell(startRow, col).Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                sheet.Cell(startRow, col).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                col++;
            }

            // إضافة البيانات
            int row = startRow + 1;
            int index = 1; // بداية الرقم التسلسلي
            foreach (var sale in sales)
            {
                col = 1;
                foreach (var field in selectedFields)
                {
                    var value = GetSaleFieldValue(sale, field, index);
                    var cell = sheet.Cell(row, col);
                    cell.Value = XLCellValue.FromObject(value);
                    cell.Style.Font.SetFontName("Tahoma");
                    cell.Style.Font.SetFontSize(11);
                    cell.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                    cell.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                    col++;
                }
                row++;
                index++; // زيادة الرقم التسلسلي
            }

            // تنسيق الجدول
            var dataRange = sheet.Range(startRow, 1, row - 1, selectedFields.Count);
            dataRange.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);
            dataRange.Style.Border.SetInsideBorder(XLBorderStyleValues.Thin);

            // ضبط عرض الأعمدة
            sheet.Columns().AdjustToContents();
        }

        private async Task CreateItemsSheetWithPreview(
            IXLWorksheet sheet,
            List<SaleItemVM> items,
            List<string> selectedFields,
            Dictionary<string, string> headers,
            int? clientId = null,
            int? warehouseId = null)
        {
            // تطبيق إعدادات عامة للورقة
            sheet.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center);
            sheet.Style.Font.SetFontName("Tahoma"); // خط أفضل للعربية
            sheet.Style.Font.SetFontSize(11);

            // تعيين اتجاه الكتابة من اليمين لليسار
            sheet.RightToLeft = true;

            // إعدادات إضافية لتحسين دعم RTL
            sheet.PageSetup.PageOrientation = XLPageOrientation.Portrait;
            sheet.PageSetup.PaperSize = XLPaperSize.A4Paper;

            // إضافة العنوان والمعلومات
            int startRow = await AddReportHeader(sheet, "تقرير أصناف المبيعات", clientId, warehouseId, selectedFields.Count);

            // إضافة رؤوس الأعمدة
            int col = 1;
            foreach (var field in selectedFields)
            {
                var header = headers.ContainsKey(field) ? headers[field] : field;
                sheet.Cell(startRow, col).Value = header;
                sheet.Cell(startRow, col).Style.Font.SetBold(true);
                sheet.Cell(startRow, col).Style.Font.SetFontName("Tahoma");
                sheet.Cell(startRow, col).Style.Font.SetFontSize(12);
                sheet.Cell(startRow, col).Style.Fill.SetBackgroundColor(XLColor.FromHtml("#134074"));
                sheet.Cell(startRow, col).Style.Font.FontColor = XLColor.White;
                sheet.Cell(startRow, col).Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                sheet.Cell(startRow, col).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                col++;
            }

            // إضافة البيانات
            int row = startRow + 1;
            foreach (var item in items)
            {
                col = 1;
                foreach (var field in selectedFields)
                {
                    var value = GetItemFieldValue(item, field);
                    var cell = sheet.Cell(row, col);
                    cell.Value = XLCellValue.FromObject(value);
                    cell.Style.Font.SetFontName("Tahoma");
                    cell.Style.Font.SetFontSize(11);
                    cell.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                    cell.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                    col++;
                }
                row++;
            }

            // تنسيق الجدول
            var dataRange = sheet.Range(startRow, 1, row - 1, selectedFields.Count);
            dataRange.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);
            dataRange.Style.Border.SetInsideBorder(XLBorderStyleValues.Thin);

            // ضبط عرض الأعمدة
            sheet.Columns().AdjustToContents();
        }

        private object GetSaleFieldValue(SaleViewModel sale, string fieldKey, int index = 0)
        {
            return fieldKey switch
            {
                "Index" => index, // استخدام الرقم التسلسلي المحسوب
                "InvoiceNumber" => sale.InvoiceNumber ?? sale.InvoiceNo.ToString(),
                "SaleDate" => sale.SaleDate.ToString("yyyy-MM-dd"),
                "ClientName" => sale.ClientName,
                "WarehouseName" => sale.WarehouseName,
                "TotalBeforeDiscount" => sale.TotalBeforeDiscount,
                "TotalDiscount" => sale.TotalDiscount,
                "TotalAmount" => sale.TotalAmount,
                "TotalReturned" => sale.TotalReturned,
                "NetAmount" => sale.NetAmount,
                "PaidAmount" => sale.PaidAmount,
                "RemainingAmount" => sale.RemainingAmount,
                _ => ""
            };
        }

        private object GetItemFieldValue(SaleItemVM item, string fieldKey)
        {
            return fieldKey switch
            {
                "SaleId" => item.SaleId,
                "ClientName" => item.ClientName ?? "",
                "WarehouseName" => item.WarehouseName ?? "",
                "Type" => item.Type,
                "Name" => item.Name,
                "ColorName" => item.ColorName ?? "",
                "Quantity" => item.Quantity,
                "ReturnedQuantity" => item.ReturnedQuantity,
                "RemainingQuantity" => item.RemainingQuantity,
                "CostPrice" => item.CostPrice,
                "OriginalPrice" => item.OriginalPrice,
                "Discount" => item.Discount,
                "SellPrice" => item.SellPrice,
                _ => ""
            };
        }

        private async Task<int> AddReportHeader(IXLWorksheet sheet, string reportTitle, int? clientId, int? warehouseId, int columnCount)
        {
            int currentRow = 1;

            // عنوان الشركة
            var companyCell = sheet.Cell(currentRow, 1);
            companyCell.Value = Properties.Settings.Default.CompanyName;
            companyCell.Style.Font.SetBold(true);
            companyCell.Style.Font.SetFontSize(18);
            companyCell.Style.Font.SetFontName("Tahoma");
            companyCell.Style.Font.FontColor = XLColor.FromHtml("#134074");
            companyCell.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Range(currentRow, 1, currentRow, columnCount).Merge();
            currentRow++;

            // عنوان التقرير
            var titleCell = sheet.Cell(currentRow, 1);
            titleCell.Value = reportTitle;
            titleCell.Style.Font.SetBold(true);
            titleCell.Style.Font.SetFontSize(16);
            titleCell.Style.Font.SetFontName("Tahoma");
            titleCell.Style.Font.FontColor = XLColor.FromHtml("#134074");
            titleCell.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Range(currentRow, 1, currentRow, columnCount).Merge();
            currentRow++;

            // معلومات إضافية
            var infoRow = currentRow;

            // العميل والمخزن
            string clientInfo = "جميع العملاء";
            string warehouseInfo = "جميع المخازن";

            if (clientId.HasValue && clientId.Value > 0)
            {
                var client = await _context.Clients.FindAsyncWithBusy(clientId.Value);
                clientInfo = client?.Name ?? "عميل غير معروف";
            }

            if (warehouseId.HasValue && warehouseId.Value != -1)
            {
                var warehouse = await _context.Warehouses.FindAsyncWithBusy(warehouseId.Value);
                warehouseInfo = warehouse?.Name ?? "مخزن غير معروف";
            }

            // الجانب الأيمن - معلومات العميل والمخزن (في RTL يصبح الأيمن هو البداية)
            var rightInfoCell = sheet.Cell(infoRow, 1);
            rightInfoCell.Value = $"العميل: {clientInfo}\nالمخزن: {warehouseInfo}";
            rightInfoCell.Style.Font.SetBold(true);
            rightInfoCell.Style.Font.SetFontSize(12);
            rightInfoCell.Style.Font.SetFontName("Tahoma");
            rightInfoCell.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Right);
            rightInfoCell.Style.Alignment.SetVertical(XLAlignmentVerticalValues.Top);
            rightInfoCell.Style.Alignment.WrapText = true;

            // الجانب الأيسر - التاريخ (في RTL يصبح الأيسر هو النهاية)
            var leftInfoCell = sheet.Cell(infoRow, columnCount);
            leftInfoCell.Value = $"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd}\nوقت الإنشاء: {DateTime.Now:HH:mm}";
            leftInfoCell.Style.Font.SetBold(true);
            leftInfoCell.Style.Font.SetFontSize(12);
            leftInfoCell.Style.Font.SetFontName("Tahoma");
            leftInfoCell.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Left);
            leftInfoCell.Style.Alignment.SetVertical(XLAlignmentVerticalValues.Top);
            leftInfoCell.Style.Alignment.WrapText = true;

            // تحديد ارتفاع الصف
            sheet.Row(infoRow).Height = 35;
            currentRow++;

            // خط فاصل
            var separatorRange = sheet.Range(currentRow, 1, currentRow, columnCount);
            separatorRange.Style.Border.SetTopBorder(XLBorderStyleValues.Medium);
            separatorRange.Style.Border.TopBorderColor = XLColor.FromHtml("#134074");
            currentRow++;

            // سطر فارغ
            currentRow++;

            return currentRow;
        }

        // تصدير المبيعات إلى ملف Excel (الطريقة القديمة للتوافق)
        public async Task ExportSalesToExcel(string filePath, List<SaleViewModel> sales, int? clientId = null, int? warehouseId = null, IProgress<int> progress = null)
        {
            using var workbook = new XLWorkbook();

            // إنشاء ورقة الفواتير
            var invoicesSheet = workbook.Worksheets.Add("الفواتير");

            // تطبيق إعدادات عامة للورقة
            invoicesSheet.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            invoicesSheet.Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center);
            invoicesSheet.Style.Font.SetFontName("Arial");
            invoicesSheet.Style.Font.SetFontSize(10);

            // إضافة معلومات العميل والمخزن إذا كانا محددين
            int startRow = 1;
            bool hasClientInfo = clientId.HasValue && clientId.Value > 0;
            bool hasWarehouseInfo = warehouseId.HasValue && warehouseId.Value != -1;

            if (hasClientInfo || hasWarehouseInfo)
            {
                // إنشاء عنوان للتقرير
                invoicesSheet.Cell(1, 1).Value = "تقرير المبيعات";
                var titleCell = invoicesSheet.Cell(1, 1);
                titleCell.Style.Font.Bold = true;
                titleCell.Style.Font.FontSize = 16;
                titleCell.Style.Font.FontColor = XLColor.FromHtml("#1F4E79");
                titleCell.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                invoicesSheet.Range(1, 1, 1, 10).Merge();

                int infoRow = 3;

                if (hasClientInfo)
                {
                    var client = await _context.Clients.FindAsyncWithBusy(clientId.Value);
                    if (client != null)
                    {

                        // إنشاء إطار للمعلومات العميل
                        var clientHeaderRange = invoicesSheet.Range(infoRow, 1, infoRow, 2);
                        clientHeaderRange.Merge();
                        clientHeaderRange.Value = "معلومات العميل";
                        clientHeaderRange.Style.Font.Bold = true;
                        clientHeaderRange.Style.Font.FontSize = 12;
                        clientHeaderRange.Style.Font.FontColor = XLColor.White;
                        clientHeaderRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#4472C4");
                        clientHeaderRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                        clientHeaderRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;

                        // إنشاء جدول معلومات العميل
                        var clientInfoRange = invoicesSheet.Range(infoRow + 1, 1, infoRow + 2, 4);
                        clientInfoRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#E6F2FF");
                        clientInfoRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                        clientInfoRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                        clientInfoRange.Style.Border.OutsideBorderColor = XLColor.FromHtml("#BFDBFF");
                        clientInfoRange.Style.Border.InsideBorderColor = XLColor.FromHtml("#BFDBFF");

                        // إضافة عنوان بمعلومات العميل
                        invoicesSheet.Cell(infoRow + 1, 1).Value = "العميل:";
                        invoicesSheet.Cell(infoRow + 1, 2).Value = client.Name;
                        invoicesSheet.Cell(infoRow + 1, 1).Style.Font.Bold = true;
                        invoicesSheet.Cell(infoRow + 1, 2).Style.Font.Bold = true;
                        invoicesSheet.Cell(infoRow + 1, 2).Style.Font.FontColor = XLColor.FromHtml("#0066CC");

                        invoicesSheet.Cell(infoRow + 2, 1).Value = "الرصيد الحالي:";
                        invoicesSheet.Cell(infoRow + 2, 2).Value = client.Balance;
                        invoicesSheet.Cell(infoRow + 2, 1).Style.Font.Bold = true;
                        invoicesSheet.Cell(infoRow + 2, 2).Style.Font.Bold = true;
                        invoicesSheet.Cell(infoRow + 2, 2).Style.NumberFormat.Format = "#,##0.00";

                        // تطبيق محاذاة النص العربي من اليمين إلى اليسار
                        clientInfoRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Right);

                        // إضافة تاريخ التقرير
                        invoicesSheet.Cell(infoRow + 1, 3).Value = "تاريخ التقرير:";
                        invoicesSheet.Cell(infoRow + 1, 4).Value = DateTime.Now;
                        invoicesSheet.Cell(infoRow + 1, 3).Style.Font.Bold = true;
                        invoicesSheet.Cell(infoRow + 1, 4).Style.DateFormat.Format = "yyyy-MM-dd";

                        infoRow += 4; // تحديث موقع الصف التالي
                    }
                }

                if (hasWarehouseInfo)
                {
                    var warehouse = await _context.Warehouses.FindAsyncWithBusy(warehouseId.Value);
                    if (warehouse != null)
                    {
                        // إنشاء إطار لمعلومات المخزن
                        var warehouseHeaderRange = invoicesSheet.Range(infoRow, 1, infoRow, 2);
                        warehouseHeaderRange.Merge();
                        warehouseHeaderRange.Value = "معلومات المخزن";
                        warehouseHeaderRange.Style.Font.Bold = true;
                        warehouseHeaderRange.Style.Font.FontSize = 12;
                        warehouseHeaderRange.Style.Font.FontColor = XLColor.White;
                        warehouseHeaderRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#70AD47");
                        warehouseHeaderRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                        warehouseHeaderRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;

                        // إنشاء جدول معلومات المخزن
                        var warehouseInfoRange = invoicesSheet.Range(infoRow + 1, 1, infoRow + 1, 4);
                        warehouseInfoRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#E2EFDA");
                        warehouseInfoRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                        warehouseInfoRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                        warehouseInfoRange.Style.Border.OutsideBorderColor = XLColor.FromHtml("#A9D18E");
                        warehouseInfoRange.Style.Border.InsideBorderColor = XLColor.FromHtml("#A9D18E");

                        // إضافة معلومات المخزن
                        invoicesSheet.Cell(infoRow + 1, 1).Value = "المخزن:";
                        invoicesSheet.Cell(infoRow + 1, 2).Value = warehouse.Name;
                        invoicesSheet.Cell(infoRow + 1, 1).Style.Font.Bold = true;
                        invoicesSheet.Cell(infoRow + 1, 2).Style.Font.Bold = true;
                        invoicesSheet.Cell(infoRow + 1, 2).Style.Font.FontColor = XLColor.FromHtml("#548235");

                        // تطبيق محاذاة النص العربي من اليمين إلى اليسار
                        warehouseInfoRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Right);

                        infoRow += 3; // تحديث موقع الصف التالي
                    }
                }

                // ترك سطر فارغ
                startRow = infoRow + 1;
            }

            // إضافة رأس الجدول
            invoicesSheet.Cell(startRow, 1).Value = "الترقيم";
            invoicesSheet.Cell(startRow, 2).Value = "رقم الفاتورة";
            invoicesSheet.Cell(startRow, 3).Value = "تاريخ الفاتورة";

            int columnIndex = 4;
            bool showClientColumn = (clientId == null || clientId <= 0);
            bool showWarehouseColumn = (warehouseId == null || warehouseId <= 0);

            if (showClientColumn)
            {
                invoicesSheet.Cell(startRow, columnIndex).Value = "العميل";
                columnIndex++;
            }

            if (showWarehouseColumn)
            {
                invoicesSheet.Cell(startRow, columnIndex).Value = "المخزن";
                columnIndex++;
            }

            invoicesSheet.Cell(startRow, columnIndex).Value = "قيمة الفاتورة";
            invoicesSheet.Cell(startRow, columnIndex + 1).Value = "قيمة المرتجع";
            invoicesSheet.Cell(startRow, columnIndex + 2).Value = "صافي الفاتورة";
            invoicesSheet.Cell(startRow, columnIndex + 3).Value = "القيمة المدفوعة";
            invoicesSheet.Cell(startRow, columnIndex + 4).Value = "المتبقي";

            // تحديد نطاق رأس الجدول
            int lastHeaderColumn = columnIndex + 4;
            var headerRange = invoicesSheet.Range(startRow, 1, startRow, lastHeaderColumn);

            // تنسيق رأس الجدول
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#4472C4");
            headerRange.Style.Font.FontColor = XLColor.White;
            headerRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            headerRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
            headerRange.Style.Border.OutsideBorderColor = XLColor.FromHtml("#2F528F");
            headerRange.Style.Border.InsideBorderColor = XLColor.FromHtml("#2F528F");
            headerRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);

            // تعديل ارتفاع صف الرأس
            invoicesSheet.Row(startRow).Height = 20;

            // إضافة البيانات (محسنة - حساب الإجماليات مسبقاً)
            int row = startRow + 1;

            // حساب الإجماليات باستخدام LINQ (محسنة)
            var totals = sales.Aggregate(new
            {
                TotalAmount = 0m,
                TotalReturned = 0m,
                TotalNetAmount = 0m,
                TotalPaid = 0m,
                TotalRemaining = 0m
            },
            (acc, sale) =>
            {
                decimal netAmount = sale.TotalAmount - sale.TotalReturned;
                decimal remainingAmount = netAmount - sale.PaidAmount;
                return new
                {
                    TotalAmount = acc.TotalAmount + sale.TotalAmount,
                    TotalReturned = acc.TotalReturned + sale.TotalReturned,
                    TotalNetAmount = acc.TotalNetAmount + netAmount,
                    TotalPaid = acc.TotalPaid + sale.PaidAmount,
                    TotalRemaining = acc.TotalRemaining + remainingAmount
                };
            });

            foreach (var sale in sales)
            {
                // حساب صافي الفاتورة (إجمالي الفاتورة - قيمة المرتجع)
                decimal netAmount = sale.TotalAmount - sale.TotalReturned;
                decimal remainingAmount = netAmount - sale.PaidAmount;

                invoicesSheet.Cell(row, 1).Value = sale.Index;
                invoicesSheet.Cell(row, 2).Value = sale.InvoiceNo;
                invoicesSheet.Cell(row, 3).Value = sale.SaleDate;
                invoicesSheet.Cell(row, 3).Style.DateFormat.Format = "yyyy-MM-dd";

                columnIndex = 4;

                if (showClientColumn)
                {
                    invoicesSheet.Cell(row, columnIndex).Value = sale.ClientName;
                    columnIndex++;
                }

                if (showWarehouseColumn)
                {
                    invoicesSheet.Cell(row, columnIndex).Value = sale.WarehouseName;
                    columnIndex++;
                }

                invoicesSheet.Cell(row, columnIndex).Value = sale.TotalAmount;
                invoicesSheet.Cell(row, columnIndex + 1).Value = sale.TotalReturned;
                invoicesSheet.Cell(row, columnIndex + 2).Value = netAmount;
                invoicesSheet.Cell(row, columnIndex + 3).Value = sale.PaidAmount;
                invoicesSheet.Cell(row, columnIndex + 4).Value = remainingAmount;

                // تنسيق الأرقام
                invoicesSheet.Cell(row, columnIndex).Style.NumberFormat.Format = "#,##0.00";
                invoicesSheet.Cell(row, columnIndex + 1).Style.NumberFormat.Format = "#,##0.00";
                invoicesSheet.Cell(row, columnIndex + 2).Style.NumberFormat.Format = "#,##0.00";
                invoicesSheet.Cell(row, columnIndex + 3).Style.NumberFormat.Format = "#,##0.00";
                invoicesSheet.Cell(row, columnIndex + 4).Style.NumberFormat.Format = "#,##0.00";

                // الإجماليات محسوبة مسبقاً - لا حاجة لحسابها هنا

                // تنسيق صف البيانات
                var dataRange = invoicesSheet.Range(row, 1, row, lastHeaderColumn);
                dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                dataRange.Style.Border.OutsideBorderColor = XLColor.FromHtml("#D9D9D9");
                dataRange.Style.Border.InsideBorderColor = XLColor.FromHtml("#D9D9D9");

                // تطبيق ألوان متناوبة للصفوف
                if ((row - startRow) % 2 == 0)
                {
                    dataRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#F2F2F2");
                }

                row++;

                // تحديث التقدم
                if (progress != null)
                {
                    int percentage = (int)((row - startRow) * 50.0 / sales.Count);
                    progress.Report(percentage);
                }
            }

            // إضافة صف الإجمالي
            int totalRow = row;
            invoicesSheet.Cell(totalRow, 1).Value = "الإجمالي";

            // دمج الخلايا من البداية حتى العمود قبل الأخير للقيم المالية
            int mergeEndColumn = columnIndex - 1;
            if (mergeEndColumn > 1)
            {
                invoicesSheet.Range(totalRow, 1, totalRow, mergeEndColumn).Merge();
            }

            invoicesSheet.Cell(totalRow, columnIndex).Value = totals.TotalAmount;
            invoicesSheet.Cell(totalRow, columnIndex + 1).Value = totals.TotalReturned;
            invoicesSheet.Cell(totalRow, columnIndex + 2).Value = totals.TotalNetAmount;
            invoicesSheet.Cell(totalRow, columnIndex + 3).Value = totals.TotalPaid;
            invoicesSheet.Cell(totalRow, columnIndex + 4).Value = totals.TotalRemaining;

            // تنسيق الأرقام
            invoicesSheet.Cell(totalRow, columnIndex).Style.NumberFormat.Format = "#,##0.00";
            invoicesSheet.Cell(totalRow, columnIndex + 1).Style.NumberFormat.Format = "#,##0.00";
            invoicesSheet.Cell(totalRow, columnIndex + 2).Style.NumberFormat.Format = "#,##0.00";
            invoicesSheet.Cell(totalRow, columnIndex + 3).Style.NumberFormat.Format = "#,##0.00";
            invoicesSheet.Cell(totalRow, columnIndex + 4).Style.NumberFormat.Format = "#,##0.00";

            // تنسيق صف الإجمالي
            var totalRowRange = invoicesSheet.Range(totalRow, 1, totalRow, lastHeaderColumn);
            totalRowRange.Style.Font.Bold = true;
            totalRowRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#FFD966");
            totalRowRange.Style.Font.FontColor = XLColor.FromHtml("#7F6000");
            totalRowRange.Style.Border.OutsideBorder = XLBorderStyleValues.Medium;
            totalRowRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
            totalRowRange.Style.Border.OutsideBorderColor = XLColor.FromHtml("#BF9000");
            totalRowRange.Style.Border.InsideBorderColor = XLColor.FromHtml("#BF9000");
            invoicesSheet.Row(totalRow).Height = 20;

            // إنشاء ورقة الأصناف
            var itemsSheet = workbook.Worksheets.Add("الأصناف");

            // تطبيق إعدادات عامة للورقة
            itemsSheet.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            itemsSheet.Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center);
            itemsSheet.Style.Font.SetFontName("Arial");
            itemsSheet.Style.Font.SetFontSize(10);

            // إضافة معلومات العميل والمخزن إذا كانا محددين في ورقة الأصناف أيضاً
            int itemsStartRow = 1;
            bool hasClientInfoInItems = clientId.HasValue && clientId.Value > 0;
            bool hasWarehouseInfoInItems = warehouseId.HasValue && warehouseId.Value != -1;

            if (hasClientInfoInItems || hasWarehouseInfoInItems)
            {
                // إنشاء عنوان للتقرير
                itemsSheet.Cell(1, 1).Value = "تفاصيل الأصناف";
                var titleCell = itemsSheet.Cell(1, 1);
                titleCell.Style.Font.Bold = true;
                titleCell.Style.Font.FontSize = 16;
                titleCell.Style.Font.FontColor = XLColor.FromHtml("#1F4E79");
                titleCell.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                itemsSheet.Range(1, 1, 1, 11).Merge();

                int itemsInfoRow = 3;

                if (hasClientInfoInItems)
                {
                    var client = await _context.Clients.FindAsyncWithBusy(clientId.Value);
                    if (client != null)
                    {
                        // إنشاء إطار للمعلومات العميل
                        var clientHeaderRange = itemsSheet.Range(itemsInfoRow, 1, itemsInfoRow, 2);
                        clientHeaderRange.Merge();
                        clientHeaderRange.Value = "معلومات العميل";
                        clientHeaderRange.Style.Font.Bold = true;
                        clientHeaderRange.Style.Font.FontSize = 12;
                        clientHeaderRange.Style.Font.FontColor = XLColor.White;
                        clientHeaderRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#4472C4");
                        clientHeaderRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                        clientHeaderRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;

                        // إنشاء جدول معلومات العميل
                        var clientInfoRange = itemsSheet.Range(itemsInfoRow + 1, 1, itemsInfoRow + 2, 4);
                        clientInfoRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#E6F2FF");
                        clientInfoRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                        clientInfoRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                        clientInfoRange.Style.Border.OutsideBorderColor = XLColor.FromHtml("#BFDBFF");
                        clientInfoRange.Style.Border.InsideBorderColor = XLColor.FromHtml("#BFDBFF");

                        // إضافة عنوان بمعلومات العميل
                        itemsSheet.Cell(itemsInfoRow + 1, 1).Value = "العميل:";
                        itemsSheet.Cell(itemsInfoRow + 1, 2).Value = client.Name;
                        itemsSheet.Cell(itemsInfoRow + 1, 1).Style.Font.Bold = true;
                        itemsSheet.Cell(itemsInfoRow + 1, 2).Style.Font.Bold = true;
                        itemsSheet.Cell(itemsInfoRow + 1, 2).Style.Font.FontColor = XLColor.FromHtml("#0066CC");

                        itemsSheet.Cell(itemsInfoRow + 2, 1).Value = "الرصيد الحالي:";
                        itemsSheet.Cell(itemsInfoRow + 2, 2).Value = client.Balance;
                        itemsSheet.Cell(itemsInfoRow + 2, 1).Style.Font.Bold = true;
                        itemsSheet.Cell(itemsInfoRow + 2, 2).Style.Font.Bold = true;
                        itemsSheet.Cell(itemsInfoRow + 2, 2).Style.NumberFormat.Format = "#,##0.00";

                        // تطبيق محاذاة النص العربي من اليمين إلى اليسار
                        clientInfoRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Right);

                        // إضافة تاريخ التقرير
                        itemsSheet.Cell(itemsInfoRow + 1, 3).Value = "تاريخ التقرير:";
                        itemsSheet.Cell(itemsInfoRow + 1, 4).Value = DateTime.Now;
                        itemsSheet.Cell(itemsInfoRow + 1, 3).Style.Font.Bold = true;
                        itemsSheet.Cell(itemsInfoRow + 1, 4).Style.DateFormat.Format = "yyyy-MM-dd";

                        itemsInfoRow += 4; // تحديث موقع الصف التالي
                    }
                }

                if (hasWarehouseInfoInItems)
                {
                    var warehouse = await _context.Warehouses.FindAsyncWithBusy(warehouseId.Value);
                    if (warehouse != null)
                    {
                        // إنشاء إطار لمعلومات المخزن
                        var warehouseHeaderRange = itemsSheet.Range(itemsInfoRow, 1, itemsInfoRow, 2);
                        warehouseHeaderRange.Merge();
                        warehouseHeaderRange.Value = "معلومات المخزن";
                        warehouseHeaderRange.Style.Font.Bold = true;
                        warehouseHeaderRange.Style.Font.FontSize = 12;
                        warehouseHeaderRange.Style.Font.FontColor = XLColor.White;
                        warehouseHeaderRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#70AD47");
                        warehouseHeaderRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                        warehouseHeaderRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;

                        // إنشاء جدول معلومات المخزن
                        var warehouseInfoRange = itemsSheet.Range(itemsInfoRow + 1, 1, itemsInfoRow + 1, 4);
                        warehouseInfoRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#E2EFDA");
                        warehouseInfoRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                        warehouseInfoRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                        warehouseInfoRange.Style.Border.OutsideBorderColor = XLColor.FromHtml("#A9D18E");
                        warehouseInfoRange.Style.Border.InsideBorderColor = XLColor.FromHtml("#A9D18E");

                        // إضافة معلومات المخزن
                        itemsSheet.Cell(itemsInfoRow + 1, 1).Value = "المخزن:";
                        itemsSheet.Cell(itemsInfoRow + 1, 2).Value = warehouse.Name;
                        itemsSheet.Cell(itemsInfoRow + 1, 1).Style.Font.Bold = true;
                        itemsSheet.Cell(itemsInfoRow + 1, 2).Style.Font.Bold = true;
                        itemsSheet.Cell(itemsInfoRow + 1, 2).Style.Font.FontColor = XLColor.FromHtml("#548235");

                        // تطبيق محاذاة النص العربي من اليمين إلى اليسار
                        warehouseInfoRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Right);

                        itemsInfoRow += 3; // تحديث موقع الصف التالي
                    }
                }

                // ترك سطر فارغ
                itemsStartRow = itemsInfoRow + 1;
            }

            // إضافة رأس جدول الأصناف
            itemsSheet.Cell(itemsStartRow, 1).Value = "رقم الفاتورة";
            itemsSheet.Cell(itemsStartRow, 2).Value = "تاريخ الفاتورة";

            int itemsColumnIndex = 3;
            bool showClientColumnInItems = (clientId == null || clientId <= 0);
            bool showWarehouseColumnInItems = (warehouseId == null || warehouseId <= 0);

            if (showClientColumnInItems)
            {
                itemsSheet.Cell(itemsStartRow, itemsColumnIndex).Value = "العميل";
                itemsColumnIndex++;
            }

            if (showWarehouseColumnInItems)
            {
                itemsSheet.Cell(itemsStartRow, itemsColumnIndex).Value = "المخزن";
                itemsColumnIndex++;
            }

            itemsSheet.Cell(itemsStartRow, itemsColumnIndex).Value = "الصنف";
            itemsSheet.Cell(itemsStartRow, itemsColumnIndex + 1).Value = "الكمية";
            itemsSheet.Cell(itemsStartRow, itemsColumnIndex + 2).Value = "الكمية المرتجعة";
            itemsSheet.Cell(itemsStartRow, itemsColumnIndex + 3).Value = "الكمية المتبقية";
            itemsSheet.Cell(itemsStartRow, itemsColumnIndex + 4).Value = "السعر";
            itemsSheet.Cell(itemsStartRow, itemsColumnIndex + 5).Value = "الخصم";
            itemsSheet.Cell(itemsStartRow, itemsColumnIndex + 6).Value = "الإجمالي";

            // تحديد نطاق رأس الجدول
            int itemsLastHeaderColumn = itemsColumnIndex + 6;
            var itemsHeaderRange = itemsSheet.Range(itemsStartRow, 1, itemsStartRow, itemsLastHeaderColumn);

            // تنسيق رأس الجدول
            itemsHeaderRange.Style.Font.Bold = true;
            itemsHeaderRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#4472C4");
            itemsHeaderRange.Style.Font.FontColor = XLColor.White;
            itemsHeaderRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            itemsHeaderRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
            itemsHeaderRange.Style.Border.OutsideBorderColor = XLColor.FromHtml("#2F528F");
            itemsHeaderRange.Style.Border.InsideBorderColor = XLColor.FromHtml("#2F528F");
            itemsHeaderRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);

            // تعديل ارتفاع صف الرأس
            itemsSheet.Row(itemsStartRow).Height = 20;

            // جلب تفاصيل الفواتير
            var saleIds = sales.Select(s => s.Id).ToList();
            var saleItems = await _context.SaleItems
                .Include(si => si.Sale).ThenInclude(x => x.Client)
                .Include(si => si.Sale).ThenInclude(x => x.Warehouse)
                .Include(si => si.ProductQuantity)
                    .ThenInclude(pq => pq.ProductColor)
                    .ThenInclude(pc => pc.Product)
                .Include(si => si.ProductQuantity)
                    .ThenInclude(pq => pq.ProductColor)
                    .ThenInclude(pc => pc.Color)
                .Include(si => si.LensQuantityRight)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Sphere)
                .Include(si => si.LensQuantityRight)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Cylinder)
                .Include(si => si.LensQuantityRight)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Pow)
                .Include(si => si.LensQuantityRight)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Lens)
                    .ThenInclude(l => l.Category)
                .Include(si => si.LensQuantityLeft)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Sphere)
                .Include(si => si.LensQuantityLeft)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Cylinder)
                .Include(si => si.LensQuantityLeft)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Pow)
                .Include(si => si.LensQuantityLeft)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Lens)
                    .ThenInclude(l => l.Category)
                .Include(si => si.Service)
                .Where(si => saleIds.Contains(si.SaleId))
                .ToListAsyncWithBusy("GetSaleItemsForExport");

            // إضافة بيانات الأصناف
            row = itemsStartRow + 1;
            decimal totalItemsAmount = 0;

            foreach (var item in saleItems)
            {
                itemsSheet.Cell(row, 1).Value = item.Sale.InvoiceNo;
                itemsSheet.Cell(row, 2).Value = item.Sale.SaleDate;
                itemsSheet.Cell(row, 2).Style.DateFormat.Format = "yyyy-MM-dd";

                // تحديد نوع الصنف واسمه
                string itemName = "";
                if (item.ProductQuantity != null)
                {
                    var product = item.ProductQuantity.ProductColor.Product;
                    var color = item.ProductQuantity.ProductColor.Color;

                    // اسم المنتج الأساسي
                    itemName = product.Name;

                    // إضافة معلومات إضافية عن المنتج
                    var productDetails = new List<string>();

                    // إضافة معلومات عن المنتج مثل الحد الأدنى للكمية إذا كان أكبر من صفر
                    if (product.MinimumQuantity > 0)
                        productDetails.Add($"الحد الأدنى: {product.MinimumQuantity}");

                    // إضافة معلومات عن تاريخ الصلاحية إذا كان المنتج له تاريخ صلاحية
                    if (product.Exp)
                        productDetails.Add("له تاريخ صلاحية");

                    // إضافة معلومات المنتج إلى الاسم
                    if (productDetails.Count > 0)
                    {
                        itemName += " - " + string.Join(" / ", productDetails);
                    }

                    // إضافة اللون بشكل بارز
                    if (color != null)
                    {
                        itemName += " - لون: " + color.Name;
                    }
                }
                else if (item.LensQuantityRight != null)
                {
                    var lensColor = item.LensQuantityRight.LensPrescriptionColor;
                    var lensPrescription = lensColor?.LensPrescription;
                    var lens = lensPrescription?.Lens;

                    if (lens != null)
                    {
                        // اسم العدسة الأساسي مع الفئة
                        itemName = lens.Name + (lens.Category != null ? " | " + lens.Category.Name : "");

                        // إضافة معلومات الوصفة الطبية
                        var specs = new List<string>();

                        // إضافة معلومات SPH, CYL, POW بشكل بارز
                        var prescriptionInfo = new List<string>();
                        if (lensPrescription.Sphere != null)
                            prescriptionInfo.Add($"SPH: {(lensPrescription.Sphere.Value == 0 ? "PL" : lensPrescription.Sphere.Value.ToString("F2"))}");

                        if (lensPrescription.Cylinder != null)
                            prescriptionInfo.Add($"CYL: {(lensPrescription.Cylinder.Value == 0 ? "PL" : lensPrescription.Cylinder.Value.ToString("F2"))}");

                        if (lensPrescription.Pow != null)
                            prescriptionInfo.Add($"POW: {(lensPrescription.Pow.Value == 0 ? "PL" : lensPrescription.Pow.Value.ToString("F2"))}");

                        // إضافة معلومات الوصفة الطبية الرئيسية أولاً
                        if (prescriptionInfo.Count > 0)
                            specs.Add($"[{string.Join(" | ", prescriptionInfo)}]");

                        // إضافة المعلومات الإضافية
                        if (item.Axis.HasValue)
                            specs.Add($"Axis: {item.Axis.Value}");

                        if (lens.BC.HasValue)
                            specs.Add($"BC: {lens.BC.Value.ToString("F2")}");

                        if (lens.Dia.HasValue)
                            specs.Add($"Dia: {lens.Dia.Value.ToString("F2")}");

                        // إضافة معلومات الوصفة الطبية إلى اسم العدسة بشكل بارز
                        if (specs.Count > 0)
                        {
                            itemName += " || " + string.Join(" || ", specs);
                        }

                        // إضافة جهة العين
                        itemName += " (يمين)";

                        // إضافة اللون بشكل بارز إذا كان متوفراً
                        if (lensColor?.Color != null)
                        {
                            itemName += " - لون: " + lensColor.Color.Name;
                        }
                    }
                    else
                    {
                        itemName = "عدسة يمين";
                    }
                }
                else if (item.LensQuantityLeft != null)
                {
                    var lensColor = item.LensQuantityLeft.LensPrescriptionColor;
                    var lensPrescription = lensColor?.LensPrescription;
                    var lens = lensPrescription?.Lens;

                    if (lens != null)
                    {
                        // اسم العدسة الأساسي مع الفئة
                        itemName = lens.Name + (lens.Category != null ? " | " + lens.Category.Name : "");

                        // إضافة معلومات الوصفة الطبية
                        var specs = new List<string>();

                        // إضافة معلومات SPH, CYL, POW بشكل بارز
                        var prescriptionInfo = new List<string>();
                        if (lensPrescription.Sphere != null)
                            prescriptionInfo.Add($"SPH: {(lensPrescription.Sphere.Value == 0 ? "PL" : lensPrescription.Sphere.Value.ToString("F2"))}");

                        if (lensPrescription.Cylinder != null)
                            prescriptionInfo.Add($"CYL: {(lensPrescription.Cylinder.Value == 0 ? "PL" : lensPrescription.Cylinder.Value.ToString("F2"))}");

                        if (lensPrescription.Pow != null)
                            prescriptionInfo.Add($"POW: {(lensPrescription.Pow.Value == 0 ? "PL" : lensPrescription.Pow.Value.ToString("F2"))}");

                        // إضافة معلومات الوصفة الطبية الرئيسية أولاً
                        if (prescriptionInfo.Count > 0)
                            specs.Add($"[{string.Join(" | ", prescriptionInfo)}]");

                        // إضافة المعلومات الإضافية
                        if (item.Axis.HasValue)
                            specs.Add($"Axis: {item.Axis.Value}");

                        if (lens.BC.HasValue)
                            specs.Add($"BC: {lens.BC.Value.ToString("F2")}");

                        if (lens.Dia.HasValue)
                            specs.Add($"Dia: {lens.Dia.Value.ToString("F2")}");

                        // إضافة معلومات الوصفة الطبية إلى اسم العدسة بشكل بارز
                        if (specs.Count > 0)
                        {
                            itemName += " || " + string.Join(" || ", specs);
                        }

                        // إضافة جهة العين
                        itemName += " (يسار)";

                        // إضافة اللون بشكل بارز إذا كان متوفراً
                        if (lensColor?.Color != null)
                        {
                            itemName += " - لون: " + lensColor.Color.Name;
                        }
                    }
                    else
                    {
                        itemName = "عدسة يسار";
                    }
                }
                else if (item.Service != null)
                {
                    itemName = item.Service.Name;
                }

                decimal itemTotal = item.SellPrice * item.Quantity;
                totalItemsAmount += itemTotal;

                // حساب الكمية المتبقية
                int remainingQuantity = item.Quantity - item.ReturnedQuantity;

                itemsColumnIndex = 3;

                if (showClientColumnInItems)
                {
                    itemsSheet.Cell(row, itemsColumnIndex).Value = item.Sale.Client.Name;
                    itemsColumnIndex++;
                }

                if (showWarehouseColumnInItems)
                {
                    itemsSheet.Cell(row, itemsColumnIndex).Value = item.Sale.Warehouse?.Name ?? "غير محدد";
                    itemsColumnIndex++;
                }

                itemsSheet.Cell(row, itemsColumnIndex).Value = itemName;
                itemsSheet.Cell(row, itemsColumnIndex + 1).Value = item.Quantity;
                itemsSheet.Cell(row, itemsColumnIndex + 2).Value = item.ReturnedQuantity;
                itemsSheet.Cell(row, itemsColumnIndex + 3).Value = remainingQuantity;
                itemsSheet.Cell(row, itemsColumnIndex + 4).Value = item.OriginalPrice;
                itemsSheet.Cell(row, itemsColumnIndex + 5).Value = item.Discount;
                itemsSheet.Cell(row, itemsColumnIndex + 6).Value = itemTotal;

                // تنسيق الأرقام
                itemsSheet.Cell(row, itemsColumnIndex + 4).Style.NumberFormat.Format = "#,##0.00";
                itemsSheet.Cell(row, itemsColumnIndex + 5).Style.NumberFormat.Format = "#,##0.00";
                itemsSheet.Cell(row, itemsColumnIndex + 6).Style.NumberFormat.Format = "#,##0.00";

                // تنسيق صف البيانات
                var dataRange = itemsSheet.Range(row, 1, row, itemsLastHeaderColumn);
                dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                dataRange.Style.Border.OutsideBorderColor = XLColor.FromHtml("#D9D9D9");
                dataRange.Style.Border.InsideBorderColor = XLColor.FromHtml("#D9D9D9");

                // تطبيق ألوان متناوبة للصفوف
                if ((row - itemsStartRow) % 2 == 0)
                {
                    dataRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#F2F2F2");
                }

                // تلوين الصف إذا كان هناك مرتجعات
                if (item.ReturnedQuantity > 0)
                {
                    dataRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#FFF2CC");
                    dataRange.Style.Font.FontColor = XLColor.FromHtml("#7F6000");
                }

                row++;

                // تحديث التقدم
                if (progress != null)
                {
                    int percentage = 50 + (int)((row - itemsStartRow - 1) * 50.0 / saleItems.Count);
                    progress.Report(percentage);
                }
            }

            // إضافة صف الإجمالي لورقة الأصناف
            int itemsTotalRow = row;
            itemsSheet.Cell(itemsTotalRow, 1).Value = "الإجمالي";

            // دمج الخلايا من البداية حتى العمود قبل الأخير للقيم المالية
            int itemsMergeEndColumn = itemsColumnIndex + 5;
            if (itemsMergeEndColumn > 1)
            {
                itemsSheet.Range(itemsTotalRow, 1, itemsTotalRow, itemsMergeEndColumn).Merge();
            }

            itemsSheet.Cell(itemsTotalRow, itemsColumnIndex + 6).Value = totalItemsAmount;
            itemsSheet.Cell(itemsTotalRow, itemsColumnIndex + 6).Style.NumberFormat.Format = "#,##0.00";

            // تنسيق صف الإجمالي
            var itemsTotalRowRange = itemsSheet.Range(itemsTotalRow, 1, itemsTotalRow, itemsLastHeaderColumn);
            itemsTotalRowRange.Style.Font.Bold = true;
            itemsTotalRowRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#FFD966");
            itemsTotalRowRange.Style.Font.FontColor = XLColor.FromHtml("#7F6000");
            itemsTotalRowRange.Style.Border.OutsideBorder = XLBorderStyleValues.Medium;
            itemsTotalRowRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
            itemsTotalRowRange.Style.Border.OutsideBorderColor = XLColor.FromHtml("#BF9000");
            itemsTotalRowRange.Style.Border.InsideBorderColor = XLColor.FromHtml("#BF9000");
            itemsSheet.Row(itemsTotalRow).Height = 20;

            // ضبط عرض الأعمدة
            invoicesSheet.Columns().AdjustToContents();
            itemsSheet.Columns().AdjustToContents();

            // حفظ الملف
            workbook.SaveAs(filePath);

            // تحديث التقدم إلى 100%
            progress?.Report(100);
        }

        // تصدير المشتريات إلى ملف Excel
        public async Task ExportPurchasesToExcel(string filePath, List<PurchaseViewModel> purchases, int? clientId = null, int? warehouseId = null, IProgress<int> progress = null)
        {
            using var workbook = new XLWorkbook();

            // إنشاء ورقة الفواتير
            var invoicesSheet = workbook.Worksheets.Add("الفواتير");

            // تطبيق إعدادات عامة للورقة
            invoicesSheet.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            invoicesSheet.Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center);
            invoicesSheet.Style.Font.SetFontName("Arial");
            invoicesSheet.Style.Font.SetFontSize(10);

            // إضافة معلومات المورد والمخزن إذا كانا محددين
            int startRow = 1;
            bool hasClientInfo = clientId.HasValue && clientId.Value > 0;
            bool hasWarehouseInfo = warehouseId.HasValue && warehouseId.Value != -1;

            if (hasClientInfo || hasWarehouseInfo)
            {
                // إنشاء عنوان للتقرير
                invoicesSheet.Cell(1, 1).Value = "تقرير المشتريات";
                var titleCell = invoicesSheet.Cell(1, 1);
                titleCell.Style.Font.Bold = true;
                titleCell.Style.Font.FontSize = 16;
                titleCell.Style.Font.FontColor = XLColor.FromHtml("#1F4E79");
                titleCell.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                invoicesSheet.Range(1, 1, 1, 8).Merge();

                int infoRow = 3;

                if (hasClientInfo)
                {
                    var client = await _context.Clients.FindAsyncWithBusy(clientId.Value);
                    if (client != null)
                    {
                        // إنشاء إطار للمعلومات المورد
                        var supplierHeaderRange = invoicesSheet.Range(infoRow, 1, infoRow, 2);
                        supplierHeaderRange.Merge();
                        supplierHeaderRange.Value = "معلومات المورد";
                        supplierHeaderRange.Style.Font.Bold = true;
                        supplierHeaderRange.Style.Font.FontSize = 12;
                        supplierHeaderRange.Style.Font.FontColor = XLColor.White;
                        supplierHeaderRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#4472C4");
                        supplierHeaderRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                        supplierHeaderRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;

                        // إنشاء جدول معلومات المورد
                        var clientInfoRange = invoicesSheet.Range(infoRow + 1, 1, infoRow + 2, 4);
                        clientInfoRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#E6F2FF");
                        clientInfoRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                        clientInfoRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                        clientInfoRange.Style.Border.OutsideBorderColor = XLColor.FromHtml("#BFDBFF");
                        clientInfoRange.Style.Border.InsideBorderColor = XLColor.FromHtml("#BFDBFF");

                        // إضافة عنوان بمعلومات المورد
                        invoicesSheet.Cell(infoRow + 1, 1).Value = "المورد:";
                        invoicesSheet.Cell(infoRow + 1, 2).Value = client.Name;
                        invoicesSheet.Cell(infoRow + 1, 1).Style.Font.Bold = true;
                        invoicesSheet.Cell(infoRow + 1, 2).Style.Font.Bold = true;
                        invoicesSheet.Cell(infoRow + 1, 2).Style.Font.FontColor = XLColor.FromHtml("#0066CC");

                        invoicesSheet.Cell(infoRow + 2, 1).Value = "الرصيد الحالي:";
                        invoicesSheet.Cell(infoRow + 2, 2).Value = client.Balance;
                        invoicesSheet.Cell(infoRow + 2, 1).Style.Font.Bold = true;
                        invoicesSheet.Cell(infoRow + 2, 2).Style.Font.Bold = true;
                        invoicesSheet.Cell(infoRow + 2, 2).Style.NumberFormat.Format = "#,##0.00";

                        // تطبيق محاذاة النص العربي من اليمين إلى اليسار
                        clientInfoRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Right);

                        // إضافة تاريخ التقرير
                        invoicesSheet.Cell(infoRow + 1, 3).Value = "تاريخ التقرير:";
                        invoicesSheet.Cell(infoRow + 1, 4).Value = DateTime.Now;
                        invoicesSheet.Cell(infoRow + 1, 3).Style.Font.Bold = true;
                        invoicesSheet.Cell(infoRow + 1, 4).Style.DateFormat.Format = "yyyy-MM-dd";

                        infoRow += 4; // تحديث موقع الصف التالي
                    }
                }

                if (hasWarehouseInfo)
                {
                    var warehouse = await _context.Warehouses.FindAsyncWithBusy(warehouseId.Value);
                    if (warehouse != null)
                    {
                        // إنشاء إطار لمعلومات المخزن
                        var warehouseHeaderRange = invoicesSheet.Range(infoRow, 1, infoRow, 2);
                        warehouseHeaderRange.Merge();
                        warehouseHeaderRange.Value = "معلومات المخزن";
                        warehouseHeaderRange.Style.Font.Bold = true;
                        warehouseHeaderRange.Style.Font.FontSize = 12;
                        warehouseHeaderRange.Style.Font.FontColor = XLColor.White;
                        warehouseHeaderRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#70AD47");
                        warehouseHeaderRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                        warehouseHeaderRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;

                        // إنشاء جدول معلومات المخزن
                        var warehouseInfoRange = invoicesSheet.Range(infoRow + 1, 1, infoRow + 1, 4);
                        warehouseInfoRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#E2EFDA");
                        warehouseInfoRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                        warehouseInfoRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                        warehouseInfoRange.Style.Border.OutsideBorderColor = XLColor.FromHtml("#A9D18E");
                        warehouseInfoRange.Style.Border.InsideBorderColor = XLColor.FromHtml("#A9D18E");

                        // إضافة معلومات المخزن
                        invoicesSheet.Cell(infoRow + 1, 1).Value = "المخزن:";
                        invoicesSheet.Cell(infoRow + 1, 2).Value = warehouse.Name;
                        invoicesSheet.Cell(infoRow + 1, 1).Style.Font.Bold = true;
                        invoicesSheet.Cell(infoRow + 1, 2).Style.Font.Bold = true;
                        invoicesSheet.Cell(infoRow + 1, 2).Style.Font.FontColor = XLColor.FromHtml("#548235");

                        // تطبيق محاذاة النص العربي من اليمين إلى اليسار
                        warehouseInfoRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Right);

                        infoRow += 3; // تحديث موقع الصف التالي
                    }
                }

                // ترك سطر فارغ
                startRow = infoRow + 1;
            }

            // إضافة رأس الجدول
            invoicesSheet.Cell(startRow, 1).Value = "الترقيم";
            invoicesSheet.Cell(startRow, 2).Value = "رقم الفاتورة";
            invoicesSheet.Cell(startRow, 3).Value = "تاريخ الفاتورة";

            int columnIndex = 4;
            bool showClientColumn = (clientId == null || clientId <= 0);
            bool showWarehouseColumn = (warehouseId == null || warehouseId <= 0);

            if (showClientColumn)
            {
                invoicesSheet.Cell(startRow, columnIndex).Value = "المورد";
                columnIndex++;
            }

            if (showWarehouseColumn)
            {
                invoicesSheet.Cell(startRow, columnIndex).Value = "المخزن";
                columnIndex++;
            }

            invoicesSheet.Cell(startRow, columnIndex).Value = "قيمة الفاتورة";
            invoicesSheet.Cell(startRow, columnIndex + 1).Value = "القيمة المدفوعة";
            invoicesSheet.Cell(startRow, columnIndex + 2).Value = "المتبقي";

            // تحديد نطاق رأس الجدول
            int lastHeaderColumn = columnIndex + 2;
            var headerRange = invoicesSheet.Range(startRow, 1, startRow, lastHeaderColumn);

            // تنسيق رأس الجدول
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#4472C4");
            headerRange.Style.Font.FontColor = XLColor.White;
            headerRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            headerRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
            headerRange.Style.Border.OutsideBorderColor = XLColor.FromHtml("#2F528F");
            headerRange.Style.Border.InsideBorderColor = XLColor.FromHtml("#2F528F");
            headerRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);

            // تعديل ارتفاع صف الرأس
            invoicesSheet.Row(startRow).Height = 20;

            // إضافة البيانات
            int row = startRow + 1;
            decimal totalAmount = 0;
            decimal totalPaid = 0;
            decimal totalRemaining = 0;

            foreach (var purchase in purchases)
            {
                invoicesSheet.Cell(row, 1).Value = purchase.Index;
                invoicesSheet.Cell(row, 2).Value = purchase.InvoiceNo;
                invoicesSheet.Cell(row, 3).Value = purchase.PurchaseDate;
                invoicesSheet.Cell(row, 3).Style.DateFormat.Format = "yyyy-MM-dd";

                columnIndex = 4;

                if (showClientColumn)
                {
                    invoicesSheet.Cell(row, columnIndex).Value = purchase.ClientName;
                    columnIndex++;
                }

                if (showWarehouseColumn)
                {
                    invoicesSheet.Cell(row, columnIndex).Value = purchase.WarehouseName;
                    columnIndex++;
                }

                invoicesSheet.Cell(row, columnIndex).Value = purchase.TotalAmount;
                invoicesSheet.Cell(row, columnIndex + 1).Value = purchase.PaidAmount;
                invoicesSheet.Cell(row, columnIndex + 2).Value = purchase.RemainingAmount;

                // تنسيق الأرقام
                invoicesSheet.Cell(row, columnIndex).Style.NumberFormat.Format = "#,##0.00";
                invoicesSheet.Cell(row, columnIndex + 1).Style.NumberFormat.Format = "#,##0.00";
                invoicesSheet.Cell(row, columnIndex + 2).Style.NumberFormat.Format = "#,##0.00";

                totalAmount += purchase.TotalAmount;
                totalPaid += purchase.PaidAmount;
                totalRemaining += purchase.RemainingAmount;

                // تنسيق صف البيانات
                var dataRange = invoicesSheet.Range(row, 1, row, lastHeaderColumn);
                dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                dataRange.Style.Border.OutsideBorderColor = XLColor.FromHtml("#D9D9D9");
                dataRange.Style.Border.InsideBorderColor = XLColor.FromHtml("#D9D9D9");

                // تطبيق ألوان متناوبة للصفوف
                if ((row - startRow) % 2 == 0)
                {
                    dataRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#F2F2F2");
                }

                row++;

                // تحديث التقدم
                if (progress != null)
                {
                    int percentage = (int)((row - startRow) * 50.0 / purchases.Count);
                    progress.Report(percentage);
                }
            }

            // إضافة صف الإجمالي
            int totalRow = row;
            invoicesSheet.Cell(totalRow, 1).Value = "الإجمالي";

            // دمج الخلايا من البداية حتى العمود قبل الأخير للقيم المالية
            int mergeEndColumn = columnIndex - 1;
            if (mergeEndColumn > 1)
            {
                invoicesSheet.Range(totalRow, 1, totalRow, mergeEndColumn).Merge();
            }

            invoicesSheet.Cell(totalRow, columnIndex).Value = totalAmount;
            invoicesSheet.Cell(totalRow, columnIndex + 1).Value = totalPaid;
            invoicesSheet.Cell(totalRow, columnIndex + 2).Value = totalRemaining;

            // تنسيق الأرقام
            invoicesSheet.Cell(totalRow, columnIndex).Style.NumberFormat.Format = "#,##0.00";
            invoicesSheet.Cell(totalRow, columnIndex + 1).Style.NumberFormat.Format = "#,##0.00";
            invoicesSheet.Cell(totalRow, columnIndex + 2).Style.NumberFormat.Format = "#,##0.00";

            // تنسيق صف الإجمالي
            var totalRowRange = invoicesSheet.Range(totalRow, 1, totalRow, lastHeaderColumn);
            totalRowRange.Style.Font.Bold = true;
            totalRowRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#FFD966");
            totalRowRange.Style.Font.FontColor = XLColor.FromHtml("#7F6000");
            totalRowRange.Style.Border.OutsideBorder = XLBorderStyleValues.Medium;
            totalRowRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
            totalRowRange.Style.Border.OutsideBorderColor = XLColor.FromHtml("#BF9000");
            totalRowRange.Style.Border.InsideBorderColor = XLColor.FromHtml("#BF9000");
            invoicesSheet.Row(totalRow).Height = 20;

            // إنشاء ورقة الأصناف
            var itemsSheet = workbook.Worksheets.Add("الأصناف");

            // إضافة معلومات المورد والمخزن إذا كانا محددين في ورقة الأصناف أيضاً
            int itemsStartRow = 1;
            bool hasClientInfoInItems = clientId.HasValue && clientId.Value > 0;
            bool hasWarehouseInfoInItems = warehouseId.HasValue && warehouseId.Value != -1;

            if (hasClientInfoInItems || hasWarehouseInfoInItems)
            {
                // إنشاء عنوان للتقرير
                itemsSheet.Cell(1, 1).Value = "تفاصيل أصناف المشتريات";
                var titleCell = itemsSheet.Cell(1, 1);
                titleCell.Style.Font.Bold = true;
                titleCell.Style.Font.FontSize = 16;
                titleCell.Style.Font.FontColor = XLColor.FromHtml("#1F4E79");
                titleCell.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                itemsSheet.Range(1, 1, 1, 8).Merge();

                int itemsInfoRow = 3;

                if (hasClientInfoInItems)
                {
                    var client = await _context.Clients.FindAsyncWithBusy(clientId.Value);
                    if (client != null)
                    {
                        // إنشاء إطار للمعلومات المورد
                        var supplierHeaderRange = itemsSheet.Range(itemsInfoRow, 1, itemsInfoRow, 2);
                        supplierHeaderRange.Merge();
                        supplierHeaderRange.Value = "معلومات المورد";
                        supplierHeaderRange.Style.Font.Bold = true;
                        supplierHeaderRange.Style.Font.FontSize = 12;
                        supplierHeaderRange.Style.Font.FontColor = XLColor.White;
                        supplierHeaderRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#4472C4");
                        supplierHeaderRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                        supplierHeaderRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;

                        // إنشاء جدول معلومات المورد
                        var clientInfoRange = itemsSheet.Range(itemsInfoRow + 1, 1, itemsInfoRow + 2, 4);
                        clientInfoRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#E6F2FF");
                        clientInfoRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                        clientInfoRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                        clientInfoRange.Style.Border.OutsideBorderColor = XLColor.FromHtml("#BFDBFF");
                        clientInfoRange.Style.Border.InsideBorderColor = XLColor.FromHtml("#BFDBFF");

                        // إضافة عنوان بمعلومات المورد
                        itemsSheet.Cell(itemsInfoRow + 1, 1).Value = "المورد:";
                        itemsSheet.Cell(itemsInfoRow + 1, 2).Value = client.Name;
                        itemsSheet.Cell(itemsInfoRow + 1, 1).Style.Font.Bold = true;
                        itemsSheet.Cell(itemsInfoRow + 1, 2).Style.Font.Bold = true;
                        itemsSheet.Cell(itemsInfoRow + 1, 2).Style.Font.FontColor = XLColor.FromHtml("#0066CC");

                        itemsSheet.Cell(itemsInfoRow + 2, 1).Value = "الرصيد الحالي:";
                        itemsSheet.Cell(itemsInfoRow + 2, 2).Value = client.Balance;
                        itemsSheet.Cell(itemsInfoRow + 2, 1).Style.Font.Bold = true;
                        itemsSheet.Cell(itemsInfoRow + 2, 2).Style.Font.Bold = true;
                        itemsSheet.Cell(itemsInfoRow + 2, 2).Style.NumberFormat.Format = "#,##0.00";

                        // تطبيق محاذاة النص العربي من اليمين إلى اليسار
                        clientInfoRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Right);

                        // إضافة تاريخ التقرير
                        itemsSheet.Cell(itemsInfoRow + 1, 3).Value = "تاريخ التقرير:";
                        itemsSheet.Cell(itemsInfoRow + 1, 4).Value = DateTime.Now;
                        itemsSheet.Cell(itemsInfoRow + 1, 3).Style.Font.Bold = true;
                        itemsSheet.Cell(itemsInfoRow + 1, 4).Style.DateFormat.Format = "yyyy-MM-dd";

                        itemsInfoRow += 4; // تحديث موقع الصف التالي
                    }
                }

                if (hasWarehouseInfoInItems)
                {
                    var warehouse = await _context.Warehouses.FindAsyncWithBusy(warehouseId.Value);
                    if (warehouse != null)
                    {
                        // إنشاء إطار لمعلومات المخزن
                        var warehouseHeaderRange = itemsSheet.Range(itemsInfoRow, 1, itemsInfoRow, 2);
                        warehouseHeaderRange.Merge();
                        warehouseHeaderRange.Value = "معلومات المخزن";
                        warehouseHeaderRange.Style.Font.Bold = true;
                        warehouseHeaderRange.Style.Font.FontSize = 12;
                        warehouseHeaderRange.Style.Font.FontColor = XLColor.White;
                        warehouseHeaderRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#70AD47");
                        warehouseHeaderRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                        warehouseHeaderRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;

                        // إنشاء جدول معلومات المخزن
                        var warehouseInfoRange = itemsSheet.Range(itemsInfoRow + 1, 1, itemsInfoRow + 1, 4);
                        warehouseInfoRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#E2EFDA");
                        warehouseInfoRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                        warehouseInfoRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                        warehouseInfoRange.Style.Border.OutsideBorderColor = XLColor.FromHtml("#A9D18E");
                        warehouseInfoRange.Style.Border.InsideBorderColor = XLColor.FromHtml("#A9D18E");

                        // إضافة معلومات المخزن
                        itemsSheet.Cell(itemsInfoRow + 1, 1).Value = "المخزن:";
                        itemsSheet.Cell(itemsInfoRow + 1, 2).Value = warehouse.Name;
                        itemsSheet.Cell(itemsInfoRow + 1, 1).Style.Font.Bold = true;
                        itemsSheet.Cell(itemsInfoRow + 1, 2).Style.Font.Bold = true;
                        itemsSheet.Cell(itemsInfoRow + 1, 2).Style.Font.FontColor = XLColor.FromHtml("#548235");

                        // تطبيق محاذاة النص العربي من اليمين إلى اليسار
                        warehouseInfoRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Right);

                        itemsInfoRow += 3; // تحديث موقع الصف التالي
                    }
                }

                // ترك سطر فارغ
                itemsStartRow = itemsInfoRow + 1;
            }

            // إضافة رأس جدول الأصناف
            itemsSheet.Cell(itemsStartRow, 1).Value = "رقم الفاتورة";
            itemsSheet.Cell(itemsStartRow, 2).Value = "تاريخ الفاتورة";

            int itemsColumnIndex = 3;
            bool showClientColumnInItems = (clientId == null || clientId <= 0);
            bool showWarehouseColumnInItems = (warehouseId == null || warehouseId <= 0);

            if (showClientColumnInItems)
            {
                itemsSheet.Cell(itemsStartRow, itemsColumnIndex).Value = "المورد";
                itemsColumnIndex++;
            }

            if (showWarehouseColumnInItems)
            {
                itemsSheet.Cell(itemsStartRow, itemsColumnIndex).Value = "المخزن";
                itemsColumnIndex++;
            }

            itemsSheet.Cell(itemsStartRow, itemsColumnIndex).Value = "الصنف";
            itemsSheet.Cell(itemsStartRow, itemsColumnIndex + 1).Value = "الكمية";
            itemsSheet.Cell(itemsStartRow, itemsColumnIndex + 2).Value = "السعر";
            itemsSheet.Cell(itemsStartRow, itemsColumnIndex + 3).Value = "الإجمالي";

            // تنسيق رأس الجدول
            var itemsHeaderRow = itemsSheet.Row(itemsStartRow);
            itemsHeaderRow.Style.Font.Bold = true;
            itemsHeaderRow.Style.Fill.BackgroundColor = XLColor.LightGray;

            // جلب تفاصيل الفواتير
            var purchaseIds = purchases.Select(p => p.Id).ToList();
            var purchaseItems = await _context.PurchaseItems
                .Include(pi => pi.Purchase).ThenInclude(p => p.Client)
                .Include(pi => pi.Purchase).ThenInclude(p => p.Warehouse)
                .Include(pi => pi.ProductColor)
                    .ThenInclude(pc => pc.Product)
                .Include(pi => pi.ProductColor)
                    .ThenInclude(pc => pc.Color)
                .Include(pi => pi.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Sphere)
                .Include(pi => pi.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Cylinder)
                .Include(pi => pi.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Pow)
                .Include(pi => pi.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Lens)
                    .ThenInclude(l => l.Category)
                .Where(pi => purchaseIds.Contains(pi.PurchaseId))
                .ToListAsyncWithBusy("GetPurchaseItemsForExport");

            // إضافة بيانات الأصناف
            row = itemsStartRow + 1;
            decimal totalItemsAmount = 0;

            foreach (var item in purchaseItems)
            {
                itemsSheet.Cell(row, 1).Value = item.Purchase.InvoiceNo;
                itemsSheet.Cell(row, 2).Value = item.Purchase.PurchaseDate;
                itemsSheet.Cell(row, 2).Style.DateFormat.Format = "yyyy-MM-dd";

                // تحديد نوع الصنف واسمه
                string itemName = "";
                if (item.ProductColor != null)
                {
                    var product = item.ProductColor.Product;
                    var color = item.ProductColor.Color;

                    // اسم المنتج الأساسي
                    itemName = product.Name;

                    // إضافة معلومات إضافية عن المنتج
                    var productDetails = new List<string>();

                    // إضافة معلومات عن المنتج مثل الحد الأدنى للكمية إذا كان أكبر من صفر
                    if (product.MinimumQuantity > 0)
                        productDetails.Add($"الحد الأدنى: {product.MinimumQuantity}");

                    // إضافة معلومات عن تاريخ الصلاحية إذا كان المنتج له تاريخ صلاحية
                    if (product.Exp)
                        productDetails.Add("له تاريخ صلاحية");

                    // إضافة معلومات المنتج إلى الاسم
                    if (productDetails.Count > 0)
                    {
                        itemName += " - " + string.Join(" / ", productDetails);
                    }

                    // إضافة اللون بشكل بارز
                    if (color != null)
                    {
                        itemName += " - لون: " + color.Name;
                    }
                }
                else if (item.LensPrescriptionColor != null)
                {
                    var lensColor = item.LensPrescriptionColor;
                    var lensPrescription = lensColor?.LensPrescription;
                    var lens = lensPrescription?.Lens;

                    if (lens != null)
                    {
                        // اسم العدسة الأساسي مع الفئة
                        itemName = lens.Name + (lens.Category != null ? " | " + lens.Category.Name : "");

                        // إضافة معلومات الوصفة الطبية
                        var specs = new List<string>();

                        // إضافة معلومات SPH, CYL, POW بشكل بارز
                        var prescriptionInfo = new List<string>();
                        if (lensPrescription.Sphere != null)
                            prescriptionInfo.Add($"SPH: {(lensPrescription.Sphere.Value == 0 ? "PL" : lensPrescription.Sphere.Value.ToString("F2"))}");

                        if (lensPrescription.Cylinder != null)
                            prescriptionInfo.Add($"CYL: {(lensPrescription.Cylinder.Value == 0 ? "PL" : lensPrescription.Cylinder.Value.ToString("F2"))}");

                        if (lensPrescription.Pow != null)
                            prescriptionInfo.Add($"POW: {(lensPrescription.Pow.Value == 0 ? "PL" : lensPrescription.Pow.Value.ToString("F2"))}");

                        // إضافة معلومات الوصفة الطبية الرئيسية أولاً
                        if (prescriptionInfo.Count > 0)
                            specs.Add($"[{string.Join(" | ", prescriptionInfo)}]");

                        // إضافة المعلومات الإضافية
                        if (lens.BC.HasValue)
                            specs.Add($"BC: {lens.BC.Value.ToString("F2")}");

                        if (lens.Dia.HasValue)
                            specs.Add($"Dia: {lens.Dia.Value.ToString("F2")}");

                        // إضافة معلومات الوصفة الطبية إلى اسم العدسة بشكل بارز
                        if (specs.Count > 0)
                        {
                            itemName += " || " + string.Join(" || ", specs);
                        }

                        // إضافة اللون بشكل بارز إذا كان متوفراً
                        if (lensColor?.Color != null)
                        {
                            itemName += " - لون: " + lensColor.Color.Name;
                        }
                    }
                    else
                    {
                        itemName = "عدسة";
                    }
                }

                decimal itemTotal = item.Price * item.Quantity;
                totalItemsAmount += itemTotal;

                itemsColumnIndex = 3;

                if (showClientColumnInItems)
                {
                    itemsSheet.Cell(row, itemsColumnIndex).Value = item.Purchase.Client.Name;
                    itemsColumnIndex++;
                }

                if (showWarehouseColumnInItems)
                {
                    itemsSheet.Cell(row, itemsColumnIndex).Value = item.Purchase.Warehouse?.Name ?? "غير محدد";
                    itemsColumnIndex++;
                }

                itemsSheet.Cell(row, itemsColumnIndex).Value = itemName;
                itemsSheet.Cell(row, itemsColumnIndex + 1).Value = item.Quantity;
                itemsSheet.Cell(row, itemsColumnIndex + 2).Value = item.Price;
                itemsSheet.Cell(row, itemsColumnIndex + 3).Value = itemTotal;

                // تنسيق الأرقام
                itemsSheet.Cell(row, itemsColumnIndex + 2).Style.NumberFormat.Format = "#,##0.00";
                itemsSheet.Cell(row, itemsColumnIndex + 3).Style.NumberFormat.Format = "#,##0.00";

                row++;

                // تحديث التقدم
                if (progress != null)
                {
                    int percentage = 50 + (int)((row - itemsStartRow - 1) * 50.0 / purchaseItems.Count);
                    progress.Report(percentage);
                }
            }

            // إضافة صف الإجمالي لورقة الأصناف
            int itemsTotalRow = row;
            itemsSheet.Cell(itemsTotalRow, 1).Value = "الإجمالي";

            // دمج الخلايا من البداية حتى العمود قبل الأخير للقيم المالية
            int itemsMergeEndColumn = itemsColumnIndex + 2;
            if (itemsMergeEndColumn > 1)
            {
                itemsSheet.Range(itemsTotalRow, 1, itemsTotalRow, itemsMergeEndColumn).Merge();
            }

            itemsSheet.Cell(itemsTotalRow, itemsColumnIndex + 3).Value = totalItemsAmount;
            itemsSheet.Cell(itemsTotalRow, itemsColumnIndex + 3).Style.NumberFormat.Format = "#,##0.00";

            // تنسيق صف الإجمالي
            itemsSheet.Row(itemsTotalRow).Style.Font.Bold = true;
            itemsSheet.Row(itemsTotalRow).Style.Fill.BackgroundColor = XLColor.LightGray;

            // ضبط عرض الأعمدة
            invoicesSheet.Columns().AdjustToContents();
            itemsSheet.Columns().AdjustToContents();

            // حفظ الملف
            workbook.SaveAs(filePath);

            // تحديث التقدم إلى 100%
            progress?.Report(100);
        }

        // تصدير كميات العدسات بتنسيق مصفوفة مع إمكانية تحديد النطاق
        public async Task ExportLensInventoryToExcel(string filePath, LensVM selectedLens,
            decimal? sphereFrom = null, decimal? sphereTo = null,
            decimal? cylinderFrom = null, decimal? cylinderTo = null,
            decimal? powerFrom = null, decimal? powerTo = null,
            int? warehouseId = null, IProgress<int> progress = null)
        {
            using var workbook = new XLWorkbook();

            // الحصول على اسم الشركة من الإعدادات
            string companyName = Properties.Settings.Default.CompanyName;

            // الحصول على بيانات العدسة المحددة مع جميع التفاصيل
            var lens = await _context.Lenses
                .Include(l => l.Category)
                .Include(l => l.LensPrescriptions)
                .ThenInclude(lp => lp.Sphere)
                .Include(l => l.LensPrescriptions)
                .ThenInclude(lp => lp.Cylinder)
                .Include(l => l.LensPrescriptions)
                .ThenInclude(lp => lp.Pow)
                .Include(l => l.LensPrescriptions)
                .ThenInclude(lp => lp.LensPrescriptionColors)
                .ThenInclude(lpc => lpc.LensQuantity)
                .FirstOrDefaultAsyncWithBusy(l => l.Id == selectedLens.Id, "GetLensForPrescriptionExport");

            if (lens == null)
            {
                throw new Exception("العدسة المحددة غير موجودة");
            }

            var lenses = new List<Lens> { lens };

            // تحديد أنواع الوصفات المستخدمة في العدسة المحددة
            bool hasSphere = lens.Sphere;
            bool hasCylinder = lens.Cylinder;
            bool hasPower = lens.Power;

            // تحديد عدد أنواع الوصفات المستخدمة
            int prescriptionTypesCount = (hasSphere ? 1 : 0) + (hasCylinder ? 1 : 0) + (hasPower ? 1 : 0);

            // إنشاء ورقة عمل منفصلة لكل مجموعة
            await CreateLensInventoryWorksheets(workbook, lenses, companyName, lens,
                hasSphere, hasCylinder, hasPower, prescriptionTypesCount,
                sphereFrom, sphereTo, cylinderFrom, cylinderTo, powerFrom, powerTo, warehouseId, progress);

            // تحديث Progress قبل حفظ الملف
            progress?.Report(95);

            // حفظ الملف
            workbook.SaveAs(filePath);

            // تحديث Progress إلى 100% عند الانتهاء
            progress?.Report(100);
        }

        // دالة مساعدة لإنشاء أوراق العمل
        private async Task CreateLensInventoryWorksheets(XLWorkbook workbook, List<Lens> lenses,
            string companyName, Lens selectedLens, bool hasSphere, bool hasCylinder, bool hasPower,
            int prescriptionTypesCount, decimal? sphereFrom, decimal? sphereTo,
            decimal? cylinderFrom, decimal? cylinderTo, decimal? powerFrom, decimal? powerTo,
            int? warehouseId, IProgress<int> progress)
        {
            progress?.Report(10); // بداية إنشاء أوراق العمل

            // إذا كان هناك نوع واحد فقط من الوصفات، إنشاء جدول عمودي
            if (prescriptionTypesCount == 1)
            {
                progress?.Report(20);
                if (hasSphere)
                {
                    await CreateVerticalWorksheet(workbook, lenses, companyName, selectedLens, "SPH",
                        "Sphere", sphereFrom, sphereTo, warehouseId, progress);
                }
                else if (hasCylinder)
                {
                    await CreateVerticalWorksheet(workbook, lenses, companyName, selectedLens, "CYL",
                        "Cylinder", cylinderFrom, cylinderTo, warehouseId, progress);
                }
                else if (hasPower)
                {
                    await CreateVerticalWorksheet(workbook, lenses, companyName, selectedLens, "POW",
                        "Power", powerFrom, powerTo, warehouseId, progress);
                }
                progress?.Report(90);
            }
            // إذا كان هناك نوعان من الوصفات، إنشاء جدول أفقي
            else if (prescriptionTypesCount == 2)
            {
                progress?.Report(20);
                if (hasSphere && hasCylinder)
                {
                    await CreateHorizontalWorksheet(workbook, lenses, companyName, selectedLens,
                        "SPH", "CYL", sphereFrom, sphereTo, cylinderFrom, cylinderTo, warehouseId, progress);
                }
                else if (hasSphere && hasPower)
                {
                    await CreateHorizontalWorksheet(workbook, lenses, companyName, selectedLens,
                        "SPH", "POW", sphereFrom, sphereTo, powerFrom, powerTo, warehouseId, progress);
                }
                else if (hasCylinder && hasPower)
                {
                    await CreateHorizontalWorksheet(workbook, lenses, companyName, selectedLens,
                        "CYL", "POW", cylinderFrom, cylinderTo, powerFrom, powerTo, warehouseId, progress);
                }
                progress?.Report(90);
            }
            // إذا كان هناك ثلاثة أنواع، إنشاء أوراق منفصلة
            else if (prescriptionTypesCount == 3)
            {
                progress?.Report(15);
                // إنشاء ورقة SPH/CYL
                await CreateHorizontalWorksheet(workbook, lenses, companyName, selectedLens,
                    "SPH", "CYL", sphereFrom, sphereTo, cylinderFrom, cylinderTo, warehouseId, progress, "SPH-CYL");
                progress?.Report(45);

                // إنشاء ورقة SPH/POW
                await CreateHorizontalWorksheet(workbook, lenses, companyName, selectedLens,
                    "SPH", "POW", sphereFrom, sphereTo, powerFrom, powerTo, warehouseId, progress, "SPH-POW");
                progress?.Report(70);

                // إنشاء ورقة CYL/POW
                await CreateHorizontalWorksheet(workbook, lenses, companyName, selectedLens,
                    "CYL", "POW", cylinderFrom, cylinderTo, powerFrom, powerTo, warehouseId, progress, "CYL-POW");
                progress?.Report(90);
            }
        }

        // دالة إنشاء ورقة عمل عمودية (لنوع واحد من الوصفات)
        private async Task CreateVerticalWorksheet(XLWorkbook workbook, List<Lens> lenses,
            string companyName, Lens selectedLens, string prescriptionType, string prescriptionProperty,
            decimal? valueFrom, decimal? valueTo, int? warehouseId, IProgress<int> progress)
        {
            progress?.Report(30);
            var worksheet = workbook.Worksheets.Add($"{selectedLens.Name} - {prescriptionType}");

            // إعداد الصفحة
            worksheet.PageSetup.PageOrientation = XLPageOrientation.Portrait;
            worksheet.PageSetup.PaperSize = XLPaperSize.A4Paper;
            worksheet.Style.Font.SetFontName("Arial");
            worksheet.Style.Font.SetFontSize(10);

            progress?.Report(40);
            // إضافة رأس الصفحة
            await AddWorksheetHeader(worksheet, companyName, selectedLens, prescriptionType, warehouseId);

            // الحصول على جميع قيم Prescription من -35 إلى +35
            var prescriptionValues = await GetAllPrescriptionValues(prescriptionProperty, valueFrom, valueTo);

            progress?.Report(50);
            // إنشاء الجدول العمودي
            int currentRow = 6;
            worksheet.Cell(currentRow, 1).Value = prescriptionType;
            worksheet.Cell(currentRow, 2).Value = "الكمية";

            // تنسيق رأس الجدول
            var headerRange = worksheet.Range(currentRow, 1, currentRow, 2);
            headerRange.Style.Font.SetBold(true);
            headerRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#D9E1F2");
            headerRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;

            currentRow++;

            progress?.Report(60);
            // إضافة البيانات - عرض جميع القيم من -35 إلى +35
            int displayedCount = 0;
            int totalCount = prescriptionValues.Count;
            foreach (var prescription in prescriptionValues)
            {
                // استخدام الدالة الجديدة للحصول على الكمية
                int totalQuantity = GetQuantityForPrescriptionValue(selectedLens, prescriptionProperty, prescription.Value, warehouseId);

                // عرض جميع القيم مع الكمية (0 إذا لم توجد)
                worksheet.Cell(currentRow, 1).Value = prescription.Value.ToString("F2");
                worksheet.Cell(currentRow, 2).Value = totalQuantity;

                // تلوين الصفوف التي تحتوي على كميات
                if (totalQuantity > 0)
                {
                    worksheet.Cell(currentRow, 2).Style.Font.SetFontColor(XLColor.FromHtml("#2E7D32"));
                    worksheet.Cell(currentRow, 2).Style.Font.SetBold(true);
                }
                else
                {
                    // عرض الصفر بلون رمادي
                    worksheet.Cell(currentRow, 2).Style.Font.SetFontColor(XLColor.FromHtml("#808080"));
                }

                currentRow++;
                displayedCount++;

                // تحديث Progress أثناء معالجة البيانات
                if (displayedCount % 20 == 0 && totalCount > 0)
                {
                    int progressValue = 60 + (int)((double)displayedCount / totalCount * 25);
                    progress?.Report(progressValue);
                }
            }

            progress?.Report(85);
            // تنسيق الجدول
            var dataRange = worksheet.Range(6, 1, currentRow - 1, 2);
            dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;

            // ضبط عرض الأعمدة
            worksheet.Columns().AdjustToContents();
        }

        // دالة إنشاء ورقة عمل أفقية (لنوعين من الوصفات)
        private async Task CreateHorizontalWorksheet(XLWorkbook workbook, List<Lens> lenses,
            string companyName, Lens selectedLens, string rowType, string colType,
            decimal? rowFrom, decimal? rowTo, decimal? colFrom, decimal? colTo,
            int? warehouseId, IProgress<int> progress, string sheetSuffix = "")
        {
            progress?.Report(30);
            // الحصول على جميع قيم Prescription للصفوف والأعمدة
            var allRowValues = await GetAllPrescriptionValues(GetPropertyName(rowType), rowFrom, rowTo);
            var allColValues = await GetAllPrescriptionValues(GetPropertyName(colType), colFrom, colTo);

            progress?.Report(40);
            // تحديد عدد الصفوف والأعمدة لكل ورقة (لتناسب A4 أفقي)
            int maxRowsPerSheet = 25; // عدد الصفوف الأقصى لكل ورقة
            int maxColsPerSheet = 15; // عدد الأعمدة الأقصى لكل ورقة

            // إنشاء أوراق متعددة حسب الحاجة
            await CreateMultipleMatrixSheets(workbook, selectedLens, companyName, rowType, colType,
                allRowValues, allColValues, maxRowsPerSheet, maxColsPerSheet, warehouseId, sheetSuffix, progress);
        }

        // دالة جديدة لإنشاء أوراق متعددة للجداول الأفقية
        private async Task CreateMultipleMatrixSheets(XLWorkbook workbook, Lens selectedLens, string companyName,
            string rowType, string colType, List<Prescription> allRowValues, List<Prescription> allColValues,
            int maxRowsPerSheet, int maxColsPerSheet, int? warehouseId, string sheetSuffix, IProgress<int> progress = null)
        {
            int totalRowSheets = (int)Math.Ceiling((double)allRowValues.Count / maxRowsPerSheet);
            int totalColSheets = (int)Math.Ceiling((double)allColValues.Count / maxColsPerSheet);
            int totalSheets = totalRowSheets * totalColSheets;

            int sheetNumber = 1;
            int completedSheets = 0;

            for (int rowSheetIndex = 0; rowSheetIndex < totalRowSheets; rowSheetIndex++)
            {
                for (int colSheetIndex = 0; colSheetIndex < totalColSheets; colSheetIndex++)
                {
                    // تحديد البيانات لهذه الورقة
                    var rowValuesForSheet = allRowValues
                        .Skip(rowSheetIndex * maxRowsPerSheet)
                        .Take(maxRowsPerSheet)
                        .ToList();

                    var colValuesForSheet = allColValues
                        .Skip(colSheetIndex * maxColsPerSheet)
                        .Take(maxColsPerSheet)
                        .ToList();

                    // إنشاء اسم الورقة
                    string sheetName;
                    if (totalRowSheets == 1 && totalColSheets == 1)
                    {
                        sheetName = string.IsNullOrEmpty(sheetSuffix) ?
                            $"{selectedLens.Name} - {rowType}-{colType}" :
                            $"{selectedLens.Name} - {sheetSuffix}";
                    }
                    else
                    {
                        string baseName = string.IsNullOrEmpty(sheetSuffix) ?
                            $"{rowType}-{colType}" : sheetSuffix;
                        sheetName = $"{selectedLens.Name} - {baseName} ({sheetNumber})";
                    }

                    // إنشاء الورقة
                    var worksheet = workbook.Worksheets.Add(sheetName);

                    // إعداد الصفحة
                    worksheet.PageSetup.PageOrientation = XLPageOrientation.Landscape;
                    worksheet.PageSetup.PaperSize = XLPaperSize.A4Paper;
                    worksheet.PageSetup.FitToPages(1, 1);
                    worksheet.Style.Font.SetFontName("Arial");
                    worksheet.Style.Font.SetFontSize(8);

                    // إضافة رأس الصفحة
                    await AddMatrixSheetHeader(worksheet, companyName, selectedLens, rowType, colType,
                        rowSheetIndex, colSheetIndex, totalRowSheets, totalColSheets, warehouseId);

                    // إنشاء الجدول المصفوفي
                    await CreateMatrixTable(worksheet, selectedLens, rowType, colType,
                        rowValuesForSheet, colValuesForSheet, warehouseId);

                    sheetNumber++;
                    completedSheets++;

                    // تحديث Progress بناءً على عدد الأوراق المكتملة
                    if (totalSheets > 0)
                    {
                        int progressValue = 45 + (int)((double)completedSheets / totalSheets * 40);
                        progress?.Report(progressValue);
                    }
                }
            }
        }

        // دالة مساعدة لإنشاء الجدول المصفوفي
        private async Task CreateMatrixTable(IXLWorksheet worksheet, Lens selectedLens,
            string rowType, string colType, List<Prescription> rowValues, List<Prescription> colValues, int? warehouseId)
        {
            // إضافة عناوين الأعمدة والصفوف مثل الصورة
            int startRow = 6;
            int startCol = 2;

            // إضافة عنوان الصف الأول (SPHERE (+) و CYLINDER (+))
            worksheet.Cell(startRow, startCol).Value = $"{rowType} ( + )";
            worksheet.Cell(startRow, startCol).Style.Font.SetBold(true);
            worksheet.Cell(startRow, startCol).Style.Fill.BackgroundColor = XLColor.FromHtml("#D9E1F2");

            // إضافة عنوان الصف الثاني (CYLINDER (+))
            worksheet.Cell(startRow + 1, startCol).Value = $"{colType} ( + )";
            worksheet.Cell(startRow + 1, startCol).Style.Font.SetBold(true);
            worksheet.Cell(startRow + 1, startCol).Style.Fill.BackgroundColor = XLColor.FromHtml("#D9E1F2");

            // إضافة قيم الأعمدة (CYL)
            int colIndex = startCol + 1;
            foreach (var colValue in colValues)
            {
                worksheet.Cell(startRow + 1, colIndex).Value = colValue.Value.ToString("F2");
                worksheet.Cell(startRow + 1, colIndex).Style.Font.SetBold(true);
                worksheet.Cell(startRow + 1, colIndex).Style.Fill.BackgroundColor = XLColor.FromHtml("#D9E1F2");
                worksheet.Cell(startRow + 1, colIndex).Style.Font.SetFontSize(8);
                colIndex++;
            }

            // إضافة قيم الصفوف (SPH) والكميات
            int rowIndex = startRow + 2;
            foreach (var rowValue in rowValues)
            {
                // إضافة قيمة الصف
                worksheet.Cell(rowIndex, startCol).Value = rowValue.Value.ToString("F2");
                worksheet.Cell(rowIndex, startCol).Style.Font.SetBold(true);
                worksheet.Cell(rowIndex, startCol).Style.Fill.BackgroundColor = XLColor.FromHtml("#D9E1F2");
                worksheet.Cell(rowIndex, startCol).Style.Font.SetFontSize(8);

                // إضافة الكميات - عرض جميع القيم مع 0 للغير موجود
                colIndex = startCol + 1;
                foreach (var colValue in colValues)
                {
                    int quantity = GetQuantityForCombination(selectedLens, rowType, colType, rowValue.Value, colValue.Value, warehouseId);

                    // عرض جميع القيم مع الكمية (0 إذا لم توجد)
                    worksheet.Cell(rowIndex, colIndex).Value = quantity;
                    worksheet.Cell(rowIndex, colIndex).Style.Font.SetFontSize(8);

                    if (quantity > 0)
                    {
                        worksheet.Cell(rowIndex, colIndex).Style.Font.SetFontColor(XLColor.FromHtml("#2E7D32"));
                        worksheet.Cell(rowIndex, colIndex).Style.Font.SetBold(true);
                    }
                    else
                    {
                        // عرض الصفر بلون رمادي
                        worksheet.Cell(rowIndex, colIndex).Style.Font.SetFontColor(XLColor.FromHtml("#808080"));
                    }
                    colIndex++;
                }
                rowIndex++;
            }

            // تنسيق الجدول
            var tableRange = worksheet.Range(startRow, startCol, rowIndex - 1, colIndex - 1);
            tableRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            tableRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
            tableRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
        }

        // دالة جديدة للحصول على جميع قيم Prescription من -35 إلى +35
        private async Task<List<Prescription>> GetAllPrescriptionValues(string propertyName,
            decimal? valueFrom, decimal? valueTo)
        {
            // تحديد النطاق الافتراضي من -35 إلى +35
            decimal startValue = valueFrom ?? -35m;
            decimal endValue = valueTo ?? 35m;

            // الحصول على جميع قيم Prescription من قاعدة البيانات في النطاق المحدد
            var allPrescriptions = await _context.Prescriptions
                .Where(p => p.Value >= startValue && p.Value <= endValue)
                .OrderBy(p => p.Value)
                .ToListAsyncWithBusy("GetAllPrescriptionsInRange");

            return allPrescriptions;
        }

        // دالة للحصول على الكمية لقيمة prescription محددة
        private int GetQuantityForPrescriptionValue(Lens selectedLens, string propertyName, decimal prescriptionValue, int? warehouseId = null)
        {
            int totalQuantity = 0;

            foreach (var prescription in selectedLens.LensPrescriptions)
            {
                bool matches = propertyName.ToLower() switch
                {
                    "sphere" => prescription.Sphere?.Value == prescriptionValue,
                    "cylinder" => prescription.Cylinder?.Value == prescriptionValue,
                    "power" => prescription.Pow?.Value == prescriptionValue,
                    _ => false
                };

                if (matches)
                {
                    foreach (var color in prescription.LensPrescriptionColors)
                    {
                        if (color.LensQuantity != null && color.LensQuantity.Any())
                        {
                            // تطبيق تصفية المخزن إذا كان محدداً
                            var quantities = warehouseId.HasValue
                                ? color.LensQuantity.Where(lq => lq.WarehouseId == warehouseId.Value)
                                : color.LensQuantity;

                            totalQuantity += quantities.Sum(lq => lq.Quantity);
                        }
                    }
                }
            }

            return totalQuantity;
        }

        // دالة للحصول على الكمية لتوليفة من قيمتين
        private int GetQuantityForCombination(Lens selectedLens, string rowType, string colType,
            decimal rowValue, decimal colValue, int? warehouseId = null)
        {
            int totalQuantity = 0;

            foreach (var prescription in selectedLens.LensPrescriptions)
            {
                bool rowMatches = rowType.ToLower() switch
                {
                    "sphere" => prescription.Sphere?.Value == rowValue,
                    "cylinder" => prescription.Cylinder?.Value == rowValue,
                    "power" => prescription.Pow?.Value == rowValue,
                    _ => false
                };

                bool colMatches = colType.ToLower() switch
                {
                    "sphere" => prescription.Sphere?.Value == colValue,
                    "cylinder" => prescription.Cylinder?.Value == colValue,
                    "power" => prescription.Pow?.Value == colValue,
                    _ => false
                };

                if (rowMatches && colMatches)
                {
                    foreach (var color in prescription.LensPrescriptionColors)
                    {
                        if (color.LensQuantity != null && color.LensQuantity.Any())
                        {
                            // تطبيق تصفية المخزن إذا كان محدداً
                            var quantities = warehouseId.HasValue
                                ? color.LensQuantity.Where(lq => lq.WarehouseId == warehouseId.Value)
                                : color.LensQuantity;

                            totalQuantity += quantities.Sum(lq => lq.Quantity);
                        }
                    }
                }
            }

            return totalQuantity;
        }

        // دوال مساعدة - تعديل للعمل مع العدسة المحددة فقط
        private async Task<List<Prescription>> GetPrescriptionValues(string propertyName, int? categoryId,
            decimal? valueFrom, decimal? valueTo, int maxCount = int.MaxValue)
        {
            // إذا لم يتم تحديد categoryId، نحصل على جميع القيم
            // إذا تم تحديده، نحصل على القيم للعدسات من هذا النوع فقط
            var query = _context.LensPrescriptions.AsQueryable();

            if (categoryId.HasValue)
            {
                query = query.Where(lp => lp.Lens.CategoryId == categoryId);
            }

            List<Prescription> prescriptions = new List<Prescription>();

            switch (propertyName.ToLower())
            {
                case "sphere":
                    prescriptions = await query
                        .Where(lp => lp.SphereId.HasValue && lp.Sphere != null)
                        .Select(lp => lp.Sphere)
                        .Distinct()
                        .Where(p => p != null &&
                                   (valueFrom == null || p.Value >= valueFrom) &&
                                   (valueTo == null || p.Value <= valueTo))
                        .OrderBy(p => p.Value)
                        .Take(maxCount)
                        .ToListAsyncWithBusy("GetSpherePrescriptions");
                    break;
                case "cylinder":
                    prescriptions = await query
                        .Where(lp => lp.CylinderId.HasValue && lp.Cylinder != null)
                        .Select(lp => lp.Cylinder)
                        .Distinct()
                        .Where(p => p != null &&
                                   (valueFrom == null || p.Value >= valueFrom) &&
                                   (valueTo == null || p.Value <= valueTo))
                        .OrderBy(p => p.Value)
                        .Take(maxCount)
                        .ToListAsyncWithBusy("GetCylinderPrescriptions");
                    break;
                case "power":
                    prescriptions = await query
                        .Where(lp => lp.PowId.HasValue && lp.Pow != null)
                        .Select(lp => lp.Pow)
                        .Distinct()
                        .Where(p => p != null &&
                                   (valueFrom == null || p.Value >= valueFrom) &&
                                   (valueTo == null || p.Value <= valueTo))
                        .OrderBy(p => p.Value)
                        .Take(maxCount)
                        .ToListAsyncWithBusy("GetPowerPrescriptions");
                    break;
            }

            // إزالة القيم الفارغة
            return prescriptions.Where(p => p != null).ToList();
        }

        // دالة لإضافة رأس الورقة للجداول المصفوفية
        private async Task AddMatrixSheetHeader(IXLWorksheet worksheet, string companyName, Lens selectedLens,
            string rowType, string colType, int rowSheetIndex, int colSheetIndex, int totalRowSheets, int totalColSheets, int? warehouseId = null)
        {
            // إضافة اسم الشركة في الوسط
            worksheet.Cell(1, 1).Value = $"{companyName} - تقرير جرد العدسات";
            worksheet.Cell(1, 1).Style.Font.SetBold(true);
            worksheet.Cell(1, 1).Style.Font.SetFontSize(12);
            worksheet.Range(1, 1, 1, 15).Merge();
            worksheet.Cell(1, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);

            // إضافة اسم العدسة في الوسط
            worksheet.Cell(2, 1).Value = $"العدسة: {selectedLens.Name}";
            worksheet.Cell(2, 1).Style.Font.SetBold(true);
            worksheet.Cell(2, 1).Style.Font.SetFontSize(11);
            worksheet.Range(2, 1, 2, 15).Merge();
            worksheet.Cell(2, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);

            // إضافة نوع العدسة في الوسط
            if (selectedLens.Category != null)
            {
                worksheet.Cell(3, 1).Value = $"النوع: {selectedLens.Category.Name}";
                worksheet.Cell(3, 1).Style.Font.SetBold(true);
                worksheet.Cell(3, 1).Style.Font.SetFontSize(10);
                worksheet.Range(3, 1, 3, 15).Merge();
                worksheet.Cell(3, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            }

            // إضافة معلومات المخزن
            if (warehouseId.HasValue)
            {
                // الحصول على اسم المخزن
                var warehouse = await _context.Warehouses.FindAsyncWithBusy(warehouseId.Value);
                string warehouseName = warehouse?.Name ?? "غير محدد";

                worksheet.Cell(4, 1).Value = $"المخزن: {warehouseName}";
                worksheet.Cell(4, 1).Style.Font.SetBold(true);
                worksheet.Cell(4, 1).Style.Font.SetFontSize(10);
                worksheet.Range(4, 1, 4, 15).Merge();
                worksheet.Cell(4, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            }
            else
            {
                worksheet.Cell(4, 1).Value = "المخزن: جميع المخازن";
                worksheet.Cell(4, 1).Style.Font.SetBold(true);
                worksheet.Cell(4, 1).Style.Font.SetFontSize(10);
                worksheet.Range(4, 1, 4, 15).Merge();
                worksheet.Cell(4, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            }
        }

        private string GetPropertyName(string prescriptionType)
        {
            return prescriptionType switch
            {
                "SPH" => "Sphere",
                "CYL" => "Cylinder",
                "POW" => "Power",
                _ => "Sphere"
            };
        }

        // حساب الكمية لوصفة واحدة للعدسة المحددة
        private int CalculateQuantityForPrescription(List<Lens> lenses, string propertyName, short prescriptionId)
        {
            int totalQuantity = 0;

            foreach (var lens in lenses)
            {
                var matchingPrescriptions = lens.LensPrescriptions.Where(lp =>
                    (propertyName == "Sphere" && lp.SphereId == prescriptionId) ||
                    (propertyName == "Cylinder" && lp.CylinderId == prescriptionId) ||
                    (propertyName == "Power" && lp.PowId == prescriptionId)
                ).ToList();

                foreach (var prescription in matchingPrescriptions)
                {
                    foreach (var color in prescription.LensPrescriptionColors)
                    {
                        if (color.LensQuantity != null && color.LensQuantity.Any())
                        {
                            totalQuantity += color.LensQuantity.Sum(lq => lq.Quantity);
                        }
                    }
                }
            }

            return totalQuantity;
        }

        // حساب الكمية الإجمالية للعدسة المحددة
        private int CalculateTotalQuantityForLens(Lens lens)
        {
            int totalQuantity = 0;

            foreach (var prescription in lens.LensPrescriptions)
            {
                foreach (var color in prescription.LensPrescriptionColors)
                {
                    if (color.LensQuantity != null && color.LensQuantity.Any())
                    {
                        totalQuantity += color.LensQuantity.Sum(lq => lq.Quantity);
                    }
                }
            }

            return totalQuantity;
        }

        // حساب الكمية لتوليفة من وصفتين للعدسة المحددة
        private int CalculateQuantityForCombination(List<Lens> lenses, string rowType, string colType,
            short rowId, short colId)
        {
            int totalQuantity = 0;

            foreach (var lens in lenses)
            {
                var matchingPrescriptions = lens.LensPrescriptions.Where(lp =>
                    ((rowType == "SPH" && lp.SphereId == rowId) ||
                     (rowType == "CYL" && lp.CylinderId == rowId) ||
                     (rowType == "POW" && lp.PowId == rowId)) &&
                    ((colType == "SPH" && lp.SphereId == colId) ||
                     (colType == "CYL" && lp.CylinderId == colId) ||
                     (colType == "POW" && lp.PowId == colId))
                ).ToList();

                foreach (var prescription in matchingPrescriptions)
                {
                    foreach (var color in prescription.LensPrescriptionColors)
                    {
                        if (color.LensQuantity != null && color.LensQuantity.Any())
                        {
                            totalQuantity += color.LensQuantity.Sum(lq => lq.Quantity);
                        }
                    }
                }
            }

            return totalQuantity;
        }

        private async Task AddWorksheetHeader(IXLWorksheet worksheet, string companyName, Lens selectedLens, string prescriptionType, int? warehouseId = null)
        {
            // إضافة اسم الشركة في الوسط
            worksheet.Cell(1, 1).Value = $"{companyName} - تقرير جرد العدسات";
            worksheet.Cell(1, 1).Style.Font.SetBold(true);
            worksheet.Cell(1, 1).Style.Font.SetFontSize(12);
            worksheet.Range(1, 1, 1, 5).Merge();
            worksheet.Cell(1, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);

            // إضافة اسم العدسة في الوسط
            worksheet.Cell(2, 1).Value = $"العدسة: {selectedLens.Name}";
            worksheet.Cell(2, 1).Style.Font.SetBold(true);
            worksheet.Cell(2, 1).Style.Font.SetFontSize(11);
            worksheet.Range(2, 1, 2, 5).Merge();
            worksheet.Cell(2, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);

            // إضافة نوع العدسة في الوسط
            if (selectedLens.Category != null)
            {
                worksheet.Cell(3, 1).Value = $"النوع: {selectedLens.Category.Name}";
                worksheet.Cell(3, 1).Style.Font.SetBold(true);
                worksheet.Cell(3, 1).Style.Font.SetFontSize(10);
                worksheet.Range(3, 1, 3, 5).Merge();
                worksheet.Cell(3, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            }

            // إضافة معلومات المخزن
            if (warehouseId.HasValue)
            {
                // الحصول على اسم المخزن
                var warehouse = await _context.Warehouses.FindAsyncWithBusy(warehouseId.Value);
                string warehouseName = warehouse?.Name ?? "غير محدد";

                worksheet.Cell(4, 1).Value = $"المخزن: {warehouseName}";
                worksheet.Cell(4, 1).Style.Font.SetBold(true);
                worksheet.Cell(4, 1).Style.Font.SetFontSize(10);
                worksheet.Range(4, 1, 4, 5).Merge();
                worksheet.Cell(4, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            }
            else
            {
                worksheet.Cell(4, 1).Value = "المخزن: جميع المخازن";
                worksheet.Cell(4, 1).Style.Font.SetBold(true);
                worksheet.Cell(4, 1).Style.Font.SetFontSize(10);
                worksheet.Range(4, 1, 4, 5).Merge();
                worksheet.Cell(4, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            }

            // إضافة نوع الوصفة في الوسط
            worksheet.Cell(5, 1).Value = prescriptionType;
            worksheet.Cell(5, 1).Style.Font.SetBold(true);
            worksheet.Cell(5, 1).Style.Font.SetFontSize(10);
            worksheet.Range(5, 1, 5, 5).Merge();
            worksheet.Cell(5, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
        }

        // تصدير الإيصالات إلى ملف Excel
        public async Task ExportReceiptsToExcel(string filePath, List<Receipt> receipts, int? warehouseId = null, IProgress<int> progress = null)
        {
            using var workbook = new XLWorkbook();

            // الحصول على اسم الشركة من الإعدادات
            string companyName = Properties.Settings.Default.CompanyName;

            // إنشاء ورقة الإيصالات
            var receiptsSheet = workbook.Worksheets.Add("الإيصالات");

            // تطبيق إعدادات عامة للورقة مع دعم RTL
            receiptsSheet.RightToLeft = true; // دعم اتجاه من اليمين إلى اليسار
            receiptsSheet.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            receiptsSheet.Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center);
            receiptsSheet.Style.Font.SetFontName("Arial");
            receiptsSheet.Style.Font.SetFontSize(10);

            // فلترة الإيصالات لاستبعاد الرصيد الافتتاحي
            var filteredReceipts = receipts.Where(r =>
                r.FinancialId != (byte)FinancialId.OpeningBalanceForClient &&
                r.FinancialId != (byte)FinancialId.OpeningBalanceForEmployee).ToList();

            // تحديد عدد الأعمدة حسب ما إذا كان سيتم عرض عمود المخزن
            bool showWarehouseColumn = warehouseId == null || warehouseId == -1; // عرض عمود المخزن إذا كان "جميع المخازن"
            int totalColumns = showWarehouseColumn ? 9 : 8;

            // إضافة رأس الصفحة
            receiptsSheet.Cell(1, 1).Value = companyName;
            receiptsSheet.Cell(1, 1).Style.Font.SetBold(true);
            receiptsSheet.Cell(1, 1).Style.Font.SetFontSize(16);
            receiptsSheet.Cell(1, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            receiptsSheet.Range(1, 1, 1, totalColumns).Merge();

            receiptsSheet.Cell(2, 1).Value = "تقرير الإيصالات";
            receiptsSheet.Cell(2, 1).Style.Font.SetBold(true);
            receiptsSheet.Cell(2, 1).Style.Font.SetFontSize(14);
            receiptsSheet.Cell(2, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            receiptsSheet.Range(2, 1, 2, totalColumns).Merge();

            // إضافة معلومات المخزن
            string warehouseInfo = "";
            if (warehouseId.HasValue && warehouseId.Value > 0)
            {
                var warehouse = await _context.Warehouses.FindAsync(warehouseId.Value);
                warehouseInfo = warehouse != null ? $"المخزن: {warehouse.Name}" : "مخزن غير محدد";
            }
            else
            {
                warehouseInfo = "جميع المخازن";
            }

            receiptsSheet.Cell(3, 1).Value = warehouseInfo;
            receiptsSheet.Cell(3, 1).Style.Font.SetBold(true);
            receiptsSheet.Cell(3, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            receiptsSheet.Range(3, 1, 3, totalColumns).Merge();

            receiptsSheet.Cell(4, 1).Value = $"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}";
            receiptsSheet.Cell(4, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            receiptsSheet.Range(4, 1, 4, totalColumns).Merge();

            // إضافة رؤوس الأعمدة
            int headerRow = 6;
            receiptsSheet.Cell(headerRow, 1).Value = "رقم الإيصال";
            receiptsSheet.Cell(headerRow, 2).Value = "التاريخ";
            receiptsSheet.Cell(headerRow, 3).Value = "نوع العملية";
            receiptsSheet.Cell(headerRow, 4).Value = "مدين";
            receiptsSheet.Cell(headerRow, 5).Value = "دائن";
            receiptsSheet.Cell(headerRow, 6).Value = "العميل/الموظف";
            receiptsSheet.Cell(headerRow, 7).Value = "طريقة الدفع";
            receiptsSheet.Cell(headerRow, 8).Value = "الملاحظات";

            if (showWarehouseColumn)
            {
                receiptsSheet.Cell(headerRow, 9).Value = "المخزن";
            }

            // تنسيق رؤوس الأعمدة مع محاذاة يمين للنصوص العربية
            var headerRange = receiptsSheet.Range(headerRow, 1, headerRow, totalColumns);
            headerRange.Style.Font.SetBold(true);
            headerRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#D9E1F2");
            headerRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            headerRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
            headerRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);

            progress?.Report(20);

            // إضافة بيانات الإيصالات مع معالجة خاصة للتحويلات
            int currentRow = headerRow + 1;
            int totalReceipts = filteredReceipts.Count;

            // قوائم لحفظ الإجماليات
            var treasuryTotals = new Dictionary<string, decimal>();
            var financialTotals = new Dictionary<string, decimal>();

            // قاموس لحفظ إجماليات طرق الدفع مع المخازن (عند عرض جميع المخازن)
            var treasuryWarehouseTotals = new Dictionary<string, decimal>();

            // جلب جميع البيانات المالية مرة واحدة (محسنة)
            var financialIds = filteredReceipts.Select(r => r.FinancialId).Distinct().ToList();
            var financials = await _context.Financials
                .Where(f => financialIds.Contains(f.Id))
                .ToDictionaryAsync(f => f.Id, f => f.Name);

            for (int i = 0; i < totalReceipts; i++)
            {
                var receipt = filteredReceipts[i];

                // الحصول على اسم العملية المالية من القاموس المحضر مسبقاً
                string financialName = financials.GetValueOrDefault(receipt.FinancialId.Value);

                // معالجة خاصة لإيصالات التحويل
                if (receipt.FinancialId == (byte)FinancialId.Transfer)
                {
                    // إضافة صفي التحويل مع دمج البيانات المشتركة
                    await AddTransferRows(receiptsSheet, receipt, currentRow, showWarehouseColumn, treasuryTotals, financialTotals, treasuryWarehouseTotals, financialName);
                    currentRow += 2; // صفين للتحويل
                }
                else
                {
                    // إيصال عادي
                    await AddNormalReceiptRow(receiptsSheet, receipt, currentRow, showWarehouseColumn, treasuryTotals, financialTotals, treasuryWarehouseTotals, financialName);
                    currentRow++;
                }

                // تحديث التقدم
                if (i % 10 == 0)
                {
                    int progressValue = 20 + (int)((double)i / totalReceipts * 50);
                    progress?.Report(progressValue);
                }
            }

            progress?.Report(70);

            // إضافة الإجماليات التفصيلية
            currentRow = await AddDetailedTotals(receiptsSheet, currentRow, treasuryTotals, financialTotals, treasuryWarehouseTotals, showWarehouseColumn);

            progress?.Report(80);

            // حساب الإجماليات العامة
            decimal totalDebit = 0;
            decimal totalCredit = 0;

            foreach (var receipt in filteredReceipts)
            {
                if (receipt.FinancialId == (byte)FinancialId.Transfer)
                {
                    // للتحويلات: نحسب المبلغ مرة واحدة فقط
                    totalDebit += receipt.Value;
                    totalCredit += receipt.Value;
                }
                else
                {
                    if (receipt.IsExchange == true) // صرف
                        totalDebit += receipt.Value;
                    else // قبض
                        totalCredit += receipt.Value;
                }
            }

            decimal netBalance = totalCredit - totalDebit;

            // إضافة الإجماليات العامة
            currentRow += 2;
            int summaryColumn = showWarehouseColumn ? 7 : 6;
            int valueColumn = showWarehouseColumn ? 8 : 7;

            // إجمالي المدين
            string debitText = $"إجمالي المدين: {totalDebit.ToString("#,##0.00")}";
            receiptsSheet.Cell(currentRow, summaryColumn).Value = debitText;
            receiptsSheet.Cell(currentRow, summaryColumn).Style.Font.SetBold(true);
            receiptsSheet.Cell(currentRow, summaryColumn).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            receiptsSheet.Cell(currentRow, summaryColumn).Style.Fill.BackgroundColor = XLColor.FromHtml("#FFE6E6");

            if (showWarehouseColumn)
            {
                receiptsSheet.Range(currentRow, summaryColumn, currentRow, valueColumn).Merge();
            }

            currentRow++;
            // إجمالي الدائن
            string creditText = $"إجمالي الدائن: {totalCredit.ToString("#,##0.00")}";
            receiptsSheet.Cell(currentRow, summaryColumn).Value = creditText;
            receiptsSheet.Cell(currentRow, summaryColumn).Style.Font.SetBold(true);
            receiptsSheet.Cell(currentRow, summaryColumn).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            receiptsSheet.Cell(currentRow, summaryColumn).Style.Fill.BackgroundColor = XLColor.FromHtml("#E6FFE6");

            if (showWarehouseColumn)
            {
                receiptsSheet.Range(currentRow, summaryColumn, currentRow, valueColumn).Merge();
            }

            currentRow++;
            // الرصيد الصافي
            string balanceText = $"الرصيد الصافي: {netBalance.ToString("#,##0.00")}";
            receiptsSheet.Cell(currentRow, summaryColumn).Value = balanceText;
            receiptsSheet.Cell(currentRow, summaryColumn).Style.Font.SetBold(true);
            receiptsSheet.Cell(currentRow, summaryColumn).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);

            if (netBalance >= 0)
            {
                receiptsSheet.Cell(currentRow, summaryColumn).Style.Fill.BackgroundColor = XLColor.FromHtml("#E2EFDA");
            }
            else
            {
                receiptsSheet.Cell(currentRow, summaryColumn).Style.Fill.BackgroundColor = XLColor.FromHtml("#FFE2E2");
            }

            if (showWarehouseColumn)
            {
                receiptsSheet.Range(currentRow, summaryColumn, currentRow, valueColumn).Merge();
            }

            // تعديل عرض الأعمدة
            receiptsSheet.Column(1).Width = 12; // رقم الإيصال
            receiptsSheet.Column(2).Width = 12; // التاريخ
            receiptsSheet.Column(3).Width = 15; // نوع العملية
            receiptsSheet.Column(4).Width = 12; // مدين
            receiptsSheet.Column(5).Width = 12; // دائن
            receiptsSheet.Column(6).Width = 20; // العميل
            receiptsSheet.Column(7).Width = 15; // طريقة الدفع
            receiptsSheet.Column(8).Width = 25; // الملاحظات
            receiptsSheet.Column(9).Width = 15; // المخزن

            // حفظ الملف
            workbook.SaveAs(filePath);

            progress?.Report(100);
        }

        /// <summary>
        /// إضافة صفي التحويل مع دمج البيانات المشتركة
        /// </summary>
        private async Task AddTransferRows(IXLWorksheet sheet, Receipt receipt, int startRow, bool showWarehouseColumn,
            Dictionary<string, decimal> treasuryTotals, Dictionary<string, decimal> financialTotals,
            Dictionary<string, decimal> treasuryWarehouseTotals, string financialName)
        {
            int totalColumns = showWarehouseColumn ? 9 : 8;

            // الصف الأول: المحول منه (صرف)
            int debitRow = startRow;
            // الصف الثاني: المحول إليه (قبض)
            int creditRow = startRow + 1;

            // البيانات المشتركة (رقم الإيصال، التاريخ، نوع العملية، الملاحظات، المخزن)

            // رقم الإيصال - دمج الصفين
            sheet.Cell(debitRow, 1).Value = receipt.ReceiptNumber;
            sheet.Cell(debitRow, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(debitRow, 1).Style.Font.SetBold(true);
            sheet.Range(debitRow, 1, creditRow, 1).Merge();

            // التاريخ - دمج الصفين
            sheet.Cell(debitRow, 2).Value = receipt.Date.ToString("yyyy/MM/dd");
            sheet.Cell(debitRow, 2).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(debitRow, 2).Style.Font.SetBold(true);
            sheet.Range(debitRow, 2, creditRow, 2).Merge();

            // نوع العملية - دمج الصفين
            sheet.Cell(debitRow, 3).Value = financialName;
            sheet.Cell(debitRow, 3).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(debitRow, 3).Style.Font.SetBold(true);
            sheet.Range(debitRow, 3, creditRow, 3).Merge();

            // المبالغ - الصف الأول (صرف)
            sheet.Cell(debitRow, 4).Value = receipt.Value;
            sheet.Cell(debitRow, 4).Style.NumberFormat.Format = "#,##0.00";
            sheet.Cell(debitRow, 4).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(debitRow, 4).Style.Font.SetBold(true);
            sheet.Cell(debitRow, 5).Value = "";

            // المبالغ - الصف الثاني (قبض)
            sheet.Cell(creditRow, 4).Value = "";
            sheet.Cell(creditRow, 5).Value = receipt.Value;
            sheet.Cell(creditRow, 5).Style.NumberFormat.Format = "#,##0.00";
            sheet.Cell(creditRow, 5).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(creditRow, 5).Style.Font.SetBold(true);

            // العميل/الموظف - فارغ للتحويلات - دمج الصفين
            sheet.Cell(debitRow, 6).Value = "";
            sheet.Range(debitRow, 6, creditRow, 6).Merge();

            // طرق الدفع
            string sourceTreasuryName = "";
            string targetTreasuryName = "";

            if (receipt.SourceTreasuryId.HasValue)
            {
                var sourceTreasury = await _context.Treasuries.FindAsync(receipt.SourceTreasuryId.Value);
                sourceTreasuryName = sourceTreasury?.Name ?? "";
            }

            if (receipt.TargetTreasuryId.HasValue)
            {
                var targetTreasury = await _context.Treasuries.FindAsync(receipt.TargetTreasuryId.Value);
                targetTreasuryName = targetTreasury?.Name ?? "";
            }

            sheet.Cell(debitRow, 7).Value = sourceTreasuryName;
            sheet.Cell(debitRow, 7).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(debitRow, 7).Style.Font.SetBold(true);

            sheet.Cell(creditRow, 7).Value = targetTreasuryName;
            sheet.Cell(creditRow, 7).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(creditRow, 7).Style.Font.SetBold(true);

            // الملاحظات - دمج الصفين
            sheet.Cell(debitRow, 8).Value = receipt.Statement ?? "";
            sheet.Cell(debitRow, 8).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(debitRow, 8).Style.Font.SetBold(true);
            sheet.Range(debitRow, 8, creditRow, 8).Merge();

            // المخزن - دمج الصفين (إذا كان مطلوب عرضه)
            if (showWarehouseColumn)
            {
                string warehouseName = "";
                if (receipt.WarehouseId.HasValue)
                {
                    var warehouse = await _context.Warehouses.FindAsync(receipt.WarehouseId.Value);
                    warehouseName = warehouse?.Name ?? "";
                }
                sheet.Cell(debitRow, 9).Value = warehouseName;
                sheet.Cell(debitRow, 9).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                sheet.Cell(debitRow, 9).Style.Font.SetBold(true);
                sheet.Range(debitRow, 9, creditRow, 9).Merge();
            }

            // إضافة للإجماليات
            await UpdateTotalsForTransfer(receipt, sourceTreasuryName, targetTreasuryName, showWarehouseColumn,
                treasuryTotals, financialTotals, treasuryWarehouseTotals, financialName);

            // تطبيق حدود على الصفوف
            var debitRowRange = sheet.Range(debitRow, 1, debitRow, totalColumns);
            debitRowRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            debitRowRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;

            var creditRowRange = sheet.Range(creditRow, 1, creditRow, totalColumns);
            creditRowRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            creditRowRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
        }

        /// <summary>
        /// تحديث الإجماليات لإيصالات التحويل
        /// </summary>
        private async Task UpdateTotalsForTransfer(Receipt receipt, string sourceTreasuryName, string targetTreasuryName,
            bool showWarehouseColumn, Dictionary<string, decimal> treasuryTotals, Dictionary<string, decimal> financialTotals,
            Dictionary<string, decimal> treasuryWarehouseTotals, string financialName)
        {
            // إضافة للإجماليات - المصدر (صرف)
            if (!string.IsNullOrEmpty(sourceTreasuryName))
            {
                if (!treasuryTotals.ContainsKey(sourceTreasuryName))
                    treasuryTotals[sourceTreasuryName] = 0;
                treasuryTotals[sourceTreasuryName] -= receipt.Value;

                // إضافة للإجماليات مع المخزن (عند عرض جميع المخازن)
                if (showWarehouseColumn)
                {
                    string warehouseName = "";
                    if (receipt.WarehouseId.HasValue)
                    {
                        var warehouse = await _context.Warehouses.FindAsync(receipt.WarehouseId.Value);
                        warehouseName = warehouse?.Name ?? "";
                    }

                    string treasuryWarehouseKey = $"{sourceTreasuryName} - {warehouseName}";
                    if (!treasuryWarehouseTotals.ContainsKey(treasuryWarehouseKey))
                        treasuryWarehouseTotals[treasuryWarehouseKey] = 0;
                    treasuryWarehouseTotals[treasuryWarehouseKey] -= receipt.Value;
                }
            }

            // إضافة للإجماليات - الهدف (قبض)
            if (!string.IsNullOrEmpty(targetTreasuryName))
            {
                if (!treasuryTotals.ContainsKey(targetTreasuryName))
                    treasuryTotals[targetTreasuryName] = 0;
                treasuryTotals[targetTreasuryName] += receipt.Value;

                // إضافة للإجماليات مع المخزن (عند عرض جميع المخازن)
                if (showWarehouseColumn)
                {
                    string warehouseName = "";
                    if (receipt.WarehouseId.HasValue)
                    {
                        var warehouse = await _context.Warehouses.FindAsync(receipt.WarehouseId.Value);
                        warehouseName = warehouse?.Name ?? "";
                    }

                    string treasuryWarehouseKey = $"{targetTreasuryName} - {warehouseName}";
                    if (!treasuryWarehouseTotals.ContainsKey(treasuryWarehouseKey))
                        treasuryWarehouseTotals[treasuryWarehouseKey] = 0;
                    treasuryWarehouseTotals[treasuryWarehouseKey] += receipt.Value;
                }
            }

            // إضافة لإجماليات الأنواع
            if (!financialTotals.ContainsKey(financialName))
                financialTotals[financialName] = 0;
            financialTotals[financialName] += receipt.Value;
        }

        /// <summary>
        /// إضافة صف إيصال تحويل (صرف أو قبض)
        /// </summary>
        private async Task AddTransferRow(IXLWorksheet sheet, Receipt receipt, int row, bool isDebit, bool showWarehouseColumn,
            Dictionary<string, decimal> treasuryTotals, Dictionary<string, decimal> financialTotals,
            Dictionary<string, decimal> treasuryWarehouseTotals, string financialName)
        {
            // رقم الإيصال والتاريخ
            sheet.Cell(row, 1).Value = receipt.ReceiptNumber;
            sheet.Cell(row, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(row, 1).Style.Font.SetBold(true);

            sheet.Cell(row, 2).Value = receipt.Date.ToString("yyyy/MM/dd");
            sheet.Cell(row, 2).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(row, 2).Style.Font.SetBold(true);

            // نوع العملية
            sheet.Cell(row, 3).Value = financialName;
            sheet.Cell(row, 3).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(row, 3).Style.Font.SetBold(true);

            // المبلغ في المدين أو الدائن
            if (isDebit) // صرف من المصدر
            {
                sheet.Cell(row, 4).Value = receipt.Value;
                sheet.Cell(row, 4).Style.NumberFormat.Format = "#,##0.00";
                sheet.Cell(row, 4).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                sheet.Cell(row, 4).Style.Font.SetBold(true);
                sheet.Cell(row, 5).Value = "";
            }
            else // قبض في الهدف
            {
                sheet.Cell(row, 4).Value = "";
                sheet.Cell(row, 5).Value = receipt.Value;
                sheet.Cell(row, 5).Style.NumberFormat.Format = "#,##0.00";
                sheet.Cell(row, 5).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                sheet.Cell(row, 5).Style.Font.SetBold(true);
            }

            // العميل/الموظف (فارغ للتحويلات)
            sheet.Cell(row, 6).Value = "";

            // طريقة الدفع
            string treasuryName = "";
            if (isDebit && receipt.SourceTreasuryId.HasValue)
            {
                var treasury = await _context.Treasuries.FindAsync(receipt.SourceTreasuryId.Value);
                treasuryName = treasury?.Name ?? "";
            }
            else if (!isDebit && receipt.TargetTreasuryId.HasValue)
            {
                var treasury = await _context.Treasuries.FindAsync(receipt.TargetTreasuryId.Value);
                treasuryName = treasury?.Name ?? "";
            }

            sheet.Cell(row, 7).Value = treasuryName;
            sheet.Cell(row, 7).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(row, 7).Style.Font.SetBold(true);

            // الملاحظات
            sheet.Cell(row, 8).Value = receipt.Statement ?? "";
            sheet.Cell(row, 8).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(row, 8).Style.Font.SetBold(true);

            // المخزن (إذا كان مطلوب عرضه)
            if (showWarehouseColumn)
            {
                string warehouseName = "";
                if (receipt.WarehouseId.HasValue)
                {
                    var warehouse = await _context.Warehouses.FindAsync(receipt.WarehouseId.Value);
                    warehouseName = warehouse?.Name ?? "";
                }
                sheet.Cell(row, 9).Value = warehouseName;
                sheet.Cell(row, 9).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                sheet.Cell(row, 9).Style.Font.SetBold(true);
            }

            // إضافة للإجماليات
            if (!string.IsNullOrEmpty(treasuryName))
            {
                if (!treasuryTotals.ContainsKey(treasuryName))
                    treasuryTotals[treasuryName] = 0;
                treasuryTotals[treasuryName] += isDebit ? -receipt.Value : receipt.Value;

                // إضافة للإجماليات مع المخزن (عند عرض جميع المخازن)
                if (showWarehouseColumn)
                {
                    string warehouseName = "";
                    if (receipt.WarehouseId.HasValue)
                    {
                        var warehouse = await _context.Warehouses.FindAsync(receipt.WarehouseId.Value);
                        warehouseName = warehouse?.Name ?? "";
                    }

                    string treasuryWarehouseKey = $"{treasuryName} - {warehouseName}";
                    if (!treasuryWarehouseTotals.ContainsKey(treasuryWarehouseKey))
                        treasuryWarehouseTotals[treasuryWarehouseKey] = 0;
                    treasuryWarehouseTotals[treasuryWarehouseKey] += isDebit ? -receipt.Value : receipt.Value;
                }
            }

            if (!financialTotals.ContainsKey(financialName))
                financialTotals[financialName] = 0;
            financialTotals[financialName] += receipt.Value;

            // تطبيق حدود على الصف
            int totalColumns = showWarehouseColumn ? 9 : 8;
            var rowRange = sheet.Range(row, 1, row, totalColumns);
            rowRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            rowRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
        }

        /// <summary>
        /// إضافة صف إيصال عادي
        /// </summary>
        private async Task AddNormalReceiptRow(IXLWorksheet sheet, Receipt receipt, int row, bool showWarehouseColumn,
            Dictionary<string, decimal> treasuryTotals, Dictionary<string, decimal> financialTotals,
            Dictionary<string, decimal> treasuryWarehouseTotals, string financialName)
        {
            // رقم الإيصال
            sheet.Cell(row, 1).Value = receipt.ReceiptNumber;
            sheet.Cell(row, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(row, 1).Style.Font.SetBold(true);

            // التاريخ
            sheet.Cell(row, 2).Value = receipt.Date.ToString("yyyy/MM/dd");
            sheet.Cell(row, 2).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(row, 2).Style.Font.SetBold(true);

            // نوع العملية
            sheet.Cell(row, 3).Value = financialName;
            sheet.Cell(row, 3).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(row, 3).Style.Font.SetBold(true);

            // توزيع المبلغ على المدين والدائن
            if (receipt.IsExchange == true) // صرف
            {
                sheet.Cell(row, 4).Value = receipt.Value;
                sheet.Cell(row, 4).Style.NumberFormat.Format = "#,##0.00";
                sheet.Cell(row, 4).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                sheet.Cell(row, 4).Style.Font.SetBold(true);
                sheet.Cell(row, 5).Value = "";
            }
            else // قبض
            {
                sheet.Cell(row, 4).Value = "";
                sheet.Cell(row, 5).Value = receipt.Value;
                sheet.Cell(row, 5).Style.NumberFormat.Format = "#,##0.00";
                sheet.Cell(row, 5).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                sheet.Cell(row, 5).Style.Font.SetBold(true);
            }

            // العميل/الموظف
            string clientName = "";
            if (receipt.ClientId.HasValue)
            {
                var client = await _context.Clients.FindAsync(receipt.ClientId.Value);
                clientName = client?.Name ?? "";
            }
            else if (receipt.EmployeeId.HasValue)
            {
                var employee = await _context.Users.FindAsync(receipt.EmployeeId.Value);
                clientName = employee?.Name ?? "";
            }
            else if (receipt.ExpenseId.HasValue)
            {
                var expense = await _context.Expenses.FindAsync(receipt.ExpenseId.Value);
                clientName = expense?.Name ?? "";
            }
            sheet.Cell(row, 6).Value = clientName;
            sheet.Cell(row, 6).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(row, 6).Style.Font.SetBold(true);

            // طريقة الدفع
            string treasuryName = "";
            if (receipt.TreasuryId.HasValue)
            {
                var treasury = await _context.Treasuries.FindAsync(receipt.TreasuryId.Value);
                treasuryName = treasury?.Name ?? "";
            }
            sheet.Cell(row, 7).Value = treasuryName;
            sheet.Cell(row, 7).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(row, 7).Style.Font.SetBold(true);

            // الملاحظات
            sheet.Cell(row, 8).Value = receipt.Statement ?? "";
            sheet.Cell(row, 8).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(row, 8).Style.Font.SetBold(true);

            // المخزن (إذا كان مطلوب عرضه)
            if (showWarehouseColumn)
            {
                string warehouseName = "";
                if (receipt.WarehouseId.HasValue)
                {
                    var warehouse = await _context.Warehouses.FindAsync(receipt.WarehouseId.Value);
                    warehouseName = warehouse?.Name ?? "";
                }
                sheet.Cell(row, 9).Value = warehouseName;
                sheet.Cell(row, 9).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                sheet.Cell(row, 9).Style.Font.SetBold(true);
            }

            // إضافة للإجماليات
            if (!string.IsNullOrEmpty(treasuryName))
            {
                if (!treasuryTotals.ContainsKey(treasuryName))
                    treasuryTotals[treasuryName] = 0;
                treasuryTotals[treasuryName] += receipt.IsExchange == true ? -receipt.Value : receipt.Value;

                // إضافة للإجماليات مع المخزن (عند عرض جميع المخازن)
                if (showWarehouseColumn)
                {
                    string warehouseName = "";
                    if (receipt.WarehouseId.HasValue)
                    {
                        var warehouse = await _context.Warehouses.FindAsync(receipt.WarehouseId.Value);
                        warehouseName = warehouse?.Name ?? "";
                    }

                    string treasuryWarehouseKey = $"{treasuryName} - {warehouseName}";
                    if (!treasuryWarehouseTotals.ContainsKey(treasuryWarehouseKey))
                        treasuryWarehouseTotals[treasuryWarehouseKey] = 0;
                    treasuryWarehouseTotals[treasuryWarehouseKey] += receipt.IsExchange == true ? -receipt.Value : receipt.Value;
                }
            }

            if (!financialTotals.ContainsKey(financialName))
                financialTotals[financialName] = 0;
            financialTotals[financialName] += receipt.Value;

            // تطبيق حدود على الصف
            int totalColumns = showWarehouseColumn ? 9 : 8;
            var rowRange = sheet.Range(row, 1, row, totalColumns);
            rowRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            rowRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
        }

        /// <summary>
        /// إضافة الإجماليات التفصيلية لطرق الدفع وأنواع الإيصالات
        /// </summary>
        private async Task<int> AddDetailedTotals(IXLWorksheet sheet, int currentRow,
            Dictionary<string, decimal> treasuryTotals, Dictionary<string, decimal> financialTotals,
            Dictionary<string, decimal> treasuryWarehouseTotals, bool showWarehouseColumn)
        {
            int summaryColumn = showWarehouseColumn ? 7 : 6;
            int valueColumn = showWarehouseColumn ? 8 : 7;

            // إضافة جدول طرق الدفع مع مجموع الإيصالات والرصيد الحالي
            currentRow = await AddCombinedTreasuryTotals(sheet, currentRow, treasuryTotals, treasuryWarehouseTotals, showWarehouseColumn);

            // إضافة إجماليات أنواع الإيصالات
            if (financialTotals.Any())
            {
                currentRow += 1;
                sheet.Cell(currentRow, 1).Value = "إجماليات أنواع الإيصالات:";
                sheet.Cell(currentRow, 1).Style.Font.SetBold(true);
                sheet.Cell(currentRow, 1).Style.Font.SetFontSize(12);
                sheet.Cell(currentRow, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                sheet.Cell(currentRow, 1).Style.Fill.BackgroundColor = XLColor.FromHtml("#F2E1D9");

                int totalColumns = showWarehouseColumn ? 9 : 8;
                sheet.Range(currentRow, 1, currentRow, totalColumns).Merge();

                currentRow++;
                foreach (var financialTotal in financialTotals.OrderBy(f => f.Key))
                {
                    // دمج النص والمبلغ في خلية واحدة
                    string fullText = $"{financialTotal.Key}: {financialTotal.Value.ToString("#,##0.00")}";

                    sheet.Cell(currentRow, summaryColumn).Value = fullText;
                    sheet.Cell(currentRow, summaryColumn).Style.Font.SetBold(true);
                    sheet.Cell(currentRow, summaryColumn).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                    sheet.Cell(currentRow, summaryColumn).Style.Fill.BackgroundColor = XLColor.FromHtml("#FFF2CC");

                    // دمج الخلايا لعرض النص بشكل أفضل
                    if (showWarehouseColumn)
                    {
                        sheet.Range(currentRow, summaryColumn, currentRow, valueColumn).Merge();
                    }

                    currentRow++;
                }
            }

            return currentRow;
        }

        /// <summary>
        /// إضافة جدول طرق الدفع مع مجموع الإيصالات والرصيد الحالي في نفس الصف
        /// </summary>
        private async Task<int> AddCombinedTreasuryTotals(IXLWorksheet sheet, int currentRow,
            Dictionary<string, decimal> treasuryTotals, Dictionary<string, decimal> treasuryWarehouseTotals,
            bool showWarehouseColumn)
        {
            var totalsToShow = showWarehouseColumn ? treasuryWarehouseTotals : treasuryTotals;

            if (!totalsToShow.Any())
                return currentRow;

            currentRow += 2;

            // تحديد موضع الجدول في المنتصف بطريقة جميلة
            int totalColumns = showWarehouseColumn ? 9 : 8;
            int summaryColumn = showWarehouseColumn ? 7 : 6;
            int valueColumn = showWarehouseColumn ? 8 : 7;

            // حساب موضع الجدول في المنتصف (6 أعمدة للجدول - كل حقل يأخذ خليتين)
            int tableWidth = 6;
            int startColumn = Math.Max(1, (totalColumns - tableWidth) / 2 + 1);

            // إضافة عنوان الجدول مع تنسيق جميل
            sheet.Cell(currentRow, 1).Value = "ملخص طرق الدفع";
            sheet.Cell(currentRow, 1).Style.Font.SetBold(true);
            sheet.Cell(currentRow, 1).Style.Font.SetFontSize(16);
            sheet.Cell(currentRow, 1).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Cell(currentRow, 1).Style.Fill.BackgroundColor = XLColor.FromHtml("#4472C4");
            sheet.Cell(currentRow, 1).Style.Font.FontColor = XLColor.White;

            // دمج العنوان عبر كامل العرض مع حدود جميلة
            sheet.Range(currentRow, 1, currentRow, totalColumns).Merge();
            sheet.Range(currentRow, 1, currentRow, totalColumns).Style.Border.OutsideBorder = XLBorderStyleValues.Medium;

            currentRow++;

            // إضافة صف فارغ للتباعد
            currentRow++;

            // إضافة رؤوس الأعمدة في المنتصف - كل حقل يأخذ خليتين
            // طريقة الدفع (خليتين)
            sheet.Cell(currentRow, startColumn).Value = "طريقة الدفع";
            sheet.Range(currentRow, startColumn, currentRow, startColumn + 1).Merge();

            // مجموع الإيصالات (خليتين)
            sheet.Cell(currentRow, startColumn + 2).Value = "مجموع الإيصالات";
            sheet.Range(currentRow, startColumn + 2, currentRow, startColumn + 3).Merge();

            // الرصيد الحالي (خليتين)
            sheet.Cell(currentRow, startColumn + 4).Value = "الرصيد الحالي";
            sheet.Range(currentRow, startColumn + 4, currentRow, startColumn + 5).Merge();

            // تنسيق رؤوس الأعمدة بشكل جميل (محسنة - تطبيق التنسيق على نطاق)
            var headerRange = sheet.Range(currentRow, startColumn, currentRow, startColumn + 5);
            headerRange.Style.Font.SetBold(true);
            headerRange.Style.Font.SetFontSize(12);
            headerRange.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            headerRange.Style.Fill.BackgroundColor = XLColor.FromHtml("#D9E2F3");
            headerRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            headerRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;

            // تحديد عرض الأعمدة (محسنة)
            for (int col = 0; col < 6; col++)
            {
                sheet.Column(startColumn + col).Width = 15;
            }

            currentRow++;

            try
            {
                // جلب جميع طرق الدفع مع أرصدتها
                var treasuries = await _context.Treasuries
                    .Include(t => t.Warehouse)
                    .OrderBy(t => t.Name)
                    .ThenBy(t => t.Warehouse != null ? t.Warehouse.Name : "")
                    .ToListAsync();

                // إنشاء قاموس للربط بين أسماء طرق الدفع والكائنات (محسنة باستخدام LINQ)
                var treasuryLookup = treasuries.ToDictionary(
                    treasury => showWarehouseColumn
                        ? $"{treasury.Name} - {treasury.Warehouse?.Name ?? "غير محدد"}"
                        : treasury.Name,
                    treasury => treasury
                );

                // عرض البيانات لكل طريقة دفع بتنسيق جميل - كل حقل يأخذ خليتين
                foreach (var treasuryTotal in totalsToShow.OrderBy(t => t.Key))
                {
                    // اسم طريقة الدفع (خليتين مدموجتين)
                    sheet.Cell(currentRow, startColumn).Value = treasuryTotal.Key;
                    sheet.Range(currentRow, startColumn, currentRow, startColumn + 1).Merge();
                    sheet.Cell(currentRow, startColumn).Style.Font.SetBold(true);
                    sheet.Cell(currentRow, startColumn).Style.Font.SetFontSize(11);
                    sheet.Cell(currentRow, startColumn).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                    sheet.Cell(currentRow, startColumn).Style.Fill.BackgroundColor = XLColor.FromHtml("#F8F9FA");

                    // مجموع الإيصالات (خليتين مدموجتين)
                    sheet.Cell(currentRow, startColumn + 2).Value = treasuryTotal.Value;
                    sheet.Range(currentRow, startColumn + 2, currentRow, startColumn + 3).Merge();
                    sheet.Cell(currentRow, startColumn + 2).Style.NumberFormat.Format = "#,##0.00";
                    sheet.Cell(currentRow, startColumn + 2).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                    sheet.Cell(currentRow, startColumn + 2).Style.Font.SetBold(true);
                    sheet.Cell(currentRow, startColumn + 2).Style.Font.SetFontSize(11);

                    // تلوين مجموع الإيصالات حسب القيمة مع تدرج لوني جميل
                    if (treasuryTotal.Value >= 0)
                    {
                        sheet.Cell(currentRow, startColumn + 2).Style.Fill.BackgroundColor = XLColor.FromHtml("#D4EDDA");
                        sheet.Cell(currentRow, startColumn + 2).Style.Font.FontColor = XLColor.FromHtml("#155724");
                    }
                    else
                    {
                        sheet.Cell(currentRow, startColumn + 2).Style.Fill.BackgroundColor = XLColor.FromHtml("#F8D7DA");
                        sheet.Cell(currentRow, startColumn + 2).Style.Font.FontColor = XLColor.FromHtml("#721C24");
                    }

                    // الرصيد الحالي (خليتين مدموجتين)
                    if (treasuryLookup.TryGetValue(treasuryTotal.Key, out Treasury treasury))
                    {
                        sheet.Cell(currentRow, startColumn + 4).Value = treasury.Balance;
                        sheet.Range(currentRow, startColumn + 4, currentRow, startColumn + 5).Merge();
                        sheet.Cell(currentRow, startColumn + 4).Style.NumberFormat.Format = "#,##0.00";
                        sheet.Cell(currentRow, startColumn + 4).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                        sheet.Cell(currentRow, startColumn + 4).Style.Font.SetBold(true);
                        sheet.Cell(currentRow, startColumn + 4).Style.Font.SetFontSize(11);

                        // تلوين الرصيد الحالي حسب القيمة مع تدرج لوني جميل
                        if (treasury.Balance >= 0)
                        {
                            sheet.Cell(currentRow, startColumn + 4).Style.Fill.BackgroundColor = XLColor.FromHtml("#D1ECF1");
                            sheet.Cell(currentRow, startColumn + 4).Style.Font.FontColor = XLColor.FromHtml("#0C5460");
                        }
                        else
                        {
                            sheet.Cell(currentRow, startColumn + 4).Style.Fill.BackgroundColor = XLColor.FromHtml("#F5C6CB");
                            sheet.Cell(currentRow, startColumn + 4).Style.Font.FontColor = XLColor.FromHtml("#721C24");
                        }
                    }
                    else
                    {
                        sheet.Cell(currentRow, startColumn + 4).Value = "غير متوفر";
                        sheet.Range(currentRow, startColumn + 4, currentRow, startColumn + 5).Merge();
                        sheet.Cell(currentRow, startColumn + 4).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                        sheet.Cell(currentRow, startColumn + 4).Style.Fill.BackgroundColor = XLColor.FromHtml("#E2E3E5");
                        sheet.Cell(currentRow, startColumn + 4).Style.Font.FontColor = XLColor.FromHtml("#6C757D");
                        sheet.Cell(currentRow, startColumn + 4).Style.Font.SetItalic(true);
                    }

                    // إضافة حدود جميلة للصف (محسنة - تطبيق على نطاق)
                    var rowRange = sheet.Range(currentRow, startColumn, currentRow, startColumn + 5);
                    rowRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                    rowRange.Style.Border.InsideBorder = XLBorderStyleValues.Hair;
                    rowRange.Style.Border.InsideBorderColor = XLColor.FromHtml("#DEE2E6");

                    currentRow++;
                }

                // إضافة حد سفلي للجدول - للخلايا الست
                var tableRange = sheet.Range(currentRow - totalsToShow.Count, startColumn, currentRow - 1, startColumn + 5);
                tableRange.Style.Border.OutsideBorder = XLBorderStyleValues.Medium;
                tableRange.Style.Border.InsideBorderColor = XLColor.FromHtml("#4472C4");
            }
            catch (Exception ex)
            {
                // في حالة حدوث خطأ، أضف رسالة خطأ بتنسيق جميل
                sheet.Cell(currentRow, startColumn).Value = $"خطأ في تحميل بيانات طرق الدفع: {ex.Message}";
                sheet.Cell(currentRow, startColumn).Style.Font.SetBold(true);
                sheet.Cell(currentRow, startColumn).Style.Font.FontColor = XLColor.FromHtml("#721C24");
                sheet.Cell(currentRow, startColumn).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                sheet.Cell(currentRow, startColumn).Style.Fill.BackgroundColor = XLColor.FromHtml("#F8D7DA");
                sheet.Range(currentRow, startColumn, currentRow, startColumn + 5).Merge();
                sheet.Range(currentRow, startColumn, currentRow, startColumn + 5).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                currentRow++;
            }

            // إضافة صف فارغ للتباعد بعد الجدول
            currentRow++;

            return currentRow;
        }

        // Implement IDisposable pattern
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                    _context?.Dispose();
                }

                // Free unmanaged resources
                _disposed = true;
            }
        }

        #region Purchase Export Methods

        /// <summary>
        /// تصدير المشتريات مع معاينة مفصلة
        /// </summary>
        public async Task ExportPurchasesWithPreviewAsync(
            string filePath,
            List<PurchaseViewModel> purchases,
            List<PurchaseItemVM> items,
            bool exportInvoices,
            bool exportItems,
            List<string> selectedInvoiceFields,
            List<string> selectedItemFields,
            Dictionary<string, string> invoiceHeaders,
            Dictionary<string, string> itemHeaders,
            int? clientId = null,
            int? warehouseId = null,
            IProgress<int> progress = null)
        {
            using var workbook = new XLWorkbook();

            int totalSteps = 0;
            int currentStep = 0;

            // حساب إجمالي الخطوات
            if (exportInvoices) totalSteps++;
            if (exportItems) totalSteps++;

            // إنشاء ورقة الفواتير
            if (exportInvoices)
            {
                var invoicesSheet = workbook.Worksheets.Add("فواتير المشتريات");
                await CreatePurchaseInvoicesSheet(invoicesSheet, purchases, selectedInvoiceFields, invoiceHeaders, clientId, warehouseId);

                currentStep++;
                progress?.Report((int)((double)currentStep / totalSteps * 100));
            }

            // إنشاء ورقة الأصناف
            if (exportItems)
            {
                var itemsSheet = workbook.Worksheets.Add("أصناف المشتريات");
                await CreatePurchaseItemsSheet(itemsSheet, items, selectedItemFields, itemHeaders, clientId, warehouseId);

                currentStep++;
                progress?.Report((int)((double)currentStep / totalSteps * 100));
            }

            // حفظ الملف
            workbook.SaveAs(filePath);
            progress?.Report(100);
        }

        /// <summary>
        /// إنشاء ورقة فواتير المشتريات
        /// </summary>
        private async Task CreatePurchaseInvoicesSheet(IXLWorksheet sheet, List<PurchaseViewModel> purchases,
            List<string> selectedFields, Dictionary<string, string> headers, int? clientId, int? warehouseId)
        {
            // تطبيق إعدادات عامة للورقة
            sheet.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center);
            sheet.Style.Font.SetFontName("Tahoma"); // خط أفضل للعربية
            sheet.Style.Font.SetFontSize(11);

            // تعيين اتجاه الكتابة من اليمين لليسار
            sheet.RightToLeft = true;

            // إعدادات إضافية لتحسين دعم RTL
            sheet.PageSetup.PageOrientation = XLPageOrientation.Portrait;
            sheet.PageSetup.PaperSize = XLPaperSize.A4Paper;

            // إضافة العنوان والمعلومات
            int startRow = await AddReportHeader(sheet, "تقرير فواتير المشتريات", clientId, warehouseId, selectedFields.Count);

            // إضافة رؤوس الأعمدة
            int col = 1;
            foreach (var field in selectedFields)
            {
                var header = headers.ContainsKey(field) ? headers[field] : field;
                sheet.Cell(startRow, col).Value = header;
                sheet.Cell(startRow, col).Style.Font.SetBold(true);
                sheet.Cell(startRow, col).Style.Font.SetFontName("Tahoma");
                sheet.Cell(startRow, col).Style.Font.SetFontSize(12);
                sheet.Cell(startRow, col).Style.Fill.SetBackgroundColor(XLColor.FromHtml("#134074"));
                sheet.Cell(startRow, col).Style.Font.FontColor = XLColor.White;
                sheet.Cell(startRow, col).Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                sheet.Cell(startRow, col).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                col++;
            }

            // إضافة البيانات
            int row = startRow + 1;
            int index = 1; // بداية الرقم التسلسلي
            foreach (var purchase in purchases)
            {
                col = 1;
                foreach (var field in selectedFields)
                {
                    var value = GetPurchaseFieldValue(purchase, field, index);
                    var cell = sheet.Cell(row, col);
                    cell.Value = XLCellValue.FromObject(value);
                    cell.Style.Font.SetFontName("Tahoma");
                    cell.Style.Font.SetFontSize(11);
                    cell.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                    cell.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                    col++;
                }
                row++;
                index++; // زيادة الرقم التسلسلي
            }

            // تنسيق الجدول
            var dataRange = sheet.Range(startRow, 1, row - 1, selectedFields.Count);
            dataRange.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);
            dataRange.Style.Border.SetInsideBorder(XLBorderStyleValues.Thin);

            // ضبط عرض الأعمدة
            sheet.Columns().AdjustToContents();
        }

        /// <summary>
        /// إنشاء ورقة أصناف المشتريات
        /// </summary>
        private async Task CreatePurchaseItemsSheet(IXLWorksheet sheet, List<PurchaseItemVM> items,
            List<string> selectedFields, Dictionary<string, string> headers, int? clientId, int? warehouseId)
        {
            // تطبيق إعدادات عامة للورقة
            sheet.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center);
            sheet.Style.Font.SetFontName("Tahoma"); // خط أفضل للعربية
            sheet.Style.Font.SetFontSize(11);

            // تعيين اتجاه الكتابة من اليمين لليسار
            sheet.RightToLeft = true;

            // إعدادات إضافية لتحسين دعم RTL
            sheet.PageSetup.PageOrientation = XLPageOrientation.Portrait;
            sheet.PageSetup.PaperSize = XLPaperSize.A4Paper;

            // إضافة العنوان والمعلومات
            int startRow = await AddReportHeader(sheet, "تقرير أصناف المشتريات", clientId, warehouseId, selectedFields.Count);

            // إضافة رؤوس الأعمدة
            int col = 1;
            foreach (var field in selectedFields)
            {
                var header = headers.ContainsKey(field) ? headers[field] : field;
                sheet.Cell(startRow, col).Value = header;
                sheet.Cell(startRow, col).Style.Font.SetBold(true);
                sheet.Cell(startRow, col).Style.Font.SetFontName("Tahoma");
                sheet.Cell(startRow, col).Style.Font.SetFontSize(12);
                sheet.Cell(startRow, col).Style.Fill.SetBackgroundColor(XLColor.FromHtml("#134074"));
                sheet.Cell(startRow, col).Style.Font.FontColor = XLColor.White;
                sheet.Cell(startRow, col).Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                sheet.Cell(startRow, col).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                col++;
            }

            // إضافة البيانات
            int row = startRow + 1;
            foreach (var item in items)
            {
                col = 1;
                foreach (var field in selectedFields)
                {
                    var value = GetPurchaseItemFieldValue(item, field);
                    var cell = sheet.Cell(row, col);
                    cell.Value = XLCellValue.FromObject(value);
                    cell.Style.Font.SetFontName("Tahoma");
                    cell.Style.Font.SetFontSize(11);
                    cell.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                    cell.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                    col++;
                }
                row++;
            }

            // تنسيق الجدول
            var dataRange = sheet.Range(startRow, 1, row - 1, selectedFields.Count);
            dataRange.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);
            dataRange.Style.Border.SetInsideBorder(XLBorderStyleValues.Thin);

            // ضبط عرض الأعمدة
            sheet.Columns().AdjustToContents();
        }

        /// <summary>
        /// الحصول على قيمة حقل المشتريات
        /// </summary>
        private object GetPurchaseFieldValue(PurchaseViewModel purchase, string fieldKey, int index = 0)
        {
            return fieldKey switch
            {
                "Index" => index, // استخدام الرقم التسلسلي المحسوب
                "InvoiceNumber" => purchase.InvoiceNumber ?? purchase.InvoiceNo.ToString(),
                "PurchaseDate" => purchase.PurchaseDate.ToString("yyyy-MM-dd"),
                "ClientName" => purchase.ClientName,
                "WarehouseName" => purchase.WarehouseName,
                "TotalBeforeDiscount" => 0, // يمكن إضافة هذا الحقل لاحقاً
                "TotalDiscount" => 0, // يمكن إضافة هذا الحقل لاحقاً
                "TotalAmount" => purchase.TotalAmount,
                "PaidAmount" => purchase.PaidAmount,
                "RemainingAmount" => purchase.RemainingAmount,
                _ => ""
            };
        }

        /// <summary>
        /// الحصول على قيمة حقل أصناف المشتريات
        /// </summary>
        private object GetPurchaseItemFieldValue(PurchaseItemVM item, string fieldKey)
        {
            return fieldKey switch
            {
                "PurchaseId" => item.PurchaseId,
                "ClientName" => item.ClientName ?? "",
                "WarehouseName" => item.WarehouseName ?? "",
                "Type" => item.Type,
                "Name" => item.Name,
                "ColorName" => item.ColorName ?? "",
                "Quantity" => item.Quantity,
                "Price" => item.Price,
                "TotalPrice" => item.TotalPrice,
                "Exp" => item.Exp?.ToString("yyyy-MM-dd") ?? "",
                _ => ""
            };
        }

        #endregion

        // Destructor
        /// <summary>
        /// تصدير المنتجات مع معاينة مفصلة
        /// </summary>
        public async Task ExportProductsWithPreviewAsync(
            string filePath,
            List<ProductExportVM> products,
            bool exportProducts,
            List<string> selectedProductFields,
            Dictionary<string, string> productHeaders,
            int? warehouseId = null,
            IProgress<int> progress = null)
        {
            using var workbook = new XLWorkbook();

            int totalSteps = 0;
            int currentStep = 0;

            // حساب إجمالي الخطوات
            if (exportProducts) totalSteps++;

            // إنشاء ورقة المنتجات
            if (exportProducts)
            {
                var productsSheet = workbook.Worksheets.Add("المنتجات");
                await CreateProductsDetailedSheet(productsSheet, products, selectedProductFields, productHeaders);

                currentStep++;
                progress?.Report((int)((double)currentStep / totalSteps * 100));
            }

            // حفظ الملف
            workbook.SaveAs(filePath);
            progress?.Report(100);
        }

        /// <summary>
        /// إنشاء ورقة المنتجات المفصلة مع تفاصيل الألوان والكميات والمخازن
        /// </summary>
        private async Task CreateProductsDetailedSheet(
            IXLWorksheet worksheet,
            List<ProductExportVM> products,
            List<string> selectedFields,
            Dictionary<string, string> headers)
        {
            // إعداد الرؤوس
            var fields = GetProductExportFields();
            int col = 1;
            var headerMapping = new Dictionary<string, int>();

            foreach (var fieldKey in selectedFields.Where(f => fields.ContainsKey(f)))
            {
                var headerText = headers.ContainsKey(fieldKey) && !string.IsNullOrEmpty(headers[fieldKey])
                    ? headers[fieldKey]
                    : fields[fieldKey].DisplayName;

                worksheet.Cell(1, col).Value = headerText;
                worksheet.Cell(1, col).Style.Font.Bold = true;
                worksheet.Cell(1, col).Style.Fill.BackgroundColor = XLColor.LightGray;
                worksheet.Cell(1, col).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                worksheet.Cell(1, col).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                headerMapping[fieldKey] = col;
                col++;
            }

            // إضافة البيانات
            int row = 2;
            foreach (var product in products)
            {
                foreach (var fieldKey in selectedFields.Where(f => fields.ContainsKey(f)))
                {
                    var cellValue = GetProductFieldValue(product, fieldKey);
                    var cell = worksheet.Cell(row, headerMapping[fieldKey]);

                    // تنسيق الخلية حسب نوع البيانات
                    if (fieldKey == "CostPrice" || fieldKey == "SellPrice")
                    {
                        if (decimal.TryParse(cellValue?.ToString(), out decimal decimalValue))
                        {
                            cell.Value = decimalValue;
                            cell.Style.NumberFormat.Format = "#,##0.000";
                        }
                    }
                    else if (fieldKey == "Quantity" || fieldKey == "MinimumQuantity")
                    {
                        if (int.TryParse(cellValue?.ToString(), out int intValue))
                        {
                            cell.Value = intValue;
                            cell.Style.NumberFormat.Format = "#,##0";
                        }
                    }
                    else if (fieldKey == "ExpiryDate")
                    {
                        cell.Value = product.ExpiryDateFormatted;
                    }
                    else
                    {
                        cell.Value = cellValue?.ToString() ?? "";
                    }

                    cell.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                    cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                }
                row++;
            }

            // تنسيق الأعمدة
            worksheet.Columns().AdjustToContents();

            // تجميد الصف الأول
            worksheet.SheetView.FreezeRows(1);
        }

        /// <summary>
        /// الحصول على قيمة الحقل للمنتج المفصل
        /// </summary>
        private object GetProductFieldValue(ProductExportVM product, string fieldKey)
        {
            return fieldKey switch
            {
                "Name" => product.Name,
                "ColorName" => product.ColorName,
                "ColorCode" => product.ColorCode,
                "Barcode" => product.Barcode,
                "CostPrice" => product.CostPrice,
                "SellPrice" => product.SellPrice,
                "Quantity" => product.Quantity,
                "WarehouseName" => product.WarehouseName,
                "ExpiryDate" => product.ExpiryDateFormatted,
                "MinimumQuantity" => product.MinimumQuantity,
                "HasColor" => product.HasColorText,
                "HasExpiry" => product.HasExpiryText,
                _ => ""
            };
        }

        /// <summary>
        /// إنشاء ورقة المنتجات
        /// </summary>
        private async Task CreateProductsSheet(IXLWorksheet sheet, List<ProductVM> products,
            List<string> selectedFields, Dictionary<string, string> headers, int? warehouseId)
        {
            // تطبيق إعدادات عامة للورقة
            sheet.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            sheet.Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center);
            sheet.Style.Font.SetFontName("Tahoma"); // خط أفضل للعربية
            sheet.Style.Font.SetFontSize(11);

            // تعيين اتجاه الكتابة من اليمين لليسار
            sheet.RightToLeft = true;

            // إعدادات إضافية لتحسين دعم RTL
            sheet.PageSetup.PageOrientation = XLPageOrientation.Portrait;
            sheet.PageSetup.PaperSize = XLPaperSize.A4Paper;

            // إضافة العنوان والمعلومات
            int startRow = await AddReportHeader(sheet, "تقرير المنتجات", null, warehouseId, selectedFields.Count);

            // إضافة رؤوس الأعمدة
            int col = 1;
            foreach (var field in selectedFields)
            {
                var header = headers.ContainsKey(field) ? headers[field] : field;
                sheet.Cell(startRow, col).Value = header;
                sheet.Cell(startRow, col).Style.Font.SetBold(true);
                sheet.Cell(startRow, col).Style.Font.SetFontName("Tahoma");
                sheet.Cell(startRow, col).Style.Font.SetFontSize(12);
                sheet.Cell(startRow, col).Style.Fill.SetBackgroundColor(XLColor.LightGray);
                sheet.Cell(startRow, col).Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                col++;
            }

            // إضافة بيانات المنتجات
            int row = startRow + 1;
            int index = 1;

            foreach (var product in products)
            {
                col = 1;
                foreach (var field in selectedFields)
                {
                    var cellValue = GetProductFieldValue(product, field, index);

                    // تحويل صريح للقيمة حسب نوعها
                    var cell = sheet.Cell(row, col);
                    switch (cellValue)
                    {
                        case string strValue:
                            cell.Value = strValue;
                            break;
                        case int intValue:
                            cell.Value = intValue;
                            break;
                        case decimal decValue:
                            cell.Value = (double)decValue;
                            break;
                        default:
                            cell.Value = cellValue?.ToString() ?? "";
                            break;
                    }

                    cell.Style.Font.SetFontName("Tahoma");
                    cell.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);

                    // تنسيق خاص للأرقام
                    if (field == "CostPrice" || field == "SellPrice")
                    {
                        cell.Style.NumberFormat.Format = "#,##0.000";
                    }

                    col++;
                }
                row++;
                index++;
            }

            // تطبيق تنسيق تلقائي للأعمدة
            sheet.Columns().AdjustToContents();

            // تحديد عرض أدنى وأقصى للأعمدة
            foreach (var column in sheet.Columns())
            {
                if (column.Width < 10) column.Width = 10;
                if (column.Width > 50) column.Width = 50;
            }
        }

        /// <summary>
        /// الحصول على قيمة حقل المنتج
        /// </summary>
        private object GetProductFieldValue(ProductVM product, string fieldKey, int index)
        {
            return fieldKey switch
            {
                "Index" => index,
                "Name" => product.Name ?? "",
                "CostPrice" => product.CostPrice,
                "SellPrice" => product.SellPrice,
                "TotalQuantity" => product.TotalQuantity,
                "MinimumQuantity" => product.MinimumQuantity,
                "HasColor" => product.Color ? "نعم" : "لا",
                "HasExpiry" => product.Exp ? "نعم" : "لا",
                _ => ""
            };
        }

        /// <summary>
        /// تصدير العدسات مع معاينة مفصلة - طريقة جديدة تدعم المعاينة
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="lensesData">بيانات العدسات المسطحة</param>
        /// <param name="selectedFields">الحقول المختارة</param>
        /// <param name="customHeaders">رؤوس الأعمدة المخصصة</param>
        /// <param name="warehouseId">معرف المخزن</param>
        /// <param name="progress">مؤشر التقدم</param>
        public async Task ExportLensesWithPreviewAsync(
            string filePath,
            List<LensExportItemVM> lensesData,
            List<string> selectedFields,
            Dictionary<string, string> customHeaders,
            int? warehouseId = null,
            IProgress<int> progress = null)
        {
            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add("العدسات");

            // تطبيق إعدادات عامة للورقة مع دعم RTL
            worksheet.RightToLeft = true;
            worksheet.Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            worksheet.Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center);
            worksheet.Style.Font.SetFontName("Tahoma");
            worksheet.Style.Font.SetFontSize(11);

            // إضافة رأس التقرير
            int startRow = await AddReportHeader(worksheet, "تقرير العدسات", null, warehouseId, selectedFields.Count);

            // إضافة رؤوس الأعمدة
            int col = 1;
            var headerMapping = new Dictionary<string, int>();

            foreach (var field in selectedFields)
            {
                var header = customHeaders.ContainsKey(field) ? customHeaders[field] : field;
                worksheet.Cell(startRow, col).Value = header;
                worksheet.Cell(startRow, col).Style.Font.SetBold(true);
                worksheet.Cell(startRow, col).Style.Font.SetFontName("Tahoma");
                worksheet.Cell(startRow, col).Style.Font.SetFontSize(12);
                worksheet.Cell(startRow, col).Style.Fill.SetBackgroundColor(XLColor.LightGray);
                worksheet.Cell(startRow, col).Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                headerMapping.Add(field, col);
                col++;
            }

            // إضافة بيانات العدسات
            int row = startRow + 1;
            int totalItems = lensesData.Count;
            int processedItems = 0;

            foreach (var lensItem in lensesData)
            {
                foreach (var field in selectedFields)
                {
                    if (headerMapping.TryGetValue(field, out int colIndex))
                    {
                        var cellValue = GetLensFieldValue(lensItem, field);

                        // تحويل صريح للقيمة حسب نوعها
                        var cell = worksheet.Cell(row, colIndex);
                        switch (cellValue)
                        {
                            case string strValue:
                                cell.Value = strValue;
                                break;
                            case int intValue:
                                cell.Value = intValue;
                                break;
                            case decimal decValue:
                                cell.Value = (double)decValue;
                                break;
                            case DateOnly dateValue:
                                cell.Value = dateValue.ToString("yyyy-MM-dd");
                                break;
                            default:
                                cell.Value = cellValue?.ToString() ?? "";
                                break;
                        }

                        cell.Style.Font.SetFontName("Tahoma");
                        cell.Style.Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                    }
                }

                row++;
                processedItems++;

                // تحديث مؤشر التقدم
                if (progress != null && totalItems > 0)
                {
                    int progressPercentage = (int)((double)processedItems / totalItems * 90); // 90% للبيانات
                    progress.Report(progressPercentage);
                }
            }

            // تطبيق تنسيق تلقائي للأعمدة
            worksheet.Columns().AdjustToContents();

            // تحديد عرض أدنى وأقصى للأعمدة
            foreach (var column in worksheet.Columns())
            {
                if (column.Width < 10) column.Width = 10;
                if (column.Width > 50) column.Width = 50;
            }

            // حفظ الملف
            progress?.Report(95);
            workbook.SaveAs(filePath);
            progress?.Report(100);
        }

        /// <summary>
        /// الحصول على قيمة حقل العدسة من النموذج المسطح
        /// </summary>
        private object GetLensFieldValue(LensExportItemVM lensItem, string fieldKey)
        {
            return fieldKey switch
            {
                "Name" => lensItem.Name,
                "BC" => lensItem.BC,
                "DIA" => lensItem.Dia,
                "ADD" => lensItem.Addtion,
                "AXIS" => lensItem.Axis,
                "SPH" => lensItem.SphereValue,
                "CYL" => lensItem.CylinderValue,
                "POW" => lensItem.PowerValue,
                "HexCode" => lensItem.ColorHexCode,
                "ColorName" => lensItem.ColorName,
                "Barcode" => lensItem.Barcode,
                "Cost" => lensItem.CostPrice,
                "Price" => lensItem.SellPrice,
                "Quantity" => lensItem.Quantity,
                "Expiration" => lensItem.Expiration,
                "Category" => lensItem.CategoryName,
                "Warehouse" => lensItem.WarehouseName,
                _ => ""
            };
        }

        ~ExportService()
        {
            Dispose(false);
        }
    }
}
