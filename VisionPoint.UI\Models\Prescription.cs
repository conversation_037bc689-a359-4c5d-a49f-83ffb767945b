﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VisionPoint.UI.Models;

public class Prescription
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public short Id { get; set; }
    [Precision(5, 2)] public decimal Value { get; set; }

    /// <summary>
    /// Row GUID for SQL Server Merge Replication - managed by database
    /// </summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid RowGuid { get; set; }
}
