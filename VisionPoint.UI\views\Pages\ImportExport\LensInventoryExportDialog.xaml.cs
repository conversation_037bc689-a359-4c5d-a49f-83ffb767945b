using Microsoft.EntityFrameworkCore;
using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.ImportExport
{
    public partial class LensInventoryExportDialog : Window
    {
        private readonly LensService _lensService;
        private readonly WarehouseService _warehouseService;
        private readonly LensVM _selectedLens;
        private List<Prescription> _allPrescriptions;
        private int? _selectedWarehouseId;
        private string _selectedWarehouseName;

        public LensInventoryExportDialog(LensVM selectedLens)
        {
            InitializeComponent();
            _lensService = new LensService();
            _warehouseService = new WarehouseService();
            _selectedLens = selectedLens;
            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                // اختيار المخزن أولاً
                await SelectWarehouse();

                // عرض معلومات العدسة المحددة
                txtLensInfo.Text = $"العدسة المحددة: {_selectedLens.Name}";

                // تحميل جميع قيم Prescription من -35 إلى +35
                var allPrescriptions = await _lensService.GetAllPrescriptionsAsync();
                _allPrescriptions = allPrescriptions
                    .Where(p => p.Value >= -35m && p.Value <= 35m)
                    .OrderBy(p => p.Value)
                    .ToList();

                // تعبئة ComboBoxes بقيم Prescription
                await PopulatePrescriptionComboBoxes();

                // إظهار/إخفاء أنواع الوصفات حسب العدسة المحددة وتحديدها تلقائياً
                await SetupPrescriptionTypes();

                // ربط أحداث تغيير ComboBoxes
                cmbSphereFrom.SelectionChanged += async (s, e) => await UpdatePreview();
                cmbSphereTo.SelectionChanged += async (s, e) => await UpdatePreview();
                cmbCylinderFrom.SelectionChanged += async (s, e) => await UpdatePreview();
                cmbCylinderTo.SelectionChanged += async (s, e) => await UpdatePreview();
                cmbPowerFrom.SelectionChanged += async (s, e) => await UpdatePreview();
                cmbPowerTo.SelectionChanged += async (s, e) => await UpdatePreview();

                // تحديث المعاينة الأولية
                await UpdatePreview();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", true);
            }
        }

        private async Task SelectWarehouse()
        {
            try
            {
                // عرض نافذة اختيار المخزن
                var warehouseSelectionWindow = new WarehouseSelectionWindow();
                warehouseSelectionWindow.ShowDialog();

                if (warehouseSelectionWindow.DialogResult)
                {
                    _selectedWarehouseId = warehouseSelectionWindow.IsAllWarehousesSelected
                        ? null
                        : warehouseSelectionWindow.SelectedWarehouseId;

                    // تحديد اسم المخزن للعرض
                    if (_selectedWarehouseId.HasValue)
                    {
                        var warehouse = await _warehouseService.GetWarehouseByIdAsync(_selectedWarehouseId.Value);
                        _selectedWarehouseName = warehouse?.Name ?? "غير محدد";
                    }
                    else
                    {
                        _selectedWarehouseName = "جميع المخازن";
                    }

                    // تحديث عنوان النافذة لإظهار المخزن المختار
                    this.Title = $"تصدير جرد العدسات - {_selectedWarehouseName}";
                }
                else
                {
                    // إذا تم إلغاء اختيار المخزن، إغلاق النافذة
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"خطأ في اختيار المخزن: {ex.Message}", "خطأ", true);
                this.Close();
            }
        }

        private async Task PopulatePrescriptionComboBoxes()
        {
            // تعبئة جميع ComboBoxes بنفس القيم
            cmbSphereFrom.ItemsSource = _allPrescriptions;
            cmbSphereTo.ItemsSource = _allPrescriptions;
            cmbCylinderFrom.ItemsSource = _allPrescriptions;
            cmbCylinderTo.ItemsSource = _allPrescriptions;
            cmbPowerFrom.ItemsSource = _allPrescriptions;
            cmbPowerTo.ItemsSource = _allPrescriptions;

            // تعيين القيم الافتراضية (-35 إلى +35)
            var minPrescription = _allPrescriptions.FirstOrDefault(p => p.Value == -35m);
            var maxPrescription = _allPrescriptions.FirstOrDefault(p => p.Value == 35m);

            if (minPrescription != null && maxPrescription != null)
            {
                cmbSphereFrom.SelectedItem = minPrescription;
                cmbSphereTo.SelectedItem = maxPrescription;
                cmbCylinderFrom.SelectedItem = minPrescription;
                cmbCylinderTo.SelectedItem = maxPrescription;
                cmbPowerFrom.SelectedItem = minPrescription;
                cmbPowerTo.SelectedItem = maxPrescription;
            }
        }

        private async Task SetupPrescriptionTypes()
        {
            // إظهار/إخفاء أنواع الوصفات حسب العدسة المحددة
            if (_selectedLens.Sphere)
            {
                lblSphere.Visibility = Visibility.Visible;
                cmbSphereFrom.Visibility = Visibility.Visible;
                cmbSphereTo.Visibility = Visibility.Visible;
            }

            if (_selectedLens.Cylinder)
            {
                lblCylinder.Visibility = Visibility.Visible;
                cmbCylinderFrom.Visibility = Visibility.Visible;
                cmbCylinderTo.Visibility = Visibility.Visible;
            }

            if (_selectedLens.Power)
            {
                lblPower.Visibility = Visibility.Visible;
                cmbPowerFrom.Visibility = Visibility.Visible;
                cmbPowerTo.Visibility = Visibility.Visible;
            }

            // تحديد عدد الأنواع المتاحة
            int availableTypes = (_selectedLens.Sphere ? 1 : 0) + (_selectedLens.Cylinder ? 1 : 0) + (_selectedLens.Power ? 1 : 0);

            if (availableTypes == 0)
            {
                ErrorBox.Show("هذه العدسة لا تحتوي على أي أنواع وصفات متاحة للتصدير", "تنبيه", true);
                Close();
                return;
            }
        }

        private async Task UpdatePreview()
        {
            try
            {
                // تحديد الأنواع المتاحة للعدسة
                bool hasSphere = _selectedLens.Sphere;
                bool hasCylinder = _selectedLens.Cylinder;
                bool hasPower = _selectedLens.Power;

                if (!hasSphere && !hasCylinder && !hasPower)
                {
                    txtPreview.Text = "يجب اختيار نوع واحد على الأقل من الوصفات المتاحة";
                    btnExport.IsEnabled = false;
                    return;
                }

                // إنشاء معاينة
                var preview = new StringBuilder();
                preview.AppendLine($"العدسة: {_selectedLens.Name}");
                preview.AppendLine($"النوع: {_selectedLens.CategoryName ?? "غير محدد"}");
                preview.AppendLine($"المخزن: {_selectedWarehouseName ?? "غير محدد"}");
                preview.AppendLine($"نطاق القيم: من -35 إلى +35");
                preview.AppendLine();

                int typesCount = (hasSphere ? 1 : 0) + (hasCylinder ? 1 : 0) + (hasPower ? 1 : 0);

                if (typesCount == 1)
                {
                    preview.AppendLine("سيتم إنشاء جدول عمودي:");
                    if (hasSphere) preview.AppendLine("- جدول SPH (عمودي)");
                    if (hasCylinder) preview.AppendLine("- جدول CYL (عمودي)");
                    if (hasPower) preview.AppendLine("- جدول POW (عمودي)");
                }
                else if (typesCount == 2)
                {
                    preview.AppendLine("سيتم إنشاء جدول أفقي:");
                    if (hasSphere && hasCylinder) preview.AppendLine("- جدول SPH/CYL (أفقي)");
                    if (hasSphere && hasPower) preview.AppendLine("- جدول SPH/POW (أفقي)");
                    if (hasCylinder && hasPower) preview.AppendLine("- جدول CYL/POW (أفقي)");

                    // حساب عدد الأوراق المتوقعة بناءً على النطاقات المحددة
                    int expectedSheets = CalculateExpectedSheets(hasSphere, hasCylinder, hasPower);

                    if (expectedSheets > 1)
                    {
                        preview.AppendLine($"- عدد الأوراق المتوقعة: {expectedSheets} ورقة");
                    }
                }
                else if (typesCount == 3)
                {
                    preview.AppendLine("سيتم إنشاء ثلاث أوراق منفصلة:");
                    preview.AppendLine("- ورقة SPH/CYL");
                    preview.AppendLine("- ورقة SPH/POW");
                    preview.AppendLine("- ورقة CYL/POW");
                }

                preview.AppendLine();
                preview.AppendLine("النطاقات المحددة:");

                if (hasSphere)
                {
                    string sphereRange = GetRangeTextFromComboBox(cmbSphereFrom, cmbSphereTo);
                    preview.AppendLine($"- SPH: {sphereRange}");
                }

                if (hasCylinder)
                {
                    string cylinderRange = GetRangeTextFromComboBox(cmbCylinderFrom, cmbCylinderTo);
                    preview.AppendLine($"- CYL: {cylinderRange}");
                }

                if (hasPower)
                {
                    string powerRange = GetRangeTextFromComboBox(cmbPowerFrom, cmbPowerTo);
                    preview.AppendLine($"- POW: {powerRange}");
                }

                txtPreview.Text = preview.ToString();
                btnExport.IsEnabled = true;
            }
            catch (Exception ex)
            {
                txtPreview.Text = $"خطأ في المعاينة: {ex.Message}";
                btnExport.IsEnabled = false;
            }
        }

        private int CalculateExpectedSheets(bool hasSphere, bool hasCylinder, bool hasPower)
        {
            try
            {
                // تحديد أنواع الوصفات المستخدمة
                var activeTypes = new List<string>();
                if (hasSphere) activeTypes.Add("SPH");
                if (hasCylinder) activeTypes.Add("CYL");
                if (hasPower) activeTypes.Add("POW");

                if (activeTypes.Count != 2)
                    return 1; // للحالات الأخرى (نوع واحد أو ثلاثة أنواع)

                // حساب عدد القيم لكل نوع بناءً على النطاق المحدد
                int rowCount = 0;
                int colCount = 0;

                if (activeTypes.Contains("SPH"))
                {
                    int sphereCount = GetRangeCount(cmbSphereFrom, cmbSphereTo);
                    if (rowCount == 0) rowCount = sphereCount;
                    else colCount = sphereCount;
                }

                if (activeTypes.Contains("CYL"))
                {
                    int cylinderCount = GetRangeCount(cmbCylinderFrom, cmbCylinderTo);
                    if (rowCount == 0) rowCount = cylinderCount;
                    else colCount = cylinderCount;
                }

                if (activeTypes.Contains("POW"))
                {
                    int powerCount = GetRangeCount(cmbPowerFrom, cmbPowerTo);
                    if (rowCount == 0) rowCount = powerCount;
                    else colCount = powerCount;
                }

                // حساب عدد الأوراق المطلوبة
                int maxRowsPerSheet = 25;
                int maxColsPerSheet = 15;

                int rowSheets = (int)Math.Ceiling((double)rowCount / maxRowsPerSheet);
                int colSheets = (int)Math.Ceiling((double)colCount / maxColsPerSheet);

                return rowSheets * colSheets;
            }
            catch
            {
                return 1; // في حالة حدوث خطأ، إرجاع ورقة واحدة
            }
        }

        private int GetRangeCount(ComboBox fromCombo, ComboBox toCombo)
        {
            var fromPrescription = fromCombo.SelectedItem as Prescription;
            var toPrescription = toCombo.SelectedItem as Prescription;

            // إذا لم يتم تحديد أي قيمة، استخدم النطاق الكامل
            if (fromPrescription == null && toPrescription == null)
                return 71; // من -35 إلى +35

            // إذا تم تحديد القيمتين
            if (fromPrescription != null && toPrescription != null)
            {
                // البحث عن فهارس القيم في القائمة
                int fromIndex = _allPrescriptions.FindIndex(p => p.Value == fromPrescription.Value);
                int toIndex = _allPrescriptions.FindIndex(p => p.Value == toPrescription.Value);

                if (fromIndex >= 0 && toIndex >= 0)
                    return Math.Abs(toIndex - fromIndex) + 1;
            }

            // إذا تم تحديد قيمة واحدة فقط، استخدم النطاق الكامل
            return 71;
        }

        private string GetRangeTextFromComboBox(ComboBox fromCombo, ComboBox toCombo)
        {
            var fromPrescription = fromCombo.SelectedItem as Prescription;
            var toPrescription = toCombo.SelectedItem as Prescription;

            if (fromPrescription == null && toPrescription == null)
                return "جميع القيم";

            if (fromPrescription != null && toPrescription != null)
                return $"من {fromPrescription.Value:F2} إلى {toPrescription.Value:F2}";

            if (fromPrescription != null)
                return $"من {fromPrescription.Value:F2} فما فوق";

            if (toPrescription != null)
                return $"حتى {toPrescription.Value:F2}";

            return "جميع القيم";
        }

        private string GetRangeText(string fromText, string toText)
        {
            if (string.IsNullOrWhiteSpace(fromText) && string.IsNullOrWhiteSpace(toText))
                return "جميع القيم";

            if (!string.IsNullOrWhiteSpace(fromText) && !string.IsNullOrWhiteSpace(toText))
                return $"من {fromText} إلى {toText}";

            if (!string.IsNullOrWhiteSpace(fromText))
                return $"من {fromText} فما فوق";

            if (!string.IsNullOrWhiteSpace(toText))
                return $"حتى {toText}";

            return "جميع القيم";
        }

        private async void btnExport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInputs())
                    return;

                // عرض نافذة حفظ الملف
                string safeLensName = _selectedLens.Name.Replace(" ", "_").Replace("/", "-").Replace("\\", "-");
                SaveFileDialog saveFileDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx",
                    FileName = $"جرد_{safeLensName}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx",
                    Title = "حفظ تقرير جرد العدسات"
                };

                if (saveFileDialog.ShowDialog() != true)
                    return;

                // جمع البيانات من ComboBoxes
                decimal? sphereFrom = (cmbSphereFrom.SelectedItem as Prescription)?.Value;
                decimal? sphereTo = (cmbSphereTo.SelectedItem as Prescription)?.Value;
                decimal? cylinderFrom = (cmbCylinderFrom.SelectedItem as Prescription)?.Value;
                decimal? cylinderTo = (cmbCylinderTo.SelectedItem as Prescription)?.Value;
                decimal? powerFrom = (cmbPowerFrom.SelectedItem as Prescription)?.Value;
                decimal? powerTo = (cmbPowerTo.SelectedItem as Prescription)?.Value;

                // إنشاء خدمة التصدير
                var exportService = new ExportService();

                // إنشاء وعرض نافذة التصدير مع Progress مثل التصدير العادي
                var exporterPage = new LensExporterPage(exportService, new List<string>(), saveFileDialog.FileName,
                    isInventoryExport: true, selectedLens: _selectedLens,
                    sphereFrom: sphereFrom, sphereTo: sphereTo,
                    cylinderFrom: cylinderFrom, cylinderTo: cylinderTo,
                    powerFrom: powerFrom, powerTo: powerTo, warehouseId: _selectedWarehouseId);

                exporterPage.ShowDialog();

                // إغلاق النافذة الحالية بعد انتهاء التصدير
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", true);
            }
        }

        private bool ValidateInputs()
        {
            // التحقق من وجود أنواع متاحة للعدسة
            if (!_selectedLens.Sphere && !_selectedLens.Cylinder && !_selectedLens.Power)
            {
                ErrorBox.Show("هذه العدسة لا تحتوي على أي أنواع وصفات متاحة للتصدير", "خطأ في البيانات", true);
                return false;
            }

            // التحقق من صحة النطاقات
            if (!ValidateRangeFromComboBox(cmbSphereFrom, cmbSphereTo, "SPH"))
                return false;

            if (!ValidateRangeFromComboBox(cmbCylinderFrom, cmbCylinderTo, "CYL"))
                return false;

            if (!ValidateRangeFromComboBox(cmbPowerFrom, cmbPowerTo, "POW"))
                return false;

            return true;
        }

        private bool ValidateRangeFromComboBox(ComboBox fromCombo, ComboBox toCombo, string fieldName)
        {
            var fromPrescription = fromCombo.SelectedItem as Prescription;
            var toPrescription = toCombo.SelectedItem as Prescription;

            if (fromPrescription != null && toPrescription != null && fromPrescription.Value > toPrescription.Value)
            {
                ErrorBox.Show($"القيمة 'من' يجب أن تكون أقل من أو تساوي القيمة 'إلى' في حقل {fieldName}", "خطأ في البيانات", true);
                return false;
            }

            return true;
        }

        private bool ValidateRange(string fromText, string toText, string fieldName)
        {
            decimal? from = ParseDecimal(fromText);
            decimal? to = ParseDecimal(toText);

            if (from.HasValue && to.HasValue && from > to)
            {
                ErrorBox.Show($"القيمة 'من' يجب أن تكون أقل من أو تساوي القيمة 'إلى' في حقل {fieldName}", "خطأ في البيانات", true);
                return false;
            }

            return true;
        }

        private decimal? ParseDecimal(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return null;

            if (decimal.TryParse(text, out decimal result))
                return result;

            return null;
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DialogResult = false;
                Close();
            }
            catch (InvalidOperationException)
            {
                // إذا لم تكن النافذة مفتوحة كـ Dialog، فقط أغلقها
                Close();
            }
        }
    }
}