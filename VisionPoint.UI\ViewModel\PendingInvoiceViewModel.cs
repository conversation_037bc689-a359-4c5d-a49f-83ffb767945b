using System;

namespace VisionPoint.UI.ViewModels
{
    /// <summary>
    /// ViewModel للفواتير المتبقية (التي بها بواقي)
    /// يستخدم لتحسين الأداء عند جلب بيانات الفواتير المتبقية فقط
    /// </summary>
    public class PendingInvoiceViewModel
    {
        /// <summary>
        /// معرف الفاتورة
        /// </summary>
        public int InvoiceId { get; set; }

        /// <summary>
        /// رقم الفاتورة
        /// </summary>
        public int InvoiceNo { get; set; }

        /// <summary>
        /// إجمالي مبلغ الفاتورة
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// المبلغ المدفوع
        /// </summary>
        public decimal PaidAmount { get; set; }

        /// <summary>
        /// المبلغ المتبقي
        /// </summary>
        public decimal RemainingAmount { get; set; }

        /// <summary>
        /// تاريخ الفاتورة
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// هل هي فاتورة مبيعات (true) أم مشتريات (false)
        /// </summary>
        public bool IsSale { get; set; }

        /// <summary>
        /// نوع الفاتورة كنص
        /// </summary>
        public string InvoiceType => IsSale ? "مبيعات" : "مشتريات";

        /// <summary>
        /// نسبة المبلغ المدفوع من إجمالي الفاتورة
        /// </summary>
        public decimal PaymentPercentage => TotalAmount > 0 ? (PaidAmount / TotalAmount) * 100 : 0;

        /// <summary>
        /// نسبة المبلغ المتبقي من إجمالي الفاتورة
        /// </summary>
        public decimal RemainingPercentage => TotalAmount > 0 ? (RemainingAmount / TotalAmount) * 100 : 0;

        /// <summary>
        /// هل الفاتورة مدفوعة بالكامل
        /// </summary>
        public bool IsFullyPaid => RemainingAmount <= 0;

        /// <summary>
        /// هل الفاتورة مدفوعة جزئياً
        /// </summary>
        public bool IsPartiallyPaid => PaidAmount > 0 && RemainingAmount > 0;

        /// <summary>
        /// تمثيل نصي للفاتورة
        /// </summary>
        public override string ToString()
        {
            return $"فاتورة {InvoiceType} رقم {InvoiceNo} - المتبقي: {RemainingAmount:N2}";
        }
    }
}
