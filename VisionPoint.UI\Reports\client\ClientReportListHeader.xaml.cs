﻿using System.Windows;
using System.Windows.Controls;

namespace VisionPoint.UI.Reports.client
{
    /// <summary>
    /// Interaction logic for ClientReportListHeader.xaml
    /// </summary>
    public partial class ClientReportListHeader : Page
    {
        public ClientReportListHeader()
        {
            InitializeComponent();
        }
        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (sender is ListView listView && listView.View is GridView gView)
            {
                var workingWidth = listView.ActualWidth - SystemParameters.VerticalScrollBarWidth;

                var columnWidths = new double[] { 0.15, 0.15, 0.2, 0.2, 0.1, 0.1, 0.1 };
                for (int i = 0; i < gView.Columns.Count - 1; i++)
                {
                    gView.Columns[i].Width = workingWidth * columnWidths[i];
                }
            }
        }
    }
}
