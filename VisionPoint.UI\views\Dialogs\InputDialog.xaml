﻿<Window
    x:Class="VisionPoint.UI.views.Dialogs.InputDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:VisionPoint.UI"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Dialogs"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils"
    Title="إدخال قيمة"
    AllowsTransparency="True"
    Background="Transparent"
    FlowDirection="RightToLeft"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">
        <Grid Width="1920" Height="1080">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="3*" />
                <ColumnDefinition Width="2*" />
                <ColumnDefinition Width="3*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="3*" />
                <RowDefinition Height="2*" />
                <RowDefinition Height="3*" />
            </Grid.RowDefinitions>

            <!--  Your 480x270 centered content  -->
            <Border
                Grid.Row="1"
                Grid.Column="1"
                Background="{StaticResource backgroundColor}"
                CornerRadius="24">

                <Grid Margin="16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition />
                        <ColumnDefinition />
                        <ColumnDefinition />
                        <ColumnDefinition />
                        <ColumnDefinition />
                        <ColumnDefinition />
                    </Grid.ColumnDefinitions>

                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>


                    <Border
                        x:Name="btnclose"
                        Grid.Column="10"
                        Width="24"
                        Height="24"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Top"
                        Background="Red"
                        CornerRadius="50"
                        MouseLeftButtonDown="btnclose_MouseLeftButtonDown" />

                    <TextBox
                        x:Name="txtAnswer"
                        Grid.Row="1"
                        Grid.ColumnSpan="6"
                        Height="60"
                        VerticalContentAlignment="Center"
                        utils:NumericInputControl.IsNumericOnly="True"
                        FontSize="16"
                        Tag="أدخل القيمة" />


                    <Border
                        x:Name="btnOk"
                        Grid.Row="2"
                        Grid.Column="1"
                        Grid.ColumnSpan="2"
                        Margin="8,16"
                        Background="{StaticResource PrimaryColor}"
                        CornerRadius="8"
                        MouseLeftButtonDown="btnOk_Click">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Foreground="White"
                            Text="موافق" />
                    </Border>
                    <Border
                        x:Name="btnCancel"
                        Grid.Row="2"
                        Grid.Column="3"
                        Grid.ColumnSpan="2"
                        Margin="8,16"
                        Background="Transparent"
                        BorderBrush="{StaticResource errorColor}"
                        BorderThickness="1"
                        CornerRadius="8"
                        MouseLeftButtonDown="btnCancel_Click">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Foreground="{StaticResource errorColor}"
                            Text="إلغاء" />
                    </Border>
                </Grid>
            </Border>
        </Grid>
    </Viewbox>
</Window>

