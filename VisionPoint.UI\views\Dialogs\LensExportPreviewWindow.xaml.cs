using Microsoft.Win32;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using VisionPoint.UI.Helper;
using VisionPoint.UI.Models;
using static VisionPoint.UI.Helper.ExportPreferencesHelper;
using VisionPoint.UI.PL;
using VisionPoint.UI.ViewModel;

namespace VisionPoint.UI.views.Dialogs
{
    /// <summary>
    /// نافذة معاينة تصدير العدسات
    /// </summary>
    public partial class LensExportPreviewWindow : Window
    {
        private readonly ExportPreviewModel _model;
        private readonly LensService _lensService;
        private readonly ExportService _exportService;
        private string _selectedSavePath = string.Empty;
        private bool _isExporting = false;

        public bool ExportCompleted { get; private set; } = false;

        public LensExportPreviewWindow(List<LensExportItemVM> lensesData, int? warehouseId = null)
        {
            InitializeComponent();

            _lensService = new LensService();
            _exportService = new ExportService();
            _model = new ExportPreviewModel
            {
                LensesData = lensesData,
                WarehouseId = warehouseId,
                FileName = $"تقرير_العدسات_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
            };

            DataContext = _model;

            // تهيئة النافذة
            InitializeWindow();
        }

        private async void InitializeWindow()
        {
            try
            {
                // تهيئة حقول التصدير
                InitializeExportFields();

                // تطبيق التفضيلات المحفوظة للعدسات
                ExportPreferencesHelper.ApplyPreferences(_model, ExportType.Lenses);

                // تحديث المعاينة
                UpdatePreview();

                // تحديث الملخص
                UpdateSummary();

                // تحديث حالة التبويبات
                UpdateTabsState();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تهيئة النافذة: {ex.Message}", "خطأ", true);
            }
        }

        private void InitializeExportFields()
        {
            // تهيئة حقول العدسات باستخدام نفس الحقول الموجودة في ExportService
            var lensFields = _exportService.GetLensExportFields();
            _model.ItemFields.Clear();

            foreach (var field in lensFields)
            {
                _model.ItemFields.Add(new ExportFieldOption
                {
                    FieldKey = field.Key,
                    DefaultName = field.Value.DisplayName,
                    CustomHeader = field.Value.DisplayName, // استخدام نفس الاسم كافتراضي
                    IsSelected = field.Value.IsRequired,
                    IsRequired = field.Value.IsRequired,
                    Description = field.Value.DisplayName
                });
            }

            // ربط الأحداث لتحديث المعاينة عند تغيير الحقول
            foreach (var field in _model.ItemFields)
            {
                field.PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(ExportFieldOption.IsSelected) ||
                        e.PropertyName == nameof(ExportFieldOption.CustomHeader))
                        UpdateLensesPreview();
                };
            }
        }

        private void UpdatePreview()
        {
            // تحديث معاينة العدسات
            UpdateLensesPreview();
        }

        private void UpdateLensesPreview()
        {
            try
            {
                // تحديث عدد العدسات في المعاينة
                txtLensesPreviewCount.Text = $"عدد العدسات: {_model.LensesData.Count}";

                // تحديث أعمدة DataGrid بناءً على الحقول المختارة
                dgLensesPreview.Columns.Clear();

                var selectedFields = _model.ItemFields.Where(f => f.IsSelected).ToList();
                foreach (var field in selectedFields)
                {
                    var column = new DataGridTextColumn
                    {
                        Header = field.CustomHeader,
                        Width = GetColumnWidth(field.FieldKey)
                    };

                    // تحديد Binding بناءً على نوع الحقل
                    column.Binding = GetColumnBinding(field.FieldKey);

                    dgLensesPreview.Columns.Add(column);
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحديث معاينة العدسات: {ex.Message}", "خطأ", true);
            }
        }

        private Binding GetColumnBinding(string fieldKey)
        {
            var binding = new Binding();

            switch (fieldKey)
            {
                case "Name":
                    binding.Path = new PropertyPath("Name");
                    break;
                case "BC":
                    binding.Path = new PropertyPath("BC");
                    binding.StringFormat = "{0:F3}";
                    break;
                case "DIA":
                    binding.Path = new PropertyPath("DIA");
                    break;
                case "ADD":
                    binding.Path = new PropertyPath("ADD");
                    break;
                case "AXIS":
                    binding.Path = new PropertyPath("AXIS");
                    break;
                case "SPH":
                    binding.Path = new PropertyPath("SPH");
                    break;
                case "CYL":
                    binding.Path = new PropertyPath("CYL");
                    break;
                case "POW":
                    binding.Path = new PropertyPath("POW");
                    break;
                case "HexCode":
                    binding.Path = new PropertyPath("HexCode");
                    break;
                case "ColorName":
                    binding.Path = new PropertyPath("ColorName");
                    break;
                case "Barcode":
                    binding.Path = new PropertyPath("Barcode");
                    break;
                case "Cost":
                    binding.Path = new PropertyPath("Cost");
                    break;
                case "Price":
                    binding.Path = new PropertyPath("Price");
                    break;
                case "Quantity":
                    binding.Path = new PropertyPath("Quantity");
                    break;
                case "Expiration":
                    binding.Path = new PropertyPath("Expiration");
                    binding.StringFormat = "{0:yyyy-MM-dd}";
                    break;
                case "Category":
                    binding.Path = new PropertyPath("Category");
                    break;
                case "Warehouse":
                    binding.Path = new PropertyPath("Warehouse");
                    break;
                default:
                    binding.Path = new PropertyPath("Name");
                    break;
            }

            return binding;
        }

        private DataGridLength GetColumnWidth(string fieldKey)
        {
            return fieldKey switch
            {
                "Name" => new DataGridLength(1, DataGridLengthUnitType.Star),
                "Category" or "ColorName" or "Warehouse" => new DataGridLength(150),
                "BC" or "DIA" or "ADD" or "AXIS" or "SPH" or "CYL" or "POW" => new DataGridLength(80),
                "Cost" or "Price" => new DataGridLength(100),
                "Quantity" => new DataGridLength(80),
                "Barcode" => new DataGridLength(120),
                "HexCode" => new DataGridLength(100),
                "Expiration" => new DataGridLength(120),
                _ => new DataGridLength(100)
            };
        }

        private void UpdateSummary()
        {
            try
            {
                var totalLenses = _model.LensesData.Count;
                txtSummary.Text = $"إجمالي العدسات: {totalLenses}";
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحديث الملخص: {ex.Message}", "خطأ", true);
            }
        }

        private void UpdateTabsState()
        {
            // تحديث حالة التبويبات بناءً على البيانات المتاحة
            var hasLenses = _model.LensesData.Any();

            // تمكين/تعطيل التبويبات
            foreach (TabItem tab in tabControl.Items)
            {
                if (tab.Header.ToString() == "معاينة البيانات")
                {
                    tab.IsEnabled = hasLenses;
                }
            }
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // تحديث المعاينة عند تحميل النافذة
            UpdatePreview();
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnExportNow.IsEnabled = false;
            btnCancel.IsEnabled = false;
            btnSelectAllLensFields.IsEnabled = false;
            btnDeselectAllLensFields.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnExportNow.IsEnabled = true;
            btnCancel.IsEnabled = true;
            btnSelectAllLensFields.IsEnabled = true;
            btnDeselectAllLensFields.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private void btnSelectAllLensFields_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                foreach (var field in _model.ItemFields.Where(f => !f.IsRequired))
                {
                    field.IsSelected = true;
                }
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnDeselectAllLensFields_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                foreach (var field in _model.ItemFields.Where(f => !f.IsRequired))
                {
                    field.IsSelected = false;
                }
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnExport_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق من وجود حقول مختارة
                var selectedLensFields = _model.ItemFields.Where(f => f.IsSelected).ToList();
                if (!selectedLensFields.Any())
                {
                    ErrorBox.Show("يجب اختيار حقل واحد على الأقل للتصدير", "تحذير", true);
                    return;
                }

                // اختيار مكان حفظ الملف
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files (*.xlsx)|*.xlsx",
                    FileName = _model.FileName,
                    InitialDirectory = ExportPreferencesHelper.GetLastExportPath(ExportType.Lenses)
                };

                if (saveDialog.ShowDialog() != true)
                    return;

                _selectedSavePath = saveDialog.FileName;

                // حفظ مسار التصدير للعدسات
                ExportPreferencesHelper.SaveLastExportPath(_selectedSavePath, ExportType.Lenses);

                // بدء عملية التصدير
                await StartExport();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء التصدير: {ex.Message}", "خطأ", true);
            }
            finally
            {
                _isExporting = false;
                progressOverlay.Visibility = Visibility.Collapsed;
            }
        }

        private async Task StartExport()
        {
            try
            {


                _isExporting = true;
                progressOverlay.Visibility = Visibility.Visible;
                progressBar.Value = 0;

                // إعداد مؤشر التقدم
                var progress = new Progress<int>(value =>
                {
                    progressBar.Value = value;
                    txtProgress.Text = $"جاري التصدير... {value}%";
                });

                // تحضير البيانات للتصدير
                var selectedLensFields = _model.ItemFields.Where(f => f.IsSelected).Select(f => f.FieldKey).ToList();

                // استخدام طريقة التصدير الحالية الموجودة في ExportService
                await _exportService.ExportLensesToExcel(_selectedSavePath, selectedLensFields, _model.WarehouseId, progress);

                // حفظ التفضيلات للاستخدام المستقبلي
                ExportPreferencesHelper.SavePreferences(_model, ExportType.Lenses);

                ExportCompleted = true;
                DialogBox.Show("تم بنجاح", "تم تصدير البيانات بنجاح");
                Close();
            }
            catch (Exception)
            {

                throw;
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (_isExporting)
                {
                    var result = QuestionBox.Show("تأكيد الإلغاء", "هل أنت متأكد من إلغاء عملية التصدير؟");
                    if (result != MessageBoxResult.Yes) return;
                }

                ExportCompleted = false;
                Close();
            }
            finally
            {
                EnableAllButtons();
            }
        }
    }
}
