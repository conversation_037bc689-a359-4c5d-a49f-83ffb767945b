using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using Microsoft.Win32;
using ClosedXML.Excel;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.Users
{
    /// <summary>
    /// Interaction logic for UserStatementWindow.xaml
    /// </summary>
    public partial class UserStatementWindow : Window
    {
        private readonly UserService _userService;
        private readonly User _user;
        private ObservableCollection<UserStatementVM> _statementItems;
        private DateTime? _fromDate;
        private DateTime? _toDate;

        public UserStatementWindow(User user)
        {
            InitializeComponent();
            _user = user;
            _userService = ServiceLocator.GetService<UserService>();
            _statementItems = new ObservableCollection<UserStatementVM>();
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // عرض معلومات المستخدم
            txtUserName.Text = _user.Name;
            txtUserBalance.Text = $"الرصيد الحالي: {_user.Balance:N3}";

            // تعيين حالة عناصر التاريخ
            chkAllPeriods.IsChecked = true;
            UpdateDateControlsState();

            // تحميل كشف الحساب
            await LoadUserStatementAsync();
        }

        /// <summary>
        /// تحديث حالة عناصر التاريخ بناءً على خيار "كل الفترات"
        /// </summary>
        private void UpdateDateControlsState()
        {
            bool isAllPeriods = chkAllPeriods.IsChecked == true;
            if(dpFromDate is null || dpToDate is null)return;
            // تعطيل/تفعيل عناصر التاريخ
            dpFromDate.IsEnabled = !isAllPeriods;
            dpToDate.IsEnabled = !isAllPeriods;

            // تحديث قيم التاريخ
            if (isAllPeriods)
            {
                _fromDate = null;
                _toDate = null;
            }
            else
            {
                _fromDate = dpFromDate.SelectedDate;
                _toDate = dpToDate.SelectedDate;
            }
        }

        private async void btnApplyFilter_Click(object sender, RoutedEventArgs e)
        {
            // التحقق من أن عناصر التاريخ ليست null
            if (dpFromDate == null || dpToDate == null)
                return;

            if (chkAllPeriods.IsChecked == false)
            {
                // تحديث قيم التاريخ فقط إذا لم يتم اختيار "كل الفترات"
                _fromDate = dpFromDate.SelectedDate;
                _toDate = dpToDate.SelectedDate;
            }

            // تحديث البيانات
            await LoadUserStatementAsync();
        }

        private async Task LoadUserStatementAsync()
        {
            try
            {
                // التحقق من أن المستخدم ليس null قبل محاولة تحديث البيانات
                if (_user == null)
                {
                    ErrorBox.Show("لم يتم تحديد المستخدم بشكل صحيح", "خطأ في التحميل", true);
                    return;
                }

                // تحميل كشف الحساب مع تطبيق الفلاتر
                var (transactions, summary) = await _userService.GetUserStatementAsync(_user.Id, _fromDate, _toDate);

                // تحديث القائمة
                _statementItems.Clear();
                foreach (var item in transactions)
                {
                    _statementItems.Add(item);
                }

                // تعيين مصدر البيانات للقائمة بشكل صحيح
                list.ItemsSource = null; // إعادة تعيين مصدر البيانات أولاً
                list.ItemsSource = _statementItems;

                // تحديث عنوان النافذة
                Title = $"كشف حساب {_user.Name} - {_statementItems.Count} عملية";

                // تحديث ملخص كشف الحساب
                txtUserBalance.Text = $"الرصيد الحالي: {_user.Balance:N3}";
                txtTotalDebit.Text = $"مجموع المدين: {summary.TotalDebit:N3}";
                txtTotalCredit.Text = $"مجموع الدائن: {summary.TotalCredit:N3}";
                txtFinalBalance.Text = $"الرصيد النهائي: {summary.FinalBalance:N3}";
                txtBalanceStatus.Text = $"حالة الرصيد: {summary.BalanceStatus}";
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل كشف الحساب: {ex.Message}", "خطأ في التحميل", true);
            }
        }

        private async void chkAllPeriods_CheckedChanged(object sender, RoutedEventArgs e)
        {
            // التحقق من أن عناصر التاريخ ليست null
            if (dpFromDate == null || dpToDate == null)
                return;

            UpdateDateControlsState();

            // تحديث البيانات تلقائياً عند تغيير خيار "كل الفترات"
            await LoadUserStatementAsync();
        }



        private void btnClose_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            ListView listView = sender as ListView;
            GridView gView = listView.View as GridView;

            var workingWidth = listView.ActualWidth - SystemParameters.VerticalScrollBarWidth;
            var col1 = 0.12;
            var col2 = 0.15;
            var col3 = 0.12;
            var col4 = 0.25;
            var col5 = 0.12;
            var col6 = 0.12;
            var col7 = 0.12;

            gView.Columns[0].Width = workingWidth * col1;
            gView.Columns[1].Width = workingWidth * col2;
            gView.Columns[2].Width = workingWidth * col3;
            gView.Columns[3].Width = workingWidth * col4;
            gView.Columns[4].Width = workingWidth * col5;
            gView.Columns[5].Width = workingWidth * col6;
            gView.Columns[6].Width = workingWidth * col7;
        }

        private void btnPrint_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء مستند للطباعة
                FlowDocument document = new FlowDocument();
                document.FontFamily = new FontFamily("Cairo");
                document.PagePadding = new Thickness(50);

                // إضافة العنوان
                Paragraph title = new Paragraph(new Run("كشف حساب المستخدم"));
                title.FontSize = 24;
                title.FontWeight = FontWeights.Bold;
                title.TextAlignment = TextAlignment.Center;
                title.Margin = new Thickness(0, 0, 0, 20);
                document.Blocks.Add(title);

                // إضافة معلومات المستخدم
                Paragraph userInfo = new Paragraph(new Run($"اسم المستخدم: {_user.Name}"));
                userInfo.TextAlignment = TextAlignment.Center;
                userInfo.Margin = new Thickness(0, 0, 0, 10);
                document.Blocks.Add(userInfo);

                // إضافة معلومات المستخدم
                Paragraph userBalance = new Paragraph(new Run($"الرصيد الحالي: {_user.Balance:N3}"));
                userBalance.TextAlignment = TextAlignment.Center;
                userBalance.Margin = new Thickness(0, 0, 0, 20);
                document.Blocks.Add(userBalance);

                // إضافة معلومات الفترة
                string periodInfo = chkAllPeriods.IsChecked == true
                    ? "الفترة: كل الفترات"
                    : $"الفترة: من {_fromDate?.ToString("yyyy-MM-dd") ?? ""} إلى {_toDate?.ToString("yyyy-MM-dd") ?? ""}";
                Paragraph period = new Paragraph(new Run(periodInfo));
                period.TextAlignment = TextAlignment.Center;
                period.Margin = new Thickness(0, 0, 0, 20);
                document.Blocks.Add(period);

                // إنشاء جدول للبيانات
                Table table = new Table();
                table.CellSpacing = 0;
                table.BorderBrush = Brushes.Black;
                table.BorderThickness = new Thickness(1);

                // تعريف أعمدة الجدول
                for (int i = 0; i < 7; i++)
                {
                    table.Columns.Add(new TableColumn());
                }

                // إضافة رأس الجدول
                TableRowGroup headerGroup = new TableRowGroup();
                TableRow headerRow = new TableRow();
                headerRow.Background = Brushes.LightGray;
                headerRow.FontWeight = FontWeights.Bold;

                string[] headers = { "التاريخ", "نوع العملية", "طريقة الدفع", "البيان", "قبض", "دفع", "الرصيد" };
                foreach (string header in headers)
                {
                    headerRow.Cells.Add(new TableCell(new Paragraph(new Run(header))));
                }
                headerGroup.Rows.Add(headerRow);
                table.RowGroups.Add(headerGroup);

                // إضافة بيانات الجدول
                TableRowGroup dataGroup = new TableRowGroup();
                foreach (var item in _statementItems)
                {
                    TableRow row = new TableRow();

                    // التاريخ
                    row.Cells.Add(new TableCell(new Paragraph(new Run(item.Date.ToString("yyyy-MM-dd")))));

                    // نوع العملية
                    row.Cells.Add(new TableCell(new Paragraph(new Run(item.OperationType))));

                    // طريقة الدفع
                    row.Cells.Add(new TableCell(new Paragraph(new Run(item.PaymentMethod ?? "غير محدد"))));

                    // البيان
                    row.Cells.Add(new TableCell(new Paragraph(new Run(item.Description))));

                    // قبض
                    TableCell incomingCell = new TableCell(new Paragraph(new Run(item.Incoming.ToString("N3"))));
                    row.Cells.Add(incomingCell);

                    // دفع
                    TableCell outgoingCell = new TableCell(new Paragraph(new Run(item.Outgoing.ToString("N3"))));
                    row.Cells.Add(outgoingCell);

                    // الرصيد
                    TableCell balanceCell = new TableCell(new Paragraph(new Run(item.Balance.ToString("N3"))));
                    row.Cells.Add(balanceCell);

                    // تنسيق الخلايا
                    if (item.Incoming > 0)
                    {
                        incomingCell.Foreground = Brushes.Green;
                        incomingCell.FontWeight = FontWeights.Bold;
                    }

                    if (item.Outgoing > 0)
                    {
                        outgoingCell.Foreground = Brushes.Red;
                        outgoingCell.FontWeight = FontWeights.Bold;
                    }

                    balanceCell.FontWeight = FontWeights.Bold;
                    if (item.Balance > 0)
                    {
                        // رصيد موجب (الموظف مدين للمتجر) - أحمر
                        balanceCell.Foreground = Brushes.Red;
                    }
                    else if (item.Balance < 0)
                    {
                        // رصيد سالب (المتجر مدين للموظف) - أخضر
                        balanceCell.Foreground = Brushes.Green;
                    }

                    dataGroup.Rows.Add(row);
                }
                table.RowGroups.Add(dataGroup);

                // إضافة صف المجاميع
                TableRowGroup summaryGroup = new TableRowGroup();
                TableRow summaryRow = new TableRow();
                summaryRow.Background = Brushes.LightGray;
                summaryRow.FontWeight = FontWeights.Bold;

                summaryRow.Cells.Add(new TableCell(new Paragraph(new Run("المجموع"))) { ColumnSpan = 4 });
                summaryRow.Cells.Add(new TableCell(new Paragraph(new Run("مجموع المدين"))));
                summaryRow.Cells.Add(new TableCell(new Paragraph(new Run("مجموع الدائن"))));
                summaryRow.Cells.Add(new TableCell(new Paragraph(new Run("الرصيد النهائي"))));

                summaryGroup.Rows.Add(summaryRow);
                table.RowGroups.Add(summaryGroup);

                // إضافة بيانات الملخص
                TableRowGroup summaryDataGroup = new TableRowGroup();
                TableRow summaryDataRow = new TableRow();

                // استخراج قيم الملخص من عناصر واجهة المستخدم
                string totalDebit = txtTotalDebit.Text.Replace("مجموع المدين: ", "");
                string totalCredit = txtTotalCredit.Text.Replace("مجموع الدائن: ", "");
                string finalBalance = txtFinalBalance.Text.Replace("الرصيد النهائي: ", "");

                summaryDataRow.Cells.Add(new TableCell(new Paragraph(new Run(""))) { ColumnSpan = 4 });
                summaryDataRow.Cells.Add(new TableCell(new Paragraph(new Run(totalDebit))) { TextAlignment = TextAlignment.Center });
                summaryDataRow.Cells.Add(new TableCell(new Paragraph(new Run(totalCredit))) { TextAlignment = TextAlignment.Center });
                summaryDataRow.Cells.Add(new TableCell(new Paragraph(new Run(finalBalance))) { TextAlignment = TextAlignment.Center });

                summaryDataGroup.Rows.Add(summaryDataRow);
                table.RowGroups.Add(summaryDataGroup);

                document.Blocks.Add(table);

                // إضافة حالة الرصيد
                Paragraph balanceStatus = new Paragraph(new Run(txtBalanceStatus.Text));
                balanceStatus.TextAlignment = TextAlignment.Center;
                balanceStatus.Margin = new Thickness(0, 20, 0, 0);
                balanceStatus.FontWeight = FontWeights.Bold;
                document.Blocks.Add(balanceStatus);

                // إنشاء نافذة الطباعة
                PrintDialog printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    IDocumentPaginatorSource paginatorSource = document;
                    printDialog.PrintDocument(paginatorSource.DocumentPaginator, "كشف حساب المستخدم");
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء الطباعة: {ex.Message}", "خطأ في الطباعة", true);
            }
        }

        private void btnExportExcel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء مربع حوار حفظ الملف
                SaveFileDialog saveFileDialog = new SaveFileDialog
                {
                    Filter = "Excel Files (*.xlsx)|*.xlsx",
                    Title = "حفظ كشف الحساب كملف Excel",
                    FileName = $"كشف_حساب_{_user.Name}_{DateTime.Now:yyyy-MM-dd}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    using (var workbook = new XLWorkbook())
                    {
                        var worksheet = workbook.Worksheets.Add("كشف الحساب");

                        // إضافة عنوان الورقة
                        worksheet.Cell("A1").Value = $"كشف حساب {_user.Name}";
                        worksheet.Range("A1:G1").Merge();
                        worksheet.Cell("A1").Style.Font.Bold = true;
                        worksheet.Cell("A1").Style.Font.FontSize = 16;
                        worksheet.Cell("A1").Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                        // إضافة معلومات المستخدم
                        worksheet.Cell("A2").Value = $"الرصيد الحالي: {_user.Balance:N3}";
                        worksheet.Range("A2:G2").Merge();
                        worksheet.Cell("A2").Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                        // إضافة معلومات الفترة
                        string periodInfo = chkAllPeriods.IsChecked == true
                            ? "الفترة: كل الفترات"
                            : $"الفترة: من {_fromDate?.ToString("yyyy-MM-dd") ?? ""} إلى {_toDate?.ToString("yyyy-MM-dd") ?? ""}";
                        worksheet.Cell("A3").Value = periodInfo;
                        worksheet.Range("A3:G3").Merge();
                        worksheet.Cell("A3").Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                        // إضافة رأس الجدول
                        string[] headers = { "التاريخ", "نوع العملية", "طريقة الدفع", "البيان", "قبض", "دفع", "الرصيد" };
                        for (int i = 0; i < headers.Length; i++)
                        {
                            worksheet.Cell(5, i + 1).Value = headers[i];
                            worksheet.Cell(5, i + 1).Style.Font.Bold = true;
                            worksheet.Cell(5, i + 1).Style.Fill.BackgroundColor = XLColor.LightGray;
                            worksheet.Cell(5, i + 1).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                            worksheet.Cell(5, i + 1).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                        }

                        // إضافة بيانات الجدول
                        int row = 6;
                        foreach (var item in _statementItems)
                        {
                            worksheet.Cell(row, 1).Value = item.Date.ToString("yyyy-MM-dd");
                            worksheet.Cell(row, 2).Value = item.OperationType;
                            worksheet.Cell(row, 3).Value = item.PaymentMethod ?? "غير محدد";
                            worksheet.Cell(row, 4).Value = item.Description;
                            worksheet.Cell(row, 5).Value = item.Incoming;
                            worksheet.Cell(row, 6).Value = item.Outgoing;
                            worksheet.Cell(row, 7).Value = item.Balance;

                            // تنسيق الخلايا
                            for (int i = 1; i <= 7; i++)
                            {
                                worksheet.Cell(row, i).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                                worksheet.Cell(row, i).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                            }

                            // تنسيق خاص للصفوف الخاصة
                            if (item.IsSpecialRow)
                            {
                                for (int i = 1; i <= 7; i++)
                                {
                                    worksheet.Cell(row, i).Style.Font.Bold = true;
                                    worksheet.Cell(row, i).Style.Fill.BackgroundColor = XLColor.FromHtml("#FFF8E1");
                                }
                            }

                            // تنسيق الأرقام
                            worksheet.Cell(row, 5).Style.NumberFormat.Format = "#,##0.000";
                            worksheet.Cell(row, 6).Style.NumberFormat.Format = "#,##0.000";
                            worksheet.Cell(row, 7).Style.NumberFormat.Format = "#,##0.000";

                            // تنسيق الألوان
                            if (item.Incoming > 0)
                            {
                                worksheet.Cell(row, 5).Style.Font.FontColor = XLColor.Green;
                                worksheet.Cell(row, 5).Style.Font.Bold = true;
                            }

                            if (item.Outgoing > 0)
                            {
                                worksheet.Cell(row, 6).Style.Font.FontColor = XLColor.Red;
                                worksheet.Cell(row, 6).Style.Font.Bold = true;
                            }

                            worksheet.Cell(row, 7).Style.Font.Bold = true;
                            if (item.Balance > 0)
                            {
                                // رصيد موجب (الموظف مدين للمتجر) - أحمر
                                worksheet.Cell(row, 7).Style.Font.FontColor = XLColor.Red;
                            }
                            else if (item.Balance < 0)
                            {
                                // رصيد سالب (المتجر مدين للموظف) - أخضر
                                worksheet.Cell(row, 7).Style.Font.FontColor = XLColor.Green;
                            }

                            row++;
                        }

                        // إضافة صف المجاميع
                        worksheet.Cell(row + 1, 1).Value = "المجموع";
                        worksheet.Range(row + 1, 1, row + 1, 4).Merge();
                        worksheet.Cell(row + 1, 1).Style.Font.Bold = true;
                        worksheet.Cell(row + 1, 1).Style.Fill.BackgroundColor = XLColor.LightGray;
                        worksheet.Cell(row + 1, 1).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                        worksheet.Cell(row + 1, 5).Value = "مجموع المدين";
                        worksheet.Cell(row + 1, 5).Style.Font.Bold = true;
                        worksheet.Cell(row + 1, 5).Style.Fill.BackgroundColor = XLColor.LightGray;
                        worksheet.Cell(row + 1, 5).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                        worksheet.Cell(row + 1, 6).Value = "مجموع الدائن";
                        worksheet.Cell(row + 1, 6).Style.Font.Bold = true;
                        worksheet.Cell(row + 1, 6).Style.Fill.BackgroundColor = XLColor.LightGray;
                        worksheet.Cell(row + 1, 6).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                        worksheet.Cell(row + 1, 7).Value = "الرصيد النهائي";
                        worksheet.Cell(row + 1, 7).Style.Font.Bold = true;
                        worksheet.Cell(row + 1, 7).Style.Fill.BackgroundColor = XLColor.LightGray;
                        worksheet.Cell(row + 1, 7).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                        // إضافة قيم المجاميع
                        decimal totalDebit = decimal.Parse(txtTotalDebit.Text.Replace("مجموع المدين: ", ""));
                        decimal totalCredit = decimal.Parse(txtTotalCredit.Text.Replace("مجموع الدائن: ", ""));
                        decimal finalBalance = decimal.Parse(txtFinalBalance.Text.Replace("الرصيد النهائي: ", ""));

                        worksheet.Cell(row + 2, 5).Value = totalDebit;
                        worksheet.Cell(row + 2, 5).Style.NumberFormat.Format = "#,##0.000";
                        worksheet.Cell(row + 2, 5).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                        worksheet.Cell(row + 2, 6).Value = totalCredit;
                        worksheet.Cell(row + 2, 6).Style.NumberFormat.Format = "#,##0.000";
                        worksheet.Cell(row + 2, 6).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                        worksheet.Cell(row + 2, 7).Value = finalBalance;
                        worksheet.Cell(row + 2, 7).Style.NumberFormat.Format = "#,##0.000";
                        worksheet.Cell(row + 2, 7).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                        // إضافة حالة الرصيد
                        string balanceStatus = txtBalanceStatus.Text;
                        worksheet.Cell(row + 4, 1).Value = balanceStatus;
                        worksheet.Range(row + 4, 1, row + 4, 7).Merge();
                        worksheet.Cell(row + 4, 1).Style.Font.Bold = true;
                        worksheet.Cell(row + 4, 1).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

                        // ضبط عرض الأعمدة
                        worksheet.Columns().AdjustToContents();

                        // حفظ الملف
                        workbook.SaveAs(saveFileDialog.FileName);

                        DialogBox.Show("تم تصدير كشف الحساب بنجاح", "نجاح");
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تصدير كشف الحساب: {ex.Message}", "خطأ في التصدير", true);
            }
        }

        private void Grid_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            chkAllPeriods.IsChecked = !chkAllPeriods.IsChecked;
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnApplyFilter.IsEnabled = false;
            btnPrint.IsEnabled = false;
            btnExportExcel.IsEnabled = false;
            btnclose.IsEnabled = false;
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnApplyFilter.IsEnabled = true;
            btnPrint.IsEnabled = true;
            btnExportExcel.IsEnabled = true;
            btnclose.IsEnabled = true;
        }

        private void btnclose_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                Close();
            }
            finally
            {
                EnableAllButtons();
            }
        }
    }
}
