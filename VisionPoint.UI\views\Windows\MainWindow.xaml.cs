﻿using System.Windows;
using System.Windows.Controls;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Helper;
using VisionPoint.UI.Services;
using VisionPoint.UI.views.Dialogs;
using VisionPoint.UI.views.Pages.Clients;
using VisionPoint.UI.views.Pages.Products;
using VisionPoint.UI.views.Pages.ProductsContent;
using VisionPoint.UI.views.Pages.purchases;
using VisionPoint.UI.views.Pages.Receipts;
using VisionPoint.UI.views.Pages.Sales;
using VisionPoint.UI.views.Pages.Settings;
using VisionPoint.UI.views.Pages.Users;
using VisionPoint.UI.views.Pages.MinimumQuantity;
using VisionPoint.UI.views.Pages.Expire;
using System.Windows.Input;


namespace VisionPoint.UI
{

    public partial class MainWindow : Window
    {
        MenuItem? _lastSelectedMenuItem;
        private ExpensesPage _expensesPage;
        private ExpireWindow _expireWindow;

        // خاصية للتحقق مما إذا كان المستخدم الحالي هو Admin
        public bool IsAdminUser => CurrentUser.HasRole("Admin");

        // خاصية للتحقق من صلاحيات المستخدم للوصول إلى واجهة معينة
        public bool HasInterfaceAccess(string[] allowedRoles)
        {
            return CurrentUser.HasAnyRole(allowedRoles);
        }

        public MainWindow()
        {
            InitializeComponent();
            this.MaxWidth = SystemParameters.MaximizedPrimaryScreenWidth;
        }


        private bool _isClosingHandled = false;

        private async void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            // إذا لم يتم التعامل مع الإغلاق بعد، قم بإلغائه مؤقتاً لتنفيذ النسخة الاحتياطية
            if (!_isClosingHandled)
            {
                e.Cancel = true;

                // تنفيذ النسخة الاحتياطية التلقائية عند إغلاق النافذة الرئيسية
                await PerformAutoBackupOnMainWindowClosingAsync();

                // تعيين العلامة لتجنب التكرار
                _isClosingHandled = true;

                // إغلاق النافذة بعد انتهاء النسخة الاحتياطية
                e.Cancel = false;
            }
        }

        /// <summary>
        /// تنفيذ النسخة الاحتياطية التلقائية عند إغلاق النافذة الرئيسية
        /// </summary>
        private async Task PerformAutoBackupOnMainWindowClosingAsync()
        {
            try
            {
                // إنشاء مثيل من خدمة النسخ الاحتياطي
                var backupService = new VisionPoint.UI.Services.DatabaseBackupService();

                // تنفيذ النسخة الاحتياطية التلقائية إذا كانت مطلوبة
                var result = await backupService.PerformAutoBackupIfNeededAsync();

                // عرض النتيجة للمستخدم وانتظار رده
                if (!result.Success)
                {
                    // عرض رسالة خطأ وانتظار رد المستخدم
                    ErrorBox.Show($"خطأ في أخذ نسخة احتياطية: {result.Message}", "خطأ في النسخة الاحتياطية", true);
                }
                else if (result.Message != "النسخ الاحتياطي التلقائي غير مفعل" &&
                         result.Message != "تم إجراء النسخ الاحتياطي التلقائي اليوم بالفعل")
                {
                    // عرض رسالة نجاح وانتظار رد المستخدم
                    DialogBox.Show( "نجح النسخ الاحتياطي",$"تم أخذ النسخة الاحتياطية بنجاح: {result.Message}");
                }
                // في حالة عدم الحاجة للنسخ الاحتياطي، لا نعرض أي رسالة
            }
            catch (Exception ex)
            {
                // عرض رسالة خطأ وانتظار رد المستخدم
                ErrorBox.Show($"خطأ أثناء النسخة الاحتياطية: {ex.Message}", "خطأ في النسخة الاحتياطية", true);
            }
            finally
            {
                // تنظيف LoadingOverlay عند إغلاق التطبيق
                MainLoadingOverlay?.Cleanup();
            }
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // تأكد من إلغاء أي عمليات معلقة من الجلسة السابقة
            var busyService = ServiceLocator.GetService<BusyService>();
            busyService.ClearAllBusyOperations();

            // تعيين LoadingOverlay كالنسخة الرئيسية للتطبيق
            MainLoadingOverlay.SetAsMainInstance();

            txtUserName.Text = CurrentUser.Name;



            // تحديث حالة ظهور زر التصغير يدويًا
            btnMinimize.Visibility = IsAdminUser ? Visibility.Visible : Visibility.Collapsed;

            // تحديث حالة ظهور الأزرار بناءً على صلاحيات المستخدم
            UpdateButtonsVisibility();

            // تحديد الصفحة الافتراضية بناءً على أدوار المستخدم
            if (CurrentUser.HasRole("Admin"))
            {
                // المدير يرى صفحة المبيعات افتراضيًا
                MoveToPage(new SalesListView(), new[] { "Admin" }, null);
            }
            else if (CurrentUser.HasRole("SalesRole") || CurrentUser.HasRole("EditSalesRole") || CurrentUser.HasRole("DeleteSalesRole"))
            {
                // مسؤول المبيعات يرى صفحة المبيعات
                MoveToPage(new SalesListView(), new[] { "Admin", "SalesRole", "EditSalesRole", "DeleteSalesRole" }, null);
            }
            else if (CurrentUser.HasRole("PurchaseRole") || CurrentUser.HasRole("EditPurchaseRole") || CurrentUser.HasRole("DeletePurchaseRole"))
            {
                // مسؤول المشتريات يرى صفحة المشتريات
                MoveToPage(new purchasePage(), new[] { "Admin", "PurchaseRole", "EditPurchaseRole", "DeletePurchaseRole" }, null);
            }
            else if (CurrentUser.HasRole("ProductRole") || CurrentUser.HasRole("EditProductRole") || CurrentUser.HasRole("DeleteProductRole") ||
                     CurrentUser.HasRole("LensRole") || CurrentUser.HasRole("EditLensRole") || CurrentUser.HasRole("DeleteLensRole") ||
                     CurrentUser.HasRole("LensCategoryRole") || CurrentUser.HasRole("EditLensCategoryRole") || CurrentUser.HasRole("DeleteLensCategoryRole") ||
                     CurrentUser.HasRole("ServiceRole") || CurrentUser.HasRole("EditServiceRole") || CurrentUser.HasRole("DeleteServiceRole"))
            {
                // مسؤول المنتجات يرى صفحة المنتجات
                MoveToPage(new SharedPage(), new[] { "Admin", "ProductRole", "EditProductRole", "DeleteProductRole", "LensRole", "EditLensRole", "DeleteLensRole", "LensCategoryRole", "EditLensCategoryRole", "DeleteLensCategoryRole", "ServiceRole", "EditServiceRole", "DeleteServiceRole" }, null);
            }
            else if (CurrentUser.HasRole("ClientRole") || CurrentUser.HasRole("EditClientRole") || CurrentUser.HasRole("DeleteClientRole"))
            {
                // مسؤول العملاء يرى صفحة العملاء
                MoveToPage(new ClientPage(), new[] { "Admin", "ClientRole", "EditClientRole", "DeleteClientRole" }, null);
            }
            else if (CurrentUser.HasRole("ReceiptRole") || CurrentUser.HasRole("EditReceiptRole") || CurrentUser.HasRole("DeleteReceiptRole"))
            {
                // مسؤول السندات يرى صفحة قائمة السندات
                MoveToPage(new ReceiptListView(), new[] { "Admin", "ReceiptRole", "EditReceiptRole", "DeleteReceiptRole" }, null);
            }
            else if (CurrentUser.HasRole("UserRole") || CurrentUser.HasRole("EditUserRole") || CurrentUser.HasRole("DeleteUserRole"))
            {
                // مسؤول المستخدمين يرى صفحة المستخدمين
                MoveToPage(new UserPage(), new[] { "Admin", "UserRole", "EditUserRole", "DeleteUserRole" }, null);
            }

            else if (CurrentUser.HasRole("SettingsRole"))
            {
                // مسؤول الإعدادات يرى صفحة الإعدادات
                MoveToPage(new SharedSettingsPage(), new[] { "Admin", "SettingsRole" }, null);
            }
            else
            {
                // إذا لم يكن للمستخدم دور محدد، نعرض صفحة المبيعات (يمكن تغييرها لصفحة أخرى)
                MoveToPage(new SalesListView(), new[] { "Admin", "SalesRole" }, null);
            }

            NavigateAction.NavigateTo += MoveToPageNoPer;
        }

        private void UpdateButtonsVisibility()
        {
            // تحديث حالة ظهور الأزرار بناءً على صلاحيات المستخدم
            btnpurchases.Visibility = HasInterfaceAccess(new[] { "Admin", "PurchaseRole", "EditPurchaseRole", "DeletePurchaseRole" }) ? Visibility.Visible : Visibility.Collapsed;
            btnProduct.Visibility = HasInterfaceAccess(new[] { "Admin", "ProductRole", "EditProductRole", "DeleteProductRole", "LensRole", "EditLensRole", "DeleteLensRole", "LensCategoryRole", "EditLensCategoryRole", "DeleteLensCategoryRole", "ServiceRole", "EditServiceRole", "DeleteServiceRole" }) ? Visibility.Visible : Visibility.Collapsed;
            btnClients.Visibility = HasInterfaceAccess(new[] { "Admin", "ClientRole", "EditClientRole", "DeleteClientRole" }) ? Visibility.Visible : Visibility.Collapsed;
            btnWarehouses.Visibility = HasInterfaceAccess(new[] { "Admin", "WarehouseRole", "EditWarehouseRole", "DeleteWarehouseRole" }) ? Visibility.Visible : Visibility.Collapsed;
            btnTreasuries.Visibility = HasInterfaceAccess(new[] { "Admin", "TreasuryRole", "EditTreasuryRole", "DeleteTreasuryRole" }) ? Visibility.Visible : Visibility.Collapsed;
            btnUsers.Visibility = HasInterfaceAccess(new[] { "Admin", "UserRole", "EditUserRole", "DeleteUserRole" }) ? Visibility.Visible : Visibility.Collapsed;
            btnReseipt.Visibility = HasInterfaceAccess(new[] { "Admin", "ReceiptRole", "EditReceiptRole", "DeleteReceiptRole" }) ? Visibility.Visible : Visibility.Collapsed;
            btnExpenses.Visibility = HasInterfaceAccess(new[] { "Admin", "ExpenseRole", "EditExpenseRole", "DeleteExpenseRole", "EmployeeFinancialRole" }) ? Visibility.Visible : Visibility.Collapsed;
            btnSettings.Visibility = HasInterfaceAccess(new[] { "Admin", "SettingsRole", "BackupRole", "SystemConfigRole" }) ? Visibility.Visible : Visibility.Collapsed;
            btnSales.Visibility = HasInterfaceAccess(new[] { "Admin", "SalesRole", "EditSalesRole", "DeleteSalesRole" }) ? Visibility.Visible : Visibility.Collapsed;
        }

        // طريقة لتعطيل جميع الأزرار
        public void DisableAllButtons()
        {
            Mouse.OverrideCursor = Cursors.Wait; // تغيير المؤشر إلى مؤشر التحميل
            btnpurchases.IsEnabled = false;
            btnSales.IsEnabled = false;
            btnProduct.IsEnabled = false;
            btnClients.IsEnabled = false;
            btnWarehouses.IsEnabled = false;
            btnTreasuries.IsEnabled = false;
            btnUsers.IsEnabled = false;
            btnReseipt.IsEnabled = false;
            btnExpenses.IsEnabled = false;
            btnSettings.IsEnabled = false;
            btnMinimize.IsEnabled = false;
        }

        // طريقة لتفعيل جميع الأزرار
        public void EnableAllButtons()
        {
            Mouse.OverrideCursor = null; // إعادة المؤشر للوضع الافتراضي
            btnpurchases.IsEnabled = true;
            btnSales.IsEnabled = true;
            btnProduct.IsEnabled = true;
            btnClients.IsEnabled = true;
            btnWarehouses.IsEnabled = true;
            btnTreasuries.IsEnabled = true;
            btnUsers.IsEnabled = true;
            btnReseipt.IsEnabled = true;
            btnExpenses.IsEnabled = true;
            btnSettings.IsEnabled = true;
            btnMinimize.IsEnabled = true;
        }

        private void btnpurchases_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                MoveToPage(new purchasePage(), new[] { "Admin", "PurchaseRole", "EditPurchaseRole", "DeletePurchaseRole" }, sender as Button);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnSales_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                MoveToPage(new SalesListView(), new[] { "Admin", "SalesRole", "EditSalesRole", "DeleteSalesRole" }, sender as Button);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnProduct_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                MoveToPage(new SharedPage(), new[] { "Admin", "ProductRole", "EditProductRole", "DeleteProductRole", "LensRole", "EditLensRole", "DeleteLensRole", "LensCategoryRole", "EditLensCategoryRole", "DeleteLensCategoryRole", "ServiceRole", "EditServiceRole", "DeleteServiceRole" }, sender as Button);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnClients_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                MoveToPage(new ClientPage(), new[] { "Admin", "ClientRole", "EditClientRole", "DeleteClientRole" }, sender as Button);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnWarehouses_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                MoveToPage(new views.Pages.Warehouses.WarehousePage(), new[] { "Admin", "WarehouseRole", "EditWarehouseRole", "DeleteWarehouseRole" }, sender as Button);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnTreasuries_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                MoveToPage(new views.Pages.Treasury.TreasuryPage(), new[] { "Admin", "TreasuryRole", "EditTreasuryRole", "DeleteTreasuryRole" }, sender as Button);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnUsers_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                MoveToPage(new UserPage(), new[] { "Admin", "UserRole", "EditUserRole", "DeleteUserRole" }, sender as Button);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnReseipt_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                MoveToPage(new ReceiptListView(), new[] { "Admin", "ReceiptRole", "EditReceiptRole", "DeleteReceiptRole" }, sender as Button);
            }
            finally
            {
                EnableAllButtons();
            }
        }


        private void btnExpenses_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (!CheckRole(new[] { "Admin", "ExpenseRole", "EditExpenseRole", "DeleteExpenseRole", "EmployeeFinancialRole" }))
                    return;
                BorrowMoneyPage borrowMoneyPage = new BorrowMoneyPage();
                borrowMoneyPage.ShowDialog();
            }
            finally
            {
                EnableAllButtons();
            }
        }


        private void btnSettings_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                MoveToPage(new SharedSettingsPage(), new[] { "Admin", "SettingsRole", "BackupRole", "SystemConfigRole", "TreasuryRole", "EditTreasuryRole", "DeleteTreasuryRole" }, sender as Button);
            }
            finally
            {
                EnableAllButtons();
            }
        }






        private async void btnLogout_Click(object sender, RoutedEventArgs e)
        {
            // تنفيذ النسخة الاحتياطية التلقائية عند تسجيل الخروج
            await PerformAutoBackupOnMainWindowClosingAsync();

            // تعيين العلامة لتجنب تكرار النسخة الاحتياطية عند إغلاق النافذة
            _isClosingHandled = true;

            Application.Current.MainWindow.Show();
            Close();
        }

        private void btnMinimize_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {


            // التحقق من صلاحية المستخدم قبل تنفيذ عملية التصغير
            if (IsAdminUser)
            {
                this.WindowState = WindowState.Minimized;
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("User is not admin, cannot minimize window");
            }
        }
        public void MoveToPage(Page page, string[] allowedRoles, Button senderButton)
        {
            if (!HasInterfaceAccess(allowedRoles))
            {
                ErrorBox.Show("لا تملك صلاحية الوصول لهذه الصفحة", "خطأ في الصلاحيات", true);
                DeselectAllMenuItems();
                if (_lastSelectedMenuItem != null)
                {
                    _lastSelectedMenuItem.IsChecked = true;
                }
                else
                {
                    var sale = btnSales.Content as MenuItem;
                    if (sale != null)
                    {
                        sale.IsChecked = true;
                    }
                }
                return;
            }

            DeselectAllMenuItems();
            if (senderButton != null)
            {
                _lastSelectedMenuItem = senderButton.Content as MenuItem;
                if (_lastSelectedMenuItem != null)
                {
                    _lastSelectedMenuItem.IsChecked = true;
                }
            }

            fContainer.Navigate(page);
        }


        public void MoveToPageNoPer(Page page)
        {
            fContainer.Navigate(page);
        }

        private void DeselectAllMenuItems()
        {
            // Assuming all your menu buttons are within a panel like a StackPanel or Grid
            foreach (var child in MenuPanel.Children)
            {
                if (child is Button button && button.Content is MenuItem menuItem)
                {
                    menuItem.IsChecked = false;
                }
            }
        }

        bool CheckRole(string[] roles)
        {
            if (!HasInterfaceAccess(roles))
            {
                ErrorBox.Show("لا تملك صلاحية الوصول لهذه الصفحة", "خطأ في الصلاحيات", true);
                return false;
            }
            return true;
        }




    }
}