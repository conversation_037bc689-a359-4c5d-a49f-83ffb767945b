﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="VisionPoint.UI.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="DefaultPrinter" Type="System.String" Scope="User">
      <Value Profile="(Default)">Microsoft Print to PDf</Value>
    </Setting>
    <Setting Name="ArabicAddress" Type="System.String" Scope="User">
      <Value Profile="(Default)">سوق الجمعة -عمارة مصرف الادخار مقبل كلية سوق الجمعة</Value>
    </Setting>
    <Setting Name="EnglishAddress" Type="System.String" Scope="User">
      <Value Profile="(Default)">Souq Al Juma'a - Al Idikhar Bank Building, In front of Souq Al Juma'a College</Value>
    </Setting>
    <Setting Name="PhoneNumber" Type="System.String" Scope="User">
      <Value Profile="(Default)">**********</Value>
    </Setting>
    <Setting Name="LicenseRemainingDays" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="AllowNegativeQuantities" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="ConnectionString" Type="System.String" Scope="Application">
      <Value Profile="(Default)">Server=.;Database=VisionPoint;Trusted_Connection=false;User id=user;password=***;TrustServerCertificate=True;MultipleActiveResultSets=true;</Value>
    </Setting>
    <Setting Name="BranchName" Type="System.String" Scope="User">
      <Value Profile="(Default)">سوق الجمعة</Value>
    </Setting>

    <Setting Name="AutoBackupEnabled" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="AutoBackupPath" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="AutoBackupMaxCount" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">5</Value>
    </Setting>
    <Setting Name="LastAutoBackupDate" Type="System.DateTime" Scope="User">
      <Value Profile="(Default)">2023-01-01</Value>
    </Setting>
    <Setting Name="EnableMinimumQuantityCheck" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="CompanyName" Type="System.String" Scope="User">
      <Value Profile="(Default)">مجموعة النمارق للبصريات</Value>
    </Setting>
    <Setting Name="CompanyNameEng" Type="System.String" Scope="User">
      <Value Profile="(Default)">Namariq Optics Group</Value>
    </Setting>
  </Settings>
</SettingsFile>