﻿CREATE TABLE [AspNetRoles] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(256) NULL,
    [NormalizedName] nvarchar(256) NULL,
    [ConcurrencyStamp] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetRoles] PRIMARY KEY ([Id])
);
GO


CREATE TABLE [Colors] (
    [Id] tinyint NOT NULL IDENTITY,
    [Name] nvarchar(50) NOT NULL,
    [HexCode] nvarchar(30) NOT NULL,
    CONSTRAINT [PK_Colors] PRIMARY KEY ([Id])
);
GO


CREATE TABLE [Prescriptions] (
    [Id] smallint NOT NULL IDENTITY,
    [Value] decimal(5,2) NOT NULL,
    CONSTRAINT [PK_Prescriptions] PRIMARY KEY ([Id])
);
GO


CREATE TABLE [Warehouses] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Code] nvarchar(20) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    CONSTRAINT [PK_Warehouses] PRIMARY KEY ([Id])
);
GO


CREATE TABLE [AspNetRoleClaims] (
    [Id] int NOT NULL IDENTITY,
    [RoleId] int NOT NULL,
    [ClaimType] nvarchar(max) NULL,
    [ClaimValue] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetRoleClaims] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_AspNetRoleClaims_AspNetRoles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [AspNetRoles] ([Id])
);
GO


CREATE TABLE [AspNetUsers] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Balance] decimal(18,3) NOT NULL,
    [Salary] decimal(18,3) NOT NULL,
    [WarehouseId] int NULL,
    [UserName] nvarchar(256) NULL,
    [NormalizedUserName] nvarchar(256) NULL,
    [Email] nvarchar(256) NULL,
    [NormalizedEmail] nvarchar(256) NULL,
    [EmailConfirmed] bit NOT NULL,
    [PasswordHash] nvarchar(max) NULL,
    [SecurityStamp] nvarchar(max) NULL,
    [ConcurrencyStamp] nvarchar(max) NULL,
    [PhoneNumber] nvarchar(max) NULL,
    [PhoneNumberConfirmed] bit NOT NULL,
    [TwoFactorEnabled] bit NOT NULL,
    [LockoutEnd] datetimeoffset NULL,
    [LockoutEnabled] bit NOT NULL,
    [AccessFailedCount] int NOT NULL,
    CONSTRAINT [PK_AspNetUsers] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_AspNetUsers_Warehouses_WarehouseId] FOREIGN KEY ([WarehouseId]) REFERENCES [Warehouses] ([Id]) ON DELETE SET NULL
);
GO


CREATE TABLE [Treasuries] (
    [Id] tinyint NOT NULL IDENTITY,
    [Name] nvarchar(50) NOT NULL,
    [Balance] decimal(18,3) NOT NULL,
    [WarehouseId] int NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    CONSTRAINT [PK_Treasuries] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Treasuries_Warehouses_WarehouseId] FOREIGN KEY ([WarehouseId]) REFERENCES [Warehouses] ([Id])
);
GO


CREATE TABLE [AspNetUserClaims] (
    [Id] int NOT NULL IDENTITY,
    [UserId] int NOT NULL,
    [ClaimType] nvarchar(max) NULL,
    [ClaimValue] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetUserClaims] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_AspNetUserClaims_AspNetUsers_UserId] FOREIGN KEY ([UserId]) REFERENCES [AspNetUsers] ([Id])
);
GO


CREATE TABLE [AspNetUserLogins] (
    [LoginProvider] nvarchar(450) NOT NULL,
    [ProviderKey] nvarchar(450) NOT NULL,
    [ProviderDisplayName] nvarchar(max) NULL,
    [UserId] int NOT NULL,
    CONSTRAINT [PK_AspNetUserLogins] PRIMARY KEY ([LoginProvider], [ProviderKey]),
    CONSTRAINT [FK_AspNetUserLogins_AspNetUsers_UserId] FOREIGN KEY ([UserId]) REFERENCES [AspNetUsers] ([Id])
);
GO


CREATE TABLE [AspNetUserRoles] (
    [UserId] int NOT NULL,
    [RoleId] int NOT NULL,
    CONSTRAINT [PK_AspNetUserRoles] PRIMARY KEY ([UserId], [RoleId]),
    CONSTRAINT [FK_AspNetUserRoles_AspNetRoles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [AspNetRoles] ([Id]),
    CONSTRAINT [FK_AspNetUserRoles_AspNetUsers_UserId] FOREIGN KEY ([UserId]) REFERENCES [AspNetUsers] ([Id])
);
GO


CREATE TABLE [AspNetUserTokens] (
    [UserId] int NOT NULL,
    [LoginProvider] nvarchar(450) NOT NULL,
    [Name] nvarchar(450) NOT NULL,
    [Value] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetUserTokens] PRIMARY KEY ([UserId], [LoginProvider], [Name]),
    CONSTRAINT [FK_AspNetUserTokens_AspNetUsers_UserId] FOREIGN KEY ([UserId]) REFERENCES [AspNetUsers] ([Id])
);
GO


CREATE TABLE [Clients] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Phone] nvarchar(20) NULL,
    [Balance] decimal(18,3) NOT NULL,
    [AllowedBalance] decimal(18,3) NOT NULL,
    [IsCustomer] bit NOT NULL,
    [IsSupplier] bit NOT NULL,
    [WarehouseId] int NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [CreatedById] int NULL,
    [ModifiedById] int NULL,
    CONSTRAINT [PK_Clients] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Clients_AspNetUsers_CreatedById] FOREIGN KEY ([CreatedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_Clients_AspNetUsers_ModifiedById] FOREIGN KEY ([ModifiedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_Clients_Warehouses_WarehouseId] FOREIGN KEY ([WarehouseId]) REFERENCES [Warehouses] ([Id]) ON DELETE SET NULL
);
GO


CREATE TABLE [Discounts] (
    [Id] int NOT NULL IDENTITY,
    [StartDate] datetime2 NOT NULL,
    [EndDate] datetime2 NOT NULL,
    [DiscountPercentage] decimal(18,3) NOT NULL,
    [ApplyToAllProducts] bit NOT NULL,
    [ApplyToAllLenses] bit NOT NULL,
    [ApplyToAllServices] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [CreatedById] int NULL,
    [ModifiedById] int NULL,
    CONSTRAINT [PK_Discounts] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Discounts_AspNetUsers_CreatedById] FOREIGN KEY ([CreatedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_Discounts_AspNetUsers_ModifiedById] FOREIGN KEY ([ModifiedById]) REFERENCES [AspNetUsers] ([Id])
);
GO


CREATE TABLE [Expenses] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [CreatedById] int NULL,
    [ModifiedById] int NULL,
    CONSTRAINT [PK_Expenses] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Expenses_AspNetUsers_CreatedById] FOREIGN KEY ([CreatedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_Expenses_AspNetUsers_ModifiedById] FOREIGN KEY ([ModifiedById]) REFERENCES [AspNetUsers] ([Id])
);
GO


CREATE TABLE [Financials] (
    [Id] tinyint NOT NULL,
    [Name] nvarchar(100) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [CreatedById] int NULL,
    [ModifiedById] int NULL,
    CONSTRAINT [PK_Financials] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Financials_AspNetUsers_CreatedById] FOREIGN KEY ([CreatedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_Financials_AspNetUsers_ModifiedById] FOREIGN KEY ([ModifiedById]) REFERENCES [AspNetUsers] ([Id])
);
GO


CREATE TABLE [LensCategories] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(50) NOT NULL,
    [Description] nvarchar(200) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [CreatedById] int NULL,
    [ModifiedById] int NULL,
    CONSTRAINT [PK_LensCategories] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_LensCategories_AspNetUsers_CreatedById] FOREIGN KEY ([CreatedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_LensCategories_AspNetUsers_ModifiedById] FOREIGN KEY ([ModifiedById]) REFERENCES [AspNetUsers] ([Id])
);
GO


CREATE TABLE [Products] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [CostPrice] decimal(18,3) NOT NULL,
    [SellPrice] decimal(18,3) NOT NULL,
    [Color] bit NOT NULL,
    [Exp] bit NOT NULL,
    [MinimumQuantity] int NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [CreatedById] int NULL,
    [ModifiedById] int NULL,
    CONSTRAINT [PK_Products] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Products_AspNetUsers_CreatedById] FOREIGN KEY ([CreatedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_Products_AspNetUsers_ModifiedById] FOREIGN KEY ([ModifiedById]) REFERENCES [AspNetUsers] ([Id])
);
GO


CREATE TABLE [Services] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Price] decimal(18,3) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [CreatedById] int NULL,
    [ModifiedById] int NULL,
    CONSTRAINT [PK_Services] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Services_AspNetUsers_CreatedById] FOREIGN KEY ([CreatedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_Services_AspNetUsers_ModifiedById] FOREIGN KEY ([ModifiedById]) REFERENCES [AspNetUsers] ([Id])
);
GO


CREATE TABLE [Purchases] (
    [Id] int NOT NULL IDENTITY,
    [InvoiceNo] int NOT NULL,
    [ClientId] int NOT NULL,
    [PurchaseDate] datetime2 NOT NULL,
    [TotalAmount] decimal(18,3) NOT NULL,
    [Notes] nvarchar(500) NULL,
    [WarehouseId] int NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [CreatedById] int NULL,
    [ModifiedById] int NULL,
    CONSTRAINT [PK_Purchases] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Purchases_AspNetUsers_CreatedById] FOREIGN KEY ([CreatedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_Purchases_AspNetUsers_ModifiedById] FOREIGN KEY ([ModifiedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_Purchases_Clients_ClientId] FOREIGN KEY ([ClientId]) REFERENCES [Clients] ([Id]),
    CONSTRAINT [FK_Purchases_Warehouses_WarehouseId] FOREIGN KEY ([WarehouseId]) REFERENCES [Warehouses] ([Id])
);
GO


CREATE TABLE [Sales] (
    [Id] int NOT NULL IDENTITY,
    [InvoiceNo] int NOT NULL,
    [ClientId] int NOT NULL,
    [WarehouseId] int NOT NULL,
    [SaleDate] datetime2 NOT NULL,
    [TotalBeforeDiscount] decimal(18,3) NOT NULL,
    [TotalDiscount] decimal(18,3) NOT NULL,
    [TotalAmount] decimal(18,3) NOT NULL,
    [TotalReturned] decimal(18,3) NOT NULL,
    [Notes] nvarchar(500) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [CreatedById] int NULL,
    [ModifiedById] int NULL,
    CONSTRAINT [PK_Sales] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Sales_AspNetUsers_CreatedById] FOREIGN KEY ([CreatedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_Sales_AspNetUsers_ModifiedById] FOREIGN KEY ([ModifiedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_Sales_Clients_ClientId] FOREIGN KEY ([ClientId]) REFERENCES [Clients] ([Id]),
    CONSTRAINT [FK_Sales_Warehouses_WarehouseId] FOREIGN KEY ([WarehouseId]) REFERENCES [Warehouses] ([Id])
);
GO


CREATE TABLE [Lenses] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Sphere] bit NOT NULL,
    [Cylinder] bit NOT NULL,
    [Power] bit NOT NULL,
    [Axis] decimal(18,3) NULL,
    [Addtion] decimal(18,3) NULL,
    [BC] decimal(18,3) NULL,
    [Dia] decimal(18,3) NULL,
    [Exp] bit NOT NULL,
    [MinimumQuantity] int NOT NULL,
    [CategoryId] int NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [CreatedById] int NULL,
    [ModifiedById] int NULL,
    CONSTRAINT [PK_Lenses] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Lenses_AspNetUsers_CreatedById] FOREIGN KEY ([CreatedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_Lenses_AspNetUsers_ModifiedById] FOREIGN KEY ([ModifiedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_Lenses_LensCategories_CategoryId] FOREIGN KEY ([CategoryId]) REFERENCES [LensCategories] ([Id])
);
GO


CREATE TABLE [DiscountProducts] (
    [Id] int NOT NULL IDENTITY,
    [DiscountId] int NOT NULL,
    [ProductId] int NOT NULL,
    CONSTRAINT [PK_DiscountProducts] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_DiscountProducts_Discounts_DiscountId] FOREIGN KEY ([DiscountId]) REFERENCES [Discounts] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_DiscountProducts_Products_ProductId] FOREIGN KEY ([ProductId]) REFERENCES [Products] ([Id])
);
GO


CREATE TABLE [ProductColors] (
    [Id] int NOT NULL IDENTITY,
    [ProductId] int NULL,
    [ColorId] tinyint NULL,
    [Barcode] nvarchar(30) NULL,
    CONSTRAINT [PK_ProductColors] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ProductColors_Colors_ColorId] FOREIGN KEY ([ColorId]) REFERENCES [Colors] ([Id]),
    CONSTRAINT [FK_ProductColors_Products_ProductId] FOREIGN KEY ([ProductId]) REFERENCES [Products] ([Id]) ON DELETE CASCADE
);
GO


CREATE TABLE [DiscountServices] (
    [Id] int NOT NULL IDENTITY,
    [DiscountId] int NOT NULL,
    [ServiceId] int NOT NULL,
    CONSTRAINT [PK_DiscountServices] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_DiscountServices_Discounts_DiscountId] FOREIGN KEY ([DiscountId]) REFERENCES [Discounts] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_DiscountServices_Services_ServiceId] FOREIGN KEY ([ServiceId]) REFERENCES [Services] ([Id])
);
GO


CREATE TABLE [Receipts] (
    [Id] int NOT NULL IDENTITY,
    [ReceiptNo] int NOT NULL,
    [Statement] nvarchar(500) NULL,
    [Date] datetime2 NOT NULL,
    [IsExchange] bit NULL,
    [Value] decimal(18,3) NOT NULL,
    [TreasuryId] tinyint NULL,
    [ClientId] int NULL,
    [PurchaseId] int NULL,
    [SaleId] int NULL,
    [EmployeeId] int NULL,
    [FinancialId] tinyint NULL,
    [ExpenseId] int NULL,
    [WarehouseId] int NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [CreatedById] int NULL,
    [ModifiedById] int NULL,
    CONSTRAINT [PK_Receipts] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Receipts_AspNetUsers_CreatedById] FOREIGN KEY ([CreatedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_Receipts_AspNetUsers_EmployeeId] FOREIGN KEY ([EmployeeId]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_Receipts_AspNetUsers_ModifiedById] FOREIGN KEY ([ModifiedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_Receipts_Clients_ClientId] FOREIGN KEY ([ClientId]) REFERENCES [Clients] ([Id]),
    CONSTRAINT [FK_Receipts_Expenses_ExpenseId] FOREIGN KEY ([ExpenseId]) REFERENCES [Expenses] ([Id]),
    CONSTRAINT [FK_Receipts_Financials_FinancialId] FOREIGN KEY ([FinancialId]) REFERENCES [Financials] ([Id]),
    CONSTRAINT [FK_Receipts_Purchases_PurchaseId] FOREIGN KEY ([PurchaseId]) REFERENCES [Purchases] ([Id]),
    CONSTRAINT [FK_Receipts_Sales_SaleId] FOREIGN KEY ([SaleId]) REFERENCES [Sales] ([Id]),
    CONSTRAINT [FK_Receipts_Treasuries_TreasuryId] FOREIGN KEY ([TreasuryId]) REFERENCES [Treasuries] ([Id]),
    CONSTRAINT [FK_Receipts_Warehouses_WarehouseId] FOREIGN KEY ([WarehouseId]) REFERENCES [Warehouses] ([Id]) ON DELETE SET NULL
);
GO


CREATE TABLE [DiscountLenses] (
    [Id] int NOT NULL IDENTITY,
    [DiscountId] int NOT NULL,
    [LensId] int NOT NULL,
    CONSTRAINT [PK_DiscountLenses] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_DiscountLenses_Discounts_DiscountId] FOREIGN KEY ([DiscountId]) REFERENCES [Discounts] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_DiscountLenses_Lenses_LensId] FOREIGN KEY ([LensId]) REFERENCES [Lenses] ([Id])
);
GO


CREATE TABLE [LensPrescriptions] (
    [Id] int NOT NULL IDENTITY,
    [LensId] int NULL,
    [SphereId] smallint NULL,
    [CylinderId] smallint NULL,
    [PowId] smallint NULL,
    [CostPrice] decimal(18,3) NOT NULL,
    [SellPrice] decimal(18,3) NOT NULL,
    CONSTRAINT [PK_LensPrescriptions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_LensPrescriptions_Lenses_LensId] FOREIGN KEY ([LensId]) REFERENCES [Lenses] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_LensPrescriptions_Prescriptions_CylinderId] FOREIGN KEY ([CylinderId]) REFERENCES [Prescriptions] ([Id]),
    CONSTRAINT [FK_LensPrescriptions_Prescriptions_PowId] FOREIGN KEY ([PowId]) REFERENCES [Prescriptions] ([Id]),
    CONSTRAINT [FK_LensPrescriptions_Prescriptions_SphereId] FOREIGN KEY ([SphereId]) REFERENCES [Prescriptions] ([Id])
);
GO


CREATE TABLE [ProductQuantities] (
    [Id] int NOT NULL IDENTITY,
    [ProductColorId] int NULL,
    [WarehouseId] int NOT NULL,
    [Exp] date NULL,
    [Quantity] int NOT NULL,
    CONSTRAINT [PK_ProductQuantities] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ProductQuantities_ProductColors_ProductColorId] FOREIGN KEY ([ProductColorId]) REFERENCES [ProductColors] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_ProductQuantities_Warehouses_WarehouseId] FOREIGN KEY ([WarehouseId]) REFERENCES [Warehouses] ([Id])
);
GO


CREATE TABLE [SalaryPayments] (
    [Id] int NOT NULL IDENTITY,
    [UserId] int NOT NULL,
    [PaymentDate] datetime2 NOT NULL,
    [Amount] decimal(18,3) NOT NULL,
    [Month] int NOT NULL,
    [Year] int NOT NULL,
    [Notes] nvarchar(500) NOT NULL,
    [ReceiptId] int NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [CreatedById] int NULL,
    [ModifiedById] int NULL,
    CONSTRAINT [PK_SalaryPayments] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_SalaryPayments_AspNetUsers_CreatedById] FOREIGN KEY ([CreatedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_SalaryPayments_AspNetUsers_ModifiedById] FOREIGN KEY ([ModifiedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_SalaryPayments_AspNetUsers_UserId] FOREIGN KEY ([UserId]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_SalaryPayments_Receipts_ReceiptId] FOREIGN KEY ([ReceiptId]) REFERENCES [Receipts] ([Id])
);
GO


CREATE TABLE [LensPrescriptionColors] (
    [Id] int NOT NULL IDENTITY,
    [LensPrescriptionId] int NULL,
    [ColorId] tinyint NULL,
    [Barcode] nvarchar(30) NULL,
    CONSTRAINT [PK_LensPrescriptionColors] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_LensPrescriptionColors_Colors_ColorId] FOREIGN KEY ([ColorId]) REFERENCES [Colors] ([Id]),
    CONSTRAINT [FK_LensPrescriptionColors_LensPrescriptions_LensPrescriptionId] FOREIGN KEY ([LensPrescriptionId]) REFERENCES [LensPrescriptions] ([Id]) ON DELETE CASCADE
);
GO


CREATE TABLE [LensQuantities] (
    [Id] int NOT NULL IDENTITY,
    [LensPrescriptionColorId] int NULL,
    [WarehouseId] int NOT NULL,
    [Exp] date NULL,
    [Quantity] int NOT NULL,
    CONSTRAINT [PK_LensQuantities] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_LensQuantities_LensPrescriptionColors_LensPrescriptionColorId] FOREIGN KEY ([LensPrescriptionColorId]) REFERENCES [LensPrescriptionColors] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_LensQuantities_Warehouses_WarehouseId] FOREIGN KEY ([WarehouseId]) REFERENCES [Warehouses] ([Id])
);
GO


CREATE TABLE [PurchaseItems] (
    [Id] int NOT NULL IDENTITY,
    [PurchaseId] int NOT NULL,
    [ProductColorId] int NULL,
    [LensPrescriptionColorId] int NULL,
    [Price] decimal(18,3) NOT NULL,
    [Quantity] int NOT NULL,
    [Exp] date NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [CreatedById] int NULL,
    [ModifiedById] int NULL,
    CONSTRAINT [PK_PurchaseItems] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_PurchaseItems_AspNetUsers_CreatedById] FOREIGN KEY ([CreatedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_PurchaseItems_AspNetUsers_ModifiedById] FOREIGN KEY ([ModifiedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_PurchaseItems_LensPrescriptionColors_LensPrescriptionColorId] FOREIGN KEY ([LensPrescriptionColorId]) REFERENCES [LensPrescriptionColors] ([Id]),
    CONSTRAINT [FK_PurchaseItems_ProductColors_ProductColorId] FOREIGN KEY ([ProductColorId]) REFERENCES [ProductColors] ([Id]),
    CONSTRAINT [FK_PurchaseItems_Purchases_PurchaseId] FOREIGN KEY ([PurchaseId]) REFERENCES [Purchases] ([Id]) ON DELETE CASCADE
);
GO


CREATE TABLE [SaleItems] (
    [Id] int NOT NULL IDENTITY,
    [SaleId] int NOT NULL,
    [ProductQuantityId] int NULL,
    [LensQuantityRightId] int NULL,
    [LensQuantityLeftId] int NULL,
    [ServiceId] int NULL,
    [PairId] uniqueidentifier NULL,
    [CostPrice] decimal(18,3) NOT NULL,
    [OriginalPrice] decimal(18,3) NOT NULL,
    [Discount] decimal(18,3) NOT NULL,
    [SellPrice] decimal(18,3) NOT NULL,
    [Quantity] int NOT NULL,
    [ReturnedQuantity] int NOT NULL,
    [Axis] decimal(18,3) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [CreatedById] int NULL,
    [ModifiedById] int NULL,
    CONSTRAINT [PK_SaleItems] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_SaleItems_AspNetUsers_CreatedById] FOREIGN KEY ([CreatedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_SaleItems_AspNetUsers_ModifiedById] FOREIGN KEY ([ModifiedById]) REFERENCES [AspNetUsers] ([Id]),
    CONSTRAINT [FK_SaleItems_LensQuantities_LensQuantityLeftId] FOREIGN KEY ([LensQuantityLeftId]) REFERENCES [LensQuantities] ([Id]),
    CONSTRAINT [FK_SaleItems_LensQuantities_LensQuantityRightId] FOREIGN KEY ([LensQuantityRightId]) REFERENCES [LensQuantities] ([Id]),
    CONSTRAINT [FK_SaleItems_ProductQuantities_ProductQuantityId] FOREIGN KEY ([ProductQuantityId]) REFERENCES [ProductQuantities] ([Id]),
    CONSTRAINT [FK_SaleItems_Sales_SaleId] FOREIGN KEY ([SaleId]) REFERENCES [Sales] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_SaleItems_Services_ServiceId] FOREIGN KEY ([ServiceId]) REFERENCES [Services] ([Id])
);
GO


CREATE INDEX [IX_AspNetRoleClaims_RoleId] ON [AspNetRoleClaims] ([RoleId]);
GO


CREATE UNIQUE INDEX [RoleNameIndex] ON [AspNetRoles] ([NormalizedName]) WHERE [NormalizedName] IS NOT NULL;
GO


CREATE INDEX [IX_AspNetUserClaims_UserId] ON [AspNetUserClaims] ([UserId]);
GO


CREATE INDEX [IX_AspNetUserLogins_UserId] ON [AspNetUserLogins] ([UserId]);
GO


CREATE INDEX [IX_AspNetUserRoles_RoleId] ON [AspNetUserRoles] ([RoleId]);
GO


CREATE INDEX [EmailIndex] ON [AspNetUsers] ([NormalizedEmail]);
GO


CREATE INDEX [IX_AspNetUsers_WarehouseId] ON [AspNetUsers] ([WarehouseId]);
GO


CREATE UNIQUE INDEX [UserNameIndex] ON [AspNetUsers] ([NormalizedUserName]) WHERE [NormalizedUserName] IS NOT NULL;
GO


CREATE INDEX [IX_Clients_CreatedById] ON [Clients] ([CreatedById]);
GO


CREATE INDEX [IX_Clients_IsCustomer] ON [Clients] ([IsCustomer]);
GO


CREATE INDEX [IX_Clients_IsSupplier] ON [Clients] ([IsSupplier]);
GO


CREATE INDEX [IX_Clients_ModifiedById] ON [Clients] ([ModifiedById]);
GO


CREATE UNIQUE INDEX [IX_Clients_Name] ON [Clients] ([Name]);
GO


CREATE INDEX [IX_Clients_Phone] ON [Clients] ([Phone]);
GO


CREATE INDEX [IX_Clients_WarehouseId] ON [Clients] ([WarehouseId]);
GO


CREATE INDEX [IX_DiscountLenses_DiscountId] ON [DiscountLenses] ([DiscountId]);
GO


CREATE INDEX [IX_DiscountLenses_LensId] ON [DiscountLenses] ([LensId]);
GO


CREATE INDEX [IX_DiscountProducts_DiscountId] ON [DiscountProducts] ([DiscountId]);
GO


CREATE INDEX [IX_DiscountProducts_ProductId] ON [DiscountProducts] ([ProductId]);
GO


CREATE INDEX [IX_Discounts_CreatedById] ON [Discounts] ([CreatedById]);
GO


CREATE INDEX [IX_Discounts_ModifiedById] ON [Discounts] ([ModifiedById]);
GO


CREATE INDEX [IX_DiscountServices_DiscountId] ON [DiscountServices] ([DiscountId]);
GO


CREATE INDEX [IX_DiscountServices_ServiceId] ON [DiscountServices] ([ServiceId]);
GO


CREATE INDEX [IX_Expenses_CreatedById] ON [Expenses] ([CreatedById]);
GO


CREATE INDEX [IX_Expenses_ModifiedById] ON [Expenses] ([ModifiedById]);
GO


CREATE INDEX [IX_Expenses_Name] ON [Expenses] ([Name]);
GO


CREATE INDEX [IX_Financials_CreatedById] ON [Financials] ([CreatedById]);
GO


CREATE INDEX [IX_Financials_ModifiedById] ON [Financials] ([ModifiedById]);
GO


CREATE UNIQUE INDEX [IX_Financials_Name] ON [Financials] ([Name]);
GO


CREATE INDEX [IX_LensCategories_CreatedById] ON [LensCategories] ([CreatedById]);
GO


CREATE INDEX [IX_LensCategories_ModifiedById] ON [LensCategories] ([ModifiedById]);
GO


CREATE UNIQUE INDEX [IX_LensCategories_Name] ON [LensCategories] ([Name]);
GO


CREATE INDEX [IX_Lenses_CategoryId] ON [Lenses] ([CategoryId]);
GO


CREATE INDEX [IX_Lenses_CreatedById] ON [Lenses] ([CreatedById]);
GO


CREATE INDEX [IX_Lenses_ModifiedById] ON [Lenses] ([ModifiedById]);
GO


CREATE UNIQUE INDEX [IX_Lenses_Name] ON [Lenses] ([Name]);
GO


CREATE UNIQUE INDEX [IX_LensPrescriptionColors_Barcode] ON [LensPrescriptionColors] ([Barcode]) WHERE [Barcode] IS NOT NULL;
GO


CREATE INDEX [IX_LensPrescriptionColors_ColorId] ON [LensPrescriptionColors] ([ColorId]);
GO


CREATE INDEX [IX_LensPrescriptionColors_LensPrescriptionId] ON [LensPrescriptionColors] ([LensPrescriptionId]);
GO


CREATE INDEX [IX_LensPrescriptions_CylinderId] ON [LensPrescriptions] ([CylinderId]);
GO


CREATE INDEX [IX_LensPrescriptions_LensId] ON [LensPrescriptions] ([LensId]);
GO


CREATE INDEX [IX_LensPrescriptions_LensId_SphereId_CylinderId_PowId] ON [LensPrescriptions] ([LensId], [SphereId], [CylinderId], [PowId]);
GO


CREATE INDEX [IX_LensPrescriptions_PowId] ON [LensPrescriptions] ([PowId]);
GO


CREATE INDEX [IX_LensPrescriptions_SphereId] ON [LensPrescriptions] ([SphereId]);
GO


CREATE INDEX [IX_LensQuantities_Exp] ON [LensQuantities] ([Exp]);
GO


CREATE INDEX [IX_LensQuantities_LensPrescriptionColorId] ON [LensQuantities] ([LensPrescriptionColorId]);
GO


CREATE INDEX [IX_LensQuantities_LensPrescriptionColorId_Exp] ON [LensQuantities] ([LensPrescriptionColorId], [Exp]);
GO


CREATE INDEX [IX_LensQuantities_LensPrescriptionColorId_WarehouseId] ON [LensQuantities] ([LensPrescriptionColorId], [WarehouseId]);
GO


CREATE INDEX [IX_LensQuantities_LensPrescriptionColorId_WarehouseId_Exp] ON [LensQuantities] ([LensPrescriptionColorId], [WarehouseId], [Exp]);
GO


CREATE INDEX [IX_LensQuantities_WarehouseId] ON [LensQuantities] ([WarehouseId]);
GO


CREATE UNIQUE INDEX [IX_ProductColors_Barcode] ON [ProductColors] ([Barcode]) WHERE [Barcode] IS NOT NULL;
GO


CREATE INDEX [IX_ProductColors_ColorId] ON [ProductColors] ([ColorId]);
GO


CREATE INDEX [IX_ProductColors_ProductId] ON [ProductColors] ([ProductId]);
GO


CREATE INDEX [IX_ProductQuantities_Exp] ON [ProductQuantities] ([Exp]);
GO


CREATE INDEX [IX_ProductQuantities_ProductColorId] ON [ProductQuantities] ([ProductColorId]);
GO


CREATE INDEX [IX_ProductQuantities_ProductColorId_Exp] ON [ProductQuantities] ([ProductColorId], [Exp]);
GO


CREATE INDEX [IX_ProductQuantities_ProductColorId_WarehouseId] ON [ProductQuantities] ([ProductColorId], [WarehouseId]);
GO


CREATE INDEX [IX_ProductQuantities_ProductColorId_WarehouseId_Exp] ON [ProductQuantities] ([ProductColorId], [WarehouseId], [Exp]);
GO


CREATE INDEX [IX_ProductQuantities_WarehouseId] ON [ProductQuantities] ([WarehouseId]);
GO


CREATE INDEX [IX_Products_CreatedById] ON [Products] ([CreatedById]);
GO


CREATE INDEX [IX_Products_ModifiedById] ON [Products] ([ModifiedById]);
GO


CREATE UNIQUE INDEX [IX_Products_Name] ON [Products] ([Name]);
GO


CREATE INDEX [IX_PurchaseItems_CreatedById] ON [PurchaseItems] ([CreatedById]);
GO


CREATE INDEX [IX_PurchaseItems_LensPrescriptionColorId] ON [PurchaseItems] ([LensPrescriptionColorId]);
GO


CREATE INDEX [IX_PurchaseItems_ModifiedById] ON [PurchaseItems] ([ModifiedById]);
GO


CREATE INDEX [IX_PurchaseItems_ProductColorId] ON [PurchaseItems] ([ProductColorId]);
GO


CREATE INDEX [IX_PurchaseItems_PurchaseId] ON [PurchaseItems] ([PurchaseId]);
GO


CREATE INDEX [IX_Purchases_ClientId_PurchaseDate] ON [Purchases] ([ClientId], [PurchaseDate]);
GO


CREATE INDEX [IX_Purchases_CreatedById] ON [Purchases] ([CreatedById]);
GO


CREATE INDEX [IX_Purchases_InvoiceNo] ON [Purchases] ([InvoiceNo]);
GO


CREATE INDEX [IX_Purchases_ModifiedById] ON [Purchases] ([ModifiedById]);
GO


CREATE INDEX [IX_Purchases_PurchaseDate] ON [Purchases] ([PurchaseDate]);
GO


CREATE INDEX [IX_Purchases_WarehouseId] ON [Purchases] ([WarehouseId]);
GO


CREATE INDEX [IX_Purchases_WarehouseId_InvoiceNo] ON [Purchases] ([WarehouseId], [InvoiceNo]);
GO


CREATE INDEX [IX_Receipts_ClientId] ON [Receipts] ([ClientId]);
GO


CREATE INDEX [IX_Receipts_CreatedById] ON [Receipts] ([CreatedById]);
GO


CREATE INDEX [IX_Receipts_Date] ON [Receipts] ([Date]);
GO


CREATE INDEX [IX_Receipts_EmployeeId] ON [Receipts] ([EmployeeId]);
GO


CREATE INDEX [IX_Receipts_ExpenseId] ON [Receipts] ([ExpenseId]);
GO


CREATE INDEX [IX_Receipts_FinancialId] ON [Receipts] ([FinancialId]);
GO


CREATE INDEX [IX_Receipts_IsExchange] ON [Receipts] ([IsExchange]);
GO


CREATE INDEX [IX_Receipts_ModifiedById] ON [Receipts] ([ModifiedById]);
GO


CREATE INDEX [IX_Receipts_PurchaseId] ON [Receipts] ([PurchaseId]);
GO


CREATE INDEX [IX_Receipts_ReceiptNo] ON [Receipts] ([ReceiptNo]);
GO


CREATE INDEX [IX_Receipts_SaleId] ON [Receipts] ([SaleId]);
GO


CREATE INDEX [IX_Receipts_TreasuryId] ON [Receipts] ([TreasuryId]);
GO


CREATE INDEX [IX_Receipts_WarehouseId] ON [Receipts] ([WarehouseId]);
GO


CREATE INDEX [IX_SalaryPayments_CreatedById] ON [SalaryPayments] ([CreatedById]);
GO


CREATE INDEX [IX_SalaryPayments_ModifiedById] ON [SalaryPayments] ([ModifiedById]);
GO


CREATE INDEX [IX_SalaryPayments_PaymentDate] ON [SalaryPayments] ([PaymentDate]);
GO


CREATE INDEX [IX_SalaryPayments_ReceiptId] ON [SalaryPayments] ([ReceiptId]);
GO


CREATE INDEX [IX_SalaryPayments_UserId] ON [SalaryPayments] ([UserId]);
GO


CREATE INDEX [IX_SaleItems_CreatedById] ON [SaleItems] ([CreatedById]);
GO


CREATE INDEX [IX_SaleItems_LensQuantityLeftId] ON [SaleItems] ([LensQuantityLeftId]);
GO


CREATE INDEX [IX_SaleItems_LensQuantityRightId] ON [SaleItems] ([LensQuantityRightId]);
GO


CREATE INDEX [IX_SaleItems_ModifiedById] ON [SaleItems] ([ModifiedById]);
GO


CREATE INDEX [IX_SaleItems_PairId] ON [SaleItems] ([PairId]);
GO


CREATE INDEX [IX_SaleItems_ProductQuantityId] ON [SaleItems] ([ProductQuantityId]);
GO


CREATE INDEX [IX_SaleItems_SaleId] ON [SaleItems] ([SaleId]);
GO


CREATE INDEX [IX_SaleItems_ServiceId] ON [SaleItems] ([ServiceId]);
GO


CREATE INDEX [IX_Sales_ClientId_SaleDate] ON [Sales] ([ClientId], [SaleDate]);
GO


CREATE INDEX [IX_Sales_CreatedById] ON [Sales] ([CreatedById]);
GO


CREATE INDEX [IX_Sales_InvoiceNo] ON [Sales] ([InvoiceNo]);
GO


CREATE INDEX [IX_Sales_ModifiedById] ON [Sales] ([ModifiedById]);
GO


CREATE INDEX [IX_Sales_SaleDate] ON [Sales] ([SaleDate]);
GO


CREATE INDEX [IX_Sales_WarehouseId] ON [Sales] ([WarehouseId]);
GO


CREATE INDEX [IX_Sales_WarehouseId_InvoiceNo] ON [Sales] ([WarehouseId], [InvoiceNo]);
GO


CREATE INDEX [IX_Services_CreatedById] ON [Services] ([CreatedById]);
GO


CREATE INDEX [IX_Services_ModifiedById] ON [Services] ([ModifiedById]);
GO


CREATE UNIQUE INDEX [IX_Services_Name] ON [Services] ([Name]);
GO


CREATE INDEX [IX_Treasuries_WarehouseId] ON [Treasuries] ([WarehouseId]);
GO


CREATE UNIQUE INDEX [IX_Warehouses_Code] ON [Warehouses] ([Code]);
GO


CREATE UNIQUE INDEX [IX_Warehouses_Name] ON [Warehouses] ([Name]);
GO


