﻿<Page
    x:Class="VisionPoint.UI.Reports.SalesReciept.PageLister"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converter="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.Reports.SalesReciept"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="PageLister"
    Width="800"
    Height="auto"
    Background="White"
    FlowDirection="RightToLeft"
    mc:Ignorable="d">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <converter:PriceMultiplierConverter x:Key="PriceMultiplierConverter" />
            <converter:IndexToNumberConverter x:Key="IndexToNumberConverter" />
            <converter:NullToVisibilityConverter x:Key="NullToVisibilityConverter" />
        </ResourceDictionary>
    </Page.Resources>

    <Grid Margin="32,0">
        <ListView
            x:Name="list"
            Grid.Row="5"
            Grid.RowSpan="4"
            Grid.ColumnSpan="8"
            VerticalAlignment="Center"
            HorizontalContentAlignment="Center"
            VerticalContentAlignment="Center"
            Background="{DynamicResource PageColor}"
            BorderThickness="1"
            FontFamily="pack://application:,,,/Assets/#Cairo"
            ItemsSource="{Binding}"
            ScrollViewer.HorizontalScrollBarVisibility="Hidden"
            SizeChanged="list_SizeChanged">
            <!--  Added this line  -->

            <ListView.BorderBrush>
                <SolidColorBrush Opacity="0.42" Color="Black" />
            </ListView.BorderBrush>

            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Style.Triggers>
                        <Trigger Property="Control.IsMouseOver" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="Foreground" Value="Black" />
                            <Setter Property="Padding" Value="0,8" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="False" />
                                <Condition Property="IsMouseOver" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter Property="FontWeight" Value="Thin" />
                            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextColor}" />
                        </MultiTrigger>
                    </Style.Triggers>
                    <Setter Property="Control.Background" Value="{DynamicResource PageColor}" />
                    <Setter Property="Control.HorizontalContentAlignment" Value="Center" />
                    <!--  Added this line  -->
                </Style>
            </ListView.ItemContainerStyle>

            <ListView.View>
                <GridView AllowsColumnReorder="False">
                    <GridView.ColumnHeaderContainerStyle>
                        <Style BasedOn="{StaticResource ListViewHeader}" TargetType="{x:Type GridViewColumnHeader}">
                            <Setter Property="IsEnabled" Value="False" />
                            <Setter Property="Visibility" Value="Collapsed" />
                            <Style.Triggers>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="TextElement.Foreground" Value="Black" />
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </GridView.ColumnHeaderContainerStyle>

                    <GridViewColumn Width="Auto" Header="#">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ListView}, Path=Tag}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Width="Auto" Header="الاسم">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Orientation="Vertical">
                                    <TextBlock
                                        VerticalAlignment="Center"
                                        FontWeight="Bold"
                                        Text="{Binding DataContext.ParsedItem.DisplayName, RelativeSource={RelativeSource AncestorType=Page}}"
                                        TextAlignment="Center" />
                                    <TextBlock
                                        VerticalAlignment="Center"
                                        FontWeight="Bold"
                                        Text="{Binding DataContext.ParsedItem.DisplayedType, RelativeSource={RelativeSource AncestorType=Page}}"
                                        TextAlignment="Center"
                                        Visibility="{Binding DataContext.ParsedItem.DisplayedType, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource NullToVisibilityConverter}}" />
                                    <!--  Conditionally show prescription values  -->
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition />
                                            <ColumnDefinition />
                                        </Grid.ColumnDefinitions>
                                        <Border
                                            Padding="4,0"
                                            BorderBrush="Black"
                                            BorderThickness="0.5,0.5,0.25,0.5"
                                            Visibility="{Binding DataContext.ParsedItem.SPH, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource NullToVisibilityConverter}}">
                                            <TextBlock
                                                Text="{Binding DataContext.ParsedItem.SPH, RelativeSource={RelativeSource AncestorType=Page}, StringFormat='SPH: {0}'}"
                                                TextAlignment="Center"
                                                Visibility="{Binding DataContext.ParsedItem.SPH, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource NullToVisibilityConverter}}" />
                                        </Border>
                                        <Border
                                            Grid.Column="1"
                                            Padding="4,0"
                                            BorderBrush="Black"
                                            BorderThickness="0.25,0.5,0.5,0.5"
                                            Visibility="{Binding DataContext.ParsedItem.SPH, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource NullToVisibilityConverter}}">
                                            <TextBlock
                                                Text="{Binding DataContext.ParsedItem.CYL, RelativeSource={RelativeSource AncestorType=Page}, StringFormat='CYL: {0}'}"
                                                TextAlignment="Center"
                                                Visibility="{Binding DataContext.ParsedItem.CYL, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource NullToVisibilityConverter}}" />
                                        </Border>
                                    </Grid>
                                    <Border
                                        Padding="4,0"
                                        BorderBrush="Black"
                                        BorderThickness="0.5,0.5,0.25,0.5"
                                        Visibility="{Binding DataContext.ParsedItem.ADD, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource NullToVisibilityConverter}}">
                                        <TextBlock
                                            Text="{Binding DataContext.ParsedItem.ADD, RelativeSource={RelativeSource AncestorType=Page}, StringFormat='ADD: {0}'}"
                                            TextAlignment="Center"
                                            Visibility="{Binding DataContext.ParsedItem.ADD, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource NullToVisibilityConverter}}" />
                                    </Border>
                                    <Border
                                        Padding="4,0"
                                        BorderBrush="Black"
                                        BorderThickness="0.5,0.5,0.25,0.5"
                                        Visibility="{Binding DataContext.ParsedItem.POW, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource NullToVisibilityConverter}}">
                                        <TextBlock
                                            Text="{Binding DataContext.ParsedItem.POW, RelativeSource={RelativeSource AncestorType=Page}, StringFormat='POW: {0}'}"
                                            TextAlignment="Center"
                                            Visibility="{Binding DataContext.ParsedItem.POW, RelativeSource={RelativeSource AncestorType=Page}, Converter={StaticResource NullToVisibilityConverter}}" />
                                    </Border>
                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Width="Auto" Header="اللون">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <Grid HorizontalAlignment="Stretch" VerticalAlignment="Center">
                                    <TextBlock
                                        MinWidth="35"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="Transparent"
                                        Text="{Binding ColorName}"
                                        TextAlignment="Center" />
                                </Grid>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Width="Auto" Header="الكمية">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <Grid HorizontalAlignment="Stretch" VerticalAlignment="Center">
                                    <TextBlock
                                        MinWidth="35"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="Transparent"
                                        Text="{Binding Quantity}"
                                        TextAlignment="Center" />
                                </Grid>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Width="Auto" Header="السعر">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <Grid HorizontalAlignment="Stretch" VerticalAlignment="Center">
                                    <TextBlock
                                        MinWidth="35"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="Transparent"
                                        Text="{Binding SellPrice}"
                                        TextAlignment="Center" />
                                </Grid>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>


                </GridView>
            </ListView.View>
        </ListView>
    </Grid>
</Page>
