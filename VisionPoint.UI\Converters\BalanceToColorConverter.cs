﻿﻿using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace VisionPoint.UI.Converters
{
    /// <summary>
    /// محول يقوم بتحويل قيمة الرصيد إلى لون مناسب
    /// </summary>
    public class BalanceToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal balance)
            {
                // نحتاج إلى معرفة نوع الكيان (عميل أو موظف) لتحديد اللون المناسب
                // بشكل افتراضي، نفترض أن القيمة تمثل رصيد عميل

                if (balance > 0)
                {
                    // رصيد موجب (العميل مدين للمتجر) - أحمر
                    return new SolidColorBrush(Colors.Red);
                }
                else if (balance < 0)
                {
                    // رصيد سالب (المتجر مدين للعميل) - أخضر
                    return new SolidColorBrush(Colors.Green);
                }
            }

            // رصيد صفر - أسود
            return new SolidColorBrush(Colors.Black);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
