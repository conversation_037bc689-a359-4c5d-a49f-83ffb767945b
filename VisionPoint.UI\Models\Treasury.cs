using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VisionPoint.UI.Models;

[Index(nameof(WarehouseId))]
public class Treasury
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public byte Id { get; set; }

    [StringLength(50, ErrorMessage = "اسم الخزينة يجب ألا يتجاوز 50 حرف")]
    public string Name { get; set; } = null!;

    [Precision(18, 3)]
    public decimal Balance { get; set; } = 0;

    // ربط طريقة الدفع بالمخزن - إجباري
    public int WarehouseId { get; set; }
    public Warehouse Warehouse { get; set; } = null!;

    public DateTime CreatedAt { get; set; } = DateTime.Now;
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// Row GUID for SQL Server Merge Replication - managed by database
    /// </summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid RowGuid { get; set; }
}
