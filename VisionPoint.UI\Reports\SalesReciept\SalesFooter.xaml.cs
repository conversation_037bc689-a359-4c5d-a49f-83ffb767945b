﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace VisionPoint.UI.Reports.SalesReciept
{
    /// <summary>
    /// Interaction logic for SalesFooter.xaml
    /// </summary>
    public partial class SalesFooter : Page
    {
        public SalesFooter(string Total, string Paid, string Baqi,string Note)
        {
            InitializeComponent();
            txtTotal.Text = Total;
            txtPaid.Text = Paid;
            txtBaqi.Text = Baqi;
            txtPhone.Text = Properties.Settings.Default.PhoneNumber;
        }
    }
}
