using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Models;
using VisionPoint.UI.Services;
using VisionPoint.UI.ViewModels;
using VisionPoint.UI.ViewModel;

namespace VisionPoint.UI.PL
{
    public class SaleService : IDisposable
    {
        private readonly AppDbContext _context = ServiceLocator.GetService<AppDbContext>();
        private bool _disposed = false;

        public SaleService()
        {
        }


        public async Task<(bool success, string message, int saleId,int invoiceNo)> AddSaleAsync(Sale sale, decimal? paid, byte? treasuryId)
        {
            try
            {
                var client = await _context.Clients.FindAsyncWithBusy(sale.ClientId);
                if (client != null)
                {
                    // حساب التغيير النهائي في الرصيد (إجمالي الفاتورة - المبلغ المدفوع)
                    decimal finalBalanceChange = sale.TotalAmount - (paid ?? 0);

                    // التحقق من الحد المسموح قبل تحديث الرصيد
                    var clientService = new ClientService();
                    var (isAllowed, message) = await clientService.CheckBalanceChangeAsync(sale.ClientId, finalBalanceChange);
                    if (!isAllowed)
                    {
                        return (false, message, 0, 0);
                    }

                    client.Balance += sale.TotalAmount;
                }

                // Generate invoice number for specific warehouse
                var lastInvoice = await _context.Sales
                    .Where(s => s.WarehouseId == sale.WarehouseId)
                    .OrderByDescending(s => s.InvoiceNo)
                    .FirstOrDefaultAsyncWithBusy("GetLastSaleInvoiceNumberByWarehouse");

                int nextInvoiceNumber = (lastInvoice?.InvoiceNo ?? 0) + 1;
                sale.InvoiceNo = nextInvoiceNumber;

                // الحصول على رمز المخزن وإنشاء رقم الفاتورة مع الرمز
                var warehouse = await _context.Warehouses
                    .FirstOrDefaultAsyncWithBusy(w => w.Id == sale.WarehouseId, "GetWarehouseForSaleAdd");
                string warehouseCode = warehouse?.Code ?? "UNK";
                sale.InvoiceNumber = $"{warehouseCode}-{nextInvoiceNumber}";

                // New sale
                sale.CreatedById = CurrentUser.Id;
                sale.ModifiedById = CurrentUser.Id;
                sale.CreatedAt = DateTime.Now;
                sale.UpdatedAt = DateTime.Now;

                await _context.Sales.AddAsyncWithBusy(sale);

                // For new sales (محسنة لتحديث المخزون المجمع)
                // تحديث خصائص العناصر
                foreach (var item in sale.SaleItems)
                {
                    item.CreatedById = CurrentUser.Id;
                    item.ModifiedById = CurrentUser.Id;
                    item.CreatedAt = DateTime.Now;
                    item.UpdatedAt = DateTime.Now;
                }

                // تحديث المخزون بشكل مجمع للمبيعات
                var (Success, Message) = await UpdateInventoryBatch(sale.SaleItems, sale.WarehouseId, isDecrease: true);
                if (!Success)
                {
                    _context.Reverse();
                    return (false, Message, 0, 0);
                }

                var result = await _context.SaveWithTransactionAndBusy("AddSale");

                if (!result.State)
                    return (false, result.Message, 0,0);

                if (treasuryId is not null)
                {
                    ReceiptService receiptService = new ReceiptService();
                    var receipt = new Receipt
                    {
                        SaleId = sale.Id,
                        Value = paid.Value,
                        ClientId = sale.ClientId,
                        TreasuryId = treasuryId,
                        Date = DateTime.Now,
                        IsExchange = false,
                        Statement = "فاتورة بيع رقم "+sale.InvoiceNo,
                        FinancialId = (byte)FinancialId.Sale,
                    };
                    await receiptService.CreateReceipt(receipt);
                }
                return (true, "تم حفظ الفاتورة بنجاح", sale.Id,sale.InvoiceNo);
            }
            catch (Exception ex)
            {
                _context.Reverse();
                return (false, $"حدث خطأ أثناء حفظ الفاتورة: {ex.Message}", 0,0);
            }
        }
        public async Task<(bool success, string message, int saleId, int invoiceNo)> EditSaleAsync(Sale sale)
        {
            try
            {
                // Updating existing sale
                var existingSale = await _context.Sales
                    .Include(s => s.SaleItems)
                    .FirstOrDefaultAsyncWithBusy(s => s.Id == sale.Id, "GetSaleForEdit");

                    if (existingSale == null)
                        return (false, "الفاتورة غير موجودة", 0, 0);

                    // Handle new client creation if needed
                    if (sale.ClientId == 0 && sale.Client != null)
                    {
                        // Create new client
                        await _context.Clients.AddAsyncWithBusy(sale.Client);
                        await _context.SaveWithTransactionAndBusy("CreateNewClientInEdit");
                        sale.ClientId = sale.Client.Id;
                    }

                    // التحقق من تغيير رصيد العميل إذا تغير المبلغ الإجمالي أو العميل
                    decimal balanceChange = 0;
                    if (existingSale.TotalAmount != sale.TotalAmount || existingSale.ClientId != sale.ClientId)
                    {
                        // حساب التغيير في الرصيد
                        if (existingSale.ClientId == sale.ClientId)
                        {
                            // نفس العميل، تغيير في المبلغ فقط
                            balanceChange = sale.TotalAmount - existingSale.TotalAmount;
                        }
                        else
                        {
                            // تغيير العميل، نحتاج للتحقق من كلا العميلين
                            // أولاً: إزالة المبلغ من العميل القديم
                            var clientService = new ClientService();
                            var (isAllowedOld, messageOld) = await clientService.CheckBalanceChangeAsync(existingSale.ClientId, -existingSale.TotalAmount);
                            if (!isAllowedOld)
                            {
                                return (false, $"خطأ في العميل القديم: {messageOld}", 0,0);
                            }

                            // ثانياً: إضافة المبلغ للعميل الجديد
                            var (isAllowedNew, messageNew) = await clientService.CheckBalanceChangeAsync(sale.ClientId, sale.TotalAmount);
                            if (!isAllowedNew)
                            {
                                return (false, $"خطأ في العميل الجديد: {messageNew}", 0, 0);
                            }
                        }

                        // إذا كان نفس العميل، التحقق من التغيير في المبلغ
                        if (existingSale.ClientId == sale.ClientId && balanceChange != 0)
                        {
                            var clientService = new ClientService();
                            var (isAllowed, message) = await clientService.CheckBalanceChangeAsync(sale.ClientId, balanceChange);
                            if (!isAllowed)
                            {
                                return (false, message, 0, 0);
                            }
                        }
                    }

                    // Check if warehouse changed
                    bool warehouseChanged = existingSale.WarehouseId != sale.WarehouseId;
                    int oldWarehouseId = existingSale.WarehouseId;

                    // If warehouse changed, generate new invoice number for the new warehouse
                    if (warehouseChanged)
                    {
                        var lastInvoice = await _context.Sales
                            .Where(s => s.WarehouseId == sale.WarehouseId && s.Id != existingSale.Id)
                            .OrderByDescending(s => s.InvoiceNo)
                            .FirstOrDefaultAsyncWithBusy("GetLastSaleInvoiceNumberByWarehouseForEdit");

                        int nextInvoiceNumber = (lastInvoice?.InvoiceNo ?? 0) + 1;
                        existingSale.InvoiceNo = nextInvoiceNumber;

                        // الحصول على رمز المخزن الجديد وإنشاء رقم الفاتورة مع الرمز
                        var warehouse = await _context.Warehouses
                            .FirstOrDefaultAsyncWithBusy(w => w.Id == sale.WarehouseId, "GetWarehouseForSaleEdit");
                        string warehouseCode = warehouse?.Code ?? "UNK";
                        existingSale.InvoiceNumber = $"{warehouseCode}-{nextInvoiceNumber}";
                    }

                    // Update existing sale properties
                    existingSale.ClientId = sale.ClientId;
                    existingSale.SaleDate = sale.SaleDate;
                    existingSale.TotalBeforeDiscount = sale.TotalBeforeDiscount;
                    existingSale.TotalDiscount = sale.TotalDiscount;
                    existingSale.TotalAmount = sale.TotalAmount;
                    existingSale.Notes = sale.Notes;
                    existingSale.WarehouseId = sale.WarehouseId;
                    existingSale.ModifiedById = CurrentUser.Id;
                    existingSale.UpdatedAt = DateTime.Now;

                    // Handle sale items
                    var existingItems = existingSale.SaleItems.ToList();
                    var newItems = sale.SaleItems.ToList();

                    // If warehouse changed, we need to handle all items differently
                    if (warehouseChanged)
                    {
                        // Return all existing items to old warehouse (محسنة)
                        var (returnSuccess, returnMessage) = await UpdateInventoryBatch(existingItems, oldWarehouseId, isDecrease: false);
                        if (!returnSuccess)
                        {
                            _context.Reverse();
                            return (false, returnMessage, 0, 0);
                        }

                        // Remove all existing items from database (محسنة)
                        _context.SaleItems.RemoveRangeWithBusy(existingItems);

                        // Add all new items to new warehouse (محسنة)
                        foreach (var item in newItems)
                        {
                            item.SaleId = existingSale.Id;
                            item.CreatedById = CurrentUser.Id;
                            item.ModifiedById = CurrentUser.Id;
                            item.CreatedAt = DateTime.Now;
                            item.UpdatedAt = DateTime.Now;
                        }
                        await _context.SaleItems.AddRangeAsyncWithBusy(newItems);

                        var (decreaseSuccess, decreaseMessage) = await UpdateInventoryBatch(newItems, sale.WarehouseId, isDecrease: true);
                        if (!decreaseSuccess)
                        {
                            _context.Reverse();
                            return (false, decreaseMessage, 0, 0);
                        }
                    }
                    else
                    {
                        // Normal handling when warehouse didn't change
                        // Find items to remove (in existing but not in new)
                        var itemsToRemove = existingItems
                            .Where(ei => !newItems.Any(ni =>
                                ni.Id == ei.Id &&
                                ni.Id != 0))
                            .ToList();

                        // Find items to add (in new but not in existing)
                        var itemsToAdd = newItems
                            .Where(ni => ni.Id == 0)
                            .ToList();

                        // Find items to update (in both)
                        var itemsToUpdate = existingItems
                            .Where(ei => newItems.Any(ni =>
                                ni.Id == ei.Id &&
                                ni.Id != 0))
                            .ToList();

                        // Remove items - increase inventory (محسنة)
                        var (removeSuccess, removeMessage) = await UpdateInventoryBatch(itemsToRemove, existingSale.WarehouseId, isDecrease: false);
                        if (!removeSuccess)
                        {
                            _context.Reverse();
                            return (false, removeMessage, 0, 0);
                        }
                        _context.SaleItems.RemoveRangeWithBusy(itemsToRemove);

                        // Add new items - decrease inventory (محسنة)
                        foreach (var item in itemsToAdd)
                        {
                            item.SaleId = existingSale.Id;
                            item.CreatedById = CurrentUser.Id;
                            item.ModifiedById = CurrentUser.Id;
                            item.CreatedAt = DateTime.Now;
                            item.UpdatedAt = DateTime.Now;
                        }
                        await _context.SaleItems.AddRangeAsyncWithBusy(itemsToAdd);

                        var (addSuccess, addMessage) = await UpdateInventoryBatch(itemsToAdd, existingSale.WarehouseId, isDecrease: true);
                        if (!addSuccess)
                        {
                            _context.Reverse();
                            return (false, addMessage, 0, 0);
                        }

                        // Update existing items
                        foreach (var existingItem in itemsToUpdate)
                        {
                            var newItem = newItems.First(ni => ni.Id == existingItem.Id);

                            // Update inventory based on quantity difference
                            int quantityDiff = existingItem.Quantity - newItem.Quantity;
                            if (quantityDiff != 0)
                            {
                                var (Success, Message) = await UpdateInventory(existingItem, quantityDiff, existingSale.WarehouseId); // Positive if reducing quantity, negative if increasing
                                if (!Success)
                                {
                                    _context.Reverse();
                                    return (false, Message, 0, 0);
                                }
                            }

                            // Update item properties
                            existingItem.ProductQuantityId = newItem.ProductQuantityId;
                            existingItem.LensQuantityRightId = newItem.LensQuantityRightId;
                            existingItem.LensQuantityLeftId = newItem.LensQuantityLeftId;
                            existingItem.ServiceId = newItem.ServiceId;
                            existingItem.Quantity = newItem.Quantity;
                            existingItem.CostPrice = newItem.CostPrice;
                            existingItem.OriginalPrice = newItem.OriginalPrice;
                            existingItem.Discount = newItem.Discount;
                            existingItem.SellPrice = newItem.SellPrice;
                            existingItem.Axis = newItem.Axis;
                            existingItem.ModifiedById = CurrentUser.Id;
                            existingItem.UpdatedAt = DateTime.Now;
                        }
                    }

                var result = await _context.SaveWithTransactionAndBusy("EditSale");
                return result.State ?
                    (true, "تم تحديث الفاتورة بنجاح", existingSale.Id, existingSale.InvoiceNo) :
                    (false, result.Message, existingSale.Id, existingSale.InvoiceNo);
            }
            catch (Exception ex)
            {
                _context.Reverse();
                return (false, $"حدث خطأ أثناء حفظ الفاتورة: {ex.Message}", 0, 0);
            }
        }

        private async Task<(bool Success, string Message)> UpdateInventory(SaleItem item, int quantityChange, int warehouseId)
        {
            // التحقق مما إذا كان مسموح بالكميات السالبة
            bool allowNegativeQuantities = Properties.Settings.Default.AllowNegativeQuantities;

            // For product items
            if (item.ProductQuantityId.HasValue)
            {
                var productQuantity = await _context.ProductQuantities
                    .FirstOrDefaultAsyncWithBusy(pq => pq.Id == item.ProductQuantityId && pq.WarehouseId == warehouseId, "GetProductQuantity");

                    if (productQuantity == null)
                        return (false, "المنتج غير موجود في المخزون المحدد");

                    // التحقق من أن الكمية لن تصبح سالبة إذا كان غير مسموح بذلك
                    if (!allowNegativeQuantities && (productQuantity.Quantity + quantityChange) < 0)
                    {
                        return (false, $"لا يمكن إتمام العملية لأن الكمية ستصبح بالسالب ({productQuantity.Quantity + quantityChange})، والنظام لا يسمح بالكميات السالبة");
                    }

                    productQuantity.Quantity += quantityChange;
                }
                // For right lens items
                else if (item.LensQuantityRightId.HasValue)
                {
                    var lensQuantity = await _context.LensQuantities
                        .FirstOrDefaultAsyncWithBusy(lq => lq.Id == item.LensQuantityRightId && lq.WarehouseId == warehouseId, "GetRightLensQuantity");

                    if (lensQuantity == null)
                        return (false, "العدسة اليمنى غير موجودة في المخزون المحدد");

                    // التحقق من أن الكمية لن تصبح سالبة إذا كان غير مسموح بذلك
                    if (!allowNegativeQuantities && (lensQuantity.Quantity + quantityChange) < 0)
                    {
                        return (false, $"لا يمكن إتمام العملية لأن كمية العدسة اليمنى ستصبح بالسالب ({lensQuantity.Quantity + quantityChange})، والنظام لا يسمح بالكميات السالبة");
                    }

                    lensQuantity.Quantity += quantityChange;
                }
                // For left lens items
                else if (item.LensQuantityLeftId.HasValue)
                {
                    var lensQuantity = await _context.LensQuantities
                        .FirstOrDefaultAsyncWithBusy(lq => lq.Id == item.LensQuantityLeftId && lq.WarehouseId == warehouseId, "GetLeftLensQuantity");

                    if (lensQuantity == null)
                        return (false, "العدسة اليسرى غير موجودة في المخزون المحدد");

                    // التحقق من أن الكمية لن تصبح سالبة إذا كان غير مسموح بذلك
                    if (!allowNegativeQuantities && (lensQuantity.Quantity + quantityChange) < 0)
                    {
                        return (false, $"لا يمكن إتمام العملية لأن كمية العدسة اليسرى ستصبح بالسالب ({lensQuantity.Quantity + quantityChange})، والنظام لا يسمح بالكميات السالبة");
                    }

                    lensQuantity.Quantity += quantityChange;
            }
            return (true, "تم تحديث المخزون بنجاح");
        }

        public async Task<(bool success, string message)> DeleteSaleAsync(int id)
        {
            try
            {
                var sale = await _context.Sales
                    .Include(s => s.SaleItems)
                    .FirstOrDefaultAsyncWithBusy(s => s.Id == id, "GetSaleForDelete");

                if (sale == null)
                    return (false, "الفاتورة غير موجودة");

                // التحقق من وجود إيصالات مرتبطة بفاتورة البيع
                bool hasReceipts = await _context.Receipts
                    .AnyAsyncWithBusy(r => r.SaleId == id, "CheckSaleReceipts");

                    if (hasReceipts)
                    {
                        return (false, "لا يمكن حذف الفاتورة لأنها مرتبطة بإيصالات");
                    }

                    // التحقق من إمكانية تحديث رصيد العميل قبل الحذف
                    var clientService = new ClientService();
                    var (isAllowed, message) = await clientService.CheckBalanceChangeAsync(sale.ClientId, -sale.TotalAmount);
                    if (!isAllowed)
                    {
                        return (false, $"لا يمكن حذف الفاتورة: {message}");
                    }

                    // Update client balance (reverse the sale)
                    var client = await _context.Clients.FindAsyncWithBusy(sale.ClientId);
                    if (client != null)
                        client.Balance -= sale.TotalAmount;

                    // Return quantities to inventory (محسنة للتحديث المجمع)
                    var (Success, Message) = await UpdateInventoryBatch(sale.SaleItems, sale.WarehouseId, isDecrease: false);
                    if (!Success)
                    {
                        _context.Reverse();
                        return (false, Message);
                    }

                    // Delete the sale
                    _context.Sales.RemoveWithBusy(sale);
                    var result = await _context.SaveWithTransactionAndBusy("DeleteSale");

                return result.State ?
                    (true, "تم حذف الفاتورة بنجاح") :
                    (false, result.Message);
            }
            catch (Exception ex)
            {
                _context.Reverse();
                return (false, $"حدث خطأ أثناء حذف الفاتورة: {ex.Message}");
            }
        }

        public async Task<SaleViewModel> GetSaleByInvoiceNoAsync(int invoiceNo)
        {
            var sale = await _context.Sales
                .Include(s => s.Client)
                .Include(s => s.Warehouse)
                .Include(s => s.SaleItems)
                    .ThenInclude(item => item.ProductQuantity)
                    .ThenInclude(pq => pq.ProductColor)
                    .ThenInclude(pc => pc.Color)
                .Include(s => s.SaleItems)
                    .ThenInclude(item => item.ProductQuantity)
                    .ThenInclude(pq => pq.ProductColor)
                    .ThenInclude(pc => pc.Product)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityRight)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.LensPrescription)
                            .ThenInclude(lp => lp.Lens).ThenInclude(l => l.Category)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityRight)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.Color)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityRight)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.LensPrescription)
                            .ThenInclude(lp => lp.Sphere)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityRight)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.LensPrescription)
                            .ThenInclude(lp => lp.Cylinder)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityRight)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.LensPrescription)
                            .ThenInclude(lp => lp.Pow)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityLeft)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.LensPrescription)
                            .ThenInclude(lp => lp.Lens).ThenInclude(l => l.Category)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityLeft)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.Color)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityLeft)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.LensPrescription)
                            .ThenInclude(lp => lp.Sphere)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityLeft)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.LensPrescription)
                            .ThenInclude(lp => lp.Cylinder)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityLeft)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.LensPrescription)
                            .ThenInclude(lp => lp.Pow)
                .Include(s => s.SaleItems)
                    .ThenInclude(item => item.Service)
                .Include(s => s.Receipts)
                .FirstOrDefaultAsyncWithBusy(s => s.InvoiceNo == invoiceNo, "GetSaleDetailsByInvoiceNo");

            if (sale == null)
                return null;

                // Calculate paid amount from receipts
                decimal paidAmount = sale.Receipts?.Sum(r => r.Value) ?? 0;

                // Map to view model
                var viewModel = new SaleViewModel
                {
                    Id = sale.Id,
                    InvoiceNo = sale.InvoiceNo,
                    SaleDate = sale.SaleDate,
                    ClientName = sale.Client.Name,
                    TotalBeforeDiscount = sale.TotalBeforeDiscount,
                    TotalDiscount = sale.TotalDiscount,
                    TotalAmount = sale.TotalAmount,
                    TotalReturned = sale.TotalReturned,
                    PaidAmount = paidAmount,
                    Sale = sale,
                    SaleItems = MapSaleItemsToViewModel(sale.SaleItems)
                };

            return viewModel;
        }

        public async Task<SaleViewModel> GetSaleByIdAsync(int id)
        {
            var sale = await _context.Sales
                .Include(s => s.Client)
                .Include(s => s.Warehouse)
                .Include(s => s.SaleItems)
                    .ThenInclude(item => item.ProductQuantity)
                    .ThenInclude(pq => pq.ProductColor)
                    .ThenInclude(pc => pc.Color)
                .Include(s => s.SaleItems)
                    .ThenInclude(item => item.ProductQuantity)
                    .ThenInclude(pq => pq.ProductColor)
                    .ThenInclude(pc => pc.Product)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityRight)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.LensPrescription)
                            .ThenInclude(lp => lp.Lens).ThenInclude(l => l.Category)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityRight)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.Color)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityRight)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.LensPrescription)
                            .ThenInclude(lp => lp.Sphere)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityRight)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.LensPrescription)
                            .ThenInclude(lp => lp.Cylinder)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityRight)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.LensPrescription)
                            .ThenInclude(lp => lp.Pow)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityLeft)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.LensPrescription)
                            .ThenInclude(lp => lp.Lens).ThenInclude(l => l.Category)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityLeft)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.Color)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityLeft)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.LensPrescription)
                            .ThenInclude(lp => lp.Sphere)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityLeft)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.LensPrescription)
                            .ThenInclude(lp => lp.Cylinder)
                        .Include(s => s.SaleItems)
                            .ThenInclude(item => item.LensQuantityLeft)
                            .ThenInclude(lq => lq.LensPrescriptionColor)
                            .ThenInclude(lq => lq.LensPrescription)
                            .ThenInclude(lp => lp.Pow)
                .Include(s => s.SaleItems)
                    .ThenInclude(item => item.Service)
                .Include(s => s.Receipts)
                .FirstOrDefaultAsyncWithBusy(s => s.Id == id, "GetSaleDetails");

            if (sale == null)
                return null;

                // Calculate paid amount from receipts
                decimal paidAmount = sale.Receipts?.Sum(r => r.Value) ?? 0;

                // Map to view model
                var viewModel = new SaleViewModel
                {
                    Id = sale.Id,
                    InvoiceNo = sale.InvoiceNo,
                    SaleDate = sale.SaleDate,
                    ClientName = sale.Client.Name,
                    TotalBeforeDiscount = sale.TotalBeforeDiscount,
                    TotalDiscount = sale.TotalDiscount,
                    TotalAmount = sale.TotalAmount,
                    TotalReturned = sale.TotalReturned,
                    PaidAmount = paidAmount,
                    Sale = sale,
                    SaleItems = MapSaleItemsToViewModel(sale.SaleItems)
                };

            return viewModel;
        }

        /// <summary>
        /// الحصول على أصناف فاتورة معينة
        /// </summary>
        /// <param name="saleId">معرف الفاتورة</param>
        /// <returns>قائمة بأصناف الفاتورة</returns>
        public async Task<List<SaleItemVM>> GetSaleItemsAsync(int saleId)
        {
            var items = await _context.SaleItems
                .Include(si => si.Sale)
                    .ThenInclude(s => s.Client)
                .Include(si => si.Sale)
                    .ThenInclude(s => s.Warehouse)
                .Include(si => si.ProductQuantity)
                    .ThenInclude(pq => pq.ProductColor)
                    .ThenInclude(pc => pc.Product)
                .Include(si => si.ProductQuantity)
                    .ThenInclude(pq => pq.ProductColor)
                    .ThenInclude(pc => pc.Color)
                .Include(si => si.LensQuantityRight)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Lens)
                    .ThenInclude(l => l.Category)
                .Include(si => si.LensQuantityRight)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Sphere)
                .Include(si => si.LensQuantityRight)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Cylinder)
                .Include(si => si.LensQuantityRight)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Pow)
                .Include(si => si.LensQuantityRight)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.Color)
                .Include(si => si.LensQuantityLeft)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Lens)
                    .ThenInclude(l => l.Category)
                .Include(si => si.LensQuantityLeft)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Sphere)
                .Include(si => si.LensQuantityLeft)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Cylinder)
                .Include(si => si.LensQuantityLeft)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Pow)
                .Include(si => si.LensQuantityLeft)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.Color)
                .Include(si => si.Service)
                .Where(si => si.SaleId == saleId)
                .ToListAsyncWithBusy("GetSaleItems");

            return MapSaleItemsToViewModel(items);
        }

        /// <summary>
        /// الحصول على أصناف عدة فواتير في استعلام واحد محسن
        /// </summary>
        /// <param name="saleIds">قائمة معرفات الفواتير</param>
        /// <returns>قائمة بأصناف الفواتير</returns>
        public async Task<List<SaleItemVM>> GetSaleItemsBySaleIdsAsync(List<int> saleIds)
        {
            if (saleIds == null || !saleIds.Any())
                return new List<SaleItemVM>();

            var items = await _context.SaleItems
                .Include(si => si.Sale)
                    .ThenInclude(s => s.Client)
                .Include(si => si.Sale)
                    .ThenInclude(s => s.Warehouse)
                .Include(si => si.ProductQuantity)
                    .ThenInclude(pq => pq.ProductColor)
                    .ThenInclude(pc => pc.Product)
                .Include(si => si.ProductQuantity)
                    .ThenInclude(pq => pq.ProductColor)
                    .ThenInclude(pc => pc.Color)
                .Include(si => si.LensQuantityRight)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Lens)
                    .ThenInclude(l => l.Category)
                .Include(si => si.LensQuantityRight)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Sphere)
                .Include(si => si.LensQuantityRight)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Cylinder)
                .Include(si => si.LensQuantityRight)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Pow)
                .Include(si => si.LensQuantityRight)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.Color)
                .Include(si => si.LensQuantityLeft)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Lens)
                    .ThenInclude(l => l.Category)
                .Include(si => si.LensQuantityLeft)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Sphere)
                .Include(si => si.LensQuantityLeft)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Cylinder)
                .Include(si => si.LensQuantityLeft)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.LensPrescription)
                    .ThenInclude(lp => lp.Pow)
                .Include(si => si.LensQuantityLeft)
                    .ThenInclude(lq => lq.LensPrescriptionColor)
                    .ThenInclude(lpc => lpc.Color)
                .Include(si => si.Service)
                .Where(si => saleIds.Contains(si.SaleId))
                .ToListAsyncWithBusy("GetSaleItemsBySaleIds");

            return MapSaleItemsToViewModel(items);
        }

        private List<SaleItemVM> MapSaleItemsToViewModel(IEnumerable<SaleItem> items)
        {
            // محسنة باستخدام LINQ Select بدلاً من foreach
            return items.Select(item =>
            {
                var vm = new SaleItemVM
                {
                    Id = item.Id,
                    SaleId = item.SaleId,
                    ProductQuantityId = item.ProductQuantityId,
                    LensQuantityRightId = item.LensQuantityRightId,
                    LensQuantityLeftId = item.LensQuantityLeftId,
                    ServiceId = item.ServiceId,
                    PairId = item.PairId, // إضافة معرف الزوج
                    CostPrice = item.CostPrice,
                    OriginalPrice = item.OriginalPrice,
                    Discount = item.Discount,
                    SellPrice = item.SellPrice,
                    Quantity = item.Quantity,
                    ReturnedQuantity = item.ReturnedQuantity,
                    Axis = item.Axis,
                    ClientName = item.Sale?.Client?.Name ?? "غير معروف",
                    WarehouseName = item.Sale?.Warehouse?.Name ?? "غير محدد"
                };

                // Set the name and type based on what type of item it is
                if (item.ProductQuantity != null)
                {
                    vm.Type = "منتج";
                    vm.Name = item.ProductQuantity.ProductColor.Product.Name;
                    if (item.ProductQuantity.ProductColor.Color != null)
                    {
                        vm.ColorName = item.ProductQuantity.ProductColor.Color.Name;
                    }
                }
                else if (item.LensQuantityRight != null || item.LensQuantityLeft != null)
                {
                    vm.Type = "عدسة";

                    // Create detailed name for lens
                    string lensName = "";
                    string lensSpecs = "";
                    string eyeSide = "";

                    if (item.LensQuantityRight != null)
                    {
                        var lensColor = item.LensQuantityRight.LensPrescriptionColor;
                        var lensPrescription = lensColor?.LensPrescription;
                        var lens = lensPrescription?.Lens;

                        if (lens != null)
                        {
                            lensName = lens.Name+" | "+lens.Category.Name;

                            // Add prescription values if available
                            var specs = new List<string>();

                            if (lensPrescription.Sphere != null)
                                specs.Add($"SPH: {(lensPrescription.Sphere.Value == 0 ? "PL" : lensPrescription.Sphere.Value.ToString("F2"))}");

                            if (lensPrescription.Cylinder != null)
                                specs.Add($"CYL: {(lensPrescription.Cylinder.Value == 0 ? "PL" : lensPrescription.Cylinder.Value.ToString("F2"))}");

                            if (lensPrescription.Pow != null)
                                specs.Add($"POW: {(lensPrescription.Pow.Value == 0 ? "PL" : lensPrescription.Pow.Value.ToString("F2"))}");

                            if (item.Axis.HasValue && item.Axis.Value != 0)
                                specs.Add($"AXIS: {item.Axis.Value:F1}");

                            if (lens.Addtion.HasValue && lens.Addtion.Value != 0)
                                specs.Add($"ADD: {lens.Addtion.Value:F2}");

                            if (lens.BC.HasValue && lens.BC.Value != 0)
                                specs.Add($"BC: {lens.BC.Value:F1}");

                            if (lens.Dia.HasValue && lens.Dia.Value != 0)
                                specs.Add($"DIA: {lens.Dia.Value:F1}");

                            lensSpecs = string.Join(" / ", specs);
                            eyeSide = "يمين";

                            // Set color name if available
                            if (lensColor?.Color != null)
                            {
                                vm.ColorName = lensColor.Color.Name;
                            }
                        }
                    }
                    else if (item.LensQuantityLeft != null)
                    {
                        var lensColor = item.LensQuantityLeft.LensPrescriptionColor;
                        var lensPrescription = lensColor?.LensPrescription;
                        var lens = lensPrescription?.Lens;

                        if (lens != null)
                        {
                            lensName = lens.Name;

                            // Add prescription values if available
                            var specs = new List<string>();

                            if (lensPrescription.Sphere != null)
                                specs.Add($"SPH: {(lensPrescription.Sphere.Value == 0 ? "PL" : lensPrescription.Sphere.Value.ToString("F2"))}");

                            if (lensPrescription.Cylinder != null)
                                specs.Add($"CYL: {(lensPrescription.Cylinder.Value == 0 ? "PL" : lensPrescription.Cylinder.Value.ToString("F2"))}");

                            if (lensPrescription.Pow != null)
                                specs.Add($"POW: {(lensPrescription.Pow.Value == 0 ? "PL" : lensPrescription.Pow.Value.ToString("F2"))}");

                            if (item.Axis.HasValue && item.Axis.Value != 0)
                                specs.Add($"AXIS: {item.Axis.Value:F1}");

                            if (lens.Addtion.HasValue && lens.Addtion.Value != 0)
                                specs.Add($"ADD: {lens.Addtion.Value:F2}");

                            if (lens.BC.HasValue && lens.BC.Value != 0)
                                specs.Add($"BC: {lens.BC.Value:F1}");

                            if (lens.Dia.HasValue && lens.Dia.Value != 0)
                                specs.Add($"DIA: {lens.Dia.Value:F1}");

                            lensSpecs = string.Join(" / ", specs);
                            eyeSide = "يسار";

                            // Set color name if available
                            if (lensColor?.Color != null)
                            {
                                vm.ColorName = lensColor.Color.Name;
                            }
                        }
                    }

                    // Construct the full lens name
                    if (!string.IsNullOrEmpty(lensName))
                    {
                        if (!string.IsNullOrEmpty(lensSpecs))
                        {
                            vm.Name = $"{lensName} - {lensSpecs} - {eyeSide}";
                        }
                        else
                        {
                            vm.Name = $"{lensName} - {eyeSide}";
                        }
                    }
                    else
                    {
                        vm.Name = "عدسة"; // Default value if no lens details are available
                    }
                }
                else if (item.Service != null)
                {
                    vm.Type = "خدمة";
                    vm.Name = item.Service.Name;
                }

                return vm;
            }).ToList();
        }

        public async Task<List<SaleViewModel>> GetAllSalesAsync()
        {
            var sales = await _context.Sales
                .Include(s => s.Client)
                .Include(s => s.Warehouse)
                .Include(s => s.SaleItems)
                .Include(s => s.Receipts)
                .ToListAsyncWithBusy("GetAllSales");

            return sales.Select((sale, index) => new SaleViewModel
            {
                Index = index + 1,
                Id = sale.Id,
                InvoiceNo = sale.InvoiceNo,
                InvoiceNumber = sale.InvoiceNumber,
                SaleDate = sale.SaleDate,
                ClientName = sale.Client.Name ?? "غير معروف",
                WarehouseName = sale.Warehouse?.Name ?? "غير محدد",
                TotalBeforeDiscount = sale.TotalBeforeDiscount,
                TotalDiscount = sale.TotalDiscount,
                TotalAmount = sale.TotalAmount,
                TotalReturned = sale.TotalReturned,
                PaidAmount = sale.Receipts.Sum(r => r.Value)
            }).ToList();
        }

        /// <summary>
        /// الحصول على فواتير المبيعات مع تطبيق عوامل التصفية
        /// </summary>
        /// <param name="fromDate">تاريخ البداية (اختياري)</param>
        /// <param name="toDate">تاريخ النهاية (اختياري)</param>
        /// <param name="clientId">معرف العميل (اختياري)</param>
        /// <param name="hasRemaining">فواتير بها رصيد متبقي (اختياري)</param>
        /// <param name="invoiceId">رقم الفاتورة (اختياري)</param>
        /// <returns>قائمة بفواتير المبيعات المصفاة</returns>
        public async Task<List<SaleViewModel>> GetFilteredSalesAsync(
            DateTime? fromDate = null,
            DateTime? toDate = null,
            int? clientId = null,
            bool? hasRemaining = null,
            int? invoiceId = null)
        {
            // بناء الاستعلام الأساسي
            var query = _context.Sales
                .Include(s => s.Client)
                .Include(s => s.SaleItems)
                .Include(s => s.Receipts)
                .AsQueryable();

                // تطبيق عوامل التصفية
                if (fromDate.HasValue)
                {
                    // تحويل التاريخ إلى بداية اليوم
                    var startDate = fromDate.Value.Date;
                    query = query.Where(s => s.SaleDate >= startDate);
                }

                if (toDate.HasValue)
                {
                    // تحويل التاريخ إلى نهاية اليوم
                    var endDate = toDate.Value.Date.AddDays(1).AddTicks(-1);
                    query = query.Where(s => s.SaleDate <= endDate);
                }

                if (clientId.HasValue)
                {
                    query = query.Where(s => s.ClientId == clientId.Value);
                }

                if (invoiceId.HasValue)
                {
                    query = query.Where(s => s.InvoiceNo == invoiceId.Value);
                }

                // تنفيذ الاستعلام وتحويل النتائج إلى قائمة
                var sales = await query.ToListAsyncWithBusy("GetFilteredSales");

                // تحويل النتائج إلى نموذج العرض
                var result = sales.Select((sale, index) => new SaleViewModel
                {
                    Index = index + 1,
                    Id = sale.Id,
                    InvoiceNo = sale.InvoiceNo,
                    InvoiceNumber = sale.InvoiceNumber,
                    SaleDate = sale.SaleDate,
                    ClientName = sale.Client.Name ?? "غير معروف",
                    WarehouseName = sale.Warehouse?.Name ?? "غير محدد",
                    TotalBeforeDiscount = sale.TotalBeforeDiscount,
                    TotalDiscount = sale.TotalDiscount,
                    TotalAmount = sale.TotalAmount,
                    TotalReturned = sale.TotalReturned,
                    PaidAmount = sale.Receipts.Sum(r => r.Value)
                }).ToList();

                // تطبيق تصفية المتبقي (يتم تطبيقه بعد حساب المبالغ المدفوعة)
                if (hasRemaining.HasValue)
                {
                    if (hasRemaining.Value)
                    {
                        // فواتير بها رصيد متبقي (المبلغ الصافي > المبلغ المدفوع)
                        result = result.Where(s => (s.TotalAmount - s.TotalReturned) > s.PaidAmount).ToList();
                    }
                    else
                    {
                        // فواتير مدفوعة بالكامل (المبلغ الصافي = المبلغ المدفوع)
                        result = result.Where(s => (s.TotalAmount - s.TotalReturned) <= s.PaidAmount).ToList();
                    }
                }

                // إعادة ترقيم الفواتير بعد التصفية (محسنة - دمج مع إنشاء النتيجة)
                result = result.Select((sale, index) =>
                {
                    sale.Index = index + 1;
                    return sale;
                }).ToList();

            return result;
        }

        public async Task<int?> GetClientBySaleId(int saleId)
        {
            var sale = await _context.Sales
                .FirstOrDefaultAsyncWithBusy(s => s.Id == saleId, "GetClientBySaleId");
            if (sale != null)
            {
                return sale.ClientId;
            }
            return null;
        }

        /// <summary>
        /// جلب فواتير المبيعات التي بها بواقي للعميل المحدد
        /// يستخدم استعلام محسن لجلب البيانات المطلوبة فقط
        /// </summary>
        public async Task<List<PendingInvoiceViewModel>> GetPendingSalesByClientIdAsync(int clientId)
        {
            // جلب فواتير المبيعات للعميل مع المبالغ المدفوعة
            var salesWithPayments = await (from sale in _context.Sales
                                          where sale.ClientId == clientId
                                          select new
                                          {
                                              sale.Id,
                                              sale.InvoiceNo,
                                              sale.TotalAmount,
                                              sale.SaleDate,
                                              Receipts = _context.Receipts.Where(r => r.SaleId == sale.Id).ToList()
                                          }).ToListAsyncWithBusy("GetSalesWithReceipts");

            // معالجة البيانات في الذاكرة لتجنب مشكلة aggregate functions
            var pendingInvoices = salesWithPayments
                .Select(s => new
                {
                    s.Id,
                    s.InvoiceNo,
                    s.TotalAmount,
                    s.SaleDate,
                    PaidAmount = s.Receipts.Sum(r => r.Value)
                })
                .Where(s => s.TotalAmount > s.PaidAmount) // فلترة الفواتير التي بها بواقي
                .OrderBy(s => s.SaleDate) // ترتيب حسب التاريخ (الأقدم أولاً)
                .Select(s => new PendingInvoiceViewModel
                {
                    InvoiceId = s.Id,
                    InvoiceNo = s.InvoiceNo,
                    TotalAmount = s.TotalAmount,
                    PaidAmount = s.PaidAmount,
                    RemainingAmount = s.TotalAmount - s.PaidAmount,
                    Date = s.SaleDate,
                    IsSale = true
                })
                .ToList();

            return pendingInvoices;
        }

        /// <summary>
        /// التحقق من وجود فواتير مبيعات متبقية للعميل
        /// </summary>
        public async Task<bool> HasPendingSalesForClientAsync(int clientId)
        {
            // جلب فواتير العميل مع الإيصالات
            var salesWithReceipts = await _context.Sales
                .Where(s => s.ClientId == clientId)
                .Include(s => s.Receipts)
                .ToListAsyncWithBusy("GetSalesForPendingCheck");

            // التحقق في الذاكرة لتجنب مشكلة aggregate functions
            return salesWithReceipts.Any(s => s.TotalAmount > (s.Receipts?.Sum(r => r.Value) ?? 0));
        }

        /// <summary>
        /// حساب إجمالي المبلغ المتبقي لفواتير المبيعات للعميل
        /// </summary>
        public async Task<decimal> GetTotalRemainingSalesAmountForClientAsync(int clientId)
        {
            // جلب فواتير العميل مع الإيصالات
            var salesWithReceipts = await _context.Sales
                .Where(s => s.ClientId == clientId)
                .Include(s => s.Receipts)
                .ToListAsyncWithBusy("GetSalesForTotalRemaining");

            // حساب المبلغ المتبقي في الذاكرة لتجنب مشكلة aggregate functions
            return salesWithReceipts
                .Where(s => s.TotalAmount > (s.Receipts?.Sum(r => r.Value) ?? 0))
                .Sum(s => s.TotalAmount - (s.Receipts?.Sum(r => r.Value) ?? 0));
        }

        /// <summary>
        /// استرجاع كمية من عنصر في الفاتورة
        /// </summary>
        /// <param name="saleItemId">معرف عنصر الفاتورة</param>
        /// <param name="returnQuantity">الكمية المراد استرجاعها</param>
        /// <returns>نتيجة العملية ورسالة</returns>
        public async Task<(bool success, string message)> ReturnItemAsync(int saleItemId, int returnQuantity)
        {
            try
            {
                // التحقق من صحة البيانات
                if (returnQuantity <= 0)
                    return (false, "يجب أن تكون الكمية المسترجعة أكبر من صفر");

                // البحث عن عنصر الفاتورة
                var saleItem = await _context.SaleItems
                    .Include(si => si.Sale)
                    .FirstOrDefaultAsyncWithBusy(si => si.Id == saleItemId, "GetSaleItemForReturn");

                    if (saleItem == null)
                        return (false, "عنصر الفاتورة غير موجود");

                    // التحقق من أن الكمية المسترجعة لا تتجاوز الكمية المتبقية
                    int remainingQuantity = saleItem.Quantity - saleItem.ReturnedQuantity;
                    if (returnQuantity > remainingQuantity)
                        return (false, $"الكمية المسترجعة تتجاوز الكمية المتبقية ({remainingQuantity})");

                    // حساب قيمة المبلغ المسترجع
                    decimal returnAmount = saleItem.SellPrice * returnQuantity;

                    // تحديث الكمية المسترجعة
                    saleItem.ReturnedQuantity += returnQuantity;
                    saleItem.ModifiedById = CurrentUser.Id;
                    saleItem.UpdatedAt = DateTime.Now;

                    // تحديث إجمالي المسترجعات في الفاتورة
                    saleItem.Sale.TotalReturned += returnAmount;
                    saleItem.Sale.ModifiedById = CurrentUser.Id;
                    saleItem.Sale.UpdatedAt = DateTime.Now;

                    // إعادة الكمية إلى المخزون
                    var (success, message) = await UpdateInventory(saleItem, returnQuantity, saleItem.Sale.WarehouseId);
                    if (!success)
                    {
                        _context.Reverse();
                        return (false, message);
                    }

                    // تحديث رصيد العميل
                    var client = await _context.Clients.FindAsyncWithBusy(saleItem.Sale.ClientId);
                    if (client != null)
                    {
                        // تخفيض رصيد العميل بقيمة المبلغ المسترجع
                        client.Balance -= returnAmount;
                    }

                await _context.SaveWithTransactionAndBusy("ReturnItem");
                return (true, "تم استرجاع الكمية بنجاح");
            }
            catch (Exception ex)
            {
                _context.Reverse();
                return (false, $"حدث خطأ أثناء استرجاع الكمية: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث معرف الزوج (PairId) عند حذف أحد العدسات المتزاوجة
        /// </summary>
        /// <param name="saleItemId">معرف عنصر الفاتورة المراد حذفه</param>
        /// <returns>نتيجة العملية ورسالة</returns>
        public async Task<(bool success, string message)> UpdatePairIdOnDeleteAsync(int saleItemId)
        {
            try
            {
                // البحث عن عنصر الفاتورة
                var saleItem = await _context.SaleItems
                    .FirstOrDefaultAsyncWithBusy(si => si.Id == saleItemId, "GetSaleItemForPairUpdate");

                if (saleItem == null)
                    return (false, "عنصر الفاتورة غير موجود");

                // التحقق من وجود معرف زوج
                if (saleItem.PairId.HasValue)
                {
                    // البحث عن العنصر المرتبط (العدسة الأخرى في الزوج)
                    var pairedItem = await _context.SaleItems
                        .FirstOrDefaultAsyncWithBusy(si => si.Id != saleItemId && si.PairId == saleItem.PairId, "GetPairedSaleItem");

                    if (pairedItem != null)
                    {
                        // إزالة الارتباط بين العدسات
                        pairedItem.PairId = null;
                        pairedItem.ModifiedById = CurrentUser.Id;
                        pairedItem.UpdatedAt = DateTime.Now;

                        await _context.SaveWithTransactionAndBusy("UpdatePairId");
                    }
                }

                return (true, "تم تحديث معرف الزوج بنجاح");
            }
            catch (Exception ex)
            {
                _context.Reverse();
                return (false, $"حدث خطأ أثناء تحديث معرف الزوج: {ex.Message}");
            }
        }

        /// <summary>
        /// جلب المبيعات حسب المخزن
        /// </summary>
        public async Task<List<SaleViewModel>> GetSalesByWarehouseAsync(int warehouseId)
        {
            var sales = await _context.Sales
                .Include(s => s.Client)
                .Include(s => s.Warehouse)
                .Where(s => s.WarehouseId == warehouseId)
                .OrderByDescending(s => s.SaleDate)
                .ToListAsyncWithBusy("GetSalesByWarehouse");

            return sales.Select(s => new SaleViewModel
            {
                Id = s.Id,
                InvoiceNo = s.InvoiceNo,
                SaleDate = s.SaleDate,
                ClientName = s.Client.Name,
                WarehouseName = s.Warehouse?.Name ?? "غير محدد",
                TotalBeforeDiscount = s.TotalBeforeDiscount,
                TotalDiscount = s.TotalDiscount,
                TotalAmount = s.TotalAmount,
                TotalReturned = s.TotalReturned,
                Sale = s
            }).ToList();
        }

        /// <summary>
        /// جلب المبيعات المصفاة مع دعم المخازن (محسنة لتجنب N+1 Query)
        /// </summary>
        public async Task<List<SaleViewModel>> GetFilteredSalesAsync(
            DateTime? fromDate = null,
            DateTime? toDate = null,
            int? clientId = null,
            bool? hasRemaining = null,
            int? invoiceId = null,
            int? warehouseId = null)
        {
            var query = _context.Sales
                .Include(s => s.Client)
                .Include(s => s.Warehouse)
                .Include(s => s.Receipts) // إضافة Include للإيصالات لتجنب N+1 Query
                .AsQueryable();

            // تصفية حسب التاريخ
            if (fromDate.HasValue)
                query = query.Where(s => s.SaleDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(s => s.SaleDate <= toDate.Value);

            // تصفية حسب العميل
            if (clientId.HasValue && clientId.Value != -1)
                query = query.Where(s => s.ClientId == clientId.Value);

            // تصفية حسب المخزن
            if (warehouseId.HasValue && warehouseId.Value != -1)
                query = query.Where(s => s.WarehouseId == warehouseId.Value);

            // تصفية حسب رقم الفاتورة
            if (invoiceId.HasValue)
                query = query.Where(s => s.InvoiceNo == invoiceId.Value);

            var sales = await query
                .OrderByDescending(s => s.SaleDate)
                .ToListAsyncWithBusy("GetFilteredSales");

            // تحويل البيانات إلى نموذج العرض بدون استعلامات إضافية
            var result = sales.Select((sale, index) =>
            {
                var paidAmount = sale.Receipts.Sum(r => r.Value); // حساب المبلغ المدفوع من البيانات المحملة مسبقاً

                var viewModel = new SaleViewModel
                {
                    Index = index + 1,
                    Id = sale.Id,
                    InvoiceNo = sale.InvoiceNo,
                    InvoiceNumber = sale.InvoiceNumber,
                    SaleDate = sale.SaleDate,
                    ClientName = sale.Client.Name,
                    WarehouseName = sale.Warehouse?.Name ?? "غير محدد",
                    TotalBeforeDiscount = sale.TotalBeforeDiscount,
                    TotalDiscount = sale.TotalDiscount,
                    TotalAmount = sale.TotalAmount,
                    TotalReturned = sale.TotalReturned,
                    PaidAmount = paidAmount,
                    Sale = sale
                };

                return viewModel;
            }).ToList();

            // تطبيق تصفية وجود الباقي إذا كانت مطلوبة
            if (hasRemaining.HasValue)
            {
                result = result.Where(viewModel =>
                {
                    decimal remaining = viewModel.TotalAmount - viewModel.PaidAmount - viewModel.TotalReturned;
                    return hasRemaining.Value ? remaining > 0 : remaining <= 0;
                }).ToList();
            }

            return result;
        }

        /// <summary>
        /// تحديث المخزون بشكل مجمع لتحسين الأداء (محسنة)
        /// </summary>
        private async Task<(bool Success, string Message)> UpdateInventoryBatch(IEnumerable<SaleItem> items, int warehouseId, bool isDecrease = false)
        {
            try
            {
                // تجميع العناصر حسب النوع والمعرف لتقليل الاستعلامات
                var productUpdates = new List<(int ProductQuantityId, int QuantityChange)>();
                var lensRightUpdates = new List<(int LensQuantityId, int QuantityChange)>();
                var lensLeftUpdates = new List<(int LensQuantityId, int QuantityChange)>();

                foreach (var item in items)
                {
                    int quantityChange = isDecrease ? -item.Quantity : item.Quantity;

                    if (item.ProductQuantityId.HasValue)
                    {
                        productUpdates.Add((item.ProductQuantityId.Value, quantityChange));
                    }
                    if (item.LensQuantityRightId.HasValue)
                    {
                        lensRightUpdates.Add((item.LensQuantityRightId.Value, quantityChange));
                    }
                    if (item.LensQuantityLeftId.HasValue)
                    {
                        lensLeftUpdates.Add((item.LensQuantityLeftId.Value, quantityChange));
                    }
                }

                // تحديث المنتجات بشكل مجمع
                if (productUpdates.Any())
                {
                    var productIds = productUpdates.Select(u => u.ProductQuantityId).ToList();
                    var productQuantities = await _context.ProductQuantities
                        .Where(pq => productIds.Contains(pq.Id) && pq.WarehouseId == warehouseId)
                        .ToListAsync();

                    foreach (var update in productUpdates)
                    {
                        var productQuantity = productQuantities.FirstOrDefault(pq => pq.Id == update.ProductQuantityId);
                        if (productQuantity != null)
                        {
                            productQuantity.Quantity += update.QuantityChange;
                            if (productQuantity.Quantity < 0)
                            {
                                return (false, $"الكمية المتاحة غير كافية للمنتج");
                            }
                        }
                    }
                }

                // تحديث العدسات اليمنى بشكل مجمع
                if (lensRightUpdates.Any())
                {
                    var lensIds = lensRightUpdates.Select(u => u.LensQuantityId).ToList();
                    var lensQuantities = await _context.LensQuantities
                        .Where(lq => lensIds.Contains(lq.Id) && lq.WarehouseId == warehouseId)
                        .ToListAsync();

                    foreach (var update in lensRightUpdates)
                    {
                        var lensQuantity = lensQuantities.FirstOrDefault(lq => lq.Id == update.LensQuantityId);
                        if (lensQuantity != null)
                        {
                            lensQuantity.Quantity += update.QuantityChange;
                            if (lensQuantity.Quantity < 0)
                            {
                                return (false, $"الكمية المتاحة غير كافية للعدسة");
                            }
                        }
                    }
                }

                // تحديث العدسات اليسرى بشكل مجمع
                if (lensLeftUpdates.Any())
                {
                    var lensIds = lensLeftUpdates.Select(u => u.LensQuantityId).ToList();
                    var lensQuantities = await _context.LensQuantities
                        .Where(lq => lensIds.Contains(lq.Id) && lq.WarehouseId == warehouseId)
                        .ToListAsync();

                    foreach (var update in lensLeftUpdates)
                    {
                        var lensQuantity = lensQuantities.FirstOrDefault(lq => lq.Id == update.LensQuantityId);
                        if (lensQuantity != null)
                        {
                            lensQuantity.Quantity += update.QuantityChange;
                            if (lensQuantity.Quantity < 0)
                            {
                                return (false, $"الكمية المتاحة غير كافية للعدسة");
                            }
                        }
                    }
                }

                return (true, "تم تحديث المخزون بنجاح");
            }
            catch (Exception ex)
            {
                return (false, $"حدث خطأ أثناء تحديث المخزون: {ex.Message}");
            }
        }

        // Implement IDisposable pattern
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                    _context?.Dispose();
                }

                // Free unmanaged resources
                _disposed = true;
            }
        }

        // Destructor
        ~SaleService()
        {
            Dispose(false);
        }
    }
}