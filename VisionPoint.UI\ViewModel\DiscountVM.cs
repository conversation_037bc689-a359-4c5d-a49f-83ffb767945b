﻿﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VisionPoint.UI.Models;

namespace VisionPoint.UI.ViewModel;

public class DiscountVM
{
    public int Id { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    [Precision(18, 3)] public decimal DiscountPercentage { get; set; }

    // Flags to indicate if the discount applies to all items in a category
    public bool ApplyToAllProducts { get; set; }
    public bool ApplyToAllLenses { get; set; }
    public bool ApplyToAllServices { get; set; }

    // Lists for selected items
    public List<int> SelectedProductIds { get; set; } = new List<int>();
    public List<int> SelectedLensIds { get; set; } = new List<int>();
    public List<int> SelectedServiceIds { get; set; } = new List<int>();

    // Selected item names
    public string? ProductName { get; set; }
    public string? LensName { get; set; }
    public string? ServiceName { get; set; }

    // Display properties
    public string FormattedStartDate => StartDate.ToString("yyyy-MM-dd");
    public string FormattedEndDate => EndDate.ToString("yyyy-MM-dd");
    public string FormattedPercentage => $"{DiscountPercentage}%";
    public string Status => DateTime.Now > EndDate ? "منتهي" : "ساري";

    public string ItemType
    {
        get
        {
            if (ApplyToAllProducts)
                return "جميع المنتجات";
            else if (ApplyToAllLenses)
                return "جميع العدسات";
            else if (ApplyToAllServices)
                return "جميع الخدمات";
            else if (SelectedProductIds.Any())
                return $"منتج: {ProductName ?? ""}";
            else if (SelectedLensIds.Any())
                return $"عدسة: {LensName ?? ""}";
            else if (SelectedServiceIds.Any())
                return $"خدمة: {ServiceName ?? ""}";
            else
                return "";
        }
    }
}
