﻿<Page
    x:Class="VisionPoint.UI.views.Pages.Sales.SalesView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converter="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Sales"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils"
    Title="SalesView"
    d:Background="White"
    d:Height="1080"
    d:Width="1570"
    FlowDirection="RightToLeft"
    Loaded="Page_Loaded"
    mc:Ignorable="d">
    <Page.Resources>
        <ResourceDictionary>
            <converter:PriceMultiplierConverter x:Key="PriceMultiplierConverter" />
            <converter:IndexToNumberConverter x:Key="IndexToNumberConverter" />
        </ResourceDictionary>
    </Page.Resources>
    <Grid
        Grid.Row="1"
        Grid.ColumnSpan="2"
        Margin="16">

        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition Height="auto" />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition Height="auto" />
        </Grid.RowDefinitions>



        <Grid Grid.ColumnSpan="5" Margin="8,0">


            <TextBox
                x:Name="txtSaleNo"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                IsReadOnly="True"
                Tag="رقم الفاتورة" />

        </Grid>

        <DatePicker
            x:Name="DtpSaleDate"
            Grid.Column="5"
            Grid.ColumnSpan="4"
            Height="60"
            Margin="8,0"
            BorderBrush="{StaticResource PrimaryTextColor}"
            BorderThickness="1"
            FontSize="18"
            SelectedDateFormat="Short">
            <DatePicker.Resources>
                <Style TargetType="{x:Type DatePickerTextBox}">
                    <Setter Property="Control.Template">
                        <Setter.Value>
                            <ControlTemplate>
                                <TextBox
                                    x:Name="PART_TextBox"
                                    VerticalAlignment="Stretch"
                                    BorderThickness="0"
                                    Foreground="{StaticResource PrimaryTextColor}"
                                    Tag="تاريخ الفاتورة"
                                    Text="{Binding Path=SelectedDate, StringFormat='dd/MM/yyyy', RelativeSource={RelativeSource AncestorType={x:Type DatePicker}}}" />
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </DatePicker.Resources>
        </DatePicker>


        <ComboBox
            x:Name="cmbClient"
            Grid.Column="9"
            Grid.ColumnSpan="4"
            Margin="8,0"
            FontSize="21"
            IsEditable="True"
            IsReadOnly="False"
            SelectionChanged="cmbClient_SelectionChanged"
            Tag="الزبون" />



        <Grid
            Grid.Column="13"
            Grid.ColumnSpan="4"
            Margin="8,0">


            <TextBox
                x:Name="txtphone"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                utils:NumericInputControl.IsNumericOnly="True"
                BorderThickness="1"
                FontSize="21"
                Tag="رقم الهاتف" />

        </Grid>

        <Border
            x:Name="btnBack"
            Grid.Column="17"
            Grid.ColumnSpan="2"
            MaxHeight="44"
            Background="Transparent"
            BorderBrush="{StaticResource errorColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnBack_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource errorColor}">
                رجوع
            </TextBlock>
        </Border>


        <Button
            Grid.Row="1"
            Grid.ColumnSpan="3"
            Background="Transparent"
            BorderBrush="Transparent"
            BorderThickness="0"
            Click="RdbProduct_Checked"
            Cursor="Hand"
            Style="{StaticResource noHover}">
            <Grid VerticalAlignment="Center">
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <TextBlock
                    Margin="0,0,0,12"
                    VerticalAlignment="Bottom"
                    FontSize="21"
                    TextAlignment="Center">
                    اصناف
                </TextBlock>

                <RadioButton
                    x:Name="RdbProduct"
                    Grid.Row="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FlowDirection="LeftToRight"
                    GroupName="ProductType"
                    IsChecked="True" />
            </Grid>
        </Button>





        <Button
            Grid.Row="1"
            Grid.Column="3"
            Grid.ColumnSpan="2"
            Background="Transparent"
            BorderBrush="Transparent"
            BorderThickness="0"
            Click="RdbLenses_Checked"
            Cursor="Hand"
            Style="{StaticResource noHover}">
            <Grid VerticalAlignment="Center">
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="21"
                    TextAlignment="Center">
                    عدسات
                </TextBlock>

                <RadioButton
                    x:Name="RdbLenses"
                    Grid.Row="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FlowDirection="LeftToRight"
                    GroupName="ProductType" />
            </Grid>
        </Button>



        <Button
            Grid.Row="1"
            Grid.Column="5"
            Grid.ColumnSpan="2"
            Background="Transparent"
            BorderBrush="Transparent"
            BorderThickness="0"
            Click="RdbService_Checked"
            Cursor="Hand"
            Style="{StaticResource noHover}">
            <Grid VerticalAlignment="Center">
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="21"
                    TextAlignment="Center">
                    خدمات
                </TextBlock>

                <RadioButton
                    x:Name="RdbService"
                    Grid.Row="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FlowDirection="LeftToRight"
                    GroupName="ProductType" />
            </Grid>
        </Button>

        <TextBlock
            x:Name="lblProductDiscount"
            Grid.Row="4"
            Grid.Column="8"
            Grid.ColumnSpan="5"
            Padding="10,0"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            FontSize="18"
            FontWeight="Bold"
            Foreground="Green"
            Text="يوجد تخفيض"
            TextWrapping="Wrap"
            Visibility="Collapsed" />



        <Grid
            x:Name="ProductLayout"
            Grid.Row="2"
            Grid.RowSpan="2"
            Grid.ColumnSpan="19">

            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />

            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition />
            </Grid.RowDefinitions>

            <Grid Grid.ColumnSpan="2" Margin="8,0">


                <TextBox
                    x:Name="txtProductBarcode"
                    Height="60"
                    VerticalAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    BorderThickness="1"
                    FontSize="21"
                    KeyDown="txtProductBarcode_KeyDown"
                    Tag="باركود" />

            </Grid>

            <ComboBox
                x:Name="cmbProductName"
                Grid.Column="2"
                Grid.ColumnSpan="3"
                Margin="8,0"
                FontSize="21"
                IsEditable="True"
                IsReadOnly="False"
                SelectionChanged="cmbProductName_SelectionChanged"
                Tag="اسم المنتج" />
            <DockPanel Grid.Row="1" Grid.ColumnSpan="7" />


            <Grid
                Grid.Row="1"
                Grid.Column="0"
                Grid.ColumnSpan="5"
                Margin="12,4,0,4">
                <DockPanel HorizontalAlignment="Stretch" LastChildFill="False">

                    <!--  Product Color ComboBox  -->
                    <ComboBox
                        x:Name="cmbProductColor"
                        Width="300"
                        Height="60"
                        MaxHeight="60"
                        Margin="0,0,24,0"
                        VerticalAlignment="Center"
                        BorderBrush="{DynamicResource SecundaryColor}"
                        BorderThickness="2"
                        FontSize="22"
                        IsEditable="True"
                        IsReadOnly="False"
                        SelectionChanged="cmbProductColor_SelectionChanged"
                        Tag="اللون"
                        Visibility="Visible" />

                    <!--  Expiration Date  -->
                    <ComboBox
                        x:Name="CmbProductExpireOn"
                        Width="320"
                        Height="60"
                        MaxHeight="60"
                        Margin="0,0,24,0"
                        VerticalAlignment="Center"
                        BorderBrush="{StaticResource PrimaryTextColor}"
                        BorderThickness="1"
                        FontSize="20"
                        IsEditable="False"
                        IsReadOnly="False"
                        KeyboardNavigation.AcceptsReturn="True"
                        KeyboardNavigation.TabIndex="7"
                        Tag="تاريخ الصلاحية"
                        Visibility="Visible" />

                    <!--  Quantity  -->
                    <TextBox
                        x:Name="txtProductQuantity"
                        Width="200"
                        Height="60"
                        MaxHeight="60"
                        Margin="0,0,24,0"
                        VerticalAlignment="Center"
                        utils:NumericInputControl.IsNumericOnly="True"
                        BorderThickness="1"
                        FontSize="22"
                        GotFocus="TextBox_GotFocus"
                        Tag="الكمية"
                        Visibility="Visible" />

                    <!--  Price and Discount  -->
                    <Grid
                        Width="180"
                        Height="65"
                        MaxHeight="60">


                        <TextBox
                            x:Name="txtProductPrice"
                            Grid.Column="0"
                            VerticalAlignment="Stretch"
                            VerticalContentAlignment="Center"
                            utils:NumericInputControl.IsDecimalOnly="True"
                            BorderThickness="1"
                            FontSize="22"
                            GotFocus="TextBox_GotFocus"
                            Tag="السعر"
                            Visibility="Visible" />

                    </Grid>


                </DockPanel>
            </Grid>







        </Grid>


        <Border
            x:Name="btnAddProduct"
            Grid.Row="5"
            Grid.Column="0"
            Grid.ColumnSpan="3"
            MaxHeight="44"
            Margin="8,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnAddProduct_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White">
                اضافة
            </TextBlock>
        </Border>


        <Border
            x:Name="btnNewItem"
            Grid.Row="5"
            Grid.Column="3"
            Grid.ColumnSpan="3"
            MaxHeight="44"
            Margin="8,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnNewItem_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}">
                جديد
            </TextBlock>
        </Border>

        <ComboBox
            x:Name="cmbTreasury"
            Grid.Row="5"
            Grid.Column="6"
            Grid.ColumnSpan="4"
            Margin="8,0"
            FontSize="21"
            IsEditable="True"
            IsReadOnly="False"
            Tag="طريقة الدفع" />


        <ComboBox
            x:Name="cmbWarehouse"
            Grid.Row="5"
            Grid.Column="10"
            Grid.ColumnSpan="4"
            FontSize="21"
            IsEditable="False"
            IsReadOnly="True"
            SelectionChanged="cmbWarehouse_SelectionChanged"
            Tag="المخزن" />

        <Grid
            x:Name="LensessLayout"
            Grid.Row="2"
            Grid.RowSpan="3"
            Grid.ColumnSpan="19"
            Visibility="Collapsed">

            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition />
                <RowDefinition />
            </Grid.RowDefinitions>

            <Border
                x:Name="btnAddLensess"
                Grid.Row="0"
                MaxHeight="44"
                Margin="8,0"
                Background="{StaticResource PrimaryColor}"
                CornerRadius="8"
                Cursor="Hand"
                MouseLeftButtonDown="btnAddLensess_MouseLeftButtonDown"
                Visibility="Collapsed">
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="18"
                    Foreground="White">
                    اضافة عدسات
                </TextBlock>
            </Border>


        </Grid>
        <Grid
            x:Name="ServiceLayout"
            Grid.Row="2"
            Grid.RowSpan="2"
            Grid.ColumnSpan="19"
            Visibility="Collapsed">

            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition />
            </Grid.RowDefinitions>
            <ComboBox
                x:Name="cmbServiceName"
                Grid.ColumnSpan="2"
                Margin="8,0"
                FontSize="21"
                IsEditable="True"
                IsReadOnly="False"
                SelectionChanged="cmbServiceName_SelectionChanged"
                Tag="اسم الخدمة" />

            <Grid
                Grid.Column="2"
                Margin="8,0"
                d:Visibility="Visible">
                <TextBox
                    x:Name="txtServicePrice"
                    Height="60"
                    VerticalAlignment="Center"
                    VerticalContentAlignment="Stretch"
                    utils:NumericInputControl.IsDecimalOnly="True"
                    BorderThickness="1"
                    FontSize="21"
                    GotFocus="TextBox_GotFocus"
                    Tag="السعر" />

            </Grid>

            <TextBlock
                x:Name="lblServiceDiscount"
                Grid.Row="1"
                Grid.Column="2"
                Grid.ColumnSpan="2"
                HorizontalAlignment="Center"
                VerticalAlignment="Top"
                FontWeight="Bold"
                Foreground="Green"
                Text="يوجد تخفيض"
                TextWrapping="Wrap"
                Visibility="Collapsed" />
            <Grid Grid.Column="3" Margin="8,0">
                <TextBox
                    x:Name="txtServiceQuantity"
                    Height="60"
                    VerticalAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    utils:NumericInputControl.IsNumericOnly="True"
                    BorderThickness="1"
                    FontSize="21"
                    GotFocus="TextBox_GotFocus"
                    Tag="الكمية" />
            </Grid>

        </Grid>
        <ListView
            x:Name="list"
            Grid.Row="6"
            Grid.RowSpan="4"
            Grid.ColumnSpan="19"
            Background="{DynamicResource PageColor}"
            BorderThickness="1"
            FontFamily="pack://application:,,,/Assets/#Cairo"
            ItemsSource="{Binding}"
            ScrollViewer.HorizontalScrollBarVisibility="Hidden"
            SizeChanged="list_SizeChanged">
            <ListView.BorderBrush>
                <SolidColorBrush Opacity="0.42" Color="Black" />
            </ListView.BorderBrush>

            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Style.Triggers>
                        <Trigger Property="Control.IsMouseOver" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>

                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="False" />
                                <Condition Property="IsMouseOver" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter Property="FontWeight" Value="Thin" />
                            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextColor}" />
                        </MultiTrigger>

                    </Style.Triggers>
                    <Setter Property="Control.Background" Value="{DynamicResource PageColor}" />
                    <Setter Property="Control.HorizontalContentAlignment" Value="Stretch" />
                </Style>
            </ListView.ItemContainerStyle>

            <ListView.View>
                <GridView AllowsColumnReorder="False">
                    <GridView.ColumnHeaderContainerStyle>
                        <Style BasedOn="{StaticResource ListViewHeader}" TargetType="{x:Type GridViewColumnHeader}">
                            <Setter Property="IsEnabled" Value="False" />
                            <Setter Property="Height" Value="60" />
                            <Style.Triggers>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="TextElement.Foreground" Value="Black" />
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </GridView.ColumnHeaderContainerStyle>
                    <GridViewColumn Width="Auto" Header="#">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Text="{Binding ., Converter={StaticResource IndexToNumberConverter}, ConverterParameter={x:Reference list}}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="الصنف">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="150"
                                    HorizontalAlignment="Right"
                                    Background="Transparent"
                                    Text="{Binding Name, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="الكمية">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="50"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding Quantity, FallbackValue='0', TargetNullValue='0'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="المسترجع">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="50"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding ReturnedQuantity, FallbackValue='0', TargetNullValue='0'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="السعر الأصلي">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="80"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding OriginalPrice, StringFormat='{}{0:N3}', FallbackValue='0.000', TargetNullValue='0.000'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="التخفيض">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="80"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding Discount, StringFormat='{}{0:N3}', FallbackValue='0.000', TargetNullValue='0.000'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="السعر بعد التخفيض">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="80"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding SellPrice, StringFormat='{}{0:N3}', FallbackValue='0.000', TargetNullValue='0.000'}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="الإجمالي">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="80"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    TextAlignment="Center">
                                    <TextBlock.Text>
                                        <MultiBinding Converter="{StaticResource PriceMultiplierConverter}" StringFormat="{}{0:N3}">
                                            <Binding Path="SellPrice" />
                                            <Binding Path="Quantity" />
                                        </MultiBinding>
                                    </TextBlock.Text>
                                </TextBlock>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="العمليات">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                    <Button
                                        Width="30"
                                        Height="30"
                                        Margin="4,0"
                                        HorizontalAlignment="Center"
                                        Background="Transparent"
                                        BorderBrush="Transparent"
                                        Click="btnDeleteItem_Click"
                                        Cursor="Hand"
                                        ToolTip="حذف المنتج">
                                        <Path
                                            Width="16"
                                            Height="16"
                                            Data="{StaticResource Trash}"
                                            Fill="{StaticResource errorColor}"
                                            Stretch="Uniform" />
                                    </Button>
                                    <Button
                                        x:Name="btnReturnItem"
                                        Width="30"
                                        Height="30"
                                        Margin="4,0"
                                        HorizontalAlignment="Center"
                                        Background="Transparent"
                                        BorderBrush="Transparent"
                                        Click="btnReturnItem_Click"
                                        Cursor="Hand"
                                        ToolTip="استرجاع">
                                        <TextBlock
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            FontSize="16"
                                            Foreground="{StaticResource PrimaryColor}"
                                            Text="↩" />
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
        </ListView>

        <Border
            x:Name="btnSaveAndPrint"
            Grid.Row="10"
            Grid.Column="0"
            Grid.ColumnSpan="3"
            MaxHeight="44"
            Margin="8,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnSaveAndPrint_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White">
                حفظ و طباعة
            </TextBlock>
        </Border>

        <Border
            x:Name="btnNesw"
            Grid.Row="10"
            Grid.Column="3"
            Grid.ColumnSpan="2"
            MaxHeight="44"
            Margin="8,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnNew_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}">
                فاتورة جديد
            </TextBlock>
        </Border>

        <Grid
            Grid.Row="10"
            Grid.Column="5"
            Grid.ColumnSpan="3"
            Margin="8,0">

            <TextBox
                x:Name="txtTotalBeforeDiscount"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                IsReadOnly="True"
                Tag="الإجمالي قبل التخفيض" />

        </Grid>
        <Grid
            Grid.Row="10"
            Grid.Column="8"
            Grid.ColumnSpan="3"
            Margin="8,0">

            <TextBox
                x:Name="txtTotalPrice"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                IsReadOnly="True"
                Tag="إجمالي الفاتورة" />

        </Grid>

        <Grid
            x:Name="gtxtTotalReturned"
            Grid.Row="11"
            Grid.Column="11"
            Grid.ColumnSpan="3"
            Margin="8,0"
            Visibility="Collapsed">

            <TextBox
                x:Name="txtTotalReturned"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                IsReadOnly="True"
                Tag="إجمالي المسترجعات" />

        </Grid>

        <Grid
            x:Name="gtxtNetAmount"
            Grid.Row="11"
            Grid.Column="14"
            Grid.ColumnSpan="3"
            Margin="8,0"
            Visibility="Collapsed">

            <TextBox
                x:Name="txtNetAmount"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                IsReadOnly="True"
                Tag="صافي الفاتورة" />

        </Grid>

        <Grid
            Grid.Row="10"
            Grid.Column="11"
            Grid.ColumnSpan="3"
            Margin="8,0">


            <TextBox
                x:Name="txtPaidPrice"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                utils:NumericInputControl.IsDecimalOnly="True"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                Tag="القيمة المدفوعة"
                TextChanged="txtPaidPrice_TextChanged" />

        </Grid>
        <Grid
            Grid.Row="10"
            Grid.Column="14"
            Grid.ColumnSpan="3"
            Margin="8,0">


            <TextBox
                x:Name="txtRemaining"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                IsReadOnly="True"
                Tag="القيمة المتبقية" />

        </Grid>

        <Grid
            Grid.Row="3"
            Grid.RowSpan="3"
            Grid.Column="14"
            Grid.ColumnSpan="5"
            Margin="8,10">

            <TextBox
                x:Name="txtNotes"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Top"
                AcceptsReturn="True"
                BorderThickness="1"
                FontSize="21"
                MaxLength="80"
                Tag="ملاحظات"
                TextWrapping="Wrap"
                VerticalScrollBarVisibility="Auto" />

        </Grid>

        <Border
            x:Name="btnAddReceipt"
            Grid.Row="10"
            Grid.Column="17"
            Grid.ColumnSpan="2"
            MaxHeight="44"
            Margin="8,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnAddReceipt_MouseLeftButtonDown"
            Visibility="Collapsed">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White">
                اضافة واصل
            </TextBlock>
        </Border>
    </Grid>
</Page>
