using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VisionPoint.UI.Models;

[Index(nameof(Name), IsUnique = true)]
[Index(nameof(Code), IsUnique = true)]
public class Warehouse
{
    public int Id { get; set; }

    [StringLength(100, ErrorMessage = "اسم المخزن يجب ألا يتجاوز 100 حرف")]
    public string Name { get; set; }

    [StringLength(20, ErrorMessage = "رمز المخزن يجب ألا يتجاوز 20 حرف")]
    public string Code { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.Now;
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// Row GUID for SQL Server Merge Replication - managed by database
    /// </summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid RowGuid { get; set; }

    // العلاقات العكسية
    public ICollection<ProductQuantity>? ProductQuantities { get; set; } = new List<ProductQuantity>();
    public ICollection<LensQuantity>? LensQuantities { get; set; } = new List<LensQuantity>();
    public ICollection<Client>? Clients { get; set; } = new List<Client>();
    public ICollection<Treasury>? Treasuries { get; set; } = new List<Treasury>();
    public ICollection<Purchase>? Purchases { get; set; } = new List<Purchase>();
    public ICollection<Sale>? Sales { get; set; } = new List<Sale>();
}
