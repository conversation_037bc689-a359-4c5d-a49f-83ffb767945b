using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace VisionPoint.UI.Models;

[Index(nameof(ClientId), nameof(PurchaseDate))]
[Index(nameof(PurchaseDate))]
[Index(nameof(InvoiceNo))]
[Index(nameof(WarehouseId))]
[Index(nameof(WarehouseId), nameof(InvoiceNo))]
public class Purchase : BaseEntity
{
    /// <summary>
    /// رقم الفاتورة
    /// </summary>
    public int InvoiceNo { get; set; }

    /// <summary>
    /// رقم الفاتورة مع رمز المخزن (مثل: MAIN-001)
    /// </summary>
    [StringLength(50)]
    public string? InvoiceNumber { get; set; }

    public int ClientId { get; set; }
    public Client Client { get; set; }
    public DateTime PurchaseDate { get; set; }
    [Precision(18, 3)] public decimal TotalAmount { get; set; }
    /// <summary>
    /// ملاحظات الفاتورة
    /// </summary>
    [StringLength(500, ErrorMessage = "الملاحظات يجب ألا تتجاوز 500 حرف")]
    public string? Notes { get; set; }

    /// <summary>
    /// معرف المخزن - إجباري
    /// </summary>
    public int WarehouseId { get; set; }
    public Warehouse Warehouse { get; set; }

    public ICollection<PurchaseItem> PurchaseItems { get; set; }
    public ICollection<Receipt>? Receipts { get; set; }
}
