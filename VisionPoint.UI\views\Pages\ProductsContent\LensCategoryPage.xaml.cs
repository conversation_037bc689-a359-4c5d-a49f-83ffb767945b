﻿﻿using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.ProductsContent
{
    /// <summary>
    /// Interaction logic for LensCategoryPage.xaml
    /// </summary>
    public partial class LensCategoryPage : Page
    {
        private readonly LensCategoryService _categoryService;
        private LensCategory _selectedCategory = new();

        public LensCategoryPage()
        {
            InitializeComponent();
            _categoryService = new LensCategoryService();
        }

        private async void Page_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadData();
        }

        private async Task LoadData()
        {
            try
            {
                var categories = await _categoryService.GetAllCategoriesAsync();
                list.ItemsSource = categories;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ: {ex.Message}", "خطأ", true);
            }
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnSave.IsEnabled = false;
            btnNew.IsEnabled = false;
            btnSearch.IsEnabled = false;
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnSave.IsEnabled = true;
            btnNew.IsEnabled = true;
            btnSearch.IsEnabled = true;
        }

        private async void btnSave_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق من صلاحيات المستخدم
                bool canAdd = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("LensCategoryRole");
                bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditLensCategoryRole");

            // إذا كان نوع عدسة جديد، نحتاج صلاحية الإضافة
            if (_selectedCategory.Id == 0 && !canAdd)
            {
                ErrorBox.Show("لا تملك صلاحية إضافة أنواع العدسات", "خطأ في الصلاحيات", true);
                return;
            }

            // إذا كان تعديل نوع عدسة موجود، نحتاج صلاحية التعديل
            if (_selectedCategory.Id != 0 && !canEdit)
            {
                ErrorBox.Show("لا تملك صلاحية تعديل أنواع العدسات", "خطأ في الصلاحيات", true);
                return;
            }

            if (string.IsNullOrEmpty(txtName.Text.Trim()))
            {
                ErrorBox.Show("الرجاء إدخال اسم نوع العدسة", "خطأ", true);
                return;
            }

         
                _selectedCategory.Name = txtName.Text.Trim();
                _selectedCategory.Description = txtDescription.Text.Trim();

                (bool success, string message) = _selectedCategory.Id == 0
                    ? await _categoryService.AddCategoryAsync(_selectedCategory)
                    : await _categoryService.UpdateCategoryAsync(_selectedCategory);

                if (success)
                {
                    DialogBox.Show("تم بنجاح", message);
                    ClearForm();
                    await LoadData();
                }
                else
                {
                    ErrorBox.Show(message, "خطأ", true);
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnNew_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                ClearForm();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnSearch_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                var searchTerm = txtName.Text.Trim();
                var searchResults = await _categoryService.SearchByNameAsync(searchTerm);
                list.ItemsSource = searchResults;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void list_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (list.SelectedItem is LensCategory selectedCategory)
            {
                _selectedCategory = selectedCategory;
                txtName.Text = selectedCategory.Name;
                txtDescription.Text = selectedCategory.Description;
            }
        }

        private async void Delete_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // التحقق من صلاحية الحذف
            bool canDelete = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("DeleteLensCategoryRole");
            if (!canDelete)
            {
                ErrorBox.Show("لا تملك صلاحية حذف أنواع العدسات", "خطأ في الصلاحيات", true);
                return;
            }

            var path = sender as System.Windows.Shapes.Path;
            if (path != null && path.DataContext is LensCategory category)
            {
                var result = QuestionBox.Show("تأكيد الحذف", $"هل أنت متأكد من حذف نوع العدسة '{category.Name}'؟");
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var (success, message) = await _categoryService.DeleteCategoryAsync(category.Id);
                        if (success)
                        {
                            DialogBox.Show("تم بنجاح", message);
                            await LoadData();
                        }
                        else
                        {
                            ErrorBox.Show(message, "خطأ", true);
                        }
                    }
                    catch (Exception ex)
                    {
                        ErrorBox.Show($"حدث خطأ: {ex.Message}", "خطأ", true);
                    }
                }
            }
        }

        private void ClearForm()
        {
            _selectedCategory = new LensCategory();
            txtName.Text = string.Empty;
            txtDescription.Text = string.Empty;
        }

        private void TextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                textBox.SelectAll();
            }
        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            try
            {
                ListView listView = sender as ListView;
                if (listView != null)
                {
                    GridView gView = listView.View as GridView;
                    if (gView != null)
                    {
                        var workingWidth = listView.ActualWidth - SystemParameters.VerticalScrollBarWidth;
                        var col1 = 0.1; // numbering column
                        var col2 = 0.4; // name column
                        var col3 = 0.4; // price column
                        var col4 = 0.1; // price column

                        gView.Columns[0].Width = workingWidth * col1;
                        gView.Columns[1].Width = workingWidth * col2;
                        gView.Columns[2].Width = workingWidth * col3;
                        gView.Columns[3].Width = workingWidth * col4;

                    }
                }
            }
            catch
            {
                return;
            }
        }
    }
}
