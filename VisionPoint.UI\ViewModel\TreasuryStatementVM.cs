using Microsoft.EntityFrameworkCore;
using System;

namespace VisionPoint.UI.ViewModel;

/// <summary>
/// نموذج عرض كشف حساب الخزينة
/// </summary>
public class TreasuryStatementVM
{
    /// <summary>
    /// معرف العملية
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// تاريخ العملية
    /// </summary>
    public DateTime? Date { get; set; }

    /// <summary>
    /// نوع العملية (إيصال قبض، إيصال صرف، فاتورة مبيعات، فاتورة مشتريات، إلخ)
    /// </summary>
    public string OperationType { get; set; }

    /// <summary>
    /// رقم العملية (رقم الفاتورة أو رقم الإيصال)
    /// </summary>
    public int? OperationNumber { get; set; }

    /// <summary>
    /// اسم العميل أو المورد
    /// </summary>
    public string? ClientName { get; set; }

    /// <summary>
    /// البيان أو الوصف
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// قيمة القبض (المبلغ الداخل للخزينة)
    /// </summary>
    [Precision(18, 3)] public decimal Incoming { get; set; } = 0;

    /// <summary>
    /// قيمة الصرف (المبلغ الخارج من الخزينة)
    /// </summary>
    [Precision(18, 3)] public decimal Outgoing { get; set; } = 0;

    /// <summary>
    /// رصيد الخزينة بعد العملية
    /// </summary>
    [Precision(18, 3)] public decimal Balance { get; set; } = 0;

    /// <summary>
    /// يشير إلى ما إذا كان هذا الصف خاصًا (مثل الرصيد السابق أو الرصيد اللاحق)
    /// </summary>
    public bool IsSpecialRow { get; set; } = false;

    /// <summary>
    /// نوع الصف الخاص (PreviousBalance, NextBalance, أو null للصفوف العادية)
    /// </summary>
    public string? SpecialRowType { get; set; }
}
