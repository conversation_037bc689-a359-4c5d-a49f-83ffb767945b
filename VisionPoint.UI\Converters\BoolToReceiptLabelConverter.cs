﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;

namespace VisionPoint.UI.Converters
{
    public class BoolToReceiptLabelConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // If IsExchange is true => it's an exchange => "دفعنا إلى:"
            if (value is bool isExchange)
                return isExchange ? "دفعنا إلى:" : "استلمنا من:";

            return "استلمنا من:";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

}
