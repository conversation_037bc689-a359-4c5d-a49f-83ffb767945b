﻿using System.Windows;
using System.Windows.Input;

namespace VisionPoint.UI.views.Dialogs
{

    public partial class DialogBox : Window
    {
        private static DialogBox instance;
        private bool windowResult = false;

        private string header = "";
        public string TextHeader
        {
            set
            {
                header = value;
                txtHeader.Text = header;
            }
            get { return header; }
        }

        private string message = "";
        public string TextMessage
        {
            set
            {
                message = value;
                txtMessage.Text = message;
            }
            get { return message; }
        }

        private DialogBox() // Private constructor
        {
            InitializeComponent();
            
        }

        public static DialogBox Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new DialogBox();
                }
                return instance;
            }
        }

        public static bool? Show(string header, string message)
        {
            var dialog = Instance;
            dialog.TextHeader = header;
            dialog.TextMessage = message;
            dialog.ShowDialog(); 
            return dialog.DialogResult; 
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnYes.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnYes.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private void btnYes_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                windowResult = true;
                this.DialogResult = true;
                this.Hide();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnNo_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                windowResult = false;
                this.DialogResult = false;
                this.Hide();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
         
            if (this.DialogResult == null)
            {
                this.DialogResult = false; 
            }

            e.Cancel = true; 
        }
    }


}
