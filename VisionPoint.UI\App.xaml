﻿<Application
    x:Class="VisionPoint.UI.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:cc="clr-namespace:VisionPoint.UI.Converters"
    xmlns:local="clr-namespace:VisionPoint.UI"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <cc:TextInputToVisibilityConverter x:Key="TextToVisibilityConverter" />
            <cc:BoolToExchangeTypeConverter x:Key="BoolToExchangeTypeConverter" />
        </ResourceDictionary>
    </Application.Resources>

</Application>
