﻿using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.Treasury
{
    /// <summary>
    /// Interaction logic for TreasuryPage.xaml
    /// </summary>
    public partial class TreasuryPage : Page
    {
        private readonly TreasuryService _treasuryService;
        private readonly WarehouseService _warehouseService;
        private ObservableCollection<Models.Treasury> _treasuries;
        private Models.Treasury? _selectedTreasury;
        private bool _isSearchMode = false;

        public TreasuryPage()
        {
            InitializeComponent();
            _treasuryService = new TreasuryService();
            _warehouseService = new WarehouseService();
            _treasuries = new ObservableCollection<Models.Treasury>();
        }

        private async void Page_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadWarehousesAsync();
            await LoadTreasuriesAsync();
        }

        private async Task LoadWarehousesAsync()
        {
            try
            {
                var warehouses = await _warehouseService.GetAllWarehousesAsync();
                cmbWarehouse.ItemsSource = warehouses;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل بيانات المخازن: {ex.Message}", "خطأ في التحميل", true);
            }
        }

        private async Task LoadTreasuriesAsync()
        {
            try
            {
                _treasuries.Clear();
                _treasuries = new ObservableCollection<Models.Treasury>(await _treasuryService.GetAllTreasuriesAsync());
                list.ItemsSource = _treasuries;

                // إظهار عدد طرق الدفع في عنوان النافذة
                Title = $"طرق الدفع - {_treasuries.Count}";
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ في التحميل", true);
            }
        }

        private void ClearForm()
        {
            _selectedTreasury = null;
            txtName.Clear();
            txtBalance.Clear();
            cmbWarehouse.SelectedValue = null;
            list.SelectedItem = null;
            txtName.Focus();
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnSave.IsEnabled = false;
            btnNew.IsEnabled = false;
            btnSearch.IsEnabled = false;
            btnStatement.IsEnabled = false;
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnSave.IsEnabled = true;
            btnNew.IsEnabled = true;
            btnSearch.IsEnabled = true;
            btnStatement.IsEnabled = true;
        }

        private async void btnSave_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق من البيانات المطلوبة
                if (string.IsNullOrEmpty(txtName.Text.Trim()))
                {
                    ErrorBox.Show("الرجاء إدخال اسم طريقة الدفع لإكمال العملية", "بيانات ناقصة", true);
                    txtName.Focus();
                    return;
                }

                if (cmbWarehouse.SelectedValue == null)
                {
                    ErrorBox.Show("الرجاء اختيار المخزن لإكمال العملية", "بيانات ناقصة", true);
                    cmbWarehouse.Focus();
                    return;
                }

                if (_selectedTreasury == null)
                {
                    // إضافة طريقة دفع جديدة
                    var newTreasury = new Models.Treasury
                    {
                        Name = txtName.Text.Trim(),
                        Balance =  0,
                        WarehouseId = (int)cmbWarehouse.SelectedValue
                    };

                    try
                    {
                        var result = await _treasuryService.AddTreasuryAsync(newTreasury);
                        if (result.State)
                        {
                            DialogBox.Show("تم بنجاح", $"تم إضافة طريقة الدفع {newTreasury.Name} بنجاح");
                        }
                        else
                        {
                            ErrorBox.Show(result.Message, "خطأ في الإضافة", true);
                            return;
                        }
                    }
                    catch (Exception ex)
                    {
                        ErrorBox.Show($"حدث خطأ أثناء إضافة طريقة الدفع: {ex.Message}", "خطأ في الإضافة", true);
                        return;
                    }
                }
                else
                {
                    // تعديل طريقة دفع موجودة
                    _selectedTreasury.Name = txtName.Text.Trim();
                    _selectedTreasury.WarehouseId = (int)cmbWarehouse.SelectedValue;

                    try
                    {
                        var result = await _treasuryService.UpdateTreasuryAsync(_selectedTreasury);
                        if (result.State)
                        {
                            DialogBox.Show("تم بنجاح", $"تم تعديل طريقة الدفع {_selectedTreasury.Name} بنجاح");
                        }
                        else
                        {
                            ErrorBox.Show(result.Message, "خطأ في التعديل", true);
                            return;
                        }
                    }
                    catch (Exception ex)
                    {
                        ErrorBox.Show($"حدث خطأ أثناء تعديل طريقة الدفع: {ex.Message}", "خطأ في التعديل", true);
                        return;
                    }
                }

                // إعادة تحميل القائمة وتنظيف الحقول
                await LoadTreasuriesAsync();
                ClearForm();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ: {ex.Message}", "خطأ في النظام", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnNew_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                ClearForm();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnSearch_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                await SearchTreasuriesAsync();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async Task SearchTreasuriesAsync()
        {
            try
            {
                string? name = txtName.Text?.Trim();

                // If all fields are empty, load all treasuries
                if (string.IsNullOrEmpty(name))
                {
                    await LoadTreasuriesAsync();
                    _isSearchMode = false;
                    return;
                }

                // Perform the search
                var searchResults = await _treasuryService.SearchTreasuriesAsync(name);

                // Update the UI
                _treasuries.Clear();
                foreach (var treasury in searchResults)
                {
                    _treasuries.Add(treasury);
                }

                list.ItemsSource = _treasuries;
                Title = $"طرق الدفع - نتائج البحث ({_treasuries.Count})";
                _isSearchMode = true;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ في البحث", true);
            }
        }
        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (list.View is GridView gridView && gridView.Columns.Count > 0)
            {
                double totalWidth = list.ActualWidth - SystemParameters.VerticalScrollBarWidth - 10;
                double actionColumnWidth = 120; // عرض ثابت لعمود العمليات
                double availableWidth = totalWidth - actionColumnWidth;
                double unitWidth = availableWidth / 4.0; // توزيع على 5 أعمدة

                gridView.Columns[0].Width = unitWidth * 0.5; // Id
                gridView.Columns[1].Width = unitWidth * 1.5; // Name
                gridView.Columns[2].Width = unitWidth * 1.0; // Warehouse
                gridView.Columns[3].Width = unitWidth * 1.0; // Balance
                gridView.Columns[4].Width = actionColumnWidth; // Actions
            }
        }

        private void list_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (list.SelectedItem is Models.Treasury selectedTreasury)
            {
                _selectedTreasury = selectedTreasury;
                DisplayTreasuryDetails();
            }
        }

        // عرض بيانات طريقة الدفع المحددة في الحقول
        private void DisplayTreasuryDetails()
        {
            if (_selectedTreasury != null)
            {
                txtName.Text = _selectedTreasury.Name;
                txtBalance.Text = _selectedTreasury.Balance.ToString();
                cmbWarehouse.SelectedValue = _selectedTreasury.WarehouseId;
            }
        }

        private void TextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox && !string.IsNullOrEmpty(textBox.Tag?.ToString()))
            {
                if (textBox.Text == textBox.Tag.ToString())
                {
                    textBox.Text = "";
                    textBox.Foreground = System.Windows.Media.Brushes.Black;
                }
            }
        }

        private void btnStatement_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // الحصول على الخزينة المحددة من القائمة
                if (list.SelectedItem is Models.Treasury selectedTreasury)
                {
                    // فتح نافذة كشف حساب الخزينة
                    var statementWindow = new TreasuryStatementWindow(selectedTreasury);
                    statementWindow.ShowDialog();
                }
                else
                {
                    ErrorBox.Show("الرجاء اختيار خزينة من القائمة لعرض كشف حسابها", "تنبيه", false);
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء فتح كشف الحساب: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على طريقة الدفع المحددة من السطر
                if (sender is Button button && button.DataContext is Models.Treasury selectedTreasury)
                {
                    // تحديد طريقة الدفع المختارة وعرض بياناتها في الحقول
                    _selectedTreasury = selectedTreasury;
                    DisplayTreasuryDetails();

                    // التركيز على حقل الاسم للتعديل
                    txtName.Focus();
                    txtName.SelectAll();
                }
                else
                {
                    ErrorBox.Show("الرجاء اختيار طريقة دفع للتعديل", "تنبيه", false);
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحديد طريقة الدفع للتعديل: {ex.Message}", "خطأ", true);
            }
        }

        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is Models.Treasury treasury)
            {
                var result = QuestionBox.Show($"هل أنت متأكد من حذف طريقة الدفع '{treasury.Name}'؟",
                    "تأكيد الحذف");

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var deleteResult = await _treasuryService.DeleteTreasuryAsync(treasury.Id);
                        if (deleteResult.State)
                        {
                            DialogBox.Show("تم حذف طريقة الدفع بنجاح", "نجح");
                            await LoadTreasuriesAsync();
                            ClearForm();
                        }
                        else
                        {
                            ErrorBox.Show(deleteResult.Message, "خطأ في الحذف", true);
                        }
                    }
                    catch (Exception ex)
                    {
                        ErrorBox.Show($"حدث خطأ أثناء حذف طريقة الدفع: {ex.Message}", "خطأ", true);
                    }
                }
            }
        }
    }
}


