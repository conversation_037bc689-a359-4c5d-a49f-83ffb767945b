using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Models;
using VisionPoint.UI.views.Pages.MinimumQuantity;

namespace VisionPoint.UI.Helper
{
    public static class MinimumQuantityChecker
    {
        /// <summary>
        /// Checks if there are any products or lenses that are below their minimum quantity
        /// </summary>
        /// <returns>A tuple with flags indicating if there are products or lenses below minimum quantity</returns>
        public static async Task<(bool HasProductsBelowMinimum, bool HasLensesBelowMinimum)> CheckForItemsBelowMinimumQuantity()
        {
            try
            {
                // التحقق من إعداد تفعيل فحص الحد الأدنى للكمية
                if (!IsMinimumQuantityCheckEnabled())
                {
                    // إذا كان الفحص غير مفعل، نعيد قيمة false لكلا العنصرين
                    return (false, false);
                }

                // Get context instance from ServiceLocator
                using var context = ServiceLocator.GetService<AppDbContext>();

                // Check for products below minimum quantity
                bool hasProductsBelowMinimum = await context.Products
                    .AnyAsync(p =>
                        p.MinimumQuantity > 0 &&
                        p.ProductColors.Any(pc => pc.ProductQuantity.Any(pq => pq.Quantity < p.MinimumQuantity)));

                // Check for lenses below minimum quantity
                bool hasLensesBelowMinimum = await context.Lenses
                    .AnyAsync(l =>
                        l.MinimumQuantity > 0 &&
                        l.LensPrescriptions.Any(lp =>
                            lp.LensPrescriptionColors.Any(lpc =>
                                lpc.LensQuantity.Any(lq => lq.Quantity < l.MinimumQuantity))));

                return (hasProductsBelowMinimum, hasLensesBelowMinimum);
            }
            catch (Exception ex)
            {
                return (false, false);
            }
        }

        /// <summary>
        /// التحقق من إعداد تفعيل فحص الحد الأدنى للكمية
        /// </summary>
        /// <returns>قيمة منطقية تشير إلى ما إذا كان فحص الحد الأدنى للكمية مفعل أم لا</returns>
        private static bool IsMinimumQuantityCheckEnabled()
        {
            try
            {
                return Properties.Settings.Default.EnableMinimumQuantityCheck;
            }
            catch (Exception ex)
            {
                return true; // القيمة الافتراضية هي تفعيل الفحص
            }
        }

        /// <summary>
        /// Gets a list of products that are below their minimum quantity
        /// </summary>
        /// <returns>A list of MinimumQuantityItemViewModel objects for products below minimum quantity</returns>
        public static async Task<List<MinimumQuantityItemViewModel>> GetProductsBelowMinimumQuantity()
        {
            try
            {
                // التحقق من إعداد تفعيل فحص الحد الأدنى للكمية
                if (!IsMinimumQuantityCheckEnabled())
                {
                    // إذا كان الفحص غير مفعل، نعيد قائمة فارغة
                    return new List<MinimumQuantityItemViewModel>();
                }

                using var context = ServiceLocator.GetService<AppDbContext>();

                var products = await context.Products
                    .Include(p => p.ProductColors)
                        .ThenInclude(pc => pc.Color)
                    .Include(p => p.ProductColors)
                        .ThenInclude(pc => pc.ProductQuantity)
                    .Where(p => p.MinimumQuantity > 0)
                    .ToListAsync();

                // تحسين الحلقات المتداخلة باستخدام LINQ SelectMany (محسنة)
                var result = products
                    .SelectMany(product => product.ProductColors
                        .SelectMany(productColor => productColor.ProductQuantity
                            .Where(quantity => quantity.Quantity < product.MinimumQuantity)
                            .Select(quantity => new MinimumQuantityItemViewModel
                            {
                                Id = product.Id,
                                Name = product.Name,
                                ColorName = productColor.Color?.Name ?? "",
                                CurrentQuantity = quantity.Quantity,
                                MinimumQuantity = product.MinimumQuantity,
                                ItemType = "منتج"
                            })))
                    .ToList();

                return result;
            }
            catch (Exception ex)
            {
                return new List<MinimumQuantityItemViewModel>();
            }
        }

        /// <summary>
        /// Gets a list of lenses that are below their minimum quantity
        /// </summary>
        /// <returns>A list of MinimumQuantityItemViewModel objects for lenses below minimum quantity</returns>
        public static async Task<List<MinimumQuantityItemViewModel>> GetLensesBelowMinimumQuantity()
        {
            try
            {
                // التحقق من إعداد تفعيل فحص الحد الأدنى للكمية
                if (!IsMinimumQuantityCheckEnabled())
                {
                    // إذا كان الفحص غير مفعل، نعيد قائمة فارغة
                    return new List<MinimumQuantityItemViewModel>();
                }

                using var context = ServiceLocator.GetService<AppDbContext>();

                var lenses = await context.Lenses
                    .Include(l => l.LensPrescriptions)
                        .ThenInclude(lp => lp.Sphere)
                    .Include(l => l.LensPrescriptions)
                        .ThenInclude(lp => lp.Cylinder)
                    .Include(l => l.LensPrescriptions)
                        .ThenInclude(lp => lp.Pow)
                    .Include(l => l.LensPrescriptions)
                        .ThenInclude(lp => lp.LensPrescriptionColors)
                            .ThenInclude(lpc => lpc.Color)
                    .Include(l => l.LensPrescriptions)
                        .ThenInclude(lp => lp.LensPrescriptionColors)
                            .ThenInclude(lpc => lpc.LensQuantity)
                    .Where(l => l.MinimumQuantity > 0)
                    .ToListAsync();

                // تحسين الحلقات المتداخلة للعدسات باستخدام LINQ SelectMany (محسنة)
                var result = lenses
                    .SelectMany(lens => lens.LensPrescriptions
                        .Select(prescription => new
                        {
                            Lens = lens,
                            Prescription = prescription,
                            Details = BuildPrescriptionDetails(prescription)
                        }))
                    .SelectMany(lp => lp.Prescription.LensPrescriptionColors
                        .SelectMany(color => color.LensQuantity
                            .Where(quantity => quantity.Quantity < lp.Lens.MinimumQuantity)
                            .Select(quantity => new MinimumQuantityItemViewModel
                            {
                                Id = lp.Lens.Id,
                                Name = lp.Lens.Name,
                                ColorName = color.Color?.Name ?? "",
                                Details = lp.Details,
                                CurrentQuantity = quantity.Quantity,
                                MinimumQuantity = lp.Lens.MinimumQuantity,
                                ItemType = "عدسة"
                            })))
                    .ToList();

                return result;
            }
            catch (Exception ex)
            {
                return new List<MinimumQuantityItemViewModel>();
            }
        }

        /// <summary>
        /// دالة مساعدة لبناء تفاصيل الوصفة (محسنة)
        /// </summary>
        private static string BuildPrescriptionDetails(LensPrescription prescription)
        {
            var details = new List<string>();

            if (prescription.Sphere != null)
                details.Add($"SPH:{(prescription.Sphere.Value == 0 ? "PL" : prescription.Sphere.Value.ToString())}");

            if (prescription.Cylinder != null)
                details.Add($"CYL:{(prescription.Cylinder.Value == 0 ? "PL" : prescription.Cylinder.Value.ToString())}");

            if (prescription.Pow != null)
                details.Add($"POW:{(prescription.Pow.Value == 0 ? "PL" : prescription.Pow.Value.ToString())}");

            return string.Join(" ", details);
        }
    }
}
