using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;

namespace VisionPoint.UI.Helper
{
    public static class ExpirationChecker
    {
        /// <summary>
        /// Checks if there are any products or lenses that are expired or about to expire
        /// </summary>
        /// <returns>A tuple with flags indicating if there are expiring products or lenses, and warehouse info</returns>
        public static async Task<(bool HasExpiringProducts, bool HasExpiringLenses, string WarehouseInfo)> CheckForExpiringItems()
        {
            try
            {
                // Check if user has permission to see expiration alerts
                bool hasExpirationAlertPermission = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ExpirationAlertRole");
                if (!hasExpirationAlertPermission)
                {
                    return (false, false, string.Empty);
                }

                // Get the remaining days setting
                int remainingDays = Properties.Settings.Default.LicenseRemainingDays;

                // Calculate the expiration threshold date
                var today = DateOnly.FromDateTime(DateTime.Today);
                var thresholdDate = DateOnly.FromDateTime(DateTime.Today.AddDays(remainingDays));

                // Check user warehouse permissions
                bool canChangeWarehouse = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ChangeWarehouseRole");
                string warehouseInfo = string.Empty;

                // Get context instance from ServiceLocator
                using var context = ServiceLocator.GetService<AppDbContext>();

                bool hasExpiringProducts = false;
                bool hasExpiringLenses = false;

                if (canChangeWarehouse)
                {
                    // User can see all warehouses - check all warehouses
                    hasExpiringProducts = await context.ProductQuantities
                        .Include(pq => pq.Warehouse)
                        .AnyAsync(pq => pq.Exp.HasValue && (
                            // Already expired
                            pq.Exp.Value <= today ||
                            // Will expire soon
                            (pq.Exp.Value > today && pq.Exp.Value <= thresholdDate)
                        ) && pq.Quantity > 0); // Only consider items with quantity > 0

                    hasExpiringLenses = await context.LensQuantities
                        .Include(lq => lq.Warehouse)
                        .AnyAsync(lq => lq.Exp.HasValue && (
                            // Already expired
                            lq.Exp.Value <= today ||
                            // Will expire soon
                            (lq.Exp.Value > today && lq.Exp.Value <= thresholdDate)
                        ) && lq.Quantity > 0); // Only consider items with quantity > 0

                    if (hasExpiringProducts || hasExpiringLenses)
                    {
                        // Get warehouse names with expiring items
                        var warehousesWithExpiringProducts = await context.ProductQuantities
                            .Include(pq => pq.Warehouse)
                            .Where(pq => pq.Exp.HasValue && (
                                pq.Exp.Value <= today ||
                                (pq.Exp.Value > today && pq.Exp.Value <= thresholdDate)
                            ) && pq.Quantity > 0)
                            .Select(pq => pq.Warehouse.Name)
                            .Distinct()
                            .ToListAsync();

                        var warehousesWithExpiringLenses = await context.LensQuantities
                            .Include(lq => lq.Warehouse)
                            .Where(lq => lq.Exp.HasValue && (
                                lq.Exp.Value <= today ||
                                (lq.Exp.Value > today && lq.Exp.Value <= thresholdDate)
                            ) && lq.Quantity > 0)
                            .Select(lq => lq.Warehouse.Name)
                            .Distinct()
                            .ToListAsync();

                        var allWarehousesWithExpiring = warehousesWithExpiringProducts
                            .Union(warehousesWithExpiringLenses)
                            .Distinct()
                            .ToList();

                        warehouseInfo = allWarehousesWithExpiring.Any()
                            ? $"المخازن المتأثرة: {string.Join(", ", allWarehousesWithExpiring)}"
                            : string.Empty;
                    }
                }
                else
                {
                    // User can only see their assigned warehouse
                    if (CurrentUser.WarehouseId.HasValue)
                    {
                        int userWarehouseId = CurrentUser.WarehouseId.Value;

                        hasExpiringProducts = await context.ProductQuantities
                            .Include(pq => pq.Warehouse)
                            .AnyAsync(pq => pq.WarehouseId == userWarehouseId &&
                                          pq.Exp.HasValue && (
                                              // Already expired
                                              pq.Exp.Value <= today ||
                                              // Will expire soon
                                              (pq.Exp.Value > today && pq.Exp.Value <= thresholdDate)
                                          ) && pq.Quantity > 0);

                        hasExpiringLenses = await context.LensQuantities
                            .Include(lq => lq.Warehouse)
                            .AnyAsync(lq => lq.WarehouseId == userWarehouseId &&
                                          lq.Exp.HasValue && (
                                              // Already expired
                                              lq.Exp.Value <= today ||
                                              // Will expire soon
                                              (lq.Exp.Value > today && lq.Exp.Value <= thresholdDate)
                                          ) && lq.Quantity > 0);

                        if (hasExpiringProducts || hasExpiringLenses)
                        {
                            var userWarehouse = await context.Warehouses
                                .FirstOrDefaultAsync(w => w.Id == userWarehouseId);
                            warehouseInfo = userWarehouse != null
                                ? $"المخزن: {userWarehouse.Name}"
                                : string.Empty;
                        }
                    }
                }

                return (hasExpiringProducts, hasExpiringLenses, warehouseInfo);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في التحقق من الصلاحيات: {ex.Message}");
                return (false, false, string.Empty);
            }
        }
    }
}
