﻿<Window
    x:Class="VisionPoint.UI.views.Pages.Users.UserStatementWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converter="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:VisionPoint.UI"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Users"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils"
    Title="كشف حساب المستخدم"
    Background="Transparent"
    FlowDirection="RightToLeft"
    Loaded="Window_Loaded"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <converter:NonZeroToBoldConverter x:Key="NonZeroToBoldConverter" />
            <converter:EmployeeBalanceToColorConverter x:Key="EmployeeBalanceToColorConverter" />
        </ResourceDictionary>
    </Window.Resources>
    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">
        <Border
            Width="1920"
            Height="1080"
            Padding="24"
            Background="{StaticResource backgroundColor}"
            CornerRadius="24">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Border
                    x:Name="btnclose"
                    Width="24"
                    Height="24"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Top"
                    Background="Red"
                    CornerRadius="50"
                    MouseLeftButtonDown="btnclose_MouseLeftButtonDown" />

                <!--  شريط العنوان  -->
                <Grid Grid.Row="0" Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <TextBlock
                        Grid.Column="1"
                        HorizontalAlignment="Center"
                        FontFamily="pack://application:,,,/Assets/#Cairo"
                        FontSize="32"
                        FontWeight="Bold"
                        Text="كشف حساب المستخدم" />


                </Grid>

                <!--  معلومات المستخدم  -->
                <StackPanel Grid.Row="1" Margin="0,0,0,20">
                    <TextBlock
                        x:Name="txtUserName"
                        HorizontalAlignment="Center"
                        FontFamily="pack://application:,,,/Assets/#Cairo"
                        FontSize="24"
                        FontWeight="Bold"
                        Text="اسم المستخدم" />
                    <TextBlock
                        x:Name="txtUserBalance"
                        HorizontalAlignment="Center"
                        FontFamily="pack://application:,,,/Assets/#Cairo"
                        FontSize="18"
                        Text="الرصيد الحالي: 0.000" />
                </StackPanel>

                <!--  خيارات الفلترة  -->
                <Border
                    Grid.Row="2"
                    Margin="0,0,0,20"
                    Padding="10"
                    CornerRadius="8">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <Grid MouseLeftButtonDown="Grid_MouseLeftButtonDown">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition />
                            </Grid.ColumnDefinitions>
                            <TextBlock VerticalAlignment="Center" Text="كل الفترات" />
                            <CheckBox
                                x:Name="chkAllPeriods"
                                Grid.Column="1"
                                VerticalAlignment="Center"
                                Checked="chkAllPeriods_CheckedChanged"
                                IsChecked="True"
                                Style="{StaticResource CircleCheckboxFL}"
                                Unchecked="chkAllPeriods_CheckedChanged" />

                        </Grid>


                        <DatePicker
                            x:Name="dpFromDate"
                            Grid.Column="1"
                            Grid.ColumnSpan="2"
                            Height="60"
                            Margin="8,0"
                            BorderBrush="{StaticResource PrimaryTextColor}"
                            BorderThickness="1"
                            DisplayDateStart="2000-01-01"
                            FontSize="18"
                            IsEnabled="False"
                            SelectedDateFormat="Short">
                            <DatePicker.Resources>
                                <Style TargetType="{x:Type DatePickerTextBox}">
                                    <Setter Property="Control.Template">
                                        <Setter.Value>
                                            <ControlTemplate>
                                                <TextBox
                                                    x:Name="PART_TextBox"
                                                    VerticalAlignment="Stretch"
                                                    BorderThickness="0"
                                                    Foreground="{StaticResource PrimaryTextColor}"
                                                    IsEnabled="{Binding Path=IsEnabled, RelativeSource={RelativeSource AncestorType={x:Type DatePicker}}}"
                                                    IsReadOnly="True"
                                                    Style="{StaticResource txtDatePick}"
                                                    Tag="من تاريخ"
                                                    Text="{Binding Path=SelectedDate, StringFormat='yyyy-MM-dd', RelativeSource={RelativeSource AncestorType={x:Type DatePicker}}}" />
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </DatePicker.Resources>
                        </DatePicker>

                        <DatePicker
                            x:Name="dpToDate"
                            Grid.Column="3"
                            Grid.ColumnSpan="2"
                            Height="60"
                            Margin="8,0"
                            BorderBrush="{StaticResource PrimaryTextColor}"
                            BorderThickness="1"
                            DisplayDateStart="2000-01-01"
                            FontSize="18"
                            IsEnabled="False"
                            SelectedDateFormat="Short">
                            <DatePicker.Resources>
                                <Style TargetType="{x:Type DatePickerTextBox}">
                                    <Setter Property="Control.Template">
                                        <Setter.Value>
                                            <ControlTemplate>
                                                <TextBox
                                                    x:Name="PART_TextBox"
                                                    VerticalAlignment="Stretch"
                                                    BorderThickness="0"
                                                    Foreground="{StaticResource PrimaryTextColor}"
                                                    IsEnabled="{Binding Path=IsEnabled, RelativeSource={RelativeSource AncestorType={x:Type DatePicker}}}"
                                                    IsReadOnly="True"
                                                    Style="{StaticResource txtDatePick}"
                                                    Tag="الى تاريخ"
                                                    Text="{Binding Path=SelectedDate, StringFormat='yyyy-MM-dd', RelativeSource={RelativeSource AncestorType={x:Type DatePicker}}}" />
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </DatePicker.Resources>
                        </DatePicker>

                        <Border
                            x:Name="btnApplyFilter"
                            Grid.Column="5"
                            Height="44"
                            Margin="8,0"
                            Background="{StaticResource PrimaryColor}"
                            BorderThickness="0"
                            CornerRadius="16"
                            MouseLeftButtonDown="btnApplyFilter_Click">
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Cursor="Hand"
                                FontSize="16"
                                FontWeight="Bold"
                                Foreground="White"
                                Text="تطبيق" />
                        </Border>
                        <Border
                            x:Name="btnPrint"
                            Grid.Column="6"
                            Height="44"
                            Margin="8,0"
                            Background="Transparent"
                            BorderBrush="{StaticResource PrimaryColor}"
                            BorderThickness="1"
                            CornerRadius="16"
                            Cursor="Hand"
                            MouseLeftButtonDown="btnPrint_Click">
                            <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                <Path
                                    Width="20"
                                    Height="20"
                                    Margin="0,0,10,0"
                                    Data="M19 8H5c-1.66 0-3 1.34-3 3v6h4v4h12v-4h4v-6c0-1.66-1.34-3-3-3zm-3 11H8v-5h8v5zm3-7c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm-1-9H6v4h12V3z"
                                    Fill="{StaticResource PrimaryColor}"
                                    Stretch="Uniform" />
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FontSize="18"
                                    FontWeight="Bold"
                                    Foreground="{StaticResource PrimaryColor}"
                                    Text="طباعة" />
                            </StackPanel>
                        </Border>

                        <Border
                            x:Name="btnExportExcel"
                            Grid.Column="7"
                            Height="44"
                            Margin="8,0"
                            Background="Transparent"
                            BorderBrush="{StaticResource PrimaryColor}"
                            BorderThickness="1"
                            CornerRadius="16"
                            Cursor="Hand"
                            MouseLeftButtonDown="btnExportExcel_Click">
                            <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                <Path
                                    Width="20"
                                    Height="20"
                                    Margin="0,0,10,0"
                                    Data="M14,2H6C4.89,2 4,2.89 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20M12,17V15H8V17H12M16,13V11H8V13H16Z"
                                    Fill="{StaticResource PrimaryColor}"
                                    Stretch="Uniform" />
                                <TextBlock
                                    VerticalAlignment="Center"
                                    FontSize="18"
                                    FontWeight="Bold"
                                    Foreground="{StaticResource PrimaryColor}"
                                    Text="تصدير Excel" />
                            </StackPanel>
                        </Border>
                    </Grid>
                </Border>

                <!--  جدول كشف الحساب  -->
                <ListView
                    x:Name="list"
                    Grid.Row="3"
                    Grid.RowSpan="6"
                    Background="{DynamicResource PageColor}"
                    BorderThickness="1"
                    FontFamily="pack://application:,,,/Assets/#Cairo"
                    ScrollViewer.HorizontalScrollBarVisibility="Hidden"
                    SizeChanged="list_SizeChanged">
                    <ListView.BorderBrush>
                        <SolidColorBrush Opacity="0.42" Color="Black" />
                    </ListView.BorderBrush>
                    <ListView.ItemContainerStyle>
                        <Style TargetType="{x:Type ListViewItem}">
                            <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                            <Setter Property="VerticalContentAlignment" Value="Center" />
                            <Setter Property="BorderThickness" Value="0,0,0,1" />
                            <Setter Property="BorderBrush" Value="#EEEEEE" />
                            <Style.Triggers>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="#E3F2FD" />
                                    <Setter Property="Foreground" Value="Black" />
                                </Trigger>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#F5F5F5" />
                                </Trigger>
                                <DataTrigger Binding="{Binding IsSpecialRow}" Value="True">
                                    <Setter Property="Background" Value="#FFF8E1" />
                                    <Setter Property="FontWeight" Value="Bold" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </ListView.ItemContainerStyle>
                    <ListView.View>
                        <GridView AllowsColumnReorder="False">
                            <GridView.ColumnHeaderContainerStyle>
                                <Style TargetType="{x:Type GridViewColumnHeader}">
                                    <Setter Property="IsEnabled" Value="False" />
                                    <Setter Property="Height" Value="40" />
                                    <Setter Property="FontWeight" Value="Bold" />
                                    <Setter Property="Background" Value="#F5F5F5" />
                                    <Style.Triggers>
                                        <Trigger Property="IsEnabled" Value="False">
                                            <Setter Property="TextElement.Foreground" Value="Black" />
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </GridView.ColumnHeaderContainerStyle>
                            <GridViewColumn Width="120" Header="التاريخ">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="30"
                                            Padding="5,5,5,5"
                                            VerticalAlignment="Center"
                                            FontWeight="{Binding IsSpecialRow, Converter={StaticResource NonZeroToBoldConverter}}"
                                            Text="{Binding Date, StringFormat=\{0:yyyy-MM-dd\}}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn Width="150" Header="نوع العملية">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="30"
                                            Padding="5,5,5,5"
                                            VerticalAlignment="Center"
                                            FontWeight="{Binding IsSpecialRow, Converter={StaticResource NonZeroToBoldConverter}}"
                                            Text="{Binding OperationType}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn Width="120" Header="طريقة الدفع">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="30"
                                            Padding="5,5,5,5"
                                            VerticalAlignment="Center"
                                            FontWeight="{Binding IsSpecialRow, Converter={StaticResource NonZeroToBoldConverter}}"
                                            Text="{Binding PaymentMethod}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn Width="250" Header="البيان">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="30"
                                            Padding="5,5,5,5"
                                            VerticalAlignment="Center"
                                            FontWeight="{Binding IsSpecialRow, Converter={StaticResource NonZeroToBoldConverter}}"
                                            Text="{Binding Description}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn Width="120" Header="قبض">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="30"
                                            Padding="5,5,5,5"
                                            VerticalAlignment="Center"
                                            FontWeight="{Binding IsSpecialRow, Converter={StaticResource NonZeroToBoldConverter}}"
                                            Foreground="Green"
                                            Text="{Binding Incoming, StringFormat=\{0:N3\}}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn Width="120" Header="صرف">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="30"
                                            Padding="5,5,5,5"
                                            VerticalAlignment="Center"
                                            FontWeight="{Binding IsSpecialRow, Converter={StaticResource NonZeroToBoldConverter}}"
                                            Foreground="Red"
                                            Text="{Binding Outgoing, StringFormat=\{0:N3\}}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                            <GridViewColumn Width="120" Header="الرصيد">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="30"
                                            Padding="5,5,5,5"
                                            VerticalAlignment="Center"
                                            FontWeight="Bold"
                                            Foreground="{Binding Balance, Converter={StaticResource EmployeeBalanceToColorConverter}}"
                                            Text="{Binding Balance, StringFormat=\{0:N3\}}" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>
                        </GridView>
                    </ListView.View>
                </ListView>

                <!--  ملخص كشف الحساب  -->
                <Border
                    Grid.Row="8"
                    Margin="0,20,0,0"
                    Padding="10"
                    Background="#F5F5F5"
                    BorderBrush="#DDDDDD"
                    BorderThickness="1"
                    CornerRadius="8">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <Border
                            Grid.Column="1"
                            Margin="10,5"
                            Padding="10,5"
                            CornerRadius="5">
                            <TextBlock
                                x:Name="txtTotalDebit"
                                FontFamily="pack://application:,,,/Assets/#Cairo"
                                FontSize="18"
                                FontWeight="Bold"
                                Foreground="Red"
                                Text="مجموع المدين: 0.000" />
                        </Border>

                        <Border
                            Grid.Column="2"
                            Margin="10,5"
                            Padding="10,5"
                            CornerRadius="5">
                            <TextBlock
                                x:Name="txtTotalCredit"
                                FontFamily="pack://application:,,,/Assets/#Cairo"
                                FontSize="18"
                                FontWeight="Bold"
                                Foreground="Green"
                                Text="مجموع الدائن: 0.000" />
                        </Border>

                        <Border
                            Grid.Column="3"
                            Margin="10,5"
                            Padding="10,5"
                            CornerRadius="5">
                            <TextBlock
                                x:Name="txtFinalBalance"
                                FontFamily="pack://application:,,,/Assets/#Cairo"
                                FontSize="18"
                                FontWeight="Bold"
                                Text="الرصيد النهائي: 0.000" />
                        </Border>

                        <Border
                            Grid.Column="4"
                            Margin="10,5"
                            Padding="10,5"
                            CornerRadius="5">
                            <TextBlock
                                x:Name="txtBalanceStatus"
                                FontFamily="pack://application:,,,/Assets/#Cairo"
                                FontSize="18"
                                FontWeight="Bold"
                                Text="حالة الرصيد: متوازن" />
                        </Border>
                    </Grid>
                </Border>

                <!--  أزرار الإجراءات  -->
                <StackPanel
                    Grid.Row="9"
                    Margin="0,20,0,0"
                    HorizontalAlignment="Center"
                    Orientation="Horizontal" />
            </Grid>
        </Border>
    </Viewbox>
</Window>

