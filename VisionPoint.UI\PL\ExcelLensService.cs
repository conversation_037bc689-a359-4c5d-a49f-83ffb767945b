﻿using ClosedXML.Excel; // مكتبة لإنشاء وتعديل ملفات Excel
using ExcelDataReader;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.IO;
using System.Text;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Models;

namespace VisionPoint.UI.PL;

public class ExcelLensService : IDisposable
{
    private readonly AppDbContext _context = ServiceLocator.GetService<AppDbContext>();
    private bool _disposed = false;
    // تخطيط الأعمدة المخصص
    private Dictionary<string, string> _customColumnMapping;
    // معرف المخزن المحدد للاستيراد
    private int? _warehouseId;

    public ExcelLensService()
    {
    }

    // تعيين معرف المخزن للاستيراد
    public void SetWarehouseId(int warehouseId)
    {
        _warehouseId = warehouseId;
    }

    // تعيين تخطيط الأعمدة المخصص
    public void SetColumnMapping(Dictionary<string, string> columnMapping)
    {
        _customColumnMapping = columnMapping;
    }

    public async Task<(int Added, int Updated, List<string> Errors, List<ExcelRecord> errorRecords)> ImportExcel(string filePath, IProgress<ProgressReport> progress = null)
    {
        var errors = new List<string>();
        var errorRecords = new List<ExcelRecord>(); // لتجميع السجلات التي بها أخطاء
        int added = 0, updated = 0;
        var state = new ProgressState();

        // التحقق من وجود مخزن محدد
        if (!_warehouseId.HasValue)
        {
            errors.Add("لم يتم تحديد مخزن للاستيراد");
            return (0, 0, errors, errorRecords);
        }

        try
        {
            progress?.Report(new ProgressReport { Percentage = 5, Message = "جاري قراءة ملف Excel..." });
            var records = await ReadExcelData(filePath);

            if (records.Count == 0)
            {
                errors.Add("لم يتم العثور على أي سجلات صالحة في الملف");
                return (0, 0, errors, errorRecords);
            }

            state.Total = records.Count;

            progress?.Report(new ProgressReport { Percentage = 10, Message = "جاري تحليل البيانات..." });
            var recordGroups = records.GroupBy(r => r.Name).ToList();
            var existingData = await GetExistingData(records);

            progress?.Report(new ProgressReport { Percentage = 20, Message = "جاري معالجة البيانات..." });
            foreach (var group in recordGroups)
            {
                var lensName = group.Key;
                var existingLens = existingData.Lenses.GetValueOrDefault(lensName);

                // في حال وجود العدسة مع اختلاف الخصائص
                var firstRecord = group.First();
                if (existingLens != null && (existingLens.Dia != firstRecord.Dia || existingLens.BC != firstRecord.BC ||
                                              existingLens.Addtion != firstRecord.Add || existingLens.Axis != firstRecord.Axis))
                {
                    string groupError = $"خطأ: العدسة '{lensName}' موجودة بالفعل ولكن بخصائص مختلفة (Dia, BC, Addtion, Axis).";
                    errors.Add(groupError);

                    // تطبيق الخطأ على جميع السجلات في المجموعة (محسنة)
                    var groupRecords = group.Select(record =>
                    {
                        record.Error = groupError;
                        return record;
                    }).ToList();
                    errorRecords.AddRange(groupRecords);
                    continue;
                }

                if (existingLens == null)
                {
                    added += await CreateNewLens(group, existingData, errors, progress, state, errorRecords);
                }
                else
                {
                    updated += await UpdateExistingLens(existingLens, group, existingData, errors, progress, state, errorRecords);
                }
            }

            progress?.Report(new ProgressReport { Percentage = 90, Message = "جاري حفظ البيانات..." });

            try
            {
                var saveResult = await _context.SaveWithTransactionAndBusy("ImportExcel");

                if (!saveResult.State)
                {
                    errors.Add($"خطأ في حفظ البيانات: {saveResult.Message}");
                    return (0, 0, errors, errorRecords);
                }
            }
            catch (Microsoft.Data.SqlClient.SqlException sqlEx)
            {
                var detailedError = $"خطأ SQL: {sqlEx.Message}\n" +
                                  $"رقم الخطأ: {sqlEx.Number}\n" +
                                  $"الحالة: {sqlEx.State}\n" +
                                  $"الكلاس: {sqlEx.Class}\n" +
                                  $"الإجراء: {sqlEx.Procedure ?? "غير محدد"}\n" +
                                  $"رقم السطر: {sqlEx.LineNumber}\n" +
                                  $"الخادم: {sqlEx.Server ?? "غير محدد"}\n" +
                                  $"المصدر: {sqlEx.Source ?? "غير محدد"}\n" +
                                  $"تفاصيل كاملة: {sqlEx.ToString()}";

                errors.Add(detailedError);
                _context.Reverse();
                return (0, 0, errors, errorRecords);
            }
            catch (Exception ex)
            {
                var detailedError = $"خطأ عام في الحفظ: {ex.Message}\n" +
                                  $"النوع: {ex.GetType().Name}\n" +
                                  $"المصدر: {ex.Source}\n" +
                                  $"Stack Trace: {ex.StackTrace}";

                errors.Add(detailedError);
                _context.Reverse();
                return (0, 0, errors, errorRecords);
            }

            progress?.Report(new ProgressReport { Percentage = 100, Message = "تم الانتهاء من الاستيراد" });

            return (added, updated, errors, errorRecords);
        }
        catch (Microsoft.EntityFrameworkCore.DbUpdateException dbUpdateEx)
        {
            _context.Reverse();

            var detailedError = $"خطأ Entity Framework في الاستيراد: {dbUpdateEx.Message}\n";

            if (dbUpdateEx.InnerException is Microsoft.Data.SqlClient.SqlException sqlEx)
            {
                detailedError += $"خطأ SQL الداخلي:\n" +
                               $"رقم الخطأ: {sqlEx.Number}\n" +
                               $"الحالة: {sqlEx.State}\n" +
                               $"الكلاس: {sqlEx.Class}\n" +
                               $"الإجراء: {sqlEx.Procedure ?? "غير محدد"}\n" +
                               $"رقم السطر: {sqlEx.LineNumber}\n" +
                               $"الخادم: {sqlEx.Server ?? "غير محدد"}\n" +
                               $"المصدر: {sqlEx.Source ?? "غير محدد"}\n" +
                               $"رسالة SQL: {sqlEx.Message}\n";
            }

            detailedError += $"تفاصيل كاملة: {dbUpdateEx.ToString()}";

            // Log the error for debugging
            System.Diagnostics.Debug.WriteLine($"DbUpdate Exception in ImportExcel: {detailedError}");
            System.Console.WriteLine($"DbUpdate Exception in ImportExcel: {detailedError}");

            errors.Add(detailedError);
            return (0, 0, errors, errorRecords);
        }
        catch (Microsoft.Data.SqlClient.SqlException sqlEx)
        {
            _context.Reverse();

            var detailedError = $"خطأ SQL في الاستيراد: {sqlEx.Message}\n" +
                              $"رقم الخطأ: {sqlEx.Number}\n" +
                              $"الحالة: {sqlEx.State}\n" +
                              $"الكلاس: {sqlEx.Class}\n" +
                              $"الإجراء: {sqlEx.Procedure ?? "غير محدد"}\n" +
                              $"رقم السطر: {sqlEx.LineNumber}\n" +
                              $"الخادم: {sqlEx.Server ?? "غير محدد"}\n" +
                              $"المصدر: {sqlEx.Source ?? "غير محدد"}\n" +
                              $"تفاصيل كاملة: {sqlEx.ToString()}";

            // Log the error for debugging
            System.Diagnostics.Debug.WriteLine($"SQL Exception in ImportExcel: {detailedError}");
            System.Console.WriteLine($"SQL Exception in ImportExcel: {detailedError}");

            errors.Add(detailedError);
            return (0, 0, errors, errorRecords);
        }
        catch (Exception ex)
        {
            _context.Reverse();

            var detailedError = $"خطأ عام في الاستيراد: {ex.Message}\n" +
                              $"النوع: {ex.GetType().Name}\n" +
                              $"المصدر: {ex.Source}\n" +
                              $"Stack Trace: {ex.StackTrace}\n" +
                              $"تفاصيل كاملة: {ex.ToString()}";

            // Log the error for debugging
            System.Diagnostics.Debug.WriteLine($"General Exception in ImportExcel: {detailedError}");
            System.Console.WriteLine($"General Exception in ImportExcel: {detailedError}");

            errors.Add(detailedError);
            return (0, 0, errors, errorRecords);
        }
    }

    private async Task<int> CreateNewLens(IGrouping<string, ExcelRecord> group, ExistingData existingData,
        List<string> errors, IProgress<ProgressReport> progress, ProgressState state, List<ExcelRecord> errorRecords)
    {
        // التحقق من صحة البيانات الأساسية للمجموعة
        var firstRecord = group.First();
        if (!ValidateBasicData(firstRecord, errors, errorRecords))
        {
            // إضافة جميع سجلات المجموعة إلى قائمة الأخطاء
            foreach (var record in group.Skip(1))
            {
                record.Error = firstRecord.Error;
                errorRecords.Add(record);
            }
            return 0;
        }

        // الحصول على نوع العدسة من أول سجل في المجموعة
        string lensCategoryName = firstRecord.LensCategory;
        int? categoryId = null;

        // البحث عن نوع العدسة في قاعدة البيانات
        if (!string.IsNullOrEmpty(lensCategoryName))
        {
            var category = await _context.LensCategories.FirstOrDefaultAsyncWithBusy(c => c.Name == lensCategoryName, "FindLensCategory");
            if (category == null)
            {
                // إنشاء نوع جديد إذا لم يكن موجوداً
                var newCategory = new LensCategory
                {
                    Name = lensCategoryName,
                    CreatedById = CurrentUser.Id,
                    ModifiedById = CurrentUser.Id
                };
                try
                {
                    await _context.LensCategories.AddAsyncWithBusy(newCategory);
                    var result = await _context.SaveWithTransactionAndBusy("SaveNewLensCategory");
                    if (!result.State)
                    {
                        errors.Add($"خطأ في حفظ نوع العدسة: {result.Message}");
                        return 0;
                    }
                }
                catch (Microsoft.Data.SqlClient.SqlException sqlEx)
                {
                    var detailedError = $"خطأ SQL في حفظ نوع العدسة '{lensCategoryName}': {sqlEx.Message}\n" +
                                      $"رقم الخطأ: {sqlEx.Number}\n" +
                                      $"الحالة: {sqlEx.State}\n" +
                                      $"الكلاس: {sqlEx.Class}\n" +
                                      $"تفاصيل كاملة: {sqlEx.ToString()}";
                    errors.Add(detailedError);
                    return 0;
                }
                catch (Exception ex)
                {
                    errors.Add($"خطأ عام في حفظ نوع العدسة '{lensCategoryName}': {ex.Message}\nتفاصيل: {ex.ToString()}");
                    return 0;
                }
                categoryId = newCategory.Id;
            }
            else
            {
                categoryId = category.Id;
            }
        }
        else
        {
            errors.Add($"خطأ: نوع العدسة مطلوب للعدسة '{firstRecord.Name}'");
            return 0;
        }

        var newLens = new Lens
        {
            Name = group.Key,
            BC = group.First().BC,
            Dia = group.First().Dia,
            Addtion = group.First().Add,
            Axis = group.First().Axis,
            MinimumQuantity = group.First().MinimumQuantity,
            CategoryId = categoryId,
            LensPrescriptions = new List<LensPrescription>()
        };

        foreach (var record in group)
        {
            try
            {
                state.Processed++;
                progress?.Report(CreateProgressReport(state, $"جاري إضافة العدسة: {record.Name}"));

                // التحقق من صحة البيانات لكل سجل
                if (!ValidateRecordData(record, errors, errorRecords))
                {
                    continue;
                }

                short? sphereId = null, cylinderId = null, powId = null;
            if (record.SPH.HasValue)
            {
                sphereId = GetPrescriptionId(record.SPH, existingData, errors);
                if (sphereId == null)
                {
                    string errMsg = $"خطأ: قيمة Sphere '{record.SPH}' غير موجودة في جدول المقاسات.";
                    errors.Add(errMsg);
                    record.Error = errMsg;
                    errorRecords.Add(record);
                    continue;
                }
            }
            if (record.CYL.HasValue)
            {
                cylinderId = GetPrescriptionId(record.CYL, existingData, errors);
                if (cylinderId == null)
                {
                    string errMsg = $"خطأ: قيمة Cylinder '{record.CYL}' غير موجودة في جدول المقاسات.";
                    errors.Add(errMsg);
                    record.Error = errMsg;
                    errorRecords.Add(record);
                    continue;
                }
            }
            if (record.Pow.HasValue)
            {
                powId = GetPrescriptionId(record.Pow, existingData, errors);
                if (powId == null)
                {
                    string errMsg = $"خطأ: قيمة Power '{record.Pow}' غير موجودة في جدول المقاسات.";
                    errors.Add(errMsg);
                    record.Error = errMsg;
                    errorRecords.Add(record);
                    continue;
                }
            }

            var prescription = GetOrCreatePrescription(newLens, record, existingData, errors, sphereId, cylinderId, powId);
            var color = await GetOrCreateColorAsync(prescription, record, existingData, errors);

            // إذا كان اللون فارغاً، نضيف السجل إلى قائمة الأخطاء ونتخطى هذا السجل
            if (color == null)
            {
                errorRecords.Add(record);
                continue;
            }

            // تحديث قيمة Barcode في حال عدم التطابق
            if (color.Barcode != (string.IsNullOrWhiteSpace(record.Barcode) ? null : record.Barcode))
            {
                color.Barcode = string.IsNullOrWhiteSpace(record.Barcode) ? null : record.Barcode;
            }

            // تعديل الكميات بحيث نجمع القيم معاً
            UpdateQuantities(color, record.Exp, record.Qte, errors);
            }
            catch (Microsoft.EntityFrameworkCore.DbUpdateException dbUpdateEx)
            {
                var detailedError = $"خطأ Entity Framework في معالجة السجل '{record.Name}': {dbUpdateEx.Message}\n";

                if (dbUpdateEx.InnerException is Microsoft.Data.SqlClient.SqlException sqlEx)
                {
                    detailedError += $"خطأ SQL الداخلي: رقم {sqlEx.Number} - {sqlEx.Message}\n";
                }

                detailedError += $"تفاصيل كاملة: {dbUpdateEx.ToString()}";

                System.Diagnostics.Debug.WriteLine($"DbUpdate Exception in CreateNewLens for record '{record.Name}': {detailedError}");
                System.Console.WriteLine($"DbUpdate Exception in CreateNewLens for record '{record.Name}': {detailedError}");

                record.Error = detailedError;
                errors.Add(detailedError);
                errorRecords.Add(record);
            }
            catch (Microsoft.Data.SqlClient.SqlException sqlEx)
            {
                var detailedError = $"خطأ SQL في معالجة السجل '{record.Name}': {sqlEx.Message}\n" +
                                  $"رقم الخطأ: {sqlEx.Number}\n" +
                                  $"تفاصيل كاملة: {sqlEx.ToString()}";

                System.Diagnostics.Debug.WriteLine($"SQL Exception in CreateNewLens for record '{record.Name}': {detailedError}");
                System.Console.WriteLine($"SQL Exception in CreateNewLens for record '{record.Name}': {detailedError}");

                record.Error = detailedError;
                errors.Add(detailedError);
                errorRecords.Add(record);
            }
            catch (Exception ex)
            {
                var detailedError = $"خطأ عام في معالجة السجل '{record.Name}': {ex.Message}\n" +
                                  $"النوع: {ex.GetType().Name}\n" +
                                  $"تفاصيل كاملة: {ex.ToString()}";

                System.Diagnostics.Debug.WriteLine($"General Exception in CreateNewLens for record '{record.Name}': {detailedError}");
                System.Console.WriteLine($"General Exception in CreateNewLens for record '{record.Name}': {detailedError}");

                record.Error = detailedError;
                errors.Add(detailedError);
                errorRecords.Add(record);
            }
        }

        // newLens.Color = newLens.LensPrescriptions.Any(lp => lp.LensPrescriptionColors.Any());
        newLens.Sphere = newLens.LensPrescriptions.Any(lp => lp.SphereId != null);
        newLens.Cylinder = newLens.LensPrescriptions.Any(lp => lp.CylinderId != null);
        newLens.Power = newLens.LensPrescriptions.Any(lp => lp.PowId != null);
        newLens.Exp = newLens.LensPrescriptions.Any(lpc => lpc.LensPrescriptionColors.Any(lpcc => lpcc.LensQuantity.Any(lq => lq.Exp != null)));

        await _context.Lenses.AddAsyncWithBusy(newLens);
        return 1;
    }

    private async Task<int> UpdateExistingLens(Lens existingLens, IGrouping<string, ExcelRecord> group,
        ExistingData existingData, List<string> errors, IProgress<ProgressReport> progress, ProgressState state, List<ExcelRecord> errorRecords)
    {
        int updates = 0;

        // الحصول على نوع العدسة من أول سجل في المجموعة
        string lensCategoryName = group.First().LensCategory;

        // تحديث نوع العدسة إذا كان مختلفاً
        if (!string.IsNullOrEmpty(lensCategoryName))
        {
            var category = await _context.LensCategories.FirstOrDefaultAsyncWithBusy(c => c.Name == lensCategoryName, "FindLensCategoryForUpdate");
            if (category == null)
            {
                // إنشاء نوع جديد إذا لم يكن موجوداً
                var newCategory = new LensCategory
                {
                    Name = lensCategoryName,
                    CreatedById = CurrentUser.Id,
                    ModifiedById = CurrentUser.Id
                };
                await _context.LensCategories.AddAsyncWithBusy(newCategory);
                var result = await _context.SaveWithTransactionAndBusy("SaveNewLensCategoryForUpdate");
                if (!result.State)
                {
                    errors.Add($"خطأ في حفظ نوع العدسة للتحديث: {result.Message}");
                    return 0;
                }
                existingLens.CategoryId = newCategory.Id;
            }
            else if (existingLens.CategoryId != category.Id)
            {
                existingLens.CategoryId = category.Id;
            }
        }

        // تحديث خصائص العدسة من أول سجل في المجموعة
        var firstRecord = group.First();
        existingLens.BC = firstRecord.BC;
        existingLens.Dia = firstRecord.Dia;
        existingLens.Addtion = firstRecord.Add;
        existingLens.Axis = firstRecord.Axis;
        existingLens.MinimumQuantity = firstRecord.MinimumQuantity;

        foreach (var record in group)
        {
            state.Processed++;
            progress?.Report(CreateProgressReport(state, $"جاري تحديث العدسة: {record.Name}"));

            // التحقق من صحة البيانات لكل سجل
            if (!ValidateRecordData(record, errors, errorRecords))
            {
                continue;
            }

            short? sphereId = null, cylinderId = null, powId = null;
            if (record.SPH.HasValue)
            {
                sphereId = GetPrescriptionId(record.SPH, existingData, errors);
                if (sphereId == null)
                {
                    string errMsg = $"خطأ: قيمة Sphere '{record.SPH}' غير موجودة في جدول المقاسات.";
                    errors.Add(errMsg);
                    record.Error = errMsg;
                    errorRecords.Add(record);
                    continue;
                }
            }
            if (record.CYL.HasValue)
            {
                cylinderId = GetPrescriptionId(record.CYL, existingData, errors);
                if (cylinderId == null)
                {
                    string errMsg = $"خطأ: قيمة Cylinder '{record.CYL}' غير موجودة في جدول المقاسات.";
                    errors.Add(errMsg);
                    record.Error = errMsg;
                    errorRecords.Add(record);
                    continue;
                }
            }
            if (record.Pow.HasValue)
            {
                powId = GetPrescriptionId(record.Pow, existingData, errors);
                if (powId == null)
                {
                    string errMsg = $"خطأ: قيمة Power '{record.Pow}' غير موجودة في جدول المقاسات.";
                    errors.Add(errMsg);
                    record.Error = errMsg;
                    errorRecords.Add(record);
                    continue;
                }
            }

            var prescription = GetOrCreatePrescription(existingLens, record, existingData, errors, sphereId, cylinderId, powId);

            if (prescription.SphereId == sphereId && prescription.CylinderId == cylinderId && prescription.PowId == powId)
            {
                prescription.CostPrice = record.Cost;
                prescription.SellPrice = record.Price;
            }

            var color = await GetOrCreateColorAsync(prescription, record, existingData, errors);

            // إذا كان اللون فارغاً، نضيف السجل إلى قائمة الأخطاء ونتخطى هذا السجل
            if (color == null)
            {
                errorRecords.Add(record);
                continue;
            }

            if (color.Barcode != (string.IsNullOrWhiteSpace(record.Barcode) ? null : record.Barcode))
            {
                color.Barcode = string.IsNullOrWhiteSpace(record.Barcode) ? null : record.Barcode;
            }
            updates += UpdateQuantities(color, record.Exp, record.Qte, errors);
        }

        // existingLens.Color = existingLens.LensPrescriptions.Any(lp => lp.LensPrescriptionColors.Any());
        existingLens.Sphere = existingLens.LensPrescriptions.Any(lp => lp.SphereId != null);
        existingLens.Cylinder = existingLens.LensPrescriptions.Any(lp => lp.CylinderId != null);
        existingLens.Power = existingLens.LensPrescriptions.Any(lp => lp.PowId != null);
        existingLens.Exp = existingLens.LensPrescriptions.Any(lpc => lpc.LensPrescriptionColors.Any(lpcc => lpcc.LensQuantity.Any(lq => lq.Exp != null)));
        return updates;
    }

    /// <summary>
    /// تحديث الكميات مع التحقق من صحة البيانات
    /// </summary>
    private int UpdateQuantities(LensPrescriptionColor color, DateOnly? exp, int qte, List<string> errors)
    {
        // التحقق من صحة الكمية - السماح بالكمية صفر
        if (qte < 0)
        {
            errors.Add($"خطأ: الكمية لا يمكن أن تكون سالبة");
            return 0;
        }

        var existingQuantity = color.LensQuantity.FirstOrDefault(q =>
            q.Exp == exp && q.WarehouseId == _warehouseId.Value);

        if (existingQuantity != null)
        {
            existingQuantity.Quantity += qte;
            return 0; // تم التحديث فقط
        }
        else
        {
            color.LensQuantity.Add(new LensQuantity
            {
                Exp = exp,
                Quantity = qte,
                WarehouseId = _warehouseId.Value
            });
            return 1; // تمت الإضافة
        }
    }

    // تعريف الدالة المساعدة لإنشاء أو استرجاع Prescription
    private LensPrescription GetOrCreatePrescription(Lens lens, ExcelRecord record, ExistingData existingData, List<string> errors, short? sphereId, short? cylinderId, short? powId)
    {
        var existingPrescription = lens.LensPrescriptions.FirstOrDefault(lp =>
            lp.SphereId == sphereId &&
            lp.CylinderId == cylinderId &&
            lp.PowId == powId);

        if (existingPrescription != null)
        {
            return existingPrescription;
        }

        var newPrescription = new LensPrescription
        {
            SphereId = sphereId,
            CylinderId = cylinderId,
            PowId = powId,
            CostPrice = record.Cost,
            SellPrice = record.Price,
            LensPrescriptionColors = new List<LensPrescriptionColor>()
        };
        lens.LensPrescriptions.Add(newPrescription);
        return newPrescription;
    }

    // تعريف الدالة المساعدة لإنشاء أو استرجاع Color
    private async Task<LensPrescriptionColor> GetOrCreateColorAsync(LensPrescription prescription, ExcelRecord record, ExistingData existingData, List<string> errors)
    {
        byte? colorId = null;

        // إدارة الألوان وفقاً للمتطلبات الجديدة
        if (string.IsNullOrWhiteSpace(record.Color) && string.IsNullOrWhiteSpace(record.HexCode))
        {
            // إذا كان اللون فارغ في الإكسل، أضف اللون الافتراضي الأول فقط إذا لم يكن هناك اسم
            if (string.IsNullOrWhiteSpace(record.Name))
            {
                colorId = await GetDefaultColorId();
            }
            else
            {
                string errMsg = $"خطأ: اللون مطلوب للعدسة '{record.Name}'";
                errors.Add(errMsg);
                record.Error = errMsg;
                return null;
            }
        }
        else if (!string.IsNullOrWhiteSpace(record.Color))
        {
            // إذا كان هناك اسم لون، ابحث عنه واستخدمه، وإذا لم يوجد فأنشئه
            colorId = await GetOrCreateColorByName(record.Color, record.HexCode, existingData, errors);
        }
        else if (!string.IsNullOrWhiteSpace(record.HexCode))
        {
            // إذا كان هناك كود وليس اسم، تحقق من وجوده في قاعدة البيانات واستخدمه
            colorId = await GetColorByHexCode(record.HexCode, existingData, errors);
        }

        // التأكد من أن colorId ليس null قبل المتابعة
        if (colorId == null)
        {
            string errMsg = $"خطأ: فشل في إنشاء أو العثور على لون للعدسة '{record.Name}'";
            errors.Add(errMsg);
            record.Error = errMsg;
            return null;
        }

        string normalizedBarcode = string.IsNullOrWhiteSpace(record.Barcode) ? null : record.Barcode;
        var existingColor = prescription.LensPrescriptionColors.FirstOrDefault(c => c.ColorId == colorId &&
            ((c.Barcode == normalizedBarcode) ||
             (c.Barcode == null && normalizedBarcode == null)));
        if (existingColor != null)
        {
            return existingColor;
        }

        var newLensColor = new LensPrescriptionColor
        {
            ColorId = colorId,
            Barcode = string.IsNullOrWhiteSpace(record.Barcode) ? null : record.Barcode,
            LensQuantity = new List<LensQuantity>()
        };
        prescription.LensPrescriptionColors.Add(newLensColor);
        return newLensColor;
    }

    /// <summary>
    /// التحقق من صحة البيانات الأساسية للسجل
    /// </summary>
    private bool ValidateBasicData(ExcelRecord record, List<string> errors, List<ExcelRecord> errorRecords)
    {
        var validationErrors = new List<string>();

        // 1. التحقق من أن الاسم غير فارغ
        if (string.IsNullOrWhiteSpace(record.Name))
        {
            validationErrors.Add("اسم العدسة مطلوب ولا يمكن أن يكون فارغاً");
        }

        // 2. التحقق من أن النوع غير فارغ
        if (string.IsNullOrWhiteSpace(record.LensCategory))
        {
            validationErrors.Add("نوع العدسة مطلوب ولا يمكن أن يكون فارغاً");
        }

        // 3. التحقق من الكمية والسعر
        if (record.Qte > 0 && record.Price <= 0)
        {
            validationErrors.Add("السعر مطلوب عندما تكون الكمية أكبر من صفر");
        }

        // 4. التحقق من صحة القيم الرقمية
        if (!ValidateNumericValues(record, validationErrors))
        {
            // تم إضافة الأخطاء في ValidateNumericValues
        }

        if (validationErrors.Any())
        {
            var errorMessage = string.Join("; ", validationErrors);
            record.Error = errorMessage;
            errors.AddRange(validationErrors);
            errorRecords.Add(record);
            return false;
        }

        return true;
    }

    /// <summary>
    /// التحقق من صحة بيانات السجل الفردي
    /// </summary>
    private bool ValidateRecordData(ExcelRecord record, List<string> errors, List<ExcelRecord> errorRecords)
    {
        var validationErrors = new List<string>();

        // التحقق من الكمية والسعر للسجل الفردي
        if (record.Qte > 0 && record.Price <= 0)
        {
            validationErrors.Add($"السعر مطلوب عندما تكون الكمية أكبر من صفر للعدسة '{record.Name}'");
        }

        // التحقق من صحة القيم الرقمية
        if (!ValidateNumericValues(record, validationErrors))
        {
            // تم إضافة الأخطاء في ValidateNumericValues
        }

        if (validationErrors.Any())
        {
            var errorMessage = string.Join("; ", validationErrors);
            record.Error = errorMessage;
            errors.AddRange(validationErrors);
            errorRecords.Add(record);
            return false;
        }

        return true;
    }

    /// <summary>
    /// التحقق من صحة القيم الرقمية وتحويلها
    /// </summary>
    private bool ValidateNumericValues(ExcelRecord record, List<string> validationErrors)
    {
        bool isValid = true;

        // التحقق من القيم الرقمية وتحويلها إذا لزم الأمر
        var numericFields = new[]
        {
            (nameof(record.SPH), record.SPH),
            (nameof(record.CYL), record.CYL),
            (nameof(record.Pow), record.Pow),
            (nameof(record.BC), record.BC),
            (nameof(record.Dia), record.Dia),
            (nameof(record.Add), record.Add),
            (nameof(record.Axis), record.Axis)
        };

        foreach (var (fieldName, value) in numericFields)
        {
            if (value.HasValue)
            {
                // التحقق من أن القيمة في النطاق المقبول
                if (value.Value < -99.99m || value.Value > 99.99m)
                {
                    validationErrors.Add($"قيمة {fieldName} ({value.Value}) خارج النطاق المقبول (-99.99 إلى 99.99)");
                    isValid = false;
                }
            }
        }

        return isValid;
    }

    /// <summary>
    /// الحصول على اللون الافتراضي (أول لون في قاعدة البيانات)
    /// </summary>
    private async Task<byte?> GetDefaultColorId()
    {
        var defaultColor = await _context.Colors.OrderBy(c => c.Id).FirstOrDefaultAsyncWithBusy("GetDefaultColor");
        return defaultColor?.Id;
    }

    /// <summary>
    /// إنشاء أو الحصول على لون بالاسم
    /// </summary>
    private async Task<byte?> GetOrCreateColorByName(string colorName, string hexCode, ExistingData existingData, List<string> errors)
    {
        // البحث عن اللون بالاسم أولاً
        var existingColor = existingData.Colors.Values.FirstOrDefault(c =>
            c.Name.Equals(colorName.Trim(), StringComparison.OrdinalIgnoreCase));

        if (existingColor != null)
        {
            return existingColor.Id;
        }

        // إذا لم يوجد، أنشئ لون جديد
        var newHexCode = !string.IsNullOrWhiteSpace(hexCode) ? hexCode.Trim() : GenerateRandomHexCode(existingData);

        var newColor = new Color
        {
            Name = colorName.Trim(),
            HexCode = newHexCode
        };

        try
        {
            _context.Colors.AddWithBusy(newColor);
            var result = await _context.SaveWithTransactionAndBusy("SaveNewColorByName");

            if (!result.State)
            {
                errors.Add($"خطأ في حفظ اللون الجديد '{colorName}': {result.Message}");
                return null;
            }
        }
        catch (Microsoft.Data.SqlClient.SqlException sqlEx)
        {
            var detailedError = $"خطأ SQL في حفظ اللون '{colorName}': {sqlEx.Message}\n" +
                              $"رقم الخطأ: {sqlEx.Number}\n" +
                              $"الحالة: {sqlEx.State}\n" +
                              $"الكلاس: {sqlEx.Class}\n" +
                              $"تفاصيل كاملة: {sqlEx.ToString()}";
            errors.Add(detailedError);
            return null;
        }
        catch (Exception ex)
        {
            errors.Add($"خطأ عام في حفظ اللون '{colorName}': {ex.Message}\nتفاصيل: {ex.ToString()}");
            return null;
        }

        existingData.Colors[newHexCode] = newColor;
        return newColor.Id;
    }

    /// <summary>
    /// الحصول على لون بالكود السادس عشري
    /// </summary>
    private async Task<byte?> GetColorByHexCode(string hexCode, ExistingData existingData, List<string> errors)
    {
        if (existingData.Colors.TryGetValue(hexCode.Trim(), out var existingColor))
        {
            return existingColor.Id;
        }

        // البحث في قاعدة البيانات
        var dbColor = await _context.Colors.FirstOrDefaultAsyncWithBusy(c => c.HexCode == hexCode.Trim(), "FindColorByHexCode");
        if (dbColor != null)
        {
            existingData.Colors[hexCode.Trim()] = dbColor;
            return dbColor.Id;
        }

        errors.Add($"لم يتم العثور على لون بالكود '{hexCode}'");
        return null;
    }

    /// <summary>
    /// إنشاء كود عشوائي للون غير موجود
    /// </summary>
    private string GenerateRandomHexCode(ExistingData existingData)
    {
        var random = new Random();
        string hexCode;

        do
        {
            // إنشاء كود عشوائي بصيغة #RRGGBB
            var r = random.Next(0, 256);
            var g = random.Next(0, 256);
            var b = random.Next(0, 256);
            hexCode = $"#{r:X2}{g:X2}{b:X2}";
        }
        while (existingData.Colors.ContainsKey(hexCode) || _context.Colors.Any(c => c.HexCode == hexCode));

        return hexCode;
    }

    private short? GetPrescriptionId(decimal? value, ExistingData existingData, List<string> errors)
    {
        if (!value.HasValue) return null;
        if (existingData.Prescriptions.TryGetValue(value.Value, out var prescription))
        {
            return prescription.Id;
        }

        errors.Add($"قيمة الوصفة {value} غير موجودة في قاعدة البيانات.");
        return null;
    }

    /// <summary>
    /// تحويل القيمة إلى decimal مع التعامل مع الأرقام الصحيحة
    /// </summary>
    private decimal? GetDecimal(object value)
    {
        if (value == null) return null;

        // التعامل مع الأنواع المختلفة
        switch (value)
        {
            case decimal d:
                return d;
            case int i:
                return (decimal)i;
            case double db:
                return (decimal)db;
            case float f:
                return (decimal)f;
            case long l:
                return (decimal)l;
            case string s when !string.IsNullOrWhiteSpace(s):
                return decimal.TryParse(s, out decimal result) ? result : null;
            default:
                return decimal.TryParse(value.ToString(), out decimal parseResult) ? parseResult : null;
        }
    }

    private DateOnly? GetDate(object value)
    {
        return value == null ? null :
            DateTime.TryParse(value.ToString(), out DateTime result) ?
            DateOnly.FromDateTime(result) : null;
    }

    private int GetInt(object value)
    {
        if (value == null) return 0;

        return value switch
        {
            int i => i,
            decimal d => (int)d,
            double db => (int)db,
            float f => (int)f,
            long l => (int)l,
            string s when int.TryParse(s, out int result) => result,
            _ => int.TryParse(value.ToString(), out int parseResult) ? parseResult : 0
        };
    }

    // تعريف الحقول المطلوبة والاختيارية للعدسات
    public Dictionary<string, views.Pages.ProductsContent.FieldInfo> GetRequiredFields()
    {
        return new Dictionary<string, views.Pages.ProductsContent.FieldInfo>
        {
            { "Name", new views.Pages.ProductsContent.FieldInfo { DisplayName = "اسم العدسة", IsRequired = true, PossibleColumnNames = new[] {"اسم العدسة", "إسم العدسة", "اسم الصنف", "إسم الصنف",  "العدسة", "Name", "Lens", "التصنيف" } } },
            { "LensCategory", new views.Pages.ProductsContent.FieldInfo { DisplayName = "نوع العدسة", IsRequired = true, PossibleColumnNames = new[] { "نوع العدسة", "التصنيف", "Category", "Type" } } },
            { "Barcode", new views.Pages.ProductsContent.FieldInfo { DisplayName = "باركود", IsRequired = false, PossibleColumnNames = new[] { "باركود الوحدة", "باركود", "الباركود", "Barcode", "Code" } } },
            { "HexCode", new views.Pages.ProductsContent.FieldInfo { DisplayName = "كود اللون", IsRequired = false, PossibleColumnNames = new[] { "كود اللون", "رمز اللون", "HexCode", "ColorCode" } } },
            { "Color", new views.Pages.ProductsContent.FieldInfo { DisplayName = "اسم اللون", IsRequired = false, PossibleColumnNames = new[] { "إسم اللون", "اسم اللون", "اللون", "Color" } } },
            { "SPH", new views.Pages.ProductsContent.FieldInfo { DisplayName = "SPH", IsRequired = false, PossibleColumnNames = new[] { "SPH", "Sphere" } } },
            { "CYL", new views.Pages.ProductsContent.FieldInfo { DisplayName = "CYL", IsRequired = false, PossibleColumnNames = new[] { "CYL", "Cylinder" } } },
            { "POW", new views.Pages.ProductsContent.FieldInfo { DisplayName = "POW", IsRequired = false, PossibleColumnNames = new[] { "POW", "Power" } } },
            { "BC", new views.Pages.ProductsContent.FieldInfo { DisplayName = "BC", IsRequired = false, PossibleColumnNames = new[] { "BC" } } },
            { "DIA", new views.Pages.ProductsContent.FieldInfo { DisplayName = "DIA", IsRequired = false, PossibleColumnNames = new[] { "DIA", "Dia" } } },
            { "ADD", new views.Pages.ProductsContent.FieldInfo { DisplayName = "ADD", IsRequired = false, PossibleColumnNames = new[] { "ADD", "Add" } } },
            { "AXIS", new views.Pages.ProductsContent.FieldInfo { DisplayName = "AXIS", IsRequired = false, PossibleColumnNames = new[] { "AXIS", "Axis" } } },
            { "Quantity", new views.Pages.ProductsContent.FieldInfo { DisplayName = "الكمية", IsRequired = true, PossibleColumnNames = new[] { "الكمية في العبوة", "الكمية", "كمية", "Quantity", "Qty" } } },
            { "MinimumQuantity", new views.Pages.ProductsContent.FieldInfo { DisplayName = "الحد الأدنى", IsRequired = false, PossibleColumnNames = new[] { "الحد الأدنى", "الحد الادنى", "الحد الأدنى للكمية", "الحد الادنى للكمية", "MinimumQuantity", "MinQty", "MinStock" } } },
            { "Cost", new views.Pages.ProductsContent.FieldInfo { DisplayName = "سعر تكلفة", IsRequired = true, PossibleColumnNames = new[] { "تكلفة العبوة", "تكلفة", "سعر التكلفة", "Cost", "CostPrice" } } },
            { "Price", new views.Pages.ProductsContent.FieldInfo { DisplayName = "سعر بيع", IsRequired = true, PossibleColumnNames = new[] { "سعر بيع الوحدة", "سعر البيع", "سعر", "Price", "SellPrice" } } },
            { "Expiration", new views.Pages.ProductsContent.FieldInfo { DisplayName = "تاريخ الصلاحية", IsRequired = false, PossibleColumnNames = new[] { "له تاريخ صلاحية", "تاريخ الصلاحية", "الصلاحية", "Expiration", "Exp" } } }
        };
    }

    // قراءة بيانات Excel باستخدام تخطيط الأعمدة المخصص
    private async Task<List<ExcelRecord>> ReadExcelData(string filePath)
    {
        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
        var records = new List<ExcelRecord>();

        using var stream = File.Open(filePath, FileMode.Open, FileAccess.Read);
        using var reader = ExcelReaderFactory.CreateReader(stream);
        var dataSet = reader.AsDataSet(new ExcelDataSetConfiguration
        {
            ConfigureDataTable = _ => new ExcelDataTableConfiguration { UseHeaderRow = true }
        });

        var table = dataSet.Tables[0];

        // الحصول على تخطيط الأعمدة من المستخدم
        var columnMapping = GetColumnMapping(table.Columns);
        if (columnMapping == null || !columnMapping.Any())
        {
            throw new Exception("لم يتم تحديد تخطيط الأعمدة بشكل صحيح");
        }

        foreach (DataRow row in table.Rows)
        {
            try
            {
                var record = new ExcelRecord
                {
                    Name = GetStringValue(row, columnMapping, "Name"),
                    LensCategory = GetStringValue(row, columnMapping, "LensCategory"),
                    Barcode = GetStringValue(row, columnMapping, "Barcode"),
                    HexCode = GetStringValue(row, columnMapping, "HexCode"),
                    Color = GetStringValue(row, columnMapping, "Color"),
                    SPH = columnMapping.ContainsKey("SPH") ? GetDecimal(row[columnMapping["SPH"]]) : null,
                    CYL = columnMapping.ContainsKey("CYL") ? GetDecimal(row[columnMapping["CYL"]]) : null,
                    Pow = columnMapping.ContainsKey("POW") ? GetDecimal(row[columnMapping["POW"]]) : null,
                    BC = columnMapping.ContainsKey("BC") ? GetDecimal(row[columnMapping["BC"]]) : null,
                    Dia = columnMapping.ContainsKey("DIA") ? GetDecimal(row[columnMapping["DIA"]]) : null,
                    Add = columnMapping.ContainsKey("ADD") ? GetDecimal(row[columnMapping["ADD"]]) : null,
                    Axis = columnMapping.ContainsKey("AXIS") ? GetDecimal(row[columnMapping["AXIS"]]) : null,
                    Qte = columnMapping.ContainsKey("Quantity") ? GetInt(row[columnMapping["Quantity"]]) : 0,
                    MinimumQuantity = columnMapping.ContainsKey("MinimumQuantity") ? GetInt(row[columnMapping["MinimumQuantity"]]) : 0,
                    Cost = columnMapping.ContainsKey("Cost") ? GetDecimal(row[columnMapping["Cost"]]) ?? 0m : 0m,
                    Price = columnMapping.ContainsKey("Price") ? GetDecimal(row[columnMapping["Price"]]) ?? 0m : 0m,
                    Exp = columnMapping.ContainsKey("Expiration") ? GetDate(row[columnMapping["Expiration"]]) : null,
                    Error = string.Empty
                };

                // تخطي السجلات التي لا تحتوي على اسم
                if (!string.IsNullOrEmpty(record.Name))
                {
                    records.Add(record);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error processing row: {ex.Message}");
                // تخطي السجلات التي بها أخطاء
                continue;
            }
        }

        return records;
    }

    // الحصول على قيمة نصية من الصف باستخدام تخطيط الأعمدة
    private string GetStringValue(DataRow row, Dictionary<string, string> columnMapping, string fieldName)
    {
        if (columnMapping.ContainsKey(fieldName))
        {
            return row[columnMapping[fieldName]]?.ToString() ?? string.Empty;
        }
        return string.Empty;
    }

    // الحصول على تخطيط الأعمدة
    private Dictionary<string, string> GetColumnMapping(DataColumnCollection columns)
    {
        // إذا تم تعيين تخطيط أعمدة مخصص، استخدمه
        if (_customColumnMapping != null && _customColumnMapping.Any())
        {
            // التحقق من وجود الأعمدة المطلوبة (محسنة)
            var missingColumns = _customColumnMapping
                .Where(mapping => !columns.Contains(mapping.Value))
                .Select(mapping => mapping.Value)
                .ToList();

            if (missingColumns.Any())
            {
                System.Diagnostics.Debug.WriteLine($"Missing columns: {string.Join(", ", missingColumns)}");
            }

            return _customColumnMapping;
        }

        // في حالة عدم وجود تخطيط مخصص، نستخدم التخطيط الافتراضي
        var defaultMapping = new Dictionary<string, string>
        {
            { "Name", "اسم العدسة" },
            { "LensCategory", "نوع العدسة" },
            { "Barcode", "باركود الوحدة" },
            { "HexCode", "كود اللون" },
            { "Color", "إسم اللون" },
            { "SPH", "SPH" },
            { "CYL", "CYL" },
            { "POW", "POW" },
            { "BC", "BC" },
            { "DIA", "DIA" },
            { "ADD", "ADD" },
            { "AXIS", "AXIS" },
            { "Quantity", "الكمية في العبوة" },
            { "MinimumQuantity", "الحد الأدنى" },
            { "Cost", "تكلفة العبوة" },
            { "Price", "سعر بيع الوحدة" },
            { "Expiration", "له تاريخ صلاحية" }
        };

        // التحقق من وجود الأعمدة المطلوبة (محسنة)
        var missingDefaultColumns = defaultMapping
            .Where(mapping => !columns.Contains(mapping.Value))
            .Select(mapping => mapping.Value)
            .ToList();

        if (missingDefaultColumns.Any())
        {
            System.Diagnostics.Debug.WriteLine($"Missing default columns: {string.Join(", ", missingDefaultColumns)}");
        }

        return defaultMapping;
    }

    private async Task<ExistingData> GetExistingData(List<ExcelRecord> records)
    {
        var lensNames = records.Select(r => r.Name).Distinct().ToList();
        var hexCodes = records.Select(r => r.HexCode).Where(h => !string.IsNullOrEmpty(h)).Distinct().ToList();
        var prescriptionValues = records
            .SelectMany(r => new[] { r.SPH, r.CYL, r.Pow })
            .Where(v => v.HasValue)
            .Select(v => v.Value)
            .Distinct()
            .ToList();

        // جلب جميع الوصفات وتجميعها حسب القيمة لتجنب المفاتيح المكررة
        var prescriptionsList = await _context.Prescriptions
            .Where(p => prescriptionValues.Contains(p.Value))
            .ToListAsyncWithBusy("LoadExistingPrescriptions");

        // إنشاء Dictionary مع أخذ أول سجل لكل قيمة في حالة وجود مكررات
        var prescriptionsDict = prescriptionsList
            .GroupBy(p => p.Value)
            .ToDictionary(g => g.Key, g => g.First());

        return new ExistingData
        {
            Lenses = await _context.Lenses
                .Include(l => l.LensPrescriptions)
                .ThenInclude(lp => lp.LensPrescriptionColors)
                .ThenInclude(lpc => lpc.LensQuantity)
                .Where(l => lensNames.Contains(l.Name))
                .ToDictionaryAsyncWithBusy(l => l.Name, "LoadExistingLenses"),
            Colors = await _context.Colors
                .Where(c => hexCodes.Contains(c.HexCode))
                .ToDictionaryAsyncWithBusy(c => c.HexCode, "LoadExistingColors"),
            Prescriptions = prescriptionsDict
        };
    }

    // دالة إنشاء تقرير التقدم مع رسالة توضيحية تشمل نوع العملية وعدد السجلات المعالجة والنسبة المئوية
    private ProgressReport CreateProgressReport(ProgressState state, string action)
    {
        int percentage = (int)((state.Processed * 100.0) / state.Total);
        string message = $"{action} - تمت معالجة {state.Processed} من {state.Total} سجل. نسبة التقدم: {percentage}%";
        return new ProgressReport
        {
            Percentage = percentage,
            Message = message
        };
    }

    // الدالة لإنشاء ملف Excel يحتوي على السجلات التي بها أخطاء مع عمود "Error Reason"
    public void ExportErrorExcel(List<ExcelRecord> errorRecords, string filePath)
    {
        var workbook = new XLWorkbook();
        var worksheet = workbook.Worksheets.Add("Errors");
        // عناوين الأعمدة
        worksheet.Cell(1, 1).Value = "الاسم";
        worksheet.Cell(1, 2).Value = "الباركود";
        worksheet.Cell(1, 3).Value = "رمز اللون";
        worksheet.Cell(1, 4).Value = "اسم اللون";
        worksheet.Cell(1, 5).Value = "SPH";
        worksheet.Cell(1, 6).Value = "CYL";
        worksheet.Cell(1, 7).Value = "Pow";
        worksheet.Cell(1, 8).Value = "BC";
        worksheet.Cell(1, 9).Value = "Dia";
        worksheet.Cell(1, 10).Value = "Add";
        worksheet.Cell(1, 11).Value = "Axis";
        worksheet.Cell(1, 12).Value = "الكمية";
        worksheet.Cell(1, 13).Value = "الحد الأدنى";
        worksheet.Cell(1, 14).Value = "سعر التكلفة";
        worksheet.Cell(1, 15).Value = "سعر البيع";
        worksheet.Cell(1, 16).Value = "الصلاحية";
        worksheet.Cell(1, 17).Value = "نوع العدسة";
        worksheet.Cell(1, 18).Value = "سبب الخطا";

        int row = 2;
        foreach (var record in errorRecords)
        {
            worksheet.Cell(row, 1).Value = record.Name;
            worksheet.Cell(row, 2).Value = record.Barcode;
            worksheet.Cell(row, 3).Value = record.HexCode;
            worksheet.Cell(row, 4).Value = record.Color;
            worksheet.Cell(row, 5).Value = record.SPH;
            worksheet.Cell(row, 6).Value = record.CYL;
            worksheet.Cell(row, 7).Value = record.Pow;
            worksheet.Cell(row, 8).Value = record.BC;
            worksheet.Cell(row, 9).Value = record.Dia;
            worksheet.Cell(row, 10).Value = record.Add;
            worksheet.Cell(row, 11).Value = record.Axis;
            worksheet.Cell(row, 12).Value = record.Qte;
            worksheet.Cell(row, 13).Value = record.MinimumQuantity;
            worksheet.Cell(row, 14).Value = record.Cost;
            worksheet.Cell(row, 15).Value = record.Price;
            worksheet.Cell(row, 16).Value = record.Exp?.ToString("yyyy-MM-dd") ?? "";
            worksheet.Cell(row, 17).Value = record.LensCategory;
            worksheet.Cell(row, 18).Value = record.Error;
            row++;
        }

        // Auto-fit columns for better readability
        worksheet.Columns().AdjustToContents();

        // If the path is a directory, create a file name with timestamp
        string errorFilePath;
        if (Directory.Exists(filePath))
        {
            errorFilePath = Path.Combine(filePath, $"LensImportErrors_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx");
        }
        else
        {
            // If it's already a file path, use it directly
            errorFilePath = filePath;
        }

        workbook.SaveAs(errorFilePath);
    }

    // تعريف الكلاس الخاص بسجل الإكسل مع إضافة خاصية Error لتخزين سبب الخطأ
    public class ExcelRecord
    {
        public string Name { get; set; } = string.Empty;
        public string Barcode { get; set; } = string.Empty;
        public string HexCode { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public decimal? SPH { get; set; }
        public decimal? CYL { get; set; }
        public decimal? Pow { get; set; }
        public decimal? BC { get; set; }
        public decimal? Dia { get; set; }
        public decimal? Add { get; set; }
        public decimal? Axis { get; set; }
        public int Qte { get; set; }
        public int MinimumQuantity { get; set; } = 0;
        public decimal Cost { get; set; }
        public decimal Price { get; set; }
        public DateOnly? Exp { get; set; }
        public string Error { get; set; } = string.Empty;
        // إضافة حقل لنوع العدسة
        public string LensCategory { get; set; } = string.Empty;
    }

    private class ExistingData
    {
        public Dictionary<string, Lens> Lenses { get; set; } = new Dictionary<string, Lens>();
        public Dictionary<string, Color> Colors { get; set; } = new Dictionary<string, Color>();
        public Dictionary<decimal, Prescription> Prescriptions { get; set; } = new Dictionary<decimal, Prescription>();
    }

    private class ProgressState
    {
        public int Processed { get; set; }
        public int Total { get; set; }
    }

    public class ProgressReport
    {
        public int Percentage { get; set; }
        public string Message { get; set; }
    }

    // Implement IDisposable pattern
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // Dispose managed resources
                _context?.Dispose();
            }

            // Free unmanaged resources
            _disposed = true;
        }
    }

    // Destructor
    ~ExcelLensService()
    {
        Dispose(false);
    }
}
