﻿﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VisionPoint.UI.Models;

public class DiscountService
{
    [Key]
    public int Id { get; set; }

    public int DiscountId { get; set; }
    public Discount Discount { get; set; }

    public int ServiceId { get; set; }
    public Service Service { get; set; }

    /// <summary>
    /// Row GUID for SQL Server Merge Replication - managed by database
    /// </summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid RowGuid { get; set; }
}
