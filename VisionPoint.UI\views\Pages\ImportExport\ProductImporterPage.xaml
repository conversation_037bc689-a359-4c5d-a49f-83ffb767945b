﻿<Window
    x:Class="VisionPoint.UI.views.Pages.ImportExport.ProductImporterPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:VisionPoint.UI"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.ImportExport"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="ProductImporterPage"
    AllowsTransparency="True"
    Background="Transparent"
    FlowDirection="RightToLeft"
    Loaded="Window_Loaded"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">
        <Grid Width="1920" Height="1080">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*" />
                <ColumnDefinition Width="2*" />
                <ColumnDefinition Width="2*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="6*" />
                <RowDefinition Height="auto" />
                <RowDefinition Height="6*" />
            </Grid.RowDefinitions>




            <Border
                Grid.Row="1"
                Grid.Column="1"
                Background="{StaticResource backgroundColor}"
                BorderBrush="{StaticResource PrimaryColor}"
                BorderThickness="1"
                CornerRadius="8">
                <DockPanel
                    Margin="16"
                    HorizontalAlignment="Stretch"
                    LastChildFill="False">

                    <Grid DockPanel.Dock="Top">
                        <TextBlock
                            x:Name="txtStatus"
                            Margin="0,16"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Text="جاهز"
                            TextAlignment="Center" />

                        <Border
                            x:Name="btnclose"
                            Grid.Column="1"
                            Width="24"
                            Height="24"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Top"
                            Background="Red"
                            CornerRadius="50"
                            MouseLeftButtonDown="btnclose_MouseLeftButtonDown" />
                    </Grid>


                    <!--  اختيار المخزن  -->
                    <StackPanel
                        Margin="16,8"
                        DockPanel.Dock="Top"
                        Orientation="Vertical">

                        <ComboBox
                            x:Name="cmbWarehouse"
                            Height="60"
                            DisplayMemberPath="Name"
                            FontSize="14"
                            SelectedValuePath="Id"
                            Tag="اختر المخزن" />
                    </StackPanel>

                    <ProgressBar
                        x:Name="importProgress"
                        Grid.Row="1"
                        Height="35"
                        Margin="16"
                        DockPanel.Dock="Top"
                        Visibility="Collapsed">
                        <ProgressBar.Resources>
                            <Style TargetType="{x:Type ProgressBar}">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="{x:Type ProgressBar}">
                                            <Grid>
                                                <Border
                                                    x:Name="PART_Track"
                                                    Background="{TemplateBinding Background}"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    CornerRadius="8" />
                                                <Decorator x:Name="PART_Indicator" HorizontalAlignment="Left">
                                                    <Grid>
                                                        <Border
                                                            x:Name="Indicator"
                                                            Background="{TemplateBinding Foreground}"
                                                            CornerRadius="5" />
                                                    </Grid>
                                                </Decorator>
                                            </Grid>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </ProgressBar.Resources>
                    </ProgressBar>

                    <Border
                        x:Name="btnCancel"
                        Height="45"
                        Margin="16"
                        Background="Transparent"
                        BorderBrush="{StaticResource errorColor}"
                        BorderThickness="1.5"
                        CornerRadius="8"
                        Cursor="Hand"
                        DockPanel.Dock="Top"
                        MouseLeftButtonDown="btnCancel_MouseLeftButtonDown">
                        <TextBlock
                            x:Name="txtButton"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="18"
                            FontWeight="Bold"
                            Foreground="{StaticResource errorColor}">
                            الغاء
                        </TextBlock>
                    </Border>

                </DockPanel>
            </Border>
        </Grid>
    </Viewbox>
</Window>

