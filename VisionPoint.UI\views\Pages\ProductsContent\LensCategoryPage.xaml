<Page
    x:Class="VisionPoint.UI.views.Pages.ProductsContent.LensCategoryPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converter="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.ProductsContent"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="أنواع العدسات"
    d:Background="White"
    d:Height="1080"
    d:Width="1570"
    FlowDirection="RightToLeft"
    Loaded="Page_Loaded"
    mc:Ignorable="d">
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <converter:IndexToNumberConverter x:Key="IndexToNumberConverter" />
        </ResourceDictionary>
    </Page.Resources>
    <Grid Margin="16">
        <Grid.ColumnDefinitions>
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
        </Grid.RowDefinitions>

        <!--  Nombre de la categoría  -->
        <Grid Grid.ColumnSpan="2" Margin="0,0,8,0">
            <TextBox
                x:Name="txtName"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                Tag="اسم نوع العدسة" />
        </Grid>

        <!--  Descripción de la categoría  -->
        <Grid
            Grid.Column="2"
            Grid.ColumnSpan="2"
            Margin="8,0">
            <TextBox
                x:Name="txtDescription"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                Tag="الوصف" />
        </Grid>

        <!--  Botón de búsqueda  -->
        <Border
            x:Name="btnSearch"
            Grid.Row="1"
            Grid.Column="5"
            MaxHeight="44"
            Margin="0,0,16,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnSearch_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White">
                بحث
            </TextBlock>
        </Border>

        <!--  Botón de guardar  -->
        <Border
            x:Name="btnSave"
            Grid.Row="1"
            MaxHeight="44"
            Margin="0,0,16,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnSave_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White">
                حفظ
            </TextBlock>
        </Border>

        <!--  Botón de nuevo  -->
        <Border
            x:Name="btnNew"
            Grid.Row="1"
            Grid.Column="1"
            MaxHeight="44"
            Margin="16,0,0,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnNew_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}">
                جديد
            </TextBlock>
        </Border>

        <!--  Lista de categorías  -->
        <ListView
            x:Name="list"
            Grid.Row="2"
            Grid.RowSpan="7"
            Grid.ColumnSpan="8"
            AlternationCount="2147483647"
            Background="{DynamicResource PageColor}"
            BorderThickness="1"
            FontFamily="pack://application:,,,/Assets/#Cairo"
            ItemsSource="{Binding}"
            MouseDoubleClick="list_MouseDoubleClick"
            ScrollViewer.HorizontalScrollBarVisibility="Hidden"
            SizeChanged="list_SizeChanged">
            <ListView.BorderBrush>
                <SolidColorBrush Opacity="0.42" Color="Black" />
            </ListView.BorderBrush>

            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Style.Triggers>
                        <Trigger Property="Control.IsMouseOver" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>

                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="False" />
                                <Condition Property="IsMouseOver" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter Property="FontWeight" Value="Thin" />
                            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextColor}" />
                        </MultiTrigger>

                    </Style.Triggers>
                    <Setter Property="Control.Background" Value="{DynamicResource PageColor}" />
                    <Setter Property="Control.HorizontalContentAlignment" Value="Stretch" />
                </Style>
            </ListView.ItemContainerStyle>
            <ListView.View>
                <GridView AllowsColumnReorder="False">
                    <GridView.ColumnHeaderContainerStyle>
                        <Style BasedOn="{StaticResource ListViewHeader}" TargetType="{x:Type GridViewColumnHeader}">
                            <Setter Property="IsEnabled" Value="False" />
                            <Setter Property="Height" Value="60" />
                            <Style.Triggers>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="TextElement.Foreground" Value="Black" />
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </GridView.ColumnHeaderContainerStyle>
                    <GridViewColumn Width="Auto" Header="#">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Text="{Binding ., Converter={StaticResource IndexToNumberConverter}, ConverterParameter={x:Reference list}}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="اسم نوع العدسة">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding Name, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="الوصف">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding Description, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="العمليات">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <Path
                                    Width="24"
                                    Height="24"
                                    HorizontalAlignment="Center"
                                    Cursor="Hand"
                                    Data="{StaticResource Trash}"
                                    Fill="{StaticResource errorColor}"
                                    FlowDirection="LeftToRight"
                                    MouseLeftButtonDown="Delete_MouseLeftButtonDown"
                                    Stretch="Uniform"
                                    StrokeThickness="1" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
        </ListView>
    </Grid>
</Page>
