using Microsoft.Win32;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Threading;
using VisionPoint.UI.Helper;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;
using Color = System.Windows.Media.Color;

namespace VisionPoint.UI.views.Pages.ImportExport
{
    public partial class ProductImporterPage : Window
    {
        private readonly ExcelProductService _excelService;
        private readonly string _path;
        private readonly WarehouseService _warehouseService;
        private bool IsDone = false;

        /// <summary>
        /// إنشاء نافذة استيراد المنتجات مع تحديد الملف وخدمة الاستيراد مسبقاً
        /// </summary>
        /// <param name="path">مسار ملف Excel</param>
        /// <param name="excelService">خدمة استيراد المنتجات المجهزة مسبقاً</param>
        public ProductImporterPage(string path, ExcelProductService excelService)
        {
            InitializeComponent();
            _path = path;
            _excelService = excelService;
            _warehouseService = new WarehouseService();

            // إخفاء شريط التقدم في البداية
            importProgress.Visibility = Visibility.Collapsed;
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // تأخير بدء عملية الاستيراد للسماح للنافذة بالظهور أولاً
            await Dispatcher.BeginInvoke(DispatcherPriority.ContextIdle, new Action(() => { }));

            if (string.IsNullOrEmpty(_path) || !System.IO.File.Exists(_path))
            {
                ErrorBox.Show("المسار غير صالح أو الملف غير موجود.", "خطأ في المسار", true);
                Close();
                return;
            }

            try
            {
                // تحميل المخازن أولاً
                txtStatus.Text = "جارٍ تحميل المخازن...";
                var warehouses = await _warehouseService.GetAllWarehousesAsync();
                cmbWarehouse.ItemsSource = warehouses;

                if (warehouses.Any())
                {
                    cmbWarehouse.SelectedIndex = 0;
                }
                else
                {
                    ErrorBox.Show("لا توجد مخازن متاحة. يجب إنشاء مخزن واحد على الأقل قبل الاستيراد.", "لا توجد مخازن", true);
                    Close();
                    return;
                }

                // انتظار اختيار المخزن
                txtStatus.Text = "اختر المخزن ثم اضغط على زر البدء";

                // تغيير زر الإلغاء إلى زر البدء
                txtButton.Text = "بدء الاستيراد";
                Color greenColor = (Color)ColorConverter.ConvertFromString("#134074");

                // Create a new SolidColorBrush from the Color
                SolidColorBrush greenBrush = new SolidColorBrush(greenColor);
                btnCancel.Background = greenBrush;
                btnCancel.BorderBrush = greenBrush;
                txtButton.Foreground = System.Windows.Media.Brushes.White;

            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل المخازن: {ex.Message}", "خطأ", true);
                Close();
            }
        }

        private async Task StartImportProcess()
        {
            if (cmbWarehouse.SelectedValue == null)
            {
                ErrorBox.Show("يرجى اختيار مخزن أولاً", "مخزن غير محدد", false);
                return;
            }

            var selectedWarehouseId = (int)cmbWarehouse.SelectedValue;

            try
            {
                importProgress.Visibility = Visibility.Visible;
                txtStatus.Text = "جارٍ بدء الاستيراد...";

                // تعطيل زر البدء وتغييره إلى إلغاء
                txtButton.Text = "إلغاء";
                btnCancel.Background = System.Windows.Media.Brushes.Red;
                btnCancel.BorderBrush = System.Windows.Media.Brushes.Red;

                // تعيين المخزن المحدد في خدمة الاستيراد
                _excelService.SetWarehouseId(selectedWarehouseId);

                // إنشاء كائن Progress لتحديث واجهة المستخدم
                var progress = new Progress<ExcelProductService.ProgressReport>(report =>
                {
                    // استخدام Dispatcher.InvokeAsync بدلاً من Invoke للسماح بتحديث واجهة المستخدم بشكل أسرع
                    Dispatcher.InvokeAsync(() =>
                    {
                        importProgress.Value = report.Percentage;
                        txtStatus.Text = report.Message;
                    }, DispatcherPriority.Render);
                });

                try
                {
                    // تنفيذ عملية الاستيراد في thread منفصل باستخدام Task.Run
                    var importTask = await Task.Run(async () => await _excelService.ImportExcel(_path, progress));
                    var (added, updated, errors, errorRecords) = importTask;

                    if (errors.Any())
                    {
                        if (errorRecords.Any())
                        {
                            if (QuestionBox.Show(
                                "أخطاء الاستيراد",
                                $"تم الاستيراد مع أخطاء:\n{string.Join("\n", errors.Take(5))}{(errors.Count > 5 ? "\n..." : "")}\nهل تريد الحصول على ملف اكسل بالاخطاء؟") == MessageBoxResult.Yes)
                            {
                                SaveFileDialog saveFileDialog = new SaveFileDialog
                                {
                                    Filter = "Excel Files|*.xlsx",
                                    FileName = $"ProductImportErrors_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx",
                                    Title = "حفظ ملف أخطاء الاستيراد"
                                };

                                if (saveFileDialog.ShowDialog() == true)
                                {
                                    _excelService.ExportErrorExcel(errorRecords, saveFileDialog.FileName);
                                    DialogBox.Show("تم تصدير ملف الأخطاء بنجاح", "تصدير الأخطاء");
                                }
                            }
                        }
                        else
                        {
                            ErrorBox.Show($"تم الاستيراد مع أخطاء:\n{string.Join("\n", errors.Take(10))}{(errors.Count > 10 ? "\n..." : "")}", "أخطاء الاستيراد", true);
                        }
                    }
                    else
                    {
                        DialogBox.Show("نجاح الاستيراد", $"تمت إضافة {added} وعدّل {updated} سجلات.");
                    }
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"حدث خطأ أثناء الاستيراد: {ex.Message}", "خطأ في الاستيراد", true);
                    System.Diagnostics.Debug.WriteLine($"Import error: {ex.Message}");
                    System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                }
            }
            finally
            {
                txtStatus.Text = "جاهز";
                txtButton.Text = "إغلاق";
                IsDone = true;
            }
        }

        private async void btnCancel_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (IsDone)
            {
                Close();
            }
            else if (txtButton.Text == "بدء الاستيراد")
            {
                // بدء عملية الاستيراد
                await StartImportProcess();
            }
            else
            {
                if (QuestionBox.Show("إلغاء الاستيراد", "هل تريد إلغاء الاستيراد؟") == MessageBoxResult.Yes)
                {
                    Close();
                }
            }
        }

        private void btnclose_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            Close();
        }
    }
}
