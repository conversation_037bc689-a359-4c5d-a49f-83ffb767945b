﻿<Page
    x:Class="VisionPoint.UI.views.Pages.Products.LensesPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:VisionPoint.UI.Controls"
    xmlns:converter="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Products"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils"
    Title="LensesPage"
    d:Background="White"
    d:Height="1080"
    d:Width="1570"
    FlowDirection="RightToLeft"
    Loaded="Page_Loaded"
    mc:Ignorable="d">
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <converter:BoolToStringConverter x:Key="BoolToStringConverter" />
            <converter:IndexToNumberConverter x:Key="IndexToNumberConverter" />
        </ResourceDictionary>
    </Page.Resources>

    <Grid Margin="16">


        <Grid.ColumnDefinitions>
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
        </Grid.RowDefinitions>







        <Grid Grid.ColumnSpan="2" Margin="0,0,8,0">
            <TextBox
                x:Name="txtName"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                Tag="اسم العدسة" />
        </Grid>

        <Grid
            Grid.Row="1"
            Grid.Column="0"
            Margin="8,0">
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition />
            </Grid.RowDefinitions>
            <TextBlock
                VerticalAlignment="Center"
                FontSize="21"
                TextAlignment="Center">
                لديه تاريخ صلاحية
            </TextBlock>

            <CheckBox
                x:Name="chkExpire"
                Grid.Row="1"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FlowDirection="LeftToRight"
                IsChecked="True"
                Style="{StaticResource CircleCheckboxFL}" />
        </Grid>

        <Grid Grid.Column="4" Margin="8,0">
            <TextBox
                x:Name="txtMinimumQuantity"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                utils:NumericInputControl.IsNumericOnly="True"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                Tag="الحد الأدنى للكمية" />
        </Grid>

        <Grid
            Grid.Column="2"
            Grid.ColumnSpan="2"
            Margin="8,0">
            <ComboBox
                x:Name="cmbCategory"
                Height="60"
                Padding="42,0"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                DisplayMemberPath="Name"
                FontSize="21"
                IsEditable="True"
                IsReadOnly="False"
                SelectedValuePath="Id"
                Tag="نوع العدسة" />
        </Grid>








        <Grid
            Grid.Row="1"
            Grid.RowSpan="2"
            Grid.Column="1"
            Grid.ColumnSpan="6">

            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition />
            </Grid.RowDefinitions>


            <Grid Grid.Column="0" Margin="8,0">
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="21"
                    TextAlignment="Center">
                    SPH
                </TextBlock>

                <CheckBox
                    x:Name="chkSph"
                    Grid.Row="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FlowDirection="LeftToRight"
                    IsChecked="False"
                    Style="{StaticResource CircleCheckboxFL}" />
            </Grid>

            <Grid Grid.Column="1" Margin="8,0">
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="21"
                    TextAlignment="Center">
                    CYL
                </TextBlock>

                <CheckBox
                    x:Name="chkCyl"
                    Grid.Row="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    IsChecked="False"
                    Style="{StaticResource CircleCheckboxFL}" />
            </Grid>

            <Grid Grid.Column="2" Margin="8,0">
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="21"
                    TextAlignment="Center">
                    Pow
                </TextBlock>

                <CheckBox
                    x:Name="chkPow"
                    Grid.Row="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    IsChecked="False"
                    Style="{StaticResource CircleCheckboxFL}" />
            </Grid>



            <Grid Grid.Column="3" Margin="8,0">
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="21"
                    TextAlignment="Center">
                    Axis
                </TextBlock>

                <CheckBox
                    x:Name="chkAxis"
                    Grid.Row="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Checked="chkAxis_Checked"
                    FlowDirection="LeftToRight"
                    Style="{StaticResource CircleCheckboxFL}" />
            </Grid>

            <Grid
                Grid.Row="1"
                Grid.Column="3"
                Margin="8,0">

                <TextBox
                    x:Name="txtExis"
                    Height="60"
                    VerticalAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    BorderThickness="1"
                    FontSize="21"
                    GotFocus="TextBox_GotFocus"
                    Tag="Axis">
                    <TextBox.Style>
                        <Style BasedOn="{StaticResource {x:Type TextBox}}" TargetType="TextBox">
                            <Setter Property="Visibility" Value="Hidden" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding ElementName=chkAxis, Path=IsChecked}" Value="True">
                                    <Setter Property="Visibility" Value="Visible" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBox.Style>
                </TextBox>
            </Grid>

            <Grid Grid.Column="4" Margin="8,0">
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="21"
                    TextAlignment="Center">
                    Dia
                </TextBlock>

                <CheckBox
                    x:Name="chkDia"
                    Grid.Row="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Checked="chkDia_Checked"
                    FlowDirection="LeftToRight"
                    Style="{StaticResource CircleCheckboxFL}" />
            </Grid>

            <Grid
                Grid.Row="1"
                Grid.Column="4"
                Margin="8,0">

                <TextBox
                    x:Name="txtDia"
                    Height="60"
                    VerticalAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    BorderThickness="1"
                    FontSize="21"
                    GotFocus="TextBox_GotFocus"
                    Tag="Dia">

                    <TextBox.Style>
                        <Style BasedOn="{StaticResource {x:Type TextBox}}" TargetType="TextBox">
                            <Setter Property="Visibility" Value="Hidden" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding ElementName=chkDia, Path=IsChecked}" Value="True">
                                    <Setter Property="Visibility" Value="Visible" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBox.Style>
                </TextBox>
            </Grid>

            <Grid Grid.Column="5" Margin="8,0">
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="21"
                    TextAlignment="Center">
                    BC
                </TextBlock>

                <CheckBox
                    x:Name="chkBC"
                    Grid.Row="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Checked="chkBC_Checked"
                    FlowDirection="LeftToRight"
                    Style="{StaticResource CircleCheckboxFL}" />
            </Grid>

            <Grid
                Grid.Row="1"
                Grid.Column="5"
                Margin="8,0">

                <TextBox
                    x:Name="txtBC"
                    Height="60"
                    VerticalAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    BorderThickness="1"
                    FontSize="21"
                    GotFocus="TextBox_GotFocus"
                    Tag="BC">
                    <TextBox.Style>
                        <Style BasedOn="{StaticResource {x:Type TextBox}}" TargetType="TextBox">
                            <Setter Property="Visibility" Value="Hidden" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding ElementName=chkBC, Path=IsChecked}" Value="True">
                                    <Setter Property="Visibility" Value="Visible" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBox.Style>
                </TextBox>
            </Grid>

            <Grid Grid.Column="6" Margin="8,0">
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="21"
                    TextAlignment="Center">
                    Add
                </TextBlock>

                <CheckBox
                    x:Name="chkAdd"
                    Grid.Row="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Checked="chkAdd_Checked"
                    FlowDirection="LeftToRight"
                    Style="{StaticResource CircleCheckboxFL}" />
            </Grid>

            <Grid
                Grid.Row="1"
                Grid.Column="6"
                Margin="8,0">

                <TextBox
                    x:Name="txtAdd"
                    Height="60"
                    VerticalAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    BorderThickness="1"
                    FontSize="21"
                    GotFocus="TextBox_GotFocus"
                    Tag="Add">
                    <TextBox.Style>
                        <Style BasedOn="{StaticResource {x:Type TextBox}}" TargetType="TextBox">
                            <Setter Property="Visibility" Value="Hidden" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding ElementName=chkAdd, Path=IsChecked}" Value="True">
                                    <Setter Property="Visibility" Value="Visible" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBox.Style>
                </TextBox>
            </Grid>

        </Grid>

        <Border
            x:Name="btnImport"
            Grid.Row="2"
            Grid.Column="0"
            MaxHeight="44"
            Margin="0,0,16,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnImport_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White">
                استيراد اكسل
            </TextBlock>
        </Border>

        <Border
            x:Name="btnExport"
            Grid.Row="2"
            Grid.Column="1"
            MaxHeight="44"
            Margin="16,0,0,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnExport_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}">
                تصدير اكسل
            </TextBlock>
        </Border>

        <Grid
            Grid.Row="0"
            Grid.Column="5"
            Grid.ColumnSpan="2"
            Margin="8,0">
            <ComboBox
                x:Name="cmbWarehouse"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                DisplayMemberPath="Name"
                FontSize="21"
                IsEditable="False"
                SelectedValuePath="Id"
                SelectionChanged="cmbWarehouse_SelectionChanged"
                Tag="المخزن" />
        </Grid>




        <Border
            x:Name="btnSearch"
            Grid.Row="3"
            Grid.Column="4"
            MaxHeight="44"
            Margin="0,0,16,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnSearch_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White">
                بحث
            </TextBlock>
        </Border>

        <Border
            x:Name="btnExportInventory"
            Grid.Row="3"
            Grid.Column="5"
            MaxHeight="44"
            Margin="16,0,0,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnExportInventory_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}">
                جرد العدسات
            </TextBlock>
        </Border>

        <Border
            x:Name="btnAddValues"
            Grid.Row="3"
            Grid.Column="6"
            MaxHeight="44"
            Margin="16,0,0,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnAddValues_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}">
                عرض العدسة
            </TextBlock>
        </Border>


        <Border
            x:Name="btnSave"
            Grid.Row="3"
            MaxHeight="44"
            Margin="0,0,16,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnSave_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White">
                حفظ
            </TextBlock>
        </Border>

        <Border
            x:Name="btnNew"
            Grid.Row="3"
            Grid.Column="1"
            MaxHeight="44"
            Margin="16,0,0,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnNew_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}">
                جديد
            </TextBlock>
        </Border>



        <Border
            x:Name="btnDelete"
            Grid.Row="3"
            Grid.Column="2"
            MaxHeight="44"
            Margin="16,0,16,0"
            Background="Transparent"
            BorderBrush="{StaticResource errorColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnDelete_MouseLeftButtonDown">

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition Width="30" />
                </Grid.ColumnDefinitions>
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="18"
                    Foreground="{StaticResource errorColor}">
                    حذف
                </TextBlock>

                <Path
                    Grid.Column="1"
                    Width="24"
                    Height="24"
                    HorizontalAlignment="Center"
                    Cursor="Hand"
                    Data="{StaticResource Trash}"
                    Fill="{StaticResource errorColor}"
                    FlowDirection="LeftToRight"
                    Stretch="Uniform"
                    StrokeThickness="1" />
            </Grid>
        </Border>
        <ListView
            x:Name="list"
            Grid.Row="4"
            Grid.RowSpan="6"
            Grid.ColumnSpan="8"
            AlternationCount="2147483647"
            Background="{DynamicResource PageColor}"
            BorderThickness="1"
            FontFamily="pack://application:,,,/Assets/#Cairo"
            ItemsSource="{Binding}"
            MouseDoubleClick="list_MouseDoubleClick"
            ScrollViewer.HorizontalScrollBarVisibility="Hidden"
            SizeChanged="list_SizeChanged">
            <ListView.BorderBrush>
                <SolidColorBrush Opacity="0.42" Color="Black" />
            </ListView.BorderBrush>

            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Style.Triggers>
                        <Trigger Property="Control.IsMouseOver" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="Foreground" Value="Black" />
                            <Setter Property="FontWeight" Value="Bold" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="False" />
                                <Condition Property="IsMouseOver" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter Property="FontWeight" Value="Thin" />
                            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextColor}" />
                        </MultiTrigger>
                    </Style.Triggers>
                    <Setter Property="Control.Background" Value="{DynamicResource PageColor}" />
                    <Setter Property="Control.HorizontalContentAlignment" Value="Stretch" />
                </Style>
            </ListView.ItemContainerStyle>

            <ListView.View>
                <GridView AllowsColumnReorder="False">
                    <GridView.ColumnHeaderContainerStyle>
                        <Style BasedOn="{StaticResource ListViewHeader}" TargetType="{x:Type GridViewColumnHeader}">
                            <Setter Property="IsEnabled" Value="False" />
                            <Setter Property="Height" Value="60" />
                            <Style.Triggers>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="TextElement.Foreground" Value="Black" />
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </GridView.ColumnHeaderContainerStyle>
                    <GridViewColumn Width="Auto" Header="#">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Text="{Binding ., Converter={StaticResource IndexToNumberConverter}, ConverterParameter={x:Reference list}}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="الاسم">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding Name, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Width="Auto" Header="نوع العدسة">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding CategoryName, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Width="Auto" Header="SPH">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding Sphere, Converter={StaticResource BoolToStringConverter}, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Width="Auto" Header="CYL">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding Cylinder, Converter={StaticResource BoolToStringConverter}, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Width="Auto" Header="POW">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding Power, Converter={StaticResource BoolToStringConverter}, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Width="Auto" Header="AXIS">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding Axis, FallbackValue='0', TargetNullValue='0'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Width="Auto" Header="ADD">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding Addtion, FallbackValue='0', TargetNullValue='0'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Width="Auto" Header="BC">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding BC, FallbackValue='0', TargetNullValue='0'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Width="Auto" Header="DIA">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding Dia, FallbackValue='0', TargetNullValue='0'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <!--  Add TotalQuantity column to the ListView  -->
                    <GridViewColumn Width="Auto" Header="الكمية">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding TotalQuantity, FallbackValue='0', TargetNullValue='0'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                </GridView>
            </ListView.View>
        </ListView>

        <!--  Loading Overlay removed - now handled at application level in MainWindow  -->
    </Grid>
</Page>
