﻿﻿﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace VisionPoint.UI.Models;
[Index(nameof(Name), IsUnique = true)]
public class LensCategory : BaseEntity
{
    [StringLength(50, ErrorMessage = "اسم نوع العدسة يجب ألا يتجاوز 50 حرف")]
    public string Name { get; set; } = string.Empty;

    [StringLength(200, ErrorMessage = "الوصف يجب ألا يتجاوز 200 حرف")]
    public string? Description { get; set; }

    // Relación con lentes
    public ICollection<Lens> Lenses { get; set; } = new List<Lens>();
}
