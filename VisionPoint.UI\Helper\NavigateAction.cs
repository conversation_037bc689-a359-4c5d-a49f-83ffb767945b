﻿using System.Windows.Controls;

namespace VisionPoint.UI.Helper
{
    public static class NavigateAction
    {
        public static Action<Page>? NavigateTo;

        /// <summary>
        /// التنقل إلى صفحة والانتظار حتى يتم إغلاقها أو التنقل بعيداً عنها
        /// </summary>
        /// <param name="page">الصفحة المراد التنقل إليها</param>
        /// <returns>Task يكتمل عند إغلاق الصفحة أو التنقل بعيداً عنها</returns>
        public static Task NavigateToAndWaitAsync(Page page)
        {
            var tcs = new TaskCompletionSource<bool>();

            // إضافة معالج للحدث Unloaded للصفحة
            page.Unloaded += (sender, e) =>
            {
                tcs.TrySetResult(true);
            };

            // التنقل إلى الصفحة
            NavigateTo?.Invoke(page);

            return tcs.Task;
        }
    }
}
