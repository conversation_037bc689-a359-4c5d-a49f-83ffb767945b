using Microsoft.EntityFrameworkCore;
using System;

namespace VisionPoint.UI.ViewModel;

/// <summary>
/// نموذج ملخص كشف حساب الخزينة
/// </summary>
public class TreasuryStatementSummaryVM
{
    /// <summary>
    /// اسم الخزينة
    /// </summary>
    public string TreasuryName { get; set; }
    
    /// <summary>
    /// مجموع المبالغ الداخلة (القبض)
    /// </summary>
    [Precision(18, 3)] public decimal TotalIncoming { get; set; } = 0;
    
    /// <summary>
    /// مجموع المبالغ الخارجة (الصرف)
    /// </summary>
    [Precision(18, 3)] public decimal TotalOutgoing { get; set; } = 0;
    
    /// <summary>
    /// الرصيد النهائي
    /// </summary>
    [Precision(18, 3)] public decimal FinalBalance { get; set; } = 0;
    
    /// <summary>
    /// توضيح حالة الرصيد (موجب/سالب)
    /// </summary>
    public string BalanceStatus { get; set; }
}
