using System;
using System.Windows;
using System.Windows.Input;

namespace VisionPoint.UI.views.Dialogs
{
    public partial class QuestionBox : Window
    {
        private string header = "";
        public string TextHeader
        {
            set
            {
                header = value;
                txtHeader.Text = header;
            }
            get { return header; }
        }

        private string message = "";
        public string TextMessage
        {
            set
            {
                message = value;
                txtMessage.Text = message;
            }
            get { return message; }
        }

        private QuestionBox() // Private constructor
        {
            InitializeComponent();
        }

        /// <summary>
        /// يعرض مربع حوار سؤال مع أزرار نعم/لا
        /// </summary>
        /// <param name="header">عنوان مربع الحوار</param>
        /// <param name="message">السؤال المراد عرضه</param>
        /// <returns>MessageBoxResult.Yes إذا تم النقر على نعم، وMessageBoxResult.No إذا تم النقر على لا</returns>
        public static MessageBoxResult Show(string header, string message)
        {
            var dialog = new QuestionBox();
            dialog.TextHeader = header;
            dialog.TextMessage = message;

            try
            {
                // عرض النافذة باستخدام ShowDialog
                dialog.ShowDialog();

                // التحقق من قيمة DialogResult
                if (dialog.DialogResult.HasValue && dialog.DialogResult.Value)
                {
                    return MessageBoxResult.Yes;
                }
                else
                {
                    return MessageBoxResult.No;
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء عرض مربع السؤال: {ex.Message}", "خطأ", true);
                return MessageBoxResult.No;
            }
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnYes.IsEnabled = false;
            btnNo.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnYes.IsEnabled = true;
            btnNo.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private void btnYes_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                this.DialogResult = true;
                this.Close();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnNo_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                this.DialogResult = false;
                this.Close();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            // إذا تم إغلاق النافذة بدون نقر على أي زر، اعتبر الإجابة لا
            if (!this.DialogResult.HasValue)
            {
                this.DialogResult = false;
            }
        }
    }
}