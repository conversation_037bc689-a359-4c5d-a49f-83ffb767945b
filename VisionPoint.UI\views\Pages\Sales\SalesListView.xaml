<Page
    x:Class="VisionPoint.UI.views.Pages.Sales.SalesListView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converter="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Sales"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="SalesListView"
    d:Background="White"
    d:Height="1080"
    d:Width="1570"
    FlowDirection="RightToLeft"
    Loaded="Window_Loaded"
    mc:Ignorable="d">
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <converter:IndexToNumberConverter x:Key="IndexToNumberConverter" />
        </ResourceDictionary>
    </Page.Resources>

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="60" />
            <RowDefinition Height="1.5*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <DockPanel Grid.ColumnSpan="2" HorizontalAlignment="Center">
            <TextBlock
                Grid.ColumnSpan="2"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="24"
                FontWeight="Bold"
                Foreground="#333333"
                Text="المبيعات" />
        </DockPanel>

        <!--  Filters and Buttons  -->
        <Grid
            Grid.Row="1"
            Grid.ColumnSpan="2"
            Margin="8,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  All Periods Checkbox  -->
            <Grid
                Grid.Row="1"
                Grid.Column="0"
                Background="Transparent"
                MouseLeftButtonDown="GridCheckBox_Toggle">
                <TextBlock
                    Grid.Row="0"
                    VerticalAlignment="Top"
                    FontSize="18"
                    FontWeight="Black"
                    Foreground="{StaticResource PrimaryColor}"
                    Text="كل الفترات"
                    TextAlignment="Center" />
                <CheckBox
                    x:Name="chkAllPeriods"
                    Grid.Row="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Checked="chkAllPeriods_CheckedChanged"
                    Style="{StaticResource CircleCheckboxFL}"
                    Unchecked="chkAllPeriods_CheckedChanged" />
            </Grid>

            <!--  From Date  -->
            <Grid
                Grid.Row="1"
                Grid.Column="1"
                Grid.ColumnSpan="2">
                <DatePicker
                    x:Name="dpFromDate"
                    Grid.Row="1"
                    Height="60"
                    Margin="8,0"
                    VerticalContentAlignment="Center"
                    BorderBrush="{StaticResource PrimaryTextColor}"
                    BorderThickness="1"
                    DisplayDateStart="2000-01-01"
                    FontSize="18"
                    SelectedDateChanged="dpFromDate_SelectedDateChanged"
                    SelectedDateFormat="Short">
                    <DatePicker.Resources>
                        <Style TargetType="{x:Type DatePickerTextBox}">
                            <Setter Property="Control.Template">
                                <Setter.Value>
                                    <ControlTemplate>
                                        <TextBox
                                            x:Name="PART_TextBox"
                                            VerticalAlignment="Stretch"
                                            BorderThickness="0"
                                            FontSize="21"
                                            Foreground="{StaticResource PrimaryTextColor}"
                                            IsReadOnly="True"
                                            Style="{StaticResource txtDatePick}"
                                            Tag="من تاريخ"
                                            Text="{Binding Path=SelectedDate, StringFormat='yyyy-MM-dd', RelativeSource={RelativeSource AncestorType={x:Type DatePicker}}}" />
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </DatePicker.Resources>
                </DatePicker>
            </Grid>

            <!--  To Date  -->
            <Grid
                Grid.Row="1"
                Grid.Column="3"
                Grid.ColumnSpan="2">
                <DatePicker
                    x:Name="dpToDate"
                    Grid.Row="1"
                    Height="60"
                    Margin="8,0"
                    VerticalContentAlignment="Center"
                    BorderBrush="{StaticResource PrimaryTextColor}"
                    BorderThickness="1"
                    DisplayDateStart="2000-01-01"
                    FontSize="18"
                    SelectedDateChanged="dpToDate_SelectedDateChanged"
                    SelectedDateFormat="Short">
                    <DatePicker.Resources>
                        <Style TargetType="{x:Type DatePickerTextBox}">
                            <Setter Property="Control.Template">
                                <Setter.Value>
                                    <ControlTemplate>
                                        <TextBox
                                            x:Name="PART_TextBox"
                                            VerticalAlignment="Stretch"
                                            BorderThickness="0"
                                            FontSize="21"
                                            Foreground="{StaticResource PrimaryTextColor}"
                                            IsReadOnly="True"
                                            Style="{StaticResource txtDatePick}"
                                            Tag="إلى تاريخ"
                                            Text="{Binding Path=SelectedDate, StringFormat='yyyy-MM-dd', RelativeSource={RelativeSource AncestorType={x:Type DatePicker}}}" />
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </DatePicker.Resources>
                </DatePicker>
            </Grid>

            <!--  Client ComboBox  -->
            <Grid
                Grid.Row="1"
                Grid.Column="5"
                Grid.ColumnSpan="2">
                <ComboBox
                    x:Name="cmbClients"
                    Grid.Row="1"
                    Height="60"
                    Margin="8,0"
                    VerticalContentAlignment="Center"
                    DisplayMemberPath="Name"
                    FontSize="21"
                    IsEditable="True"
                    IsReadOnly="False"
                    SelectedValuePath="Id"
                    SelectionChanged="cmbClients_SelectionChanged"
                    Tag="العميل" />
            </Grid>

            <!--  Has Remaining Checkbox  -->
            <Grid
                Grid.Row="1"
                Grid.Column="7"
                Background="Transparent"
                MouseLeftButtonDown="GridCheckBox_Toggle">
                <TextBlock
                    Grid.Row="0"
                    VerticalAlignment="Top"
                    FontSize="16"
                    Text="فواتير بها متبقي"
                    TextAlignment="Center" />
                <CheckBox
                    x:Name="chkHasRemaining"
                    Grid.Row="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Checked="chkHasRemaining_CheckedChanged"
                    Style="{StaticResource CircleCheckboxFL}"
                    Unchecked="chkHasRemaining_CheckedChanged" />
            </Grid>

            <!--  Returns Filter ComboBox  -->
            <ComboBox
                x:Name="cmbReturnsFilter"
                Grid.Row="2"
                Grid.Column="5"
                Grid.ColumnSpan="2"
                Height="60"
                Margin="8,0"
                VerticalContentAlignment="Center"
                FontSize="18"
                IsEditable="False"
                SelectionChanged="cmbReturnsFilter_SelectionChanged"
                Tag="فلتر المسترجعات">
                <ComboBoxItem Content="الكل" Tag="All" />
                <ComboBoxItem Content="فواتير بها مسترجعات" Tag="HasReturns" />
                <ComboBoxItem Content="فواتير بدون مسترجعات" Tag="NoReturns" />
            </ComboBox>

            <!--  Warehouse ComboBox  -->
            <ComboBox
                x:Name="cmbWarehouses"
                Grid.Column="2"
                Grid.ColumnSpan="2"
                Height="60"
                Margin="8,0"
                VerticalContentAlignment="Center"
                DisplayMemberPath="Name"
                FontSize="21"
                IsEditable="False"
                SelectedValuePath="Id"
                SelectionChanged="cmbWarehouses_SelectionChanged"
                Tag="المخزن" />

            <!--  Invoice Number TextBox  -->
            <TextBox
                x:Name="txtInvoiceNumber"
                Grid.ColumnSpan="2"
                Height="60"
                Margin="8,0"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                Tag="رقم الفاتورة" />

            <!--  Add Button  -->
            <Border
                x:Name="btnAdd"
                Grid.Row="2"
                MaxHeight="44"
                Margin="8,0"
                Background="{StaticResource PrimaryColor}"
                CornerRadius="8"
                Cursor="Hand"
                MouseLeftButtonDown="btnAdd_MouseLeftButtonDown">
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="21"
                    Foreground="White">
                    اضافة
                </TextBlock>
            </Border>

            <!--  Search Button  -->
            <Border
                x:Name="btnSearch"
                Grid.Row="2"
                Grid.Column="1"
                MaxHeight="44"
                Margin="8,0"
                Background="Transparent"
                BorderBrush="{StaticResource PrimaryColor}"
                BorderThickness="1"
                CornerRadius="8"
                Cursor="Hand"
                MouseLeftButtonDown="btnSearch_MouseLeftButtonDown">
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="21"
                    Foreground="{StaticResource PrimaryColor}">
                    بحث
                </TextBlock>
            </Border>

            <!--  Export Button  -->
            <Border
                x:Name="btnExport"
                Grid.Row="2"
                Grid.Column="2"
                MaxHeight="44"
                Margin="8,0"
                Background="Transparent"
                BorderBrush="{StaticResource PrimaryColor}"
                BorderThickness="1"
                CornerRadius="8"
                Cursor="Hand"
                MouseLeftButtonDown="btnExport_MouseLeftButtonDown">
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="21"
                    Foreground="{StaticResource PrimaryColor}">
                    تصدير
                </TextBlock>
            </Border>

            <!--  Reset Filters Button  -->
            <Border
                x:Name="btnResetFilters"
                Grid.Row="2"
                Grid.Column="3"
                Grid.ColumnSpan="2"
                MaxHeight="44"
                Margin="8,0"
                Background="Transparent"
                BorderBrush="{StaticResource PrimaryColor}"
                BorderThickness="1"
                CornerRadius="8"
                Cursor="Hand"
                MouseLeftButtonDown="btnResetFilters_MouseLeftButtonDown">
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="18"
                    Foreground="{StaticResource PrimaryColor}">
                    إعادة تعيين الفلاتر
                </TextBlock>
            </Border>
        </Grid>

        <!--  Sales List  -->
        <ListView
            x:Name="list"
            Grid.Row="2"
            Grid.RowSpan="4"
            Grid.ColumnSpan="2"
            Margin="8,0"
            AlternationCount="100"
            Background="{DynamicResource PageColor}"
            BorderThickness="1"
            FontFamily="pack://application:,,,/Assets/#Cairo"
            ItemsSource="{Binding}"
            MouseDoubleClick="list_MouseDoubleClick"
            ScrollViewer.HorizontalScrollBarVisibility="Hidden"
            SizeChanged="list_SizeChanged">
            <ListView.BorderBrush>
                <SolidColorBrush Opacity="0.42" Color="Black" />
            </ListView.BorderBrush>
            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Style.Triggers>
                        <Trigger Property="Control.IsMouseOver" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="False" />
                                <Condition Property="IsMouseOver" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter Property="FontWeight" Value="Thin" />
                            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextColor}" />
                        </MultiTrigger>
                    </Style.Triggers>
                    <Setter Property="Control.Background" Value="{DynamicResource PageColor}" />
                    <Setter Property="Control.HorizontalContentAlignment" Value="Stretch" />
                </Style>
            </ListView.ItemContainerStyle>
            <ListView.View>
                <GridView AllowsColumnReorder="False">
                    <GridView.ColumnHeaderContainerStyle>
                        <Style BasedOn="{StaticResource ListViewHeader}" TargetType="{x:Type GridViewColumnHeader}">
                            <Setter Property="IsEnabled" Value="False" />
                            <Setter Property="Height" Value="60" />
                            <Style.Triggers>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="TextElement.Foreground" Value="Black" />
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </GridView.ColumnHeaderContainerStyle>
                    <GridViewColumn Width="Auto" Header="#">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Text="{Binding ., Converter={StaticResource IndexToNumberConverter}, ConverterParameter={x:Reference list}}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="120" Header="رقم الفاتورة">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding InvoiceNumber}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="150" Header="تاريخ الفاتورة">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding SaleDate, StringFormat='{}{0:yyyy-MM-dd}'}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="180" Header="العميل">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding ClientName}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="120" Header="المخزن">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding WarehouseName}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="120" Header="قيمة الفاتورة">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding TotalAmount, StringFormat='{}{0:N2}'}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="120" Header="القيمة المدفوعة">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding PaidAmount, StringFormat='{}{0:N2}'}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="120" Header="المتبقي">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding RemainingAmount, StringFormat='{}{0:N2}'}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="120" Header="المسترجعات">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding TotalReturned, StringFormat='{}{0:N2}'}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="100" Header="العمليات">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                    <!--  Edit Button  -->
                                    <Button
                                        Width="30"
                                        Height="30"
                                        Margin="5,0"
                                        Background="Transparent"
                                        BorderBrush="Transparent"
                                        Click="EditButton_Click"
                                        Cursor="Hand"
                                        ToolTip="تعديل الفاتورة">
                                        <Path
                                            Width="16"
                                            Height="16"
                                            Data="M16.84,2.73C16.45,2.73 16.07,2.88 15.77,3.17L13.35,5.59L18.41,10.65L20.83,8.23C21.42,7.64 21.42,6.69 20.83,6.1L17.9,3.17C17.6,2.88 17.22,2.73 16.84,2.73M12.94,6L4.83,14.11L4.5,14.44L4.5,19.5H9.56L9.89,19.17L18,11.06L12.94,6Z"
                                            Fill="{StaticResource PrimaryColor}"
                                            Stretch="Uniform" />
                                    </Button>

                                    <!--  زر الحذف  -->
                                    <Button
                                        Width="30"
                                        Height="30"
                                        Margin="5,0"
                                        Background="Transparent"
                                        BorderBrush="Transparent"
                                        Click="DeleteButton_Click"
                                        Cursor="Hand"
                                        ToolTip="حذف الفاتورة">
                                        <Path
                                            Width="16"
                                            Height="16"
                                            Data="{StaticResource Trash}"
                                            Fill="{StaticResource errorColor}"
                                            Stretch="Uniform" />
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
        </ListView>
    </Grid>
</Page>