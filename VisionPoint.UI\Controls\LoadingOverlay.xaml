<UserControl
    x:Class="VisionPoint.UI.Controls.LoadingOverlay"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:cc="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DesignHeight="450"
    d:DesignWidth="800"
    HorizontalAlignment="Stretch"
    VerticalAlignment="Stretch"
    mc:Ignorable="d">
    <Grid
        x:Name="OverlayGrid"
        Background="#80000000"
        Visibility="Collapsed">
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
            <ProgressBar
                Width="100"
                Height="100"
                Background="#30FFFFFF"
                Foreground="{StaticResource SuccessColor}"
                IsIndeterminate="True"
                Style="{StaticResource circleProgressbar}" />
            <TextBlock
                x:Name="MessageText"
                Margin="0,10,0,0"
                HorizontalAlignment="Center"
                FontSize="16"
                Foreground="White"
                Text="جاري تنفيذ العملية..." />
        </StackPanel>
    </Grid>
</UserControl>