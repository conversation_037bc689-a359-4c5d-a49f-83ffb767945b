﻿using Microsoft.EntityFrameworkCore;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Models;

namespace VisionPoint.UI.PL
{
    /// <summary>
    /// خدمة إدارة المرتبات
    /// </summary>
    public class SalaryService : IDisposable
    {
        private readonly AppDbContext _context = ServiceLocator.GetService<AppDbContext>();
        private readonly UserService _userService;
        private bool _disposed = false;

        public SalaryService(UserService userService)
        {
            _userService = userService;
        }

        /// <summary>
        /// الحصول على جميع دفعات المرتبات
        /// </summary>
        public async Task<List<SalaryPayment>> GetAllSalaryPaymentsAsync()
        {
            return await _context.SalaryPayments
                .Include(s => s.User)
                .OrderByDescending(s => s.PaymentDate)
                .ToListAsyncWithBusy("GetAllSalaryPayments");
        }

        /// <summary>
        /// الحصول على دفعات المرتبات لمستخدم معين
        /// </summary>
        public async Task<List<SalaryPayment>> GetSalaryPaymentsByUserIdAsync(int userId)
        {
            return await _context.SalaryPayments
                .Include(s => s.User)
                .Where(s => s.UserId == userId)
                .OrderByDescending(s => s.PaymentDate)
                .ToListAsyncWithBusy("GetSalaryPaymentsByUserId");
        }

        /// <summary>
        /// إضافة دفعة مرتب يدوياً
        /// </summary>
        public async Task<(bool success, string message)> AddSalaryPaymentAsync(SalaryPayment salaryPayment)
        {
            try
            {
                // إضافة سجل دفعة المرتب
                _context.SalaryPayments.AddWithBusy(salaryPayment);

                // تحديث رصيد المستخدم
                var user = await _context.Users.FindAsyncWithBusy(salaryPayment.UserId);
                if (user != null)
                {
                    user.Balance += salaryPayment.Amount; // زيادة رصيد الموظف (صرف المرتب)
                }

                var result = await _context.SaveWithTransactionAndBusy("SaveSalaryPayment");

                return result.State ? (true, "تم إضافة المرتب بنجاح") : (false, result.Message);
            }
            catch (Exception ex)
            {
                _context.Reverse();
                return (false, $"حدث خطأ أثناء إضافة المرتب: {ex.Message}");
            }
        }

        /// <summary>
        /// إضافة المرتبات الشهرية تلقائياً إلى أرصدة المستخدمين
        /// </summary>
        public async Task<(bool success, string message, int processedCount)> ProcessMonthlySalariesAsync(int month, int year)
        {
            try
            {
                // 1. الحصول على جميع المستخدمين الذين لديهم مرتب أكبر من صفر
                var usersWithSalary = await _context.Users
                    .Where(u => u.Salary > 0)
                    .ToListAsyncWithBusy("GetUsersWithSalary");

                if (!usersWithSalary.Any())
                {
                    return (true, "لا يوجد مستخدمين لديهم مرتبات لإضافتها", 0);
                }

                // 2. التحقق من عدم وجود دفعات سابقة لنفس الشهر والسنة
                var existingPayments = await _context.SalaryPayments
                    .Where(s => s.Month == month && s.Year == year)
                    .Select(s => s.UserId)
                    .ToListAsyncWithBusy("GetExistingSalaryPayments");

                // 3. إضافة المرتبات للمستخدمين الذين لم يتم إضافة مرتباتهم بعد (محسنة)
                var usersToProcess = usersWithSalary.Where(user => !existingPayments.Contains(user.Id)).ToList();
                int processedCount = usersToProcess.Count;
                decimal totalSalaries = usersToProcess.Sum(user => user.Salary);

                if (processedCount > 0)
                {
                    var currentTime = DateTime.Now;
                    var currentUserId = CurrentUser.Id > 0 ? CurrentUser.Id : 1;

                    // إنشاء جميع الإيصالات مرة واحدة (محسنة)
                    var receipts = usersToProcess.Select(user => new Receipt
                    {
                        Statement = $"إضافة مرتب شهري تلقائي - {month}/{year}",
                        Date = currentTime,
                        Value = user.Salary,
                        IsExchange = null, // لا يؤثر على الخزينة
                        TreasuryId = null, // لا يوجد خزينة مرتبطة
                        EmployeeId = user.Id, // المستخدم المستلم للمرتب
                        FinancialId = (byte)FinancialId.AutomaticSalary, // نوع العملية: إضافة راتب تلقائي
                        CreatedById = currentUserId,
                        ModifiedById = currentUserId,
                        CreatedAt = currentTime,
                        UpdatedAt = currentTime
                    }).ToList();

                    // إدراج جميع الإيصالات دفعة واحدة
                    await _context.Receipts.AddRangeAsyncWithBusy(receipts);
                    var receiptResult = await _context.SaveWithTransactionAndBusy("SaveReceiptsForSalaries");

                    if (receiptResult.State)
                    {
                        // إنشاء جميع سجلات دفعات المرتبات مرة واحدة (محسنة)
                        var salaryPayments = usersToProcess.Select((user, index) => new SalaryPayment
                        {
                            UserId = user.Id,
                            PaymentDate = currentTime,
                            Amount = user.Salary,
                            Month = month,
                            Year = year,
                            Notes = "إضافة مرتب شهري تلقائي",
                            ReceiptId = receipts[index].Id, // ربط بالإيصال
                            CreatedById = currentUserId,
                            ModifiedById = currentUserId,
                            CreatedAt = currentTime,
                            UpdatedAt = currentTime
                        }).ToList();

                        // إدراج جميع سجلات المرتبات دفعة واحدة
                        await _context.SalaryPayments.AddRangeAsyncWithBusy(salaryPayments);

                        // تحديث أرصدة جميع المستخدمين (محسنة)
                        foreach (var user in usersToProcess)
                        {
                            user.Balance -= user.Salary;
                        }
                    }
                }

                var salariesResult = await _context.SaveWithTransactionAndBusy("SaveMonthlySalaries");

                if (processedCount > 0)
                {
                    return salariesResult.State ?
                        (true, $"تم إضافة المرتبات بنجاح لعدد {processedCount} مستخدم بإجمالي {totalSalaries}", processedCount) :
                        (false, salariesResult.Message, 0);
                }
                else
                {
                    return (true, "تم إضافة جميع المرتبات لهذا الشهر مسبقاً", 0);
                }
            }
            catch (Exception ex)
            {
                _context.Reverse();
                return (false, $"حدث خطأ أثناء إضافة المرتبات: {ex.Message}", 0);
            }
        }

        // Implement IDisposable pattern
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                    _userService?.Dispose();
                    _context?.Dispose();
                }

                // Free unmanaged resources
                _disposed = true;
            }
        }

        // Destructor
        ~SalaryService()
        {
            Dispose(false);
        }
    }
}
