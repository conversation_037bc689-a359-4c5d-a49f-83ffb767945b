using Microsoft.EntityFrameworkCore;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Models;

namespace VisionPoint.UI.PL
{
    public class ColorService : IDisposable
    {
        private readonly AppDbContext _context = Converters.ServiceLocator.GetService<AppDbContext>();
        private bool _disposed = false;

        public ColorService()
        {
        }
        public async Task<List<Color>> GetAllColorsAsync()
        {
            return await _context.Colors.ToListAsyncWithBusy("GetAllColors");
        }

        public async Task<Color> GetColorByIdAsync(byte id)
        {
            return await _context.Colors.FindAsyncWithBusy(id);
        }

        public async Task<(bool State, string Message)> AddColorAsync(Color color)
        {
            await _context.Colors.AddAsyncWithBusy(color);
            return await _context.SaveWithTransactionAndBusy("AddColor");
        }

        public async Task<(bool State, string Message)> UpdateColorAsync(Color color)
        {
            _context.Colors.UpdateWithBusy(color);
            return await _context.SaveWithTransactionAndBusy("UpdateColor");
        }

        public async Task<(bool State, string Message)> DeleteColorAsync(byte id)
        {
            var color = await _context.Colors.FindAsyncWithBusy(id);
            if (color == null)
            {
                return (false, "اللون غير موجود");
            }

            // التحقق من وجود ألوان منتجات مرتبطة باللون
            bool hasProductColors = await _context.ProductColors.AnyAsyncWithBusy(pc => pc.ColorId == id, "CheckProductColors");

            if (hasProductColors)
            {
                return (false, "لا يمكن حذف اللون لأنه مرتبط بألوان منتجات");
            }

            // التحقق من وجود ألوان وصفات عدسات مرتبطة باللون
            bool hasLensPrescriptionColors = await _context.LensPrescriptionColors.AnyAsyncWithBusy(lpc => lpc.ColorId == id, "CheckLensPrescriptionColors");

            if (hasLensPrescriptionColors)
            {
                return (false, "لا يمكن حذف اللون لأنه مرتبط بألوان وصفات عدسات");
            }

            _context.Colors.RemoveWithBusy(color);
            return await _context.SaveWithTransactionAndBusy("DeleteColor");
        }

        // Implement IDisposable pattern
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                    _context?.Dispose();
                }

                // Free unmanaged resources
                _disposed = true;
            }
        }

        // Destructor
        ~ColorService()
        {
            Dispose(false);
        }
    }
}
