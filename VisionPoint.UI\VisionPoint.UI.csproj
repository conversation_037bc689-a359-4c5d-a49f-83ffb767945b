﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <PackageIcon></PackageIcon>
    <ApplicationIcon>Assets\icon.ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="credentials\**" />
    <EmbeddedResource Remove="credentials\**" />
    <None Remove="credentials\**" />
    <Page Remove="credentials\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Sumatra\SumatraPDF.exe" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Assets\icon.ico" />
    <Content Include="Sumatra\SumatraPDF.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.104.2" />
    <PackageReference Include="EPPlus" Version="7.6.1" />
    <PackageReference Include="ExcelDataReader" Version="3.7.0" />
    <PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0" />

    <PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.3.1" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.13" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.13" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.13" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.13">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.2" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.2" />
    <PackageReference Include="PdfSharp" Version="6.1.1" />
    <PackageReference Include="ZXing.Net" Version="0.16.10" />
    <PackageReference Include="ZXing.Net.Bindings.Windows.Compatibility" Version="0.16.13" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Assets\Logo.png" />
    <EmbeddedResource Include="Assets\company_logo.png" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Properties\Settings.Designer.cs">
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <None Update="Assets\ElMessiri.ttf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Assets\Logo.png">
      <Pack>True</Pack>
      <PackagePath>\</PackagePath>
    </None>
    <None Update="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>

</Project>
