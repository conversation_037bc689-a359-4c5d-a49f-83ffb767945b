﻿<Window
    x:Class="VisionPoint.UI.views.Pages.ProductsContent.LensPrescriptionPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converter="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:VisionPoint.UI"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.ProductsContent"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils"
    Title="LensPrescriptionPage"
    AllowsTransparency="True"
    Background="Transparent"
    FlowDirection="RightToLeft"
    Loaded="Window_Loaded"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <converter:IndexToNumberConverter x:Key="IndexToNumberConverter" />
        </ResourceDictionary>
    </Window.Resources>
    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">
        <Border
            Width="1920"
            Height="1080"
            Background="{StaticResource backgroundColor}"
            BorderBrush="LightGray"
            BorderThickness="1.5"
            CornerRadius="16">


            <Grid Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="60" />
                    <RowDefinition />
                </Grid.RowDefinitions>

                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="24">
                    العدسات
                </TextBlock>

                <Border
                    x:Name="btnclose"
                    Width="24"
                    Height="24"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Top"
                    Background="Red"
                    CornerRadius="50"
                    MouseLeftButtonDown="btnclose_MouseLeftButtonDown" />

                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition />
                        <ColumnDefinition />
                        <ColumnDefinition />
                        <ColumnDefinition />
                        <ColumnDefinition />
                        <ColumnDefinition />
                        <ColumnDefinition />
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition />
                        <RowDefinition />
                        <RowDefinition />
                        <RowDefinition />
                        <RowDefinition />
                        <RowDefinition />
                        <RowDefinition />
                        <RowDefinition />
                        <RowDefinition />
                        <RowDefinition />
                    </Grid.RowDefinitions>



                    <TextBlock
                        x:Name="cmbLensesName"
                        Grid.Column="1"
                        Grid.ColumnSpan="2"
                        Margin="8,0"
                        VerticalAlignment="Center"
                        FontSize="21"
                        Text="اسم العدسة"
                        TextAlignment="Center" />


                    <Grid Grid.Column="3" Margin="0,8">
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <TextBlock
                            Margin="8,0"
                            VerticalAlignment="Center"
                            FontSize="21"
                            Text="Axis"
                            TextAlignment="Center" />
                        <TextBlock
                            x:Name="txtAxis"
                            Grid.Row="1"
                            Margin="8,0"
                            VerticalAlignment="Center"
                            FontSize="21"
                            Text="0"
                            TextAlignment="Center" />
                    </Grid>


                    <Grid Grid.Column="4" Margin="0,8">
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <TextBlock
                            Margin="8,0"
                            VerticalAlignment="Center"
                            FontSize="21"
                            Text="BC"
                            TextAlignment="Center" />
                        <TextBlock
                            x:Name="txtBC"
                            Grid.Row="1"
                            Margin="8,0"
                            VerticalAlignment="Center"
                            FontSize="21"
                            Text="0"
                            TextAlignment="Center" />
                    </Grid>





                    <Grid Grid.Column="5" Margin="0,8">
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <TextBlock
                            Margin="8,0"
                            VerticalAlignment="Center"
                            FontSize="21"
                            Text="Dia"
                            TextAlignment="Center" />
                        <TextBlock
                            x:Name="TxtDia"
                            Grid.Row="1"
                            Margin="8,0"
                            VerticalAlignment="Center"
                            FontSize="21"
                            Text="0"
                            TextAlignment="Center" />
                    </Grid>







                    <Grid Grid.Column="6" Margin="0,8">
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <TextBlock
                            Margin="8,0"
                            VerticalAlignment="Center"
                            FontSize="21"
                            Text="Add"
                            TextAlignment="Center" />
                        <TextBlock
                            x:Name="txtAdd"
                            Grid.Row="1"
                            Margin="8,0"
                            VerticalAlignment="Center"
                            FontSize="21"
                            Text="0"
                            TextAlignment="Center" />
                    </Grid>


                    <Grid
                        Grid.Row="1"
                        Grid.ColumnSpan="2"
                        Margin="8,0">

                        <ComboBox
                            x:Name="cmbCyl"
                            Height="60"
                            VerticalAlignment="Stretch"
                            VerticalContentAlignment="Stretch"
                            BorderThickness="1"
                            FontSize="21"
                            IsEditable="True"
                            Tag="Cyl" />
                    </Grid>


                    <Grid
                        Grid.Row="1"
                        Grid.Column="2"
                        Grid.ColumnSpan="2"
                        Margin="8,0">

                        <ComboBox
                            x:Name="cmbSph"
                            Height="60"
                            VerticalAlignment="Stretch"
                            VerticalContentAlignment="Stretch"
                            BorderThickness="1"
                            FontSize="21"
                            IsEditable="True"
                            Tag="Sph" />
                    </Grid>


                    <Grid
                        Grid.Row="1"
                        Grid.Column="4"
                        Grid.ColumnSpan="2"
                        Margin="8,0">

                        <ComboBox
                            x:Name="cmbPow"
                            Height="60"
                            VerticalAlignment="Stretch"
                            VerticalContentAlignment="Stretch"
                            Background="red"
                            BorderThickness="1"
                            FontSize="21"
                            IsEditable="True"
                            KeyDown="cmbPow_KeyDown"
                            SelectionChanged="cmbPow_SelectionChanged"
                            Tag="Pow" />
                    </Grid>

                    <Grid
                        Grid.Row="0"
                        Grid.Column="0"
                        Grid.ColumnSpan="1"
                        Margin="8,0">
                        <ComboBox
                            x:Name="cmbWarehouse"
                            Height="60"
                            VerticalAlignment="Stretch"
                            VerticalContentAlignment="Stretch"
                            BorderThickness="1"
                            DisplayMemberPath="Name"
                            FontSize="21"
                            IsEditable="False"
                            SelectedValuePath="Id"
                            SelectionChanged="cmbWarehouse_SelectionChanged"
                            Tag="المخزن" />
                    </Grid>

                    <Border
                        x:Name="btnShow"
                        Grid.Row="1"
                        Grid.Column="6"
                        MaxHeight="44"
                        Margin="8,0"
                        Background="{StaticResource PrimaryColor}"
                        CornerRadius="8"
                        Cursor="Hand"
                        MouseLeftButtonDown="btnShow_MouseLeftButtonDown">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="18"
                            Foreground="White">
                            عرض
                        </TextBlock>
                    </Border>




                    <Grid
                        Grid.Row="2"
                        Grid.ColumnSpan="2"
                        Margin="8,0">


                        <TextBox
                            x:Name="txtBarcode"
                            Height="60"
                            VerticalAlignment="Stretch"
                            VerticalContentAlignment="Stretch"
                            BorderThickness="1"
                            FontSize="21"
                            Tag="باركود" />

                    </Grid>

                    <Grid
                        Grid.Row="2"
                        Grid.Column="2"
                        Grid.ColumnSpan="2"
                        Margin="8,0">
                        <ComboBox
                            x:Name="cmbColor"
                            Grid.Column="0"
                            Grid.ColumnSpan="2"
                            Height="60"
                            BorderBrush="{DynamicResource SecundaryColor}"
                            BorderThickness="2"
                            FontSize="21"
                            IsEditable="True"
                            IsReadOnly="False"
                            Tag="اللون" />

                    </Grid>

                    <Grid Grid.Row="2" Grid.Column="4">
                        <Border
                            x:Name="btnAddColor"
                            Width="auto"
                            MaxHeight="44"
                            Padding="32,0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Stretch"
                            Background="{StaticResource PrimaryColor}"
                            CornerRadius="8"
                            Cursor="Hand"
                            MouseLeftButtonDown="btnAddColor_MouseLeftButtonDown">
                            <TextBlock
                                Height="54"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="36"
                                Foreground="White"
                                TextAlignment="Center">
                                +
                            </TextBlock>
                        </Border>
                    </Grid>

                    <Grid
                        Grid.Row="3"
                        Grid.Column="0"
                        Grid.ColumnSpan="2"
                        Margin="8,0">
                        <TextBox
                            x:Name="txtCostPrice"
                            Height="60"
                            VerticalAlignment="Stretch"
                            VerticalContentAlignment="Stretch"
                            utils:NumericInputControl.IsDecimalOnly="True"
                            BorderThickness="1"
                            FontSize="21"
                            IsReadOnly="True"
                            Tag="السعر التكلفة" />

                    </Grid>


                    <Grid
                        Grid.Row="3"
                        Grid.Column="2"
                        Grid.ColumnSpan="2"
                        Margin="8,0">
                        <TextBox
                            x:Name="txtSellPrice"
                            Height="60"
                            VerticalAlignment="Stretch"
                            VerticalContentAlignment="Stretch"
                            utils:NumericInputControl.IsDecimalOnly="True"
                            BorderThickness="1"
                            FontSize="21"
                            Tag="السعر البيع" />

                    </Grid>




                    <Border
                        x:Name="btnSave"
                        Grid.Row="4"
                        Grid.Column="2"
                        MaxHeight="44"
                        Margin="8,0"
                        Background="{StaticResource PrimaryColor}"
                        CornerRadius="8"
                        Cursor="Hand"
                        MouseLeftButtonDown="btnSave_MouseLeftButtonDown">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="18"
                            Foreground="White">
                            حفظ
                        </TextBlock>
                    </Border>

                    <Border
                        x:Name="btnNew"
                        Grid.Row="4"
                        Grid.Column="3"
                        MaxHeight="44"
                        Margin="8,0"
                        BorderBrush="{StaticResource PrimaryColor}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Cursor="Hand"
                        MouseLeftButtonDown="btnNew_MouseLeftButtonDown">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="18"
                            Foreground="{StaticResource PrimaryColor}">
                            جديد
                        </TextBlock>
                    </Border>



                    <Border
                        x:Name="btnDelete"
                        Grid.Row="4"
                        Grid.Column="4"
                        MaxHeight="44"
                        Margin="8,0"
                        BorderBrush="{StaticResource errorColor}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Cursor="Hand"
                        MouseLeftButtonDown="btnDelete_MouseLeftButtonDown">

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition Width="30" />
                            </Grid.ColumnDefinitions>
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="18"
                                Foreground="{StaticResource errorColor}">
                                حذف
                            </TextBlock>

                            <Path
                                Grid.Column="1"
                                Width="24"
                                Height="24"
                                HorizontalAlignment="Center"
                                Cursor="Hand"
                                Data="{StaticResource Trash}"
                                Fill="{StaticResource errorColor}"
                                FlowDirection="LeftToRight"
                                Stretch="Uniform"
                                StrokeThickness="1" />
                        </Grid>

                    </Border>

                    <ListView
                        x:Name="list"
                        Grid.Row="5"
                        Grid.RowSpan="6"
                        Grid.ColumnSpan="8"
                        AlternationCount="2147483647"
                        Background="{DynamicResource PageColor}"
                        BorderThickness="1"
                        FontFamily="pack://application:,,,/Assets/#Cairo"
                        ItemsSource="{Binding}"
                        MouseDoubleClick="list_MouseDoubleClick"
                        ScrollViewer.HorizontalScrollBarVisibility="Hidden"
                        SizeChanged="list_SizeChanged">
                        <ListView.BorderBrush>
                            <SolidColorBrush Opacity="0.42" Color="Black" />
                        </ListView.BorderBrush>
                        <!--  SelectionChanged="ListView_SelectionChanged"  -->

                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem">
                                <Style.Triggers>
                                    <Trigger Property="Control.IsMouseOver" Value="True">
                                        <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                                        <Setter Property="FontWeight" Value="Bold" />
                                        <Setter Property="Foreground" Value="Black" />
                                    </Trigger>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                                        <Setter Property="Foreground" Value="Black" />
                                    </Trigger>

                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="FontWeight" Value="Bold" />
                                        <Setter Property="Foreground" Value="Black" />
                                    </Trigger>

                                    <MultiTrigger>
                                        <MultiTrigger.Conditions>
                                            <Condition Property="IsSelected" Value="False" />
                                            <Condition Property="IsMouseOver" Value="False" />
                                        </MultiTrigger.Conditions>
                                        <Setter Property="FontWeight" Value="Thin" />
                                        <Setter Property="Foreground" Value="{DynamicResource PrimaryTextColor}" />
                                    </MultiTrigger>

                                </Style.Triggers>
                                <Setter Property="Control.Background" Value="{DynamicResource PageColor}" />
                                <Setter Property="Control.HorizontalContentAlignment" Value="Stretch" />
                            </Style>

                        </ListView.ItemContainerStyle>
                        <ListView.View>
                            <GridView AllowsColumnReorder="False">
                                <GridView.ColumnHeaderContainerStyle>
                                    <Style BasedOn="{StaticResource ListViewHeader}" TargetType="{x:Type GridViewColumnHeader}">
                                        <Setter Property="IsEnabled" Value="False" />
                                        <Setter Property="Height" Value="60" />
                                        <Style.Triggers>
                                            <Trigger Property="IsEnabled" Value="False">
                                                <Setter Property="TextElement.Foreground" Value="Black" />
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </GridView.ColumnHeaderContainerStyle>
                                <GridViewColumn Width="Auto" Header="#">
                                    <GridViewColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                Height="45"
                                                MinWidth="35"
                                                HorizontalAlignment="Center"
                                                Text="{Binding ., Converter={StaticResource IndexToNumberConverter}, ConverterParameter={x:Reference list}}"
                                                TextAlignment="Center" />
                                        </DataTemplate>
                                    </GridViewColumn.CellTemplate>
                                </GridViewColumn>
                                <GridViewColumn Width="Auto" Header="الباركود">
                                    <GridViewColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                Height="45"
                                                MinWidth="35"
                                                HorizontalAlignment="Center"
                                                Background="Transparent"
                                                Text="{Binding Barcode, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}"
                                                TextAlignment="Center" />
                                        </DataTemplate>
                                    </GridViewColumn.CellTemplate>
                                </GridViewColumn>

                                <GridViewColumn Width="Auto" Header="اللون">
                                    <GridViewColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                Height="45"
                                                MinWidth="35"
                                                HorizontalAlignment="Center"
                                                Background="Transparent"
                                                Text="{Binding Color.Name, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}"
                                                TextAlignment="Center" />
                                        </DataTemplate>
                                    </GridViewColumn.CellTemplate>
                                </GridViewColumn>

                                <GridViewColumn Width="Auto" Header="الكمية">
                                    <GridViewColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBlock
                                                Height="45"
                                                MinWidth="35"
                                                HorizontalAlignment="Center"
                                                Background="Transparent"
                                                Text="{Binding TotalQuantity, FallbackValue='0', TargetNullValue='0'}"
                                                TextAlignment="Center" />
                                        </DataTemplate>
                                    </GridViewColumn.CellTemplate>
                                </GridViewColumn>

                            </GridView>

                        </ListView.View>
                    </ListView>


                </Grid>
            </Grid>
        </Border>
    </Viewbox>
</Window>