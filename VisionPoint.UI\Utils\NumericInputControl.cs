using System;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace VisionPoint.UI.Utils
{
    public static class NumericInputControl
    {
        private static readonly Regex _numericRegex = new Regex("[^0-9]+");
        private static readonly Regex _decimalRegex = new Regex("[^0-9.,]+");
        private static readonly Regex _negativeDecimalRegex = new Regex("[^0-9.,-]+");

        #region IsNumericOnly Property

        public static readonly DependencyProperty IsNumericOnlyProperty =
            DependencyProperty.RegisterAttached(
                "IsNumericOnly",
                typeof(bool),
                typeof(NumericInputControl),
                new PropertyMetadata(false, OnIsNumericOnlyChanged));

        public static bool GetIsNumericOnly(DependencyObject obj)
        {
            return (bool)obj.GetValue(IsNumericOnlyProperty);
        }

        public static void SetIsNumericOnly(DependencyObject obj, bool value)
        {
            obj.SetValue(IsNumericOnlyProperty, value);
        }

        private static void OnIsNumericOnlyChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is TextBox textBox)
            {
                bool isNumeric = (bool)e.NewValue;

                if (isNumeric)
                {
                    textBox.PreviewTextInput += NumericOnlyTextInput;
                    textBox.PreviewKeyDown += CommonPreviewKeyDown;
                    DataObject.AddPastingHandler(textBox, NumericOnlyPasting);
                    textBox.TextChanged += NumericTextChanged;
                    
                    // Initialize with valid content
                    if (!string.IsNullOrEmpty(textBox.Text))
                    {
                        // Remove any non-numeric characters from initial text
                        textBox.Text = Regex.Replace(textBox.Text, "[^0-9]", "");
                    }
                }
                else
                {
                    textBox.PreviewTextInput -= NumericOnlyTextInput;
                    textBox.PreviewKeyDown -= CommonPreviewKeyDown;
                    DataObject.RemovePastingHandler(textBox, NumericOnlyPasting);
                    textBox.TextChanged -= NumericTextChanged;
                }
            }
        }

        private static void NumericTextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                // Ensure the text only contains digits
                string cleanText = Regex.Replace(textBox.Text, "[^0-9]", "");
                if (cleanText != textBox.Text)
                {
                    int caretPosition = textBox.CaretIndex;
                    int diff = textBox.Text.Length - cleanText.Length;
                    textBox.Text = cleanText;
                    // Try to maintain caret position
                    textBox.CaretIndex = Math.Max(0, caretPosition - diff);
                }
            }
        }

        private static void NumericOnlyTextInput(object sender, TextCompositionEventArgs e)
        {
            e.Handled = _numericRegex.IsMatch(e.Text);
        }

        private static void NumericOnlyPasting(object sender, DataObjectPastingEventArgs e)
        {
            if (e.DataObject.GetDataPresent(typeof(string)))
            {
                string text = (string)e.DataObject.GetData(typeof(string));
                if (_numericRegex.IsMatch(text))
                {
                    e.CancelCommand();
                }
            }
            else
            {
                e.CancelCommand();
            }
        }

        #endregion

        #region IsDecimalOnly Property

        public static readonly DependencyProperty IsDecimalOnlyProperty =
            DependencyProperty.RegisterAttached(
                "IsDecimalOnly",
                typeof(bool),
                typeof(NumericInputControl),
                new PropertyMetadata(false, OnIsDecimalOnlyChanged));

        public static readonly DependencyProperty AllowNegativeProperty =
            DependencyProperty.RegisterAttached(
                "AllowNegative",
                typeof(bool),
                typeof(NumericInputControl),
                new PropertyMetadata(false));

        public static bool GetAllowNegative(DependencyObject obj)
        {
            return (bool)obj.GetValue(AllowNegativeProperty);
        }

        public static void SetAllowNegative(DependencyObject obj, bool value)
        {
            obj.SetValue(AllowNegativeProperty, value);
        }

        public static bool GetIsDecimalOnly(DependencyObject obj)
        {
            return (bool)obj.GetValue(IsDecimalOnlyProperty);
        }

        public static void SetIsDecimalOnly(DependencyObject obj, bool value)
        {
            obj.SetValue(IsDecimalOnlyProperty, value);
        }

        private static void OnIsDecimalOnlyChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is TextBox textBox)
            {
                bool isDecimal = (bool)e.NewValue;

                if (isDecimal)
                {
                    textBox.PreviewTextInput += DecimalOnlyTextInput;
                    textBox.PreviewKeyDown += CommonPreviewKeyDown;
                    DataObject.AddPastingHandler(textBox, DecimalOnlyPasting);
                    textBox.TextChanged += DecimalTextChanged;
                    
                    // Initialize with valid content
                    if (!string.IsNullOrEmpty(textBox.Text))
                    {
                        // Clean decimal text
                        string validText = CleanDecimalText(textBox.Text, GetAllowNegative(textBox));
                        if (validText != textBox.Text)
                        {
                            textBox.Text = validText;
                        }
                    }
                }
                else
                {
                    textBox.PreviewTextInput -= DecimalOnlyTextInput;
                    textBox.PreviewKeyDown -= CommonPreviewKeyDown;
                    DataObject.RemovePastingHandler(textBox, DecimalOnlyPasting);
                    textBox.TextChanged -= DecimalTextChanged;
                }
            }
        }

        private static void DecimalTextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                bool allowNegative = GetAllowNegative(textBox);
                string originalText = textBox.Text;
                string cleanText = CleanDecimalText(originalText, allowNegative);
                
                if (cleanText != originalText)
                {
                    int caretPosition = textBox.CaretIndex;
                    int diff = originalText.Length - cleanText.Length;
                    textBox.Text = cleanText;
                    
                    // Try to maintain caret position
                    textBox.CaretIndex = Math.Max(0, caretPosition - diff);
                }
            }
        }

        private static string CleanDecimalText(string text, bool allowNegative)
        {
            // Handle negative sign if allowed
            bool isNegative = allowNegative && text.StartsWith("-");
            string textWithoutSign = isNegative ? text.Substring(1) : text;
            
            // Remove invalid characters
            string cleanText = Regex.Replace(textWithoutSign, "[^0-9.,]", "");
            
            // Handle decimal point (keep only first one)
            int decimalPoint = cleanText.IndexOf('.');
            if (decimalPoint >= 0)
            {
                int commaPoint = cleanText.IndexOf(',');
                if (commaPoint >= 0)
                {
                    // If both exist, remove all commas
                    cleanText = cleanText.Replace(",", "");
                }
            }
            else
            {
                // If no period, check for comma and replace first one with period
                int commaPoint = cleanText.IndexOf(',');
                if (commaPoint >= 0)
                {
                    cleanText = cleanText.Substring(0, commaPoint) + "." + 
                               cleanText.Substring(commaPoint + 1).Replace(",", "");
                }
            }
            
            // Handle multiple decimal points
            int finalDecimalPoint = cleanText.IndexOf('.');
            if (finalDecimalPoint >= 0)
            {
                cleanText = cleanText.Substring(0, finalDecimalPoint + 1) + 
                            cleanText.Substring(finalDecimalPoint + 1).Replace(".", "");
            }
            
            // Add back negative sign if needed
            return isNegative ? "-" + cleanText : cleanText;
        }

        private static void DecimalOnlyTextInput(object sender, TextCompositionEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                bool allowNegative = GetAllowNegative(textBox);
                string text = textBox.Text;
                int selectionStart = textBox.SelectionStart;
                
                // Special handling for negative sign
                if (allowNegative && e.Text == "-")
                {
                    // Allow only at the beginning of the text
                    if (selectionStart == 0 && !text.Contains("-"))
                    {
                        e.Handled = false;
                        return;
                    }
                    else
                    {
                        e.Handled = true;
                        return;
                    }
                }
                
                // Check if the text already contains a decimal point and the new input is also a decimal point
                if ((e.Text == "." || e.Text == ",") && 
                    (text.Contains(".") || text.Contains(",")))
                {
                    e.Handled = true;
                    return;
                }
                
                // Use the appropriate regex based on whether negative is allowed
                if (allowNegative)
                {
                    e.Handled = _negativeDecimalRegex.IsMatch(e.Text);
                }
                else
                {
                    e.Handled = _decimalRegex.IsMatch(e.Text);
                }
            }
        }

        private static void DecimalOnlyPasting(object sender, DataObjectPastingEventArgs e)
        {
            if (e.DataObject.GetDataPresent(typeof(string)))
            {
                string pastingText = (string)e.DataObject.GetData(typeof(string));
                
                if (sender is TextBox textBox)
                {
                    bool allowNegative = GetAllowNegative(textBox);
                    string currentText = textBox.Text;
                    int selectionStart = textBox.SelectionStart;
                    int selectionLength = textBox.SelectionLength;
                    
                    // Create the text that would result after pasting
                    string resultText = currentText.Substring(0, selectionStart) + 
                                       pastingText + 
                                       currentText.Substring(selectionStart + selectionLength);
                    
                    // Check if the result would be valid
                    string cleanText = CleanDecimalText(resultText, allowNegative);
                    if (cleanText != resultText)
                    {
                        e.CancelCommand();
                    }
                }
                else
                {
                    // Use the appropriate regex for checking
                    bool allowNegative = (sender is TextBox tb) && GetAllowNegative(tb);
                    if (allowNegative)
                    {
                        e.Handled = _negativeDecimalRegex.IsMatch(pastingText);
                    }
                    else
                    {
                        e.Handled = _decimalRegex.IsMatch(pastingText);
                    }
                }
            }
            else
            {
                e.CancelCommand();
            }
        }

        #endregion

        // Common handler for both numeric and decimal inputs
        private static void CommonPreviewKeyDown(object sender, KeyEventArgs e)
        {
            // Allow control keys like backspace, delete, etc.
            if (e.Key == Key.Space)
            {
                e.Handled = true;
            }
        }
    }
} 