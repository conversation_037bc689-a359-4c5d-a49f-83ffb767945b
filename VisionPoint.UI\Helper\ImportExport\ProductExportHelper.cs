using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;
using VisionPoint.UI.views.Pages.ImportExport;
using VisionPoint.UI.views.Pages.ProductsContent;

namespace VisionPoint.UI.Helper.ImportExport
{
    /// <summary>
    /// مساعد لعملية تصدير المنتجات
    /// </summary>
    public class ProductExportHelper
    {
        public ProductExportHelper()
        {
        }

        /// <summary>
        /// بدء عملية تصدير المنتجات
        /// </summary>
        /// <returns>قيمة تشير إلى نجاح العملية</returns>
        public async Task<bool> StartExportProcess()
        {
            // 1. عرض نافذة اختيار المخزن
            var warehouseSelectionWindow = new WarehouseSelectionWindow();
            warehouseSelectionWindow.ShowDialog();

            if (!warehouseSelectionWindow.DialogResult)
            {
                return false; // تم إلغاء العملية
            }

            int? selectedWarehouseId = warehouseSelectionWindow.IsAllWarehousesSelected
                ? null
                : warehouseSelectionWindow.SelectedWarehouseId;

            // 2. إنشاء خدمة تصدير
            var exportService = new ExportService();

            // 3. عرض نافذة اختيار الحقول
            var selectedFields = ShowFieldsSelectionWindow(exportService);
            if (selectedFields == null || selectedFields.Count == 0)
            {
                return false; // تم إلغاء العملية
            }

            // 4. عرض نافذة حفظ الملف
            var savePath = SelectSaveLocation();
            if (string.IsNullOrEmpty(savePath))
            {
                return false; // تم إلغاء العملية
            }

            // 5. عرض نافذة التصدير وبدء العملية
            var exporterPage = new ProductExporterPage(exportService, selectedFields, savePath, selectedWarehouseId);
            exporterPage.ShowDialog();

            return true;
        }

        /// <summary>
        /// عرض نافذة اختيار الحقول
        /// </summary>
        /// <param name="exportService">خدمة التصدير</param>
        /// <returns>قائمة الحقول المختارة أو null إذا تم الإلغاء</returns>
        private List<string> ShowFieldsSelectionWindow(ExportService exportService)
        {
            var fieldsWindow = new ExportFieldsWindow(exportService.GetProductExportFields());
            var result = fieldsWindow.ShowDialog();

            if (result == true)
            {
                return fieldsWindow.SelectedFields;
            }

            return null;
        }

        /// <summary>
        /// اختيار مكان حفظ الملف
        /// </summary>
        /// <returns>مسار الملف المختار أو سلسلة فارغة إذا تم الإلغاء</returns>
        private string SelectSaveLocation()
        {
            SaveFileDialog saveFileDialog = new SaveFileDialog
            {
                Filter = "Excel Files|*.xlsx",
                FileName = $"Products_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx",
                Title = "حفظ ملف المنتجات"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                return saveFileDialog.FileName;
            }

            return string.Empty;
        }
    }
}
