﻿<Page
    x:Class="VisionPoint.UI.views.Pages.Settings.SettingsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Settings"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils"
    Title="SettingsPage"
    d:Background="White"
    d:Height="1080"
    d:Width="1570"
    FlowDirection="RightToLeft"
    Loaded="Page_Loaded"
    mc:Ignorable="d">

    <Grid Margin="32">
        <Grid.ColumnDefinitions>
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
        </Grid.RowDefinitions>

        <Grid Grid.ColumnSpan="3" Margin="8,0">
            <ComboBox
                x:Name="cmbDefaultPrinter"
                Height="60"
                Padding="42,0"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                IsEditable="True"
                IsReadOnly="False"
                Tag="الطابعة الافتراضية" />
        </Grid>



        <Grid
            Grid.RowSpan="2"
            Grid.Column="3"
            Grid.ColumnSpan="2"
            Margin="8,0">

            <TextBox
                x:Name="txtArabicAddress"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                Style="{StaticResource textStatment}"
                Tag="العنوان بالعربي"
                TextWrapping="Wrap" />

        </Grid>

        <Grid
            Grid.RowSpan="2"
            Grid.Column="5"
            Grid.ColumnSpan="2"
            Margin="8,0">


            <TextBox
                x:Name="txtEnglishAddress"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                Style="{StaticResource textStatment}"
                Tag="العنوان بالانجليزي"
                TextWrapping="Wrap" />

        </Grid>

        <Grid
            Grid.Row="1"
            Grid.ColumnSpan="3"
            Margin="8,0">


            <TextBox
                x:Name="txtBranch"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                Tag="اسم الفرع" />

        </Grid>

        <Grid
            Grid.Row="2"
            Grid.ColumnSpan="3"
            Margin="8,0">


            <TextBox
                x:Name="txtPhoneNumber"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                utils:NumericInputControl.IsNumericOnly="True"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                Tag="رقم الهاتف" />

        </Grid>

        <Border
            x:Name="btnSaveSettings"
            Grid.Row="3"
            Grid.Column="0"
            MaxHeight="44"
            Margin="16,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnSaveSettings_MouseLeftButtonDown">

            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White"
                Text="حفظ" />
        </Border>
        <Border
            Grid.Row="4"
            Grid.Column="0"
            Grid.ColumnSpan="10"
            Height="2"
            VerticalAlignment="Top"
            Background="{StaticResource PrimaryColor}" />

        <Border
            x:Name="btnBorrowMoney"
            Grid.Row="4"
            Grid.Column="0"
            MaxHeight="44"
            Margin="16,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnBorrowMoney_MouseLeftButtonDown">

            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White"
                Text="سحب/ايداع موظف" />
        </Border>




        <Border
            x:Name="btnReports"
            Grid.Row="5"
            Grid.Column="0"
            MaxHeight="44"
            Margin="16,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand">

            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White"
                Text="التقارير" />
        </Border>

        <Border
            x:Name="btnDiscount"
            Grid.Row="5"
            Grid.Column="1"
            MaxHeight="44"
            Margin="16,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnDiscount_MouseLeftButtonDown">

            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}"
                Text="التخفيض" />
        </Border>


        <Border
            x:Name="btnExipres"
            Grid.Row="6"
            Grid.Column="0"
            MaxHeight="44"
            Margin="16,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnExipres_MouseLeftButtonDown">

            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White"
                Text="صلاحية الاصناف" />
        </Border>

        <Border
            x:Name="btnMinimumQuantity"
            Grid.Row="6"
            Grid.Column="3"
            MaxHeight="44"
            Margin="16,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnMinimumQuantity_MouseLeftButtonDown">

            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}"
                Text="الحد الأدنى للكمية" />
        </Border>

        <Border
            Grid.Row="6"
            Grid.Column="0"
            Grid.ColumnSpan="10"
            Height="2"
            VerticalAlignment="Bottom"
            Background="{StaticResource PrimaryColor}" />

        <Grid
            Grid.Row="6"
            Grid.Column="1"
            Grid.ColumnSpan="2"
            Margin="8,0">

            <TextBox
                x:Name="txtDefaultExpireDate"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                utils:NumericInputControl.IsNumericOnly="True"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                Tag="الايام المتبقية للصلاحيات"
                TextChanged="txtDefaultExpireDate_TextChanged" />

        </Grid>

        <Grid
            Grid.Row="4"
            Grid.Column="4"
            Margin="8,0">

            <DockPanel>
                <TextBlock
                    HorizontalAlignment="Center"
                    DockPanel.Dock="Top"
                    Text="السماح بالكميات بالسالب" />
                <CheckBox
                    x:Name="chkAllowNegativeQuantities"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Checked="chkAllowNegativeQuantities_Checked"
                    DockPanel.Dock="Top"
                    Style="{StaticResource CircleCheckboxFL}"
                    Unchecked="chkAllowNegativeQuantities_Unchecked" />
            </DockPanel>
        </Grid>



        <Grid
            Grid.Row="6"
            Grid.Column="4"
            Grid.ColumnSpan="2"
            Margin="8,0">

            <DockPanel>
                <TextBlock
                    HorizontalAlignment="Center"
                    DockPanel.Dock="Top"
                    Text="تفعيل فحص الحد الأدنى للكمية" />
                <CheckBox
                    x:Name="chkEnableMinimumQuantityCheck"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Checked="chkEnableMinimumQuantityCheck_Checked"
                    DockPanel.Dock="Top"
                    Style="{StaticResource CircleCheckboxFL}"
                    Unchecked="chkEnableMinimumQuantityCheck_Unchecked" />
            </DockPanel>
        </Grid>



        <Border
            Grid.Row="9"
            Grid.Column="0"
            Grid.ColumnSpan="10"
            Height="2"
            VerticalAlignment="Top"
            Background="{StaticResource PrimaryColor}" />

        <Border
            x:Name="btnBackup"
            Grid.Row="8"
            Grid.Column="0"
            MaxHeight="44"
            Margin="16,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnBackup_MouseLeftButtonDown">

            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White"
                Text="نسخة احتياطية" />
        </Border>

        <Border
            x:Name="btnRestore"
            Grid.Row="8"
            Grid.Column="1"
            MaxHeight="44"
            Margin="16,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnRestore_MouseLeftButtonDown">

            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}"
                Text="استعادة النسخة" />
        </Border>

        <!--  Auto Backup Settings  -->
        <TextBlock
            Grid.Row="7"
            Grid.ColumnSpan="1"
            Margin="16,0,0,0"
            VerticalAlignment="Center"
            FontSize="18"
            FontWeight="Bold"
            Text="إعدادات النسخ الاحتياطية" />

        <Grid
            Grid.Row="7"
            Grid.Column="1"
            Margin="8,0">

            <DockPanel VerticalAlignment="Center" LastChildFill="False">
                <TextBlock
                    Margin="0,8"
                    HorizontalAlignment="Center"
                    DockPanel.Dock="Top"
                    Text="تفعيل النسخ التلقائي" />
                <CheckBox
                    x:Name="chkAutoBackupEnabled"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Checked="chkAutoBackupEnabled_Checked"
                    DockPanel.Dock="Top"
                    Style="{StaticResource CircleCheckboxFL}"
                    Unchecked="chkAutoBackupEnabled_Unchecked" />
            </DockPanel>
        </Grid>

        <Grid
            Grid.Row="7"
            Grid.Column="2"
            Grid.ColumnSpan="2"
            Margin="8,0">

            <TextBox
                x:Name="txtAutoBackupMaxCount"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                utils:NumericInputControl.IsNumericOnly="True"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                Tag="عدد النسخ الاحتياطية"
                TextChanged="txtAutoBackupMaxCount_TextChanged" />
        </Grid>

        <Grid
            Grid.Row="7"
            Grid.Column="4"
            Grid.ColumnSpan="2"
            Margin="8,0">

            <TextBox
                x:Name="txtAutoBackupPath"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                IsReadOnly="True"
                Tag="مسار النسخ الاحتياطي التلقائي" />
        </Grid>



        <Border
            x:Name="btnBrowseAutoBackupPath"
            Grid.Row="7"
            Grid.Column="6"
            MaxHeight="44"
            Margin="16,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnBrowseAutoBackupPath_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White"
                Text="تعديل المسار" />
        </Border>
    </Grid>
</Page>
