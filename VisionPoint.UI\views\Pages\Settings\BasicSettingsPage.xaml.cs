﻿using Microsoft.Win32;
using System.Drawing.Printing;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.Settings
{
    public partial class BasicSettingsPage : Page
    {
        private const string LogoFileName = "company_logo.png";
        // private string logoFileName = null; // This variable is not used, can be removed

        public BasicSettingsPage()
        {
            InitializeComponent();
        }
        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            LoadBasicSettings();

        }
        private void LoadBasicSettings()
        {
            var printers = GetAllPrinterNames();
            if (printers != null)
            {
                cmbDefaultPrinter.ItemsSource = printers;
                cmbDefaultPrinter.SelectedItem = GetDefaultPrinterName();
            }
            else
            {
                ErrorBox.Show("لا توجد طابعات متاحة", "خطأ", true);
            }
            txtCompanyName.Text = Properties.Settings.Default.CompanyName;
            txtArabicAddress.Text = Properties.Settings.Default.ArabicAddress;
            txtEnglishAddress.Text = Properties.Settings.Default.EnglishAddress;
            txtPhoneNumber.Text = Properties.Settings.Default.PhoneNumber;
            txtBranch.Text = Properties.Settings.Default.BranchName;
            txtCompanyNameEng.Text = Properties.Settings.Default.CompanyNameEng; // Assuming you have this line in your XAML

            // Always load logo from Assets/company_logo.png
            string assetsPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Assets", LogoFileName);
            if (File.Exists(assetsPath))
            {
                try
                {
                    BitmapImage bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri(assetsPath);
                    bitmap.CacheOption = BitmapCacheOption.OnLoad; // CRUCIAL: Releases file lock after loading
                    bitmap.EndInit();
                    bitmap.Freeze(); // Make it immutable for better performance/thread safety
                    imgLogoPreview.Source = bitmap;
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"Error loading initial logo: {ex.Message}", "Error");
                    imgLogoPreview.Source = null; // Clear source if loading fails
                }
            }
            else
            {
                imgLogoPreview.Source = null;
            }
        }

        // Get all available printers
        public string[]? GetAllPrinterNames()
        {
            try
            {
                PrinterSettings.StringCollection printers = PrinterSettings.InstalledPrinters;
                return printers.Cast<string>().ToArray();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على الطابعات: {ex.Message}");
                return null;
            }
        }

        public string GetDefaultPrinterName()
        {
            try
            {
                string defaultPrinter = Properties.Settings.Default.DefaultPrinter;
                return defaultPrinter;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على الطابعة الافتراضية: {ex.Message}");
                return string.Empty; // Return empty if error occurs
            }
        }

        public void SetDefaultPrinterName(string printerName)
        {
            try
            {
                if (printerName == Properties.Settings.Default.DefaultPrinter)
                {
                    return;
                }
                Properties.Settings.Default.DefaultPrinter = printerName;
                Properties.Settings.Default.Save(); // Save to user config
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تعيين الطابعة الافتراضية: {ex.Message}");
            }
        }
        private void btnUploadLogo_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Title = "اختر صورة الشعار", // Choose logo image
                Filter = "Image Files|*.jpg;*.jpeg;*.png;*.bmp|All Files|*.*"
            };

            if (dialog.ShowDialog() == true)
            {
                string assetsDir = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Assets");
                if (!Directory.Exists(assetsDir))
                    Directory.CreateDirectory(assetsDir);

                string destPath = System.IO.Path.Combine(assetsDir, LogoFileName);

                try
                {
                    // 1. CRUCIAL: Release the current image source to free up the file handle
                    if (imgLogoPreview.Source != null)
                    {
                        imgLogoPreview.Source = null;
                    }

                    // 2. CRUCIAL: Delete the existing file if it's there
                    if (File.Exists(destPath))
                    {
                        File.Delete(destPath);
                    }

                    // 3. Copy the new file
                    File.Copy(dialog.FileName, destPath, true);

                    // 4. Load the new image from a MemoryStream to bypass WPF's URI caching
                    BitmapImage newLogo = new BitmapImage();
                    using (var stream = new FileStream(destPath, FileMode.Open, FileAccess.Read))
                    {
                        newLogo.BeginInit();
                        newLogo.StreamSource = stream; // Use StreamSource to load from file content directly
                        newLogo.CacheOption = BitmapCacheOption.OnLoad; // Still important for releasing file lock *after* loading
                        newLogo.EndInit();
                    }
                    newLogo.Freeze(); // Make it immutable for better performance/thread safety
                    imgLogoPreview.Source = newLogo; // Update the UI Image control

                }
                catch (IOException ioEx)
                {
                    ErrorBox.Show($"Error copying or loading file: {ioEx.Message}\nMake sure the file isn't open in another program.", "File Error");
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"An unexpected error occurred: {ex.Message}", "Error");
                }
            }
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            // يمكن إضافة فحص الصلاحيات هنا إذا لزم الأمر
            // if (!CheckRole(new[] { "Admin", "BasicSettingsRole" }))
            //     return;

            Properties.Settings.Default.CompanyName = txtCompanyName.Text;
            Properties.Settings.Default.CompanyNameEng = txtCompanyNameEng.Text;
            Properties.Settings.Default.ArabicAddress = txtArabicAddress.Text;
            Properties.Settings.Default.EnglishAddress = txtEnglishAddress.Text;
            Properties.Settings.Default.PhoneNumber = txtPhoneNumber.Text;
            Properties.Settings.Default.BranchName = txtBranch.Text;
            Properties.Settings.Default.Save();
            SetDefaultPrinterName((string)cmbDefaultPrinter.SelectedItem);

            DialogBox.Show("حفظ", "تم حفظ البيانات بنجاح"); // Data saved successfully
        }

        // دالة فحص الصلاحيات (يمكن تفعيلها عند الحاجة)
        private bool CheckRole(string[] roles)
        {
            if (!CurrentUser.HasAnyRole(roles))
            {
                ErrorBox.Show("لا تملك صلاحية تعديل هذه الإعدادات", "خطأ في الصلاحيات", true);
                return false;
            }
            return true;
        }


    }
}