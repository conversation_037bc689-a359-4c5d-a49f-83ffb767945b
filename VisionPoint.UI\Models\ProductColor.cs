﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VisionPoint.UI.Models;
[Index(nameof(Barcode), IsUnique = true, IsDescending = [false])]
public class ProductColor
{
    [Key]
    public int Id { get; set; }
    public int? ProductId { get; set; }
    public Product? Product { get; set; }
    public byte? ColorId { get; set; }
    public Color? Color { get; set; }
    [StringLength(30, ErrorMessage = "الباركود يجب ألا يتجاوز 30 حرف")]
    public string? Barcode { get; set; }
    public ICollection<ProductQuantity>? ProductQuantity { get; set; } = new List<ProductQuantity>();

    /// <summary>
    /// Row GUID for SQL Server Merge Replication - managed by database
    /// </summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid RowGuid { get; set; }
}
