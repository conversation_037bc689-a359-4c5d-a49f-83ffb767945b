﻿﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Models;
using VisionPoint.UI.Services;
using VisionPoint.UI.ViewModel;

namespace VisionPoint.UI.PL;

public class DiscountManager : IDisposable
{
    private readonly AppDbContext _context = ServiceLocator.GetService<AppDbContext>();
    private bool _disposed = false;

    public DiscountManager()
    {
    }
    public async Task<List<Discount>> GetAllDiscounts()
    {
        return await _context.Discounts
            .Include(d => d.DiscountProducts)
                .ThenInclude(dp => dp.Product)
            .Include(d => d.DiscountLenses)
                .ThenInclude(dl => dl.Lens)
            .Include(d => d.DiscountServices)
                .ThenInclude(ds => ds.Service)
            .ToListAsyncWithBusy("GetAllDiscounts");
    }

    public async Task<List<DiscountVM>> GetAllDiscountVMs()
    {
        var discounts = await GetAllDiscounts();
        return discounts.Select(d => new DiscountVM
        {
            Id = d.Id,
            StartDate = d.StartDate,
            EndDate = d.EndDate,
            DiscountPercentage = d.DiscountPercentage,
            ApplyToAllProducts = d.ApplyToAllProducts,
            ApplyToAllLenses = d.ApplyToAllLenses,
            ApplyToAllServices = d.ApplyToAllServices,
            SelectedProductIds = d.DiscountProducts?.Select(dp => dp.ProductId).ToList() ?? new List<int>(),
            SelectedLensIds = d.DiscountLenses?.Select(dl => dl.LensId).ToList() ?? new List<int>(),
            SelectedServiceIds = d.DiscountServices?.Select(ds => ds.ServiceId).ToList() ?? new List<int>(),
            // Get the names of the selected items
            ProductName = d.DiscountProducts?.FirstOrDefault()?.Product?.Name,
            LensName = d.DiscountLenses?.FirstOrDefault()?.Lens?.Name,
            ServiceName = d.DiscountServices?.FirstOrDefault()?.Service?.Name
        }).ToList();
    }

    public async Task<Discount?> GetDiscountById(int id)
    {
        return await _context.Discounts
            .Include(d => d.DiscountProducts)
                .ThenInclude(dp => dp.Product)
            .Include(d => d.DiscountLenses)
                .ThenInclude(dl => dl.Lens)
            .Include(d => d.DiscountServices)
                .ThenInclude(ds => ds.Service)
            .FirstOrDefaultAsyncWithBusy(d => d.Id == id, "GetDiscountById");
    }

    public async Task<(bool State, string Message)> CreateDiscount(Discount discount, List<int> productIds, List<int> lensIds, List<int> serviceIds)
    {
        try
        {
            discount.CreatedById = CurrentUser.Id;
            discount.ModifiedById = CurrentUser.Id;
            discount.CreatedAt = DateTime.Now;
            discount.UpdatedAt = DateTime.Now;

            await _context.Discounts.AddAsyncWithBusy(discount);
            var result = await _context.SaveWithTransactionAndBusy("CreateDiscount");

            if (!result.State)
                return (false, result.Message);

            // Add product relationships if not applying to all products (محسنة - إدراج مجمع)
            if (!discount.ApplyToAllProducts && productIds != null && productIds.Any())
            {
                var discountProducts = productIds.Select(productId => new DiscountProduct
                {
                    DiscountId = discount.Id,
                    ProductId = productId
                }).ToList();

                await _context.DiscountProducts.AddRangeAsyncWithBusy(discountProducts);
            }

            // Add lens relationships if not applying to all lenses (محسنة - إدراج مجمع)
            if (!discount.ApplyToAllLenses && lensIds != null && lensIds.Any())
            {
                var discountLenses = lensIds.Select(lensId => new DiscountLens
                {
                    DiscountId = discount.Id,
                    LensId = lensId
                }).ToList();

                await _context.DiscountLenses.AddRangeAsyncWithBusy(discountLenses);
            }

            // Add service relationships if not applying to all services (محسنة للإدراج المجمع)
            if (!discount.ApplyToAllServices && serviceIds != null && serviceIds.Any())
            {
                var discountServices = serviceIds.Select(serviceId => new Models.DiscountService
                {
                    DiscountId = discount.Id,
                    ServiceId = serviceId
                }).ToList();

                await _context.DiscountServices.AddRangeAsyncWithBusy(discountServices);
            }

            result = await _context.SaveWithTransactionAndBusy("CreateDiscountRelationships");
            return result.State ? (true, "تم إضافة الخصم بنجاح") : (false, result.Message);
        }
        catch (Exception ex)
        {
            _context.Reverse();
            return (false, ex.Message);
        }
    }

    public async Task<(bool State, string Message)> UpdateDiscount(Discount discount, List<int> productIds, List<int> lensIds, List<int> serviceIds)
    {
        try
        {
            var existingDiscount = await _context.Discounts
                .Include(d => d.DiscountProducts)
                .Include(d => d.DiscountLenses)
                .Include(d => d.DiscountServices)
                .FirstOrDefaultAsyncWithBusy(d => d.Id == discount.Id, "GetDiscountForUpdate");

                if (existingDiscount == null)
                    return (false, "الخصم غير موجود");

                // Update basic properties
                existingDiscount.StartDate = discount.StartDate;
                existingDiscount.EndDate = discount.EndDate;
                existingDiscount.DiscountPercentage = discount.DiscountPercentage;
                existingDiscount.ApplyToAllProducts = discount.ApplyToAllProducts;
                existingDiscount.ApplyToAllLenses = discount.ApplyToAllLenses;
                existingDiscount.ApplyToAllServices = discount.ApplyToAllServices;
                existingDiscount.ModifiedById = CurrentUser.Id;
                existingDiscount.UpdatedAt = DateTime.Now;

                // Remove existing relationships (محسنة - حذف مجمع)
                if (existingDiscount.DiscountProducts?.Any() == true)
                {
                    _context.DiscountProducts.RemoveRangeWithBusy(existingDiscount.DiscountProducts);
                }

                if (existingDiscount.DiscountLenses?.Any() == true)
                {
                    _context.DiscountLenses.RemoveRangeWithBusy(existingDiscount.DiscountLenses);
                }

                if (existingDiscount.DiscountServices?.Any() == true)
                {
                    _context.DiscountServices.RemoveRangeWithBusy(existingDiscount.DiscountServices);
                }

                var result = await _context.SaveWithTransactionAndBusy("RemoveDiscountRelationships");

                if (!result.State)
                    return (false, result.Message);

                // Add product relationships if not applying to all products (محسنة - إدراج مجمع)
                if (!discount.ApplyToAllProducts && productIds != null && productIds.Any())
                {
                    var discountProducts = productIds.Select(productId => new DiscountProduct
                    {
                        DiscountId = discount.Id,
                        ProductId = productId
                    }).ToList();

                    await _context.DiscountProducts.AddRangeAsyncWithBusy(discountProducts);
                }

                // Add lens relationships if not applying to all lenses (محسنة للإدراج المجمع)
                if (!discount.ApplyToAllLenses && lensIds != null && lensIds.Any())
                {
                    var discountLenses = lensIds.Select(lensId => new DiscountLens
                    {
                        DiscountId = discount.Id,
                        LensId = lensId
                    }).ToList();

                    await _context.DiscountLenses.AddRangeAsyncWithBusy(discountLenses);
                }

                // Add service relationships if not applying to all services (محسنة للإدراج المجمع)
                if (!discount.ApplyToAllServices && serviceIds != null && serviceIds.Any())
                {
                    var discountServices = serviceIds.Select(serviceId => new Models.DiscountService
                    {
                        DiscountId = discount.Id,
                        ServiceId = serviceId
                    }).ToList();

                    await _context.DiscountServices.AddRangeAsyncWithBusy(discountServices);
                }

            result = await _context.SaveWithTransactionAndBusy("UpdateDiscountRelationships");
            return result.State ? (true, "تم تحديث الخصم بنجاح") : (false, result.Message);
        }
        catch (Exception ex)
        {
            _context.Reverse();
            return (false, ex.Message);
        }
    }

    public async Task<(bool State, string Message)> DeleteDiscount(int id)
    {
        try
        {
            var discount = await _context.Discounts
                .Include(d => d.DiscountProducts)
                .Include(d => d.DiscountLenses)
                .Include(d => d.DiscountServices)
                .FirstOrDefaultAsyncWithBusy(d => d.Id == id, "GetDiscountForDelete");

                if (discount == null)
                    return (false, "الخصم غير موجود");

                // Remove relationships (محسنة للحذف المجمع)
                if (discount.DiscountProducts?.Any() == true)
                {
                    _context.DiscountProducts.RemoveRangeWithBusy(discount.DiscountProducts);
                }

                if (discount.DiscountLenses?.Any() == true)
                {
                    _context.DiscountLenses.RemoveRangeWithBusy(discount.DiscountLenses);
                }

                if (discount.DiscountServices?.Any() == true)
                {
                    _context.DiscountServices.RemoveRangeWithBusy(discount.DiscountServices);
                }

                // Remove discount
                _context.Discounts.RemoveWithBusy(discount);

            var result = await _context.SaveWithTransactionAndBusy("DeleteDiscount");
            return result.State ? (true, "تم حذف الخصم بنجاح") : (false, result.Message);
        }
        catch (Exception ex)
        {
            _context.Reverse();
            return (false, ex.Message);
        }
    }

    // Get active discounts for a specific date
    public async Task<List<Discount>> GetActiveDiscounts(DateTime date)
    {
        return await _context.Discounts
            .Include(d => d.DiscountProducts)
                .ThenInclude(dp => dp.Product)
            .Include(d => d.DiscountLenses)
                .ThenInclude(dl => dl.Lens)
            .Include(d => d.DiscountServices)
                .ThenInclude(ds => ds.Service)
            .Where(d => d.StartDate <= date && d.EndDate >= date)
            .ToListAsyncWithBusy("GetActiveDiscounts");
    }

    // التحقق من وجود تخفيض ساري للمنتج
    public async Task<(bool HasDiscount, decimal DiscountPercentage)> CheckProductDiscount(int productId, DateTime date)
    {
        var activeDiscounts = await GetActiveDiscounts(date);

        // البحث عن تخفيض يطبق على جميع المنتجات
        var globalProductDiscount = activeDiscounts.FirstOrDefault(d => d.ApplyToAllProducts);
        if (globalProductDiscount != null)
        {
            return (true, globalProductDiscount.DiscountPercentage);
        }

        // البحث عن تخفيض خاص بهذا المنتج
        var specificProductDiscount = activeDiscounts
            .FirstOrDefault(d => d.DiscountProducts != null &&
                                d.DiscountProducts.Any(dp => dp.ProductId == productId));

        if (specificProductDiscount != null)
        {
            return (true, specificProductDiscount.DiscountPercentage);
        }

        return (false, 0);
    }

    // التحقق من وجود تخفيض ساري للعدسة
    public async Task<(bool HasDiscount, decimal DiscountPercentage)> CheckLensDiscount(int lensId, DateTime date)
    {
        var activeDiscounts = await GetActiveDiscounts(date);

        // البحث عن تخفيض يطبق على جميع العدسات
        var globalLensDiscount = activeDiscounts.FirstOrDefault(d => d.ApplyToAllLenses);
        if (globalLensDiscount != null)
        {
            return (true, globalLensDiscount.DiscountPercentage);
        }

        // البحث عن تخفيض خاص بهذه العدسة
        var specificLensDiscount = activeDiscounts
            .FirstOrDefault(d => d.DiscountLenses != null &&
                                d.DiscountLenses.Any(dl => dl.LensId == lensId));

        if (specificLensDiscount != null)
        {
            return (true, specificLensDiscount.DiscountPercentage);
        }

        return (false, 0);
    }

    // التحقق من وجود تخفيض ساري للخدمة
    public async Task<(bool HasDiscount, decimal DiscountPercentage)> CheckServiceDiscount(int serviceId, DateTime date)
    {
        var activeDiscounts = await GetActiveDiscounts(date);

        // البحث عن تخفيض يطبق على جميع الخدمات
        var globalServiceDiscount = activeDiscounts.FirstOrDefault(d => d.ApplyToAllServices);
        if (globalServiceDiscount != null)
        {
            return (true, globalServiceDiscount.DiscountPercentage);
        }

        // البحث عن تخفيض خاص بهذه الخدمة
        var specificServiceDiscount = activeDiscounts
            .FirstOrDefault(d => d.DiscountServices != null &&
                                d.DiscountServices.Any(ds => ds.ServiceId == serviceId));

        if (specificServiceDiscount != null)
        {
            return (true, specificServiceDiscount.DiscountPercentage);
        }

        return (false, 0);
    }

    // حساب السعر بعد التخفيض
    public decimal CalculateDiscountedPrice(decimal originalPrice, decimal discountPercentage)
    {
        decimal discountAmount = originalPrice * (discountPercentage / 100);
        return originalPrice - discountAmount;
    }

    // Implement IDisposable pattern
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // Dispose managed resources
                _context?.Dispose();
            }

            // Free unmanaged resources
            _disposed = true;
        }
    }

    // Destructor
    ~DiscountManager()
    {
        Dispose(false);
    }
}
