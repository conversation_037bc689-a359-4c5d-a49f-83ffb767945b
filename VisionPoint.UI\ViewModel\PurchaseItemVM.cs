using Microsoft.EntityFrameworkCore;

namespace VisionPoint.UI.ViewModel;

public class PurchaseItemVM
{
    public int Index { get; set; }
    public int Id { get; set; }
    public int PurchaseId { get; set; }
    public int? ProductColorId { get; set; }
    public int? LensPrescriptionColorId { get; set; }
    public string Name { get; set; }
    public string Type { get; set; } // نوع الصنف (منتج أو عدسة)
    [Precision(18, 3)] public decimal Price { get; set; }
    public int Quantity { get; set; }
    public DateOnly? Exp { get; set; }
    public string ColorName { get; set; }
    public string ClientName { get; set; } // اسم العميل
    public string WarehouseName { get; set; } // اسم المخزن
    public decimal TotalPrice => Price * Quantity; // إجمالي السعر
}
