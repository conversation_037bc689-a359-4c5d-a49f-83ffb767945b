﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VisionPoint.UI.Models;

/// <summary>
/// جدول الواصلات
/// </summary>
[Index(nameof(ReceiptNo))]
[Index(nameof(Date))]
[Index(nameof(ClientId))]
[Index(nameof(SaleId))]
[Index(nameof(PurchaseId))]
[Index(nameof(FinancialId))]
[Index(nameof(TreasuryId))]
[Index(nameof(ExpenseId))]
[Index(nameof(IsExchange))]
[Index(nameof(WarehouseId))]
[Index(nameof(SourceTreasuryId))]
[Index(nameof(TargetTreasuryId))]
public class Receipt : BaseEntity
{
    public int ReceiptNo { get; set; } = 0;

    /// <summary>
    /// رقم الواصل مع رمز المخزن (مثل: MAIN-001)
    /// </summary>
    [StringLength(50)]
    public string? ReceiptNumber { get; set; }

    [StringLength(500, ErrorMessage = "البيان يجب ألا يتجاوز 500 حرف")]
    public string? Statement { get; set; }
    public DateTime Date { get; set; }
    /// <summary>
    /// يكون صحيح في حال الصرف وخطأ في حال القبض
    /// </summary>
    public bool? IsExchange { get; set; }
    /// <summary>
    /// القيمة
    /// </summary>

    [Precision(18, 3)] public decimal Value { get; set; }
    /// <summary>
    /// الخزينة المصروف والمقبوض منها واليها
    /// </summary>
    public byte? TreasuryId { get; set; }
    [ForeignKey("TreasuryId")]
    public Treasury? Treasury { get; set; }

    /// <summary>
    /// طريقة الدفع المصدر (للتحويل بين طرق الدفع)
    /// </summary>
    public byte? SourceTreasuryId { get; set; }
    [ForeignKey("SourceTreasuryId")]
    public Treasury? SourceTreasury { get; set; }

    /// <summary>
    /// طريقة الدفع الهدف (للتحويل بين طرق الدفع)
    /// </summary>
    public byte? TargetTreasuryId { get; set; }
    [ForeignKey("TargetTreasuryId")]
    public Treasury? TargetTreasury { get; set; }
    /// <summary>
    /// العميل
    /// </summary>
    public int? ClientId { get; set; }
    public Client? Client { get; set; }
    /// <summary>
    /// رقم فاتورة الشراء
    /// </summary>
    public int? PurchaseId { get; set; }
    public Purchase? Purchase { get; set; }
    /// <summary>
    /// رقم فاتورة البيع
    /// </summary>
    public int? SaleId { get; set; }
    public Sale? Sale { get; set; }
    public int? EmployeeId { get; set; }
    public User? Employee { get; set; }
    public byte? FinancialId { get; set; }
    public Financial? Financial { get; set; }
    /// <summary>
    /// المصروف
    /// </summary>
    public int? ExpenseId { get; set; }
    public Expense? Expense { get; set; }

    /// <summary>
    /// معرف المخزن - يحدد المخزن المرتبط بالواصل
    /// إذا كان null فالواصل عام لجميع المخازن
    /// </summary>
    public int? WarehouseId { get; set; }

    /// <summary>
    /// المخزن المرتبط بالواصل
    /// </summary>
    [ForeignKey("WarehouseId")]
    public Warehouse? Warehouse { get; set; }
}
