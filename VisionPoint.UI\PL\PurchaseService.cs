using Microsoft.EntityFrameworkCore;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Models;
using VisionPoint.UI.Services;
using VisionPoint.UI.ViewModels;
using VisionPoint.UI.ViewModel;

namespace VisionPoint.UI.PL
{
    public class PurchaseService : IDisposable
    {
        private readonly AppDbContext _context = ServiceLocator.GetService<AppDbContext>();
        private bool _disposed = false;

        public PurchaseService()
        {
        }

        public async Task<List<PurchaseViewModel>> GetAllPurchasesAsync()
        {
            var purchases = await _context.Purchases
                .Include(i => i.Client)
                .Include(i => i.Warehouse)
                .Include(i => i.PurchaseItems)
                    .ThenInclude(item => item.ProductColor)
                .Include(i => i.PurchaseItems)
                    .ThenInclude(item => item.LensPrescriptionColor)
                .Include(i => i.Receipts)
                .ToListAsyncWithBusy("GetAllPurchases");

            return purchases.Select((purchase, index) => new PurchaseViewModel
            {
                Index = index + 1,
                Id = purchase.Id,
                InvoiceNo = purchase.InvoiceNo,
                InvoiceNumber = purchase.InvoiceNumber,
                PurchaseDate = purchase.PurchaseDate,
                ClientName = purchase.Client.Name ?? "غير معروف",
                WarehouseName = purchase.Warehouse?.Name ?? "غير محدد",
                TotalAmount = purchase.TotalAmount,
                PaidAmount = purchase.Receipts.Sum(r => r.Value),
                RemainingAmount = purchase.TotalAmount - purchase.Receipts.Sum(r => r.Value)
            }).ToList();
        }

        /// <summary>
        /// الحصول على جميع فواتير المشتريات لمخزن معين
        /// </summary>
        /// <param name="warehouseId">معرف المخزن</param>
        /// <returns>قائمة فواتير المشتريات</returns>
        public async Task<List<PurchaseViewModel>> GetPurchasesByWarehouseAsync(int warehouseId)
        {
            var purchases = await _context.Purchases
                .Where(p => p.WarehouseId == warehouseId)
                .Include(i => i.Client)
                .Include(i => i.Warehouse)
                .Include(i => i.PurchaseItems)
                    .ThenInclude(item => item.ProductColor)
                .Include(i => i.PurchaseItems)
                    .ThenInclude(item => item.LensPrescriptionColor)
                .Include(i => i.Receipts)
                .ToListAsyncWithBusy("GetPurchasesByWarehouse");

            return purchases.Select((purchase, index) => new PurchaseViewModel
            {
                Index = index + 1,
                Id = purchase.Id,
                InvoiceNo = purchase.InvoiceNo,
                InvoiceNumber = purchase.InvoiceNumber,
                PurchaseDate = purchase.PurchaseDate,
                ClientName = purchase.Client.Name ?? "غير معروف",
                WarehouseName = purchase.Warehouse?.Name ?? "غير محدد",
                TotalAmount = purchase.TotalAmount,
                PaidAmount = purchase.Receipts.Sum(r => r.Value),
                RemainingAmount = purchase.TotalAmount - purchase.Receipts.Sum(r => r.Value)
            }).ToList();
        }
        public async Task<PurchaseViewModel?> GetPurchaseByInvoiceNoAsync(int invoiceNo)
        {
            var purchase = await _context.Purchases
                .Include(i => i.Client)
                .Include(i => i.Warehouse)
                .Include(i => i.PurchaseItems)
                    .ThenInclude(item => item.ProductColor)
                    .ThenInclude(item => item.Color)
                .Include(i => i.PurchaseItems)
                    .ThenInclude(item => item.ProductColor)
                    .ThenInclude(item => item.Product)
                .Include(i => i.PurchaseItems)
                    .ThenInclude(item => item.LensPrescriptionColor)
                    .ThenInclude(item => item.Color)
                .Include(i => i.PurchaseItems)
                    .ThenInclude(item => item.LensPrescriptionColor)
                    .ThenInclude(item => item.LensPrescription)
                    .ThenInclude(item => item.Lens)
                .Include(i => i.Receipts)
                .FirstOrDefaultAsyncWithBusy(i => i.InvoiceNo == invoiceNo, "GetPurchaseDetailsByInvoiceNo");

            if (purchase == null)
                return null;

            return new PurchaseViewModel
            {
                Id = purchase.Id,
                InvoiceNo = purchase.InvoiceNo,
                InvoiceNumber = purchase.InvoiceNumber,
                PurchaseDate = purchase.PurchaseDate,
                ClientName = purchase.Client.Name ?? "غير معروف",
                WarehouseName = purchase.Warehouse?.Name ?? "غير محدد",
                TotalAmount = purchase.TotalAmount,
                PaidAmount = purchase.Receipts.Sum(r => r.Value),
                RemainingAmount = purchase.TotalAmount - purchase.Receipts.Sum(r => r.Value),
                Purchase = purchase,
                PurchaseItems = purchase.PurchaseItems.Select(item => new PurchaseItemVM
                {
                    Id = item.Id,
                    ProductColorId = item.ProductColorId,
                    LensPrescriptionColorId = item.LensPrescriptionColorId,
                    Quantity = item.Quantity,
                    Price = item.Price,
                    ColorName = item.ProductColor != null ? item.ProductColor.Color==null?"لايوجد": item.ProductColor.Color.Name : item.LensPrescriptionColor != null ? item.LensPrescriptionColor.Color.Name : string.Empty,
                    Exp = item.Exp,
                    Name = item.ProductColor != null ? item.ProductColor.Product.Name : item.LensPrescriptionColor != null ? item.LensPrescriptionColor.LensPrescription.Lens.Name : string.Empty,
                    PurchaseId = item.PurchaseId
                }).ToList()
            };
        }

        public async Task<PurchaseViewModel?> GetPurchaseByIdAsync(int id)
        {
            var purchase = await _context.Purchases
                .Include(i => i.Client)
                .Include(i => i.Warehouse)
                .Include(i => i.PurchaseItems)
                    .ThenInclude(item => item.ProductColor)
                    .ThenInclude(item => item.Color)
                .Include(i => i.PurchaseItems)
                    .ThenInclude(item => item.ProductColor)
                    .ThenInclude(item => item.Product)
                .Include(i => i.PurchaseItems)
                    .ThenInclude(item => item.LensPrescriptionColor)
                    .ThenInclude(item => item.Color)
                .Include(i => i.PurchaseItems)
                    .ThenInclude(item => item.LensPrescriptionColor)
                    .ThenInclude(item => item.LensPrescription)
                    .ThenInclude(item => item.Lens)
                .Include(i => i.Receipts)
                .FirstOrDefaultAsyncWithBusy(i => i.Id == id, "GetPurchaseDetails");

            if (purchase == null)
                return null;

            return new PurchaseViewModel
            {
                Id = purchase.Id,
                InvoiceNo = purchase.InvoiceNo,
                InvoiceNumber = purchase.InvoiceNumber,
                PurchaseDate = purchase.PurchaseDate,
                ClientName = purchase.Client.Name ?? "غير معروف",
                WarehouseName = purchase.Warehouse?.Name ?? "غير محدد",
                TotalAmount = purchase.TotalAmount,
                PaidAmount = purchase.Receipts.Sum(r => r.Value),
                RemainingAmount = purchase.TotalAmount - purchase.Receipts.Sum(r => r.Value),
                Purchase = purchase,
                PurchaseItems = purchase.PurchaseItems.Select(item => new PurchaseItemVM
                {
                    Id = item.Id,
                    ProductColorId = item.ProductColorId,
                    LensPrescriptionColorId = item.LensPrescriptionColorId,
                    Quantity = item.Quantity,
                    Price = item.Price,
                    ColorName = item.ProductColor != null ? item.ProductColor.Color==null?"لايوجد": item.ProductColor.Color.Name : item.LensPrescriptionColor != null ? item.LensPrescriptionColor.Color.Name : string.Empty,
                    Exp = item.Exp,
                    Name = item.ProductColor != null ? item.ProductColor.Product.Name : item.LensPrescriptionColor != null ? item.LensPrescriptionColor.LensPrescription.Lens.Name : string.Empty,
                    PurchaseId = item.PurchaseId
                }).ToList()
            };
        }
        public async Task<(bool Success, string Message)> DeletePurchaseAsync(int id)
        {
            try
            {
                var purchase = await _context.Purchases
                    .Include(p => p.PurchaseItems)
                    .FirstOrDefaultAsyncWithBusy(p => p.Id == id, "GetPurchaseForDelete");

                if (purchase == null)
                    return (false, "الفاتورة غير موجودة");

                // التحقق من وجود إيصالات مرتبطة بفاتورة الشراء
                bool hasReceipts = await _context.Receipts.AnyAsyncWithBusy(r => r.PurchaseId == id, "CheckPurchaseReceipts");

                if (hasReceipts)
                {
                    return (false, "لا يمكن حذف الفاتورة لأنها مرتبطة بإيصالات");
                }

                // التحقق من إمكانية تحديث رصيد العميل قبل الحذف
                var clientService = new ClientService();
                var (isAllowed, message) = await clientService.CheckBalanceChangeAsync(purchase.ClientId, purchase.TotalAmount);
                if (!isAllowed)
                {
                    return (false, $"لا يمكن حذف الفاتورة: {message}");
                }

                // Update client balance (reverse the purchase)
                var client = await _context.Clients.FindAsyncWithBusy(purchase.ClientId);
                if (client != null)
                    client.Balance += purchase.TotalAmount;

                // إعادة الكميات إلى المخزون (محسنة للتحديث المجمع)
                // إنشاء عناصر بكميات سالبة لإعادة الكميات
                var reversedItems = purchase.PurchaseItems.Select(item => new PurchaseItem
                {
                    ProductColorId = item.ProductColorId,
                    LensPrescriptionColorId = item.LensPrescriptionColorId,
                    Quantity = -item.Quantity, // كمية سالبة لإعادة الكمية
                    Exp = item.Exp,
                    Price = item.Price
                }).ToList();

                var (Success, Message) = await UpdateInventoryBatch(reversedItems, purchase.WarehouseId);
                if (!Success)
                {
                    _context.Reverse();
                    return (false, Message);
                }

                // حذف الفاتورة
                _context.Purchases.RemoveWithBusy(purchase);
                var result = await _context.SaveWithTransactionAndBusy("DeletePurchase");

                return result.State ? (true, "تم حذف الفاتورة بنجاح") : (false, result.Message);
            }
            catch (Exception ex)
            {
                _context.Reverse();
                return (false, $"حدث خطأ أثناء حذف الفاتورة: {ex.Message}");
            }
        }
        public async Task<(bool Success, string Message, int PurchaseId, int InvoiceNo)> EditPurchaseAsync(Purchase purchase)
        {
            try
            {
                if (purchase.Id == 0)
                {
                    // New purchase - this should not happen in EditPurchaseAsync, but handle it anyway
                    return (false, "لا يمكن تعديل فاتورة غير موجودة", 0, 0);
                }
                else
                {
                    // Updating existing purchase
                    var existingPurchase = await _context.Purchases
                        .Include(p => p.PurchaseItems)
                        .FirstOrDefaultAsyncWithBusy(p => p.Id == purchase.Id, "GetPurchaseForEdit");

                        if (existingPurchase == null)
                            return (false, "الفاتورة غير موجودة", 0, 0);

                        // التحقق من تغيير رصيد العميل إذا تغير المبلغ الإجمالي أو العميل
                        decimal balanceChange = 0;
                        if (existingPurchase.TotalAmount != purchase.TotalAmount || existingPurchase.ClientId != purchase.ClientId)
                        {
                            // حساب التغيير في الرصيد
                            if (existingPurchase.ClientId == purchase.ClientId)
                            {
                                // نفس العميل، تغيير في المبلغ فقط
                                balanceChange = existingPurchase.TotalAmount - purchase.TotalAmount; // عكس المبيعات
                            }
                            else
                            {
                                // تغيير العميل، نحتاج للتحقق من كلا العميلين
                                // أولاً: إضافة المبلغ للعميل القديم (إلغاء المشتريات)
                                var clientService = new ClientService();
                                var (isAllowedOld, messageOld) = await clientService.CheckBalanceChangeAsync(existingPurchase.ClientId, existingPurchase.TotalAmount);
                                if (!isAllowedOld)
                                {
                                    return (false, $"خطأ في العميل القديم: {messageOld}", 0, 0);
                                }

                                // ثانياً: خصم المبلغ من العميل الجديد
                                var (isAllowedNew, messageNew) = await clientService.CheckBalanceChangeAsync(purchase.ClientId, -purchase.TotalAmount);
                                if (!isAllowedNew)
                                {
                                    return (false, $"خطأ في العميل الجديد: {messageNew}", 0, 0);
                                }
                            }

                            // إذا كان نفس العميل، التحقق من التغيير في المبلغ
                            if (existingPurchase.ClientId == purchase.ClientId && balanceChange != 0)
                            {
                                var clientService = new ClientService();
                                var (isAllowed, message) = await clientService.CheckBalanceChangeAsync(purchase.ClientId, balanceChange);
                                if (!isAllowed)
                                {
                                    return (false, message, 0, 0);
                                }
                            }
                        }

                        // Check if warehouse changed
                        bool warehouseChanged = existingPurchase.WarehouseId != purchase.WarehouseId;
                        int oldWarehouseId = existingPurchase.WarehouseId;

                        // If warehouse changed, generate new invoice number for the new warehouse
                        if (warehouseChanged)
                        {
                            var lastInvoice = await _context.Purchases
                                .Where(p => p.WarehouseId == purchase.WarehouseId && p.Id != existingPurchase.Id)
                                .OrderByDescending(p => p.InvoiceNo)
                                .FirstOrDefaultAsyncWithBusy("GetLastPurchaseInvoiceNumberByWarehouseForEdit");

                            int nextInvoiceNumber = (lastInvoice?.InvoiceNo ?? 0) + 1;
                            existingPurchase.InvoiceNo = nextInvoiceNumber;

                            // الحصول على رمز المخزن الجديد وإنشاء رقم الفاتورة مع الرمز
                            var warehouse = await _context.Warehouses
                                .FirstOrDefaultAsyncWithBusy(w => w.Id == purchase.WarehouseId, "GetWarehouseForPurchaseEdit");
                            string warehouseCode = warehouse?.Code ?? "UNK";
                            existingPurchase.InvoiceNumber = $"{warehouseCode}-{nextInvoiceNumber}";
                        }

                        // Update existing purchase properties
                        existingPurchase.ClientId = purchase.ClientId;
                        existingPurchase.PurchaseDate = purchase.PurchaseDate;
                        existingPurchase.TotalAmount = purchase.TotalAmount;
                        existingPurchase.Notes = purchase.Notes;
                        existingPurchase.WarehouseId = purchase.WarehouseId;
                        existingPurchase.ModifiedById = CurrentUser.Id;
                        existingPurchase.UpdatedAt = DateTime.Now;

                        // Handle purchase items
                        var existingItems = existingPurchase.PurchaseItems.ToList();
                        var newItems = purchase.PurchaseItems.ToList();

                        // If warehouse changed, we need to handle all items differently
                        if (warehouseChanged)
                        {
                            // Remove all existing items from old warehouse
                            foreach (var item in existingItems)
                            {
                                var (removeSuccess, removeMessage) = await UpdateInventory(item, -item.Quantity, oldWarehouseId);
                                if (!removeSuccess)
                                {
                                    _context.Reverse();
                                    return (false, removeMessage, 0, 0);
                                }
                            }

                            // Remove all existing items from database
                            foreach (var item in existingItems)
                            {
                                _context.PurchaseItems.RemoveWithBusy(item);
                            }

                            // Add all new items to new warehouse
                            foreach (var item in newItems)
                            {
                                item.PurchaseId = existingPurchase.Id;
                                item.CreatedById = CurrentUser.Id;
                                item.ModifiedById = CurrentUser.Id;
                                item.CreatedAt = DateTime.Now;
                                item.UpdatedAt = DateTime.Now;
                                await _context.PurchaseItems.AddAsyncWithBusy(item);
                                var (addSuccess, addMessage) = await UpdateInventory(item, item.Quantity, purchase.WarehouseId);
                                if (!addSuccess)
                                {
                                    _context.Reverse();
                                    return (false, addMessage, 0, 0);
                                }
                            }
                        }
                        else
                        {
                            // Normal handling when warehouse didn't change
                            // Find items to remove (in existing but not in new)
                            var itemsToRemove = existingItems
                                .Where(ei => !newItems.Any(ni =>
                                    ni.ProductColorId == ei.ProductColorId &&
                                    ni.LensPrescriptionColorId == ei.LensPrescriptionColorId))
                                .ToList();

                            // Find items to add (in new but not in existing)
                            var itemsToAdd = newItems
                                .Where(ni => !existingItems.Any(ei =>
                                    ni.ProductColorId == ei.ProductColorId &&
                                    ni.LensPrescriptionColorId == ei.LensPrescriptionColorId))
                                .ToList();

                            // Find items to update (in both)
                            var itemsToUpdate = existingItems
                                .Where(ei => newItems.Any(ni =>
                                    ni.ProductColorId == ei.ProductColorId &&
                                    ni.LensPrescriptionColorId == ei.LensPrescriptionColorId))
                                .ToList();

                            // Remove items
                            foreach (var item in itemsToRemove)
                            {
                                var (removeItemSuccess, removeItemMessage) = await UpdateInventory(item, -item.Quantity, existingPurchase.WarehouseId);
                                if (!removeItemSuccess)
                                {
                                    _context.Reverse();
                                    return (false, removeItemMessage, 0, 0);
                                }
                                _context.PurchaseItems.RemoveWithBusy(item);
                            }

                            // Add new items
                            foreach (var item in itemsToAdd)
                            {
                                item.PurchaseId = existingPurchase.Id;
                                item.CreatedById = CurrentUser.Id;
                                item.ModifiedById = CurrentUser.Id;
                                item.CreatedAt = DateTime.Now;
                                item.UpdatedAt = DateTime.Now;
                                await _context.PurchaseItems.AddAsyncWithBusy(item);
                                var (newItemSuccess, newItemMessage) = await UpdateInventory(item, item.Quantity, existingPurchase.WarehouseId);
                                if (!newItemSuccess)
                                {
                                    _context.Reverse();
                                    return (false, newItemMessage, 0, 0);
                                }
                            }

                            // Update existing items
                            foreach (var existingItem in itemsToUpdate)
                            {
                                var newItem = newItems.First(ni =>
                                    ni.ProductColorId == existingItem.ProductColorId &&
                                    ni.LensPrescriptionColorId == existingItem.LensPrescriptionColorId);

                                // Update inventory based on quantity difference
                                int quantityDiff = newItem.Quantity - existingItem.Quantity;
                                if (quantityDiff != 0)
                                {
                                    var (updateSuccess, updateMessage) = await UpdateInventory(existingItem, quantityDiff, existingPurchase.WarehouseId);
                                    if (!updateSuccess)
                                    {
                                        _context.Reverse();
                                        return (false, updateMessage, 0, 0);
                                    }
                                }

                                // Update item properties
                                existingItem.Quantity = newItem.Quantity;
                                existingItem.Price = newItem.Price;
                                existingItem.Exp = newItem.Exp;
                                existingItem.ModifiedById = CurrentUser.Id;
                                existingItem.UpdatedAt = DateTime.Now;
                            }
                        }

                        var result = await _context.SaveWithTransactionAndBusy("UpdatePurchase");
                        return result.State ?
                            (true, "تم تحديث الفاتورة بنجاح", existingPurchase.Id, existingPurchase.InvoiceNo) :
                            (false, result.Message, existingPurchase.Id, existingPurchase.InvoiceNo);
                    }

                    // For new purchases (محسنة لتحديث المخزون المجمع)
                    // تحديث خصائص العناصر
                    foreach (var item in purchase.PurchaseItems)
                    {
                        item.CreatedById = CurrentUser.Id;
                        item.ModifiedById = CurrentUser.Id;
                        item.CreatedAt = DateTime.Now;
                        item.UpdatedAt = DateTime.Now;
                    }

                    // تحديث المخزون بشكل مجمع
                    var (Success, Message) = await UpdateInventoryBatch(purchase.PurchaseItems, purchase.WarehouseId);
                    if (!Success)
                    {
                        _context.Reverse();
                        return (false, Message, 0, 0);
                    }

                var saveResult = await _context.SaveWithTransactionAndBusy("AddPurchase");
                return saveResult.State ?
                    (true, "تم حفظ الفاتورة بنجاح", purchase.Id, purchase.InvoiceNo) :
                    (false, saveResult.Message, purchase.Id, purchase.InvoiceNo);
            }
            catch (Exception ex)
            {
                _context.Reverse();
                return (false, $"حدث خطأ أثناء حفظ الفاتورة: {ex.Message}", 0, 0);
            }
        }

        public async Task<(bool Success, string Message, int PurchaseId,int invoiceNo)> AddPurchaseAsync(Purchase purchase, decimal? paid, byte? treasuryId)
        {
            try
            {
                // Update client balance - المتجر مدين للعميل (نقص رصيد العميل)
                var client = await _context.Clients.FindAsyncWithBusy(purchase.ClientId);
                if (client != null)
                {
                    // حساب التغيير النهائي في الرصيد (سالب إجمالي الفاتورة + المبلغ المدفوع)
                    decimal finalBalanceChange = -purchase.TotalAmount + (paid ?? 0);

                    // التحقق من الحد المسموح قبل تحديث الرصيد
                    var clientService = new ClientService();
                    var (isAllowed, message) = await clientService.CheckBalanceChangeAsync(purchase.ClientId, finalBalanceChange);
                    if (!isAllowed)
                    {
                        return (false, message, 0, 0);
                    }

                    client.Balance -= purchase.TotalAmount;
                }

                // Generate invoice number for specific warehouse
                var lastInvoice = await _context.Purchases
                    .Where(p => p.WarehouseId == purchase.WarehouseId)
                    .OrderByDescending(p => p.InvoiceNo)
                    .FirstOrDefaultAsyncWithBusy("GetLastPurchaseInvoiceNumberByWarehouse");

                int nextInvoiceNumber = (lastInvoice?.InvoiceNo ?? 0) + 1;
                purchase.InvoiceNo = nextInvoiceNumber;

                // الحصول على رمز المخزن وإنشاء رقم الفاتورة مع الرمز
                var warehouse = await _context.Warehouses
                    .FirstOrDefaultAsyncWithBusy(w => w.Id == purchase.WarehouseId, "GetWarehouseForPurchaseAdd");
                string warehouseCode = warehouse?.Code ?? "UNK";
                purchase.InvoiceNumber = $"{warehouseCode}-{nextInvoiceNumber}";

                        // New purchase
                        purchase.CreatedById = CurrentUser.Id;
                        purchase.ModifiedById = CurrentUser.Id;
                        purchase.CreatedAt = DateTime.Now;
                        purchase.UpdatedAt = DateTime.Now;
                        await _context.Purchases.AddAsyncWithBusy(purchase);

                        // For new purchases
                        foreach (var item in purchase.PurchaseItems)
                        {
                            item.CreatedById = CurrentUser.Id;
                            item.ModifiedById = CurrentUser.Id;
                            item.CreatedAt = DateTime.Now;
                            item.UpdatedAt = DateTime.Now;
                            var (Success, Message) = await UpdateInventory(item, item.Quantity, purchase.WarehouseId);
                            if (!Success)
                            {
                                _context.Reverse();
                                return (false, Message, 0, 0);
                            }
                        }

                        var result = await _context.SaveWithTransactionAndBusy("AddPurchase");

                        if (!result.State)
                            return (false, result.Message, 0, 0);

                        // Create receipt for payment if treasury is selected and amount is paid
                        if (treasuryId.HasValue && paid.HasValue && paid.Value > 0)
                        {
                            ReceiptService receiptService = new ReceiptService();
                            var receipt = new Receipt
                            {
                                PurchaseId = purchase.Id,
                                Value = paid.Value,
                                ClientId = purchase.ClientId,
                                TreasuryId = treasuryId.Value,
                                Date = DateTime.Now,
                                IsExchange = true,
                                Statement = "فاتورة مشتريات رقم "+purchase.InvoiceNo ,
                                FinancialId = (byte)FinancialId.Purchase,
                            };
                            await receiptService.CreateReceipt(receipt);
                        }

                return (true, "تم حفظ الفاتورة بنجاح", purchase.Id, purchase.InvoiceNo);
            }
            catch (Exception ex)
            {
                _context.Reverse();
                return (false, $"حدث خطأ أثناء حفظ الفاتورة: {ex.Message}", 0, 0);
            }
        }

        private async Task<(bool Success, string Message)> UpdateInventory(PurchaseItem item, int quantityChange, int warehouseId)
        {
            // التحقق مما إذا كان مسموح بالكميات السالبة
            bool allowNegativeQuantities = Properties.Settings.Default.AllowNegativeQuantities;

            if (item.ProductColorId.HasValue)
            {
                // Check if we need to track by expiration date
                if (item.Exp.HasValue)
                {
                    // Find product quantity with matching expiration date and warehouse
                    var productQuantity = await _context.ProductQuantities
                        .FirstOrDefaultAsyncWithBusy(pq => pq.ProductColorId == item.ProductColorId && pq.Exp == item.Exp && pq.WarehouseId == warehouseId, "FindProductQuantityWithExp");

                        if (productQuantity == null)
                        {
                            productQuantity = new ProductQuantity
                            {
                                ProductColorId = item.ProductColorId.Value,
                                WarehouseId = warehouseId,
                                Quantity = 0,
                                Exp = item.Exp
                            };
                            await _context.ProductQuantities.AddAsyncWithBusy(productQuantity);
                        }

                        // التحقق من أن الكمية لن تصبح سالبة إذا كان غير مسموح بذلك
                        if (!allowNegativeQuantities && (productQuantity.Quantity + quantityChange) < 0)
                        {
                            return (false, $"لا يمكن إتمام العملية لأن الكمية ستصبح بالسالب ({productQuantity.Quantity + quantityChange})، والنظام لا يسمح بالكميات السالبة");
                        }

                        // Only update cost price when adding inventory (positive quantity change)
                        if (quantityChange > 0)
                        {
                            var productColor = await _context.ProductColors
                                .Include(pc => pc.Product)
                                .FirstOrDefaultAsyncWithBusy(pc => pc.Id == item.ProductColorId, "GetProductColorForCostUpdate");

                            if (productColor?.Product != null)
                            {
                                // Calculate new average cost price
                                decimal currentStockValue = productQuantity.Quantity * productColor.Product.CostPrice;
                                decimal newStockValue = quantityChange * item.Price;
                                int totalQuantity = productQuantity.Quantity + quantityChange;

                                decimal newAverageCost = (currentStockValue + newStockValue) / totalQuantity;
                                productColor.Product.CostPrice = newAverageCost;
                            }
                        }

                        productQuantity.Quantity += quantityChange;
                    }
                    else
                    {
                        // Standard handling without expiration date
                        var productQuantity = await _context.ProductQuantities
                            .FirstOrDefaultAsyncWithBusy(pq => pq.ProductColorId == item.ProductColorId && !pq.Exp.HasValue && pq.WarehouseId == warehouseId, "FindProductQuantityWithoutExp");

                        if (productQuantity == null)
                        {
                            productQuantity = new ProductQuantity
                            {
                                ProductColorId = item.ProductColorId.Value,
                                WarehouseId = warehouseId,
                                Quantity = 0,
                                Exp = null
                            };
                            await _context.ProductQuantities.AddAsyncWithBusy(productQuantity);
                        }

                        // التحقق من أن الكمية لن تصبح سالبة إذا كان غير مسموح بذلك
                        if (!allowNegativeQuantities && (productQuantity.Quantity + quantityChange) < 0)
                        {
                            return (false, $"لا يمكن إتمام العملية لأن الكمية ستصبح بالسالب ({productQuantity.Quantity + quantityChange})، والنظام لا يسمح بالكميات السالبة");
                        }

                        // Only update cost price when adding inventory (positive quantity change)
                        if (quantityChange > 0)
                        {
                            var productColor = await _context.ProductColors
                                .Include(pc => pc.Product)
                                .FirstOrDefaultAsyncWithBusy(pc => pc.Id == item.ProductColorId, "GetProductColorForCostUpdate");

                            if (productColor?.Product != null)
                            {
                                // Calculate new average cost price
                                decimal currentStockValue = productQuantity.Quantity * productColor.Product.CostPrice;
                                decimal newStockValue = quantityChange * item.Price;
                                int totalQuantity = productQuantity.Quantity + quantityChange;

                                decimal newAverageCost = (currentStockValue + newStockValue) / totalQuantity;
                                productColor.Product.CostPrice = newAverageCost;
                            }
                        }

                        productQuantity.Quantity += quantityChange;
                    }
                }
                else if (item.LensPrescriptionColorId.HasValue)
                {
                    // Check if we need to track by expiration date
                    if (item.Exp.HasValue)
                    {
                        // Find lens quantity with matching expiration date and warehouse
                        var lensQuantity = await _context.LensQuantities
                            .FirstOrDefaultAsyncWithBusy(lq => lq.LensPrescriptionColorId == item.LensPrescriptionColorId && lq.Exp == item.Exp && lq.WarehouseId == warehouseId, "FindLensQuantityWithExp");

                        if (lensQuantity == null)
                        {
                            lensQuantity = new LensQuantity
                            {
                                LensPrescriptionColorId = item.LensPrescriptionColorId.Value,
                                WarehouseId = warehouseId,
                                Quantity = 0,
                                Exp = item.Exp
                            };
                            await _context.LensQuantities.AddAsyncWithBusy(lensQuantity);
                        }

                        // التحقق من أن الكمية لن تصبح سالبة إذا كان غير مسموح بذلك
                        if (!allowNegativeQuantities && (lensQuantity.Quantity + quantityChange) < 0)
                        {
                            return (false, $"لا يمكن إتمام العملية لأن كمية العدسة ستصبح بالسالب ({lensQuantity.Quantity + quantityChange})، والنظام لا يسمح بالكميات السالبة");
                        }

                        // Only update cost price when adding inventory (positive quantity change)
                        if (quantityChange > 0)
                        {
                            var lensPrescriptionColor = await _context.LensPrescriptionColors
                                .Include(lpc => lpc.LensPrescription)
                                .FirstOrDefaultAsyncWithBusy(lpc => lpc.Id == item.LensPrescriptionColorId, "GetLensPrescriptionColorForCostUpdate");

                            if (lensPrescriptionColor?.LensPrescription != null)
                            {
                                // Calculate new average cost price
                                decimal currentStockValue = lensQuantity.Quantity * lensPrescriptionColor.LensPrescription.CostPrice;
                                decimal newStockValue = quantityChange * item.Price;
                                int totalQuantity = lensQuantity.Quantity + quantityChange;

                                decimal newAverageCost = (currentStockValue + newStockValue) / totalQuantity;
                                lensPrescriptionColor.LensPrescription.CostPrice = newAverageCost;
                            }
                        }

                        lensQuantity.Quantity += quantityChange;
                    }
                    else
                    {
                        // Standard handling without expiration date
                        var lensQuantity = await _context.LensQuantities
                            .FirstOrDefaultAsyncWithBusy(lq => lq.LensPrescriptionColorId == item.LensPrescriptionColorId && !lq.Exp.HasValue && lq.WarehouseId == warehouseId, "FindLensQuantityWithoutExp");

                        if (lensQuantity == null)
                        {
                            lensQuantity = new LensQuantity
                            {
                                LensPrescriptionColorId = item.LensPrescriptionColorId.Value,
                                WarehouseId = warehouseId,
                                Quantity = 0,
                                Exp = null
                            };
                            await _context.LensQuantities.AddAsyncWithBusy(lensQuantity);
                        }

                        // التحقق من أن الكمية لن تصبح سالبة إذا كان غير مسموح بذلك
                        if (!allowNegativeQuantities && (lensQuantity.Quantity + quantityChange) < 0)
                        {
                            return (false, $"لا يمكن إتمام العملية لأن كمية العدسة ستصبح بالسالب ({lensQuantity.Quantity + quantityChange})، والنظام لا يسمح بالكميات السالبة");
                        }

                        // Only update cost price when adding inventory (positive quantity change)
                        if (quantityChange > 0)
                        {
                            var lensPrescriptionColor = await _context.LensPrescriptionColors
                                .Include(lpc => lpc.LensPrescription)
                                .FirstOrDefaultAsyncWithBusy(lpc => lpc.Id == item.LensPrescriptionColorId, "GetLensPrescriptionColorForCostUpdate");

                            if (lensPrescriptionColor?.LensPrescription != null)
                            {
                                // Calculate new average cost price
                                decimal currentStockValue = lensQuantity.Quantity * lensPrescriptionColor.LensPrescription.CostPrice;
                                decimal newStockValue = quantityChange * item.Price;
                                int totalQuantity = lensQuantity.Quantity + quantityChange;

                                decimal newAverageCost = (currentStockValue + newStockValue) / totalQuantity;
                                lensPrescriptionColor.LensPrescription.CostPrice = newAverageCost;
                            }
                        }

                        lensQuantity.Quantity += quantityChange;
                    }
            }
            return (true, "تم تحديث المخزون بنجاح");
        }
        public async Task<int?> GetClientByPurchaseId(int purchaseId)
        {
            var purchase = await _context.Purchases
                .FirstOrDefaultAsyncWithBusy(s => s.Id == purchaseId, "GetClientByPurchaseId");
            if (purchase != null)
            {
                return purchase.ClientId;
            }
            return null;
        }

        /// <summary>
        /// جلب فواتير المشتريات التي بها بواقي للعميل المحدد
        /// يستخدم استعلام محسن لجلب البيانات المطلوبة فقط
        /// </summary>
        public async Task<List<PendingInvoiceViewModel>> GetPendingPurchasesByClientIdAsync(int clientId)
        {
            // جلب فواتير المشتريات للعميل مع المبالغ المدفوعة
            var purchasesWithPayments = await (from purchase in _context.Purchases
                                              where purchase.ClientId == clientId
                                              select new
                                              {
                                                  purchase.Id,
                                                  purchase.InvoiceNo,
                                                  purchase.TotalAmount,
                                                  purchase.PurchaseDate,
                                                  Receipts = _context.Receipts.Where(r => r.PurchaseId == purchase.Id).ToList()
                                              }).ToListAsyncWithBusy("GetPurchasesWithReceipts");

            // معالجة البيانات في الذاكرة لتجنب مشكلة aggregate functions
            var pendingInvoices = purchasesWithPayments
                .Select(p => new
                {
                    p.Id,
                    p.InvoiceNo,
                    p.TotalAmount,
                    p.PurchaseDate,
                    PaidAmount = p.Receipts.Sum(r => r.Value)
                })
                .Where(p => p.TotalAmount > p.PaidAmount) // فلترة الفواتير التي بها بواقي
                .OrderBy(p => p.PurchaseDate) // ترتيب حسب التاريخ (الأقدم أولاً)
                .Select(p => new PendingInvoiceViewModel
                {
                    InvoiceId = p.Id,
                    InvoiceNo = p.InvoiceNo,
                    TotalAmount = p.TotalAmount,
                    PaidAmount = p.PaidAmount,
                    RemainingAmount = p.TotalAmount - p.PaidAmount,
                    Date = p.PurchaseDate,
                    IsSale = false
                })
                .ToList();

            return pendingInvoices;
        }

        /// <summary>
        /// التحقق من وجود فواتير مشتريات متبقية للعميل
        /// </summary>
        public async Task<bool> HasPendingPurchasesForClientAsync(int clientId)
        {
            // جلب فواتير العميل مع الإيصالات
            var purchasesWithReceipts = await _context.Purchases
                .Where(p => p.ClientId == clientId)
                .Include(p => p.Receipts)
                .ToListAsyncWithBusy("GetPurchasesForPendingCheck");

            // التحقق في الذاكرة لتجنب مشكلة aggregate functions
            return purchasesWithReceipts.Any(p => p.TotalAmount > (p.Receipts?.Sum(r => r.Value) ?? 0));
        }

        /// <summary>
        /// حساب إجمالي المبلغ المتبقي لفواتير المشتريات للعميل
        /// </summary>
        public async Task<decimal> GetTotalRemainingPurchasesAmountForClientAsync(int clientId)
        {
            // جلب فواتير العميل مع الإيصالات
            var purchasesWithReceipts = await _context.Purchases
                .Where(p => p.ClientId == clientId)
                .Include(p => p.Receipts)
                .ToListAsyncWithBusy("GetPurchasesForTotalRemaining");

            // حساب المبلغ المتبقي في الذاكرة لتجنب مشكلة aggregate functions
            return purchasesWithReceipts
                .Where(p => p.TotalAmount > (p.Receipts?.Sum(r => r.Value) ?? 0))
                .Sum(p => p.TotalAmount - (p.Receipts?.Sum(r => r.Value) ?? 0));
        }

        /// <summary>
        /// الحصول على فواتير المشتريات مع تطبيق عوامل التصفية
        /// </summary>
        /// <param name="fromDate">تاريخ البداية (اختياري)</param>
        /// <param name="toDate">تاريخ النهاية (اختياري)</param>
        /// <param name="clientId">معرف العميل (اختياري)</param>
        /// <param name="hasRemaining">فواتير بها رصيد متبقي (اختياري)</param>
        /// <param name="invoiceId">رقم الفاتورة (اختياري)</param>
        /// <param name="warehouseId">معرف المخزن (اختياري)</param>
        /// <returns>قائمة بفواتير المشتريات المصفاة</returns>
        public async Task<List<PurchaseViewModel>> GetFilteredPurchasesAsync(
            DateTime? fromDate = null,
            DateTime? toDate = null,
            int? clientId = null,
            bool? hasRemaining = null,
            int? invoiceId = null,
            int? warehouseId = null)
        {
            // بناء الاستعلام الأساسي
            var query = _context.Purchases
                .Include(p => p.Client)
                .Include(p => p.Warehouse)
                .Include(p => p.PurchaseItems)
                .Include(p => p.Receipts)
                .AsQueryable();

                // تطبيق عوامل التصفية
                if (fromDate.HasValue)
                {
                    // تحويل التاريخ إلى بداية اليوم
                    var startDate = fromDate.Value.Date;
                    query = query.Where(p => p.PurchaseDate >= startDate);
                }

                if (toDate.HasValue)
                {
                    // تحويل التاريخ إلى نهاية اليوم
                    var endDate = toDate.Value.Date.AddDays(1).AddTicks(-1);
                    query = query.Where(p => p.PurchaseDate <= endDate);
                }

                if (clientId.HasValue)
                {
                    query = query.Where(p => p.ClientId == clientId.Value);
                }

                if (invoiceId.HasValue)
                {
                    query = query.Where(p => p.InvoiceNo == invoiceId.Value);
                }

                if (warehouseId.HasValue)
                {
                    query = query.Where(p => p.WarehouseId == warehouseId.Value);
                }

                // تنفيذ الاستعلام وتحويل النتائج إلى قائمة
                var purchases = await query.ToListAsyncWithBusy("GetFilteredPurchases");

                // تحويل النتائج إلى نموذج العرض
                var result = purchases.Select((purchase, index) => new PurchaseViewModel
                {
                    Index = index + 1,
                    Id = purchase.Id,
                    InvoiceNo = purchase.InvoiceNo,
                    InvoiceNumber = purchase.InvoiceNumber,
                    PurchaseDate = purchase.PurchaseDate,
                    ClientName = purchase.Client.Name ?? "غير معروف",
                    WarehouseName = purchase.Warehouse?.Name ?? "غير محدد",
                    TotalAmount = purchase.TotalAmount,
                    PaidAmount = purchase.Receipts.Sum(r => r.Value),
                    RemainingAmount = purchase.TotalAmount - purchase.Receipts.Sum(r => r.Value)
                }).ToList();

                // تطبيق تصفية المتبقي (يتم تطبيقه بعد حساب المبالغ المدفوعة)
                if (hasRemaining.HasValue)
                {
                    if (hasRemaining.Value)
                    {
                        // فواتير بها رصيد متبقي (المبلغ الإجمالي > المبلغ المدفوع)
                        result = result.Where(p => p.RemainingAmount > 0).ToList();
                    }
                    else
                    {
                        // فواتير مدفوعة بالكامل (المبلغ الإجمالي = المبلغ المدفوع)
                        result = result.Where(p => p.RemainingAmount <= 0).ToList();
                    }
                }

                // إعادة ترقيم الفواتير بعد التصفية (محسنة - دمج مع إنشاء النتيجة)
                result = result.Select((purchase, index) =>
                {
                    purchase.Index = index + 1;
                    return purchase;
                }).ToList();

            return result;
        }

        /// <summary>
        /// الحصول على أصناف المشتريات لفاتورة معينة
        /// </summary>
        public async Task<List<PurchaseItemVM>> GetPurchaseItemsAsync(int purchaseId)
        {
            try
            {
                var purchase = await _context.Purchases
                    .Include(p => p.Client)
                    .Include(p => p.Warehouse)
                    .Include(p => p.PurchaseItems)
                        .ThenInclude(item => item.ProductColor)
                        .ThenInclude(pc => pc.Product)
                    .Include(p => p.PurchaseItems)
                        .ThenInclude(item => item.ProductColor)
                        .ThenInclude(pc => pc.Color)
                    .Include(p => p.PurchaseItems)
                        .ThenInclude(item => item.LensPrescriptionColor)
                        .ThenInclude(lpc => lpc.LensPrescription)
                        .ThenInclude(lp => lp.Lens)
                    .Include(p => p.PurchaseItems)
                        .ThenInclude(item => item.LensPrescriptionColor)
                        .ThenInclude(lpc => lpc.LensPrescription)
                        .ThenInclude(lp => lp.Sphere)
                    .Include(p => p.PurchaseItems)
                        .ThenInclude(item => item.LensPrescriptionColor)
                        .ThenInclude(lpc => lpc.LensPrescription)
                        .ThenInclude(lp => lp.Cylinder)
                    .Include(p => p.PurchaseItems)
                        .ThenInclude(item => item.LensPrescriptionColor)
                        .ThenInclude(lpc => lpc.LensPrescription)
                        .ThenInclude(lp => lp.Pow)
                    .Include(p => p.PurchaseItems)
                        .ThenInclude(item => item.LensPrescriptionColor)
                        .ThenInclude(lpc => lpc.Color)
                    .FirstOrDefaultAsyncWithBusy(p => p.Id == purchaseId, "GetPurchaseItems");

                if (purchase == null)
                    return new List<PurchaseItemVM>();

                return purchase.PurchaseItems.Select(item => new PurchaseItemVM
                {
                    Id = item.Id,
                    PurchaseId = item.PurchaseId,
                    ProductColorId = item.ProductColorId,
                    LensPrescriptionColorId = item.LensPrescriptionColorId,
                    Name = item.ProductColorId.HasValue
                        ? item.ProductColor?.Product?.Name ?? "منتج غير معروف"
                        : FormatLensNameWithPrescription(item.LensPrescriptionColor),
                    Type = item.ProductColorId.HasValue ? "منتج" : "عدسة",
                    Price = item.Price,
                    Quantity = item.Quantity,
                    Exp = item.Exp,
                    ColorName = item.ProductColorId.HasValue
                        ? item.ProductColor?.Color?.Name ?? "بدون لون"
                        : item.LensPrescriptionColor?.Color?.Name ?? "بدون لون",
                    ClientName = purchase.Client?.Name ?? "غير معروف",
                    WarehouseName = purchase.Warehouse?.Name ?? "غير محدد"
                }).ToList();
            }
            catch (Exception ex)
            {
                throw new Exception($"حدث خطأ أثناء جلب أصناف المشتريات: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على أصناف عدة فواتير مشتريات في استعلام واحد محسن
        /// </summary>
        /// <param name="purchaseIds">قائمة معرفات فواتير المشتريات</param>
        /// <returns>قائمة بأصناف المشتريات</returns>
        public async Task<List<PurchaseItemVM>> GetPurchaseItemsByPurchaseIdsAsync(List<int> purchaseIds)
        {
            try
            {
                if (purchaseIds == null || !purchaseIds.Any())
                    return new List<PurchaseItemVM>();

                var purchaseItems = await _context.PurchaseItems
                    .Include(item => item.Purchase)
                        .ThenInclude(p => p.Client)
                    .Include(item => item.Purchase)
                        .ThenInclude(p => p.Warehouse)
                    .Include(item => item.ProductColor)
                        .ThenInclude(pc => pc.Product)
                    .Include(item => item.ProductColor)
                        .ThenInclude(pc => pc.Color)
                    .Include(item => item.LensPrescriptionColor)
                        .ThenInclude(lpc => lpc.LensPrescription)
                        .ThenInclude(lp => lp.Lens)
                    .Include(item => item.LensPrescriptionColor)
                        .ThenInclude(lpc => lpc.LensPrescription)
                        .ThenInclude(lp => lp.Sphere)
                    .Include(item => item.LensPrescriptionColor)
                        .ThenInclude(lpc => lpc.LensPrescription)
                        .ThenInclude(lp => lp.Cylinder)
                    .Include(item => item.LensPrescriptionColor)
                        .ThenInclude(lpc => lpc.LensPrescription)
                        .ThenInclude(lp => lp.Pow)
                    .Include(item => item.LensPrescriptionColor)
                        .ThenInclude(lpc => lpc.Color)
                    .Where(item => purchaseIds.Contains(item.PurchaseId))
                    .ToListAsyncWithBusy("GetPurchaseItemsByPurchaseIds");

                return purchaseItems.Select(item => new PurchaseItemVM
                {
                    Id = item.Id,
                    PurchaseId = item.PurchaseId,
                    ProductColorId = item.ProductColorId,
                    LensPrescriptionColorId = item.LensPrescriptionColorId,
                    Name = item.ProductColorId.HasValue
                        ? item.ProductColor?.Product?.Name ?? "منتج غير معروف"
                        : FormatLensNameWithPrescription(item.LensPrescriptionColor),
                    Type = item.ProductColorId.HasValue ? "منتج" : "عدسة",
                    Price = item.Price,
                    Quantity = item.Quantity,
                    Exp = item.Exp,
                    ColorName = item.ProductColorId.HasValue
                        ? item.ProductColor?.Color?.Name ?? "بدون لون"
                        : item.LensPrescriptionColor?.Color?.Name ?? "بدون لون",
                    ClientName = item.Purchase?.Client?.Name ?? "غير معروف",
                    WarehouseName = item.Purchase?.Warehouse?.Name ?? "غير محدد"
                }).ToList();
            }
            catch (Exception ex)
            {
                throw new Exception($"حدث خطأ أثناء جلب أصناف المشتريات: {ex.Message}");
            }
        }

        /// <summary>
        /// تنسيق اسم العدسة مع الأرقام الخاصة بها
        /// </summary>
        private string FormatLensNameWithPrescription(LensPrescriptionColor? lensPrescriptionColor)
        {
            if (lensPrescriptionColor?.LensPrescription?.Lens == null)
                return "عدسة غير معروفة";

            var lens = lensPrescriptionColor.LensPrescription.Lens;
            var prescription = lensPrescriptionColor.LensPrescription;

            var nameParts = new List<string> { lens.Name };
            var prescriptionParts = new List<string>();

            // إضافة قيم الوصفة إذا كانت موجودة
            if (prescription.Sphere != null)
            {
                var sphValue = prescription.Sphere.Value == 0 ? "PL" : prescription.Sphere.Value.ToString("F2");
                prescriptionParts.Add($"SPH: {sphValue}");
            }

            if (prescription.Cylinder != null)
            {
                var cylValue = prescription.Cylinder.Value == 0 ? "PL" : prescription.Cylinder.Value.ToString("F2");
                prescriptionParts.Add($"CYL: {cylValue}");
            }

            if (prescription.Pow != null)
            {
                var powValue = prescription.Pow.Value == 0 ? "PL" : prescription.Pow.Value.ToString("F2");
                prescriptionParts.Add($"POW: {powValue}");
            }

            // إضافة قيم العدسة الثابتة إذا كانت موجودة
            if (lens.Axis.HasValue && lens.Axis.Value != 0)
            {
                prescriptionParts.Add($"AXIS: {lens.Axis.Value:F1}");
            }

            if (lens.Addtion.HasValue && lens.Addtion.Value != 0)
            {
                prescriptionParts.Add($"ADD: {lens.Addtion.Value:F2}");
            }

            if (lens.BC.HasValue && lens.BC.Value != 0)
            {
                prescriptionParts.Add($"BC: {lens.BC.Value:F1}");
            }

            if (lens.Dia.HasValue && lens.Dia.Value != 0)
            {
                prescriptionParts.Add($"DIA: {lens.Dia.Value:F1}");
            }

            // دمج اسم العدسة مع الأرقام
            if (prescriptionParts.Any())
            {
                nameParts.Add($"({string.Join(" / ", prescriptionParts)})");
            }

            return string.Join(" ", nameParts);
        }

        /// <summary>
        /// تحديث المخزون بشكل مجمع لتحسين الأداء
        /// </summary>
        private async Task<(bool Success, string Message)> UpdateInventoryBatch(IEnumerable<PurchaseItem> items, int warehouseId)
        {
            try
            {
                // تجميع العناصر حسب ProductColorId و تاريخ الانتهاء
                var groupedItems = items
                    .Where(item => item.ProductColorId.HasValue || item.LensPrescriptionColorId.HasValue)
                    .GroupBy(item => new
                    {
                        item.ProductColorId,
                        item.LensPrescriptionColorId,
                        item.Exp
                    })
                    .ToList();

                foreach (var group in groupedItems)
                {
                    var totalQuantityChange = group.Sum(item => item.Quantity);
                    var firstItem = group.First();

                    // استخدام الدالة الأصلية لكل مجموعة
                    var (Success, Message) = await UpdateInventory(firstItem, totalQuantityChange, warehouseId);
                    if (!Success)
                    {
                        return (false, Message);
                    }
                }

                return (true, "تم تحديث المخزون بنجاح");
            }
            catch (Exception ex)
            {
                return (false, $"حدث خطأ أثناء تحديث المخزون: {ex.Message}");
            }
        }

        // Implement IDisposable pattern
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                    _context?.Dispose();
                }

                // Free unmanaged resources
                _disposed = true;
            }
        }

        // Destructor
        ~PurchaseService()
        {
            Dispose(false);
        }
    }
}