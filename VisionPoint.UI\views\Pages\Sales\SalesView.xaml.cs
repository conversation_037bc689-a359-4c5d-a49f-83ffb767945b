using System.Collections.ObjectModel;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;
using VisionPoint.UI.Helper;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.Reports.SalesReciept;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Dialogs;
using VisionPoint.UI.views.Pages.Receipts;

namespace VisionPoint.UI.views.Pages.Sales
{
    /// <summary>
    /// Interaction logic for SalesView.xaml
    /// </summary>
    public partial class SalesView : Page
    {
        private readonly ClientService _clientService;
        private readonly ProductService _productService;
        private readonly SaleService _saleService;
        private readonly ServiceService _serviceService;
        private readonly TreasuryService _treasuryService;
        private readonly DiscountManager _discountManager;
        private readonly WarehouseService _warehouseService;

        private int _saleId = 0;
        private List<Client> _clients;
        private List<Product> _products;
        private List<Service> _services;
        private List<Models.Treasury> _treasuries;
        private List<Warehouse> _warehouses;

        // Property removed - using BusyService in services instead

        // Collection for storing sale items
        private ObservableCollection<SaleItemVM> _saleItems;

        // Properties to calculate totals
        private decimal TotalBeforeDiscount => _saleItems?.Sum(item => item.OriginalPrice * item.Quantity) ?? 0;
        private decimal TotalDiscount => _saleItems?.Sum(item => item.Discount * item.Quantity) ?? 0;
        private decimal TotalAmount => _saleItems?.Sum(item => item.SellPrice * item.Quantity) ?? 0;

        public SalesView(int saleId = 0)
        {
            InitializeComponent();

            // Initialize the collection
            _saleItems = new ObservableCollection<SaleItemVM>();

            // Set data source for the list view
            list.ItemsSource = _saleItems;

            // Get services from ServiceLocator
            _clientService = new ClientService();
            _productService = new ProductService();
            _saleService = new SaleService();
            _serviceService = new ServiceService();
            _treasuryService = new TreasuryService();
            _discountManager = new DiscountManager();
            _warehouseService = new WarehouseService();

            // Store the sale ID if provided
            _saleId = saleId;
        }

        private async void Page_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                txtProductQuantity.Text = "1";
                txtProductPrice.Text = "0";
                txtTotalPrice.Text = "0.000";
                txtPaidPrice.Text = "0.000";
                txtRemaining.Text = "0.000";

                // Load warehouses first
                await LoadWarehousesAsync();

                // Load clients
                // await LoadClientsAsync();

                // Load treasuries
                // await LoadTreasuriesAsync();

                // Load appropriate data based on selected tab
                //if (RdbProduct != null && RdbProduct.IsChecked == true)
                //{
                //    await LoadProductsAsync();
                //}
                //else if (RdbService != null && RdbService.IsChecked == true)
                //{
                //    await LoadServicesAsync();
                //}

                // Set current date
                if (DtpSaleDate != null)
                {
                    DtpSaleDate.SelectedDate = DateTime.Now;

                    // التحقق من صلاحية تغيير التاريخ
                    bool canChangeDate = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ChangeSaleDateRole");
                    DtpSaleDate.IsEnabled = canChangeDate;
                }
                btnAddReceipt.Visibility = Visibility.Collapsed;
                cmbTreasury.Visibility = Visibility.Visible;

                // تعيين الحالة الافتراضية للأزرار (المنتجات مختارة بشكل افتراضي)
                if (btnAddProduct != null) btnAddProduct.Visibility = Visibility.Visible;
                if (btnAddLensess != null) btnAddLensess.Visibility = Visibility.Collapsed;

                // If sale ID is provided, load the sale; otherwise, start a new one
                if (_saleId > 0)
                {
                    await GetSale(_saleId);
                }

            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل البيانات: {ex.Message}");
            }
        }

        // طريقة لتعطيل عناصر التحكم الخاصة بالتعديل
        private void DisableEditingControls()
        {
            // تعطيل حقول الإدخال
            cmbClient.IsEnabled = false;
            txtphone.IsEnabled = false;
            DtpSaleDate.IsEnabled = false;

            // تعطيل أزرار الإضافة والتعديل
            btnAddProduct.IsEnabled = false;
            btnAddLensess.IsEnabled = false;

            // تغيير وظيفة زر الحفظ والطباعة ليكون للطباعة فقط
            // لا نقوم بتعطيل زر الطباعة حتى يتمكن المستخدم من طباعة الفاتورة
            btnSaveAndPrint.IsEnabled = true; // إبقاء زر الطباعة متاحًا

            // تعطيل حقول المنتجات
            cmbProductName.IsEnabled = false;
            cmbProductColor.IsEnabled = false;
            CmbProductExpireOn.IsEnabled = false;
            txtProductQuantity.IsEnabled = false;
            txtProductPrice.IsEnabled = false;
            txtProductBarcode.IsEnabled = false;

            // تعطيل حقول الخدمات
            cmbServiceName.IsEnabled = false;
            txtServicePrice.IsEnabled = false;

            // تعطيل أزرار الاختيار
            RdbProduct.IsEnabled = false;
            RdbService.IsEnabled = false;
            RdbLenses.IsEnabled = false;

            // تعطيل أزرار الحذف في القائمة
            foreach (var item in list.Items)
            {
                var container = list.ItemContainerGenerator.ContainerFromItem(item) as ListViewItem;
                if (container != null)
                {
                    var button = FindVisualChild<Button>(container);
                    if (button != null)
                    {
                        button.IsEnabled = false;
                    }
                }
            }

            // تعطيل حقل المبلغ المدفوع
            txtPaidPrice.IsEnabled = false;
            cmbTreasury.IsEnabled = false;
            txtNotes.IsEnabled = false;
        }
        // طريقة لتعطيل عناصر التحكم الخاصة بالتعديل
        private void EnableEditingControls()
        {
            // تعطيل حقول الإدخال
            cmbClient.IsEnabled = true;
            txtphone.IsEnabled = true;
            DtpSaleDate.IsEnabled = true;

            // تعطيل أزرار الإضافة والتعديل
            btnAddProduct.IsEnabled = true;
            btnAddLensess.IsEnabled = true;

            // تغيير وظيفة زر الحفظ والطباعة ليكون للطباعة فقط
            // لا نقوم بتعطيل زر الطباعة حتى يتمكن المستخدم من طباعة الفاتورة

            // تعطيل حقول المنتجات
            cmbProductName.IsEnabled = true;
            cmbProductColor.IsEnabled = true;
            CmbProductExpireOn.IsEnabled = true;
            txtProductQuantity.IsEnabled = true;
            txtProductPrice.IsEnabled = true;

            // تعطيل حقول الخدمات
            cmbServiceName.IsEnabled = true;
            txtServicePrice.IsEnabled = true;
            // تعطيل حقل المبلغ المدفوع
            txtPaidPrice.IsEnabled = true;
            cmbTreasury.IsEnabled = true;
        }
        // طريقة مساعدة للبحث عن عنصر في شجرة العناصر المرئية
        private T FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(parent, i);

                if (child != null && child is T)
                    return (T)child;

                T childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }

            return null;
        }

        private void btnAddLensess_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق من اختيار المخزن
                if (cmbWarehouse.SelectedValue == null)
                {
                    ShowMessage("بيانات ناقصة", "الرجاء اختيار المخزن أولاً");
                    return;
                }

                int warehouseId = (int)cmbWarehouse.SelectedValue;

                // فتح نافذة إضافة العدسات مع تمرير معرف المخزن
                var addSalesLenses = new AddSalesLenses(warehouseId);
                if (addSalesLenses.ShowDialog() == true)
                {
                    // الحصول على العدسات المضافة من النافذة
                    if (addSalesLenses.Tag is List<SaleItemVM> selectedLenses && selectedLenses.Any())
                    {
                        // إضافة العدسات إلى قائمة عناصر البيع
                        foreach (var lensItem in selectedLenses)
                        {
                            _saleItems.Add(lensItem);
                        }

                        // تحديث إجمالي السعر
                        UpdateTotalPrice();

                        ShowMessage("تمت الإضافة", "تمت إضافة العدسات بنجاح");
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء إضافة العدسات: {ex.Message}");
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnNew_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                EnableEditingControls();
                gtxtNetAmount.Visibility = Visibility.Collapsed;
                gtxtTotalReturned.Visibility = Visibility.Collapsed;

                btnAddReceipt.Visibility = Visibility.Collapsed;
                cmbTreasury.Visibility = Visibility.Visible;

                // تعيين الحالة الافتراضية للأزرار (المنتجات مختارة بشكل افتراضي)
                if (btnAddProduct != null) btnAddProduct.Visibility = Visibility.Visible;
                if (btnAddLensess != null) btnAddLensess.Visibility = Visibility.Collapsed;

                // Clear all fields if they exist
                if (txtSaleNo != null) txtSaleNo.Clear();
                if (DtpSaleDate != null) DtpSaleDate.SelectedDate = DateTime.Now;
                if (cmbWarehouse != null && _warehouses != null && _warehouses.Count > 0) cmbWarehouse.SelectedIndex = 0;
                if (cmbClient != null) cmbClient.SelectedIndex = -1;
                if (cmbTreasury != null) cmbTreasury.SelectedIndex = -1;

                // Clear product fields if they exist
                ClearProductInputs();

                // Clear service fields if they exist
                ClearServiceInputs();

                // Clear collections and totals
                _saleItems.Clear();
                if (txtTotalPrice != null) txtTotalPrice.Text = "0.000";
                if (txtPaidPrice != null)
                {
                    txtPaidPrice.Text = "0.000";
                    txtPaidPrice.IsReadOnly = false; // إعادة تعيين خاصية القراءة فقط عند إنشاء فاتورة جديدة
                }
                if (txtRemaining != null) txtRemaining.Text = "0.000";
                if (txtTotalBeforeDiscount != null) txtTotalBeforeDiscount.Text = "0.000";
                _saleId = 0;
                cmbClient.SelectedValue = 1;
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ: {ex.Message}");
            }
        }

        private void txtPaidPrice_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateRemainingAmount();
        }

        private void UpdateRemainingAmount()
        {
            if (txtNetAmount == null || txtPaidPrice == null || txtRemaining == null) return;

            if (decimal.TryParse(txtNetAmount.Text, out decimal netAmount) &&
                decimal.TryParse(txtPaidPrice.Text, out decimal paidAmount))
            {
                // Ensure paid amount doesn't exceed net amount
                if (paidAmount > netAmount && _saleId == 0)
                {
                    paidAmount = netAmount;
                    txtPaidPrice.Text = paidAmount.ToString("N3");
                }

                decimal remainingAmount = netAmount - paidAmount;
                txtRemaining.Text = remainingAmount.ToString("N3");
            }
            else
            {
                txtRemaining.Text = "0.000";
            }
        }

        private void UpdateTotalPrice()
        {
            if (txtTotalBeforeDiscount == null || txtTotalPrice == null ||
                txtTotalReturned == null || txtNetAmount == null) return;

            // Calcular el total antes del descuento
            txtTotalBeforeDiscount.Text = TotalBeforeDiscount.ToString("N3");

            // Calcular el total de la factura
            txtTotalPrice.Text = TotalAmount.ToString("N3");

            // Calcular el total de devoluciones
            decimal totalReturned = 0;
            if (_saleId > 0 && _saleItems.Any(item => item.ReturnedQuantity > 0))
            {
                totalReturned = _saleItems.Sum(item => item.ReturnedQuantity * item.SellPrice);
            }
            txtTotalReturned.Text = totalReturned.ToString("N3");

            // Calcular el valor neto después de devoluciones
            decimal netAmount = TotalAmount - totalReturned;
            txtNetAmount.Text = netAmount.ToString("N3");

            // Actualizar el monto restante
            UpdateRemainingAmount();
        }

        private async void txtProductBarcode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key != Key.Enter) return;

            try
            {
                e.Handled = true; // Prevent the event from bubbling up

                if (txtProductBarcode == null || cmbProductName == null || cmbProductColor == null || txtProductQuantity == null) return;

                string barcode = txtProductBarcode.Text.Trim();
                if (string.IsNullOrEmpty(barcode))
                {
                    ShowMessage("بيانات ناقصة", "الرجاء إدخال الباركود للبحث");
                    return;
                }

                // Find product by barcode with warehouse validation
                ProductColor? productColor = null;

                // Check if warehouse is selected
                if (cmbWarehouse.SelectedValue != null && cmbWarehouse.SelectedValue is int warehouseId)
                {
                    // Search for product color by barcode in the selected warehouse
                    productColor = await _productService.GetProductColorByBarcodeAndWarehouseAsync(barcode, warehouseId);
                }
                else
                {
                    // If no warehouse selected, search in all warehouses
                    productColor = await _productService.GetProductColorByBarcodeAsync(barcode);
                }

                if (productColor != null)
                {
                    // Find the product in the loaded products list
                    var product = _products?.FirstOrDefault(p => p.Id == productColor.ProductId);
                    if (product != null)
                    {
                        // Select the product in the dropdown
                        cmbProductName.SelectedItem = product;
                        cmbProductName_SelectionChanged(sender, null);

                        // Select the color by barcode
                        cmbProductColor.SelectedValue = productColor.Id;
                        cmbProductColor_SelectionChanged(sender, null);

                        // If product has expiration dates, select the first one
                        if (product.Exp && CmbProductExpireOn.Visibility == Visibility.Visible)
                        {
                            if (CmbProductExpireOn.Items.Count > 0)
                            {
                                CmbProductExpireOn.SelectedIndex = 0;
                            }
                        }

                        // Set default quantity to 1
                        txtProductQuantity.Text = "1";

                        // Focus on quantity field and select all text for easy editing
                        txtProductQuantity.Focus();
                        txtProductQuantity.SelectAll();
                    }
                    else
                    {
                        ShowError("المنتج غير متوفر في المخزن المختار");
                    }
                }
                else
                {
                    if (cmbWarehouse.SelectedValue != null)
                    {
                        ShowError("لم يتم العثور على منتج بهذا الباركود في المخزن المختار");
                    }
                    else
                    {
                        ShowError("لم يتم العثور على منتج بهذا الباركود");
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء معالجة الباركود: {ex.Message}");
            }
        }

        // Helper method to load expiration dates for a product color
        //private async Task LoadProductExpirationDates(int productColorId)
        //{
        //    try
        //    {
        //        var quantities = await _productService.GetProductQuantitiesByColorIdAsync(productColorId);

        //        if (quantities != null && quantities.Any())
        //        {
        //            var expirationItems = quantities
        //                .Select(q => new
        //                {
        //                    Id = q.Id,
        //                    Text = q.Exp?.ToString("yyyy-MM-dd") ?? "غير محدد",
        //                    Quantity = q.Quantity
        //                })
        //                .ToList();

        //            CmbProductExpireOn.ItemsSource = expirationItems;
        //            CmbProductExpireOn.DisplayMemberPath = "Text";
        //            CmbProductExpireOn.SelectedValuePath = "Id";
        //            CmbProductExpireOn.Visibility = Visibility.Visible;
        //        }
        //        else
        //        {
        //            CmbProductExpireOn.ItemsSource = null;
        //            CmbProductExpireOn.Visibility = Visibility.Collapsed;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        ShowError($"حدث خطأ أثناء تحميل تواريخ الصلاحية: {ex.Message}");
        //    }
        //}

        private async void cmbProductName_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (cmbProductName.SelectedItem is Product selectedProduct)
                {
                    CmbProductExpireOn.Visibility = selectedProduct.Exp ? Visibility.Visible : Visibility.Collapsed;
                    // Handle color visibility
                    if (selectedProduct.Color)
                    {
                        // Load colors for this product
                        var colors = selectedProduct.ProductColors
                            .Where(pc => pc.Color != null)
                            .Select(pc => new { pc.Id, pc.Color.Name })
                            .ToList();

                        cmbProductColor.ItemsSource = colors;
                        cmbProductColor.DisplayMemberPath = "Name";
                        cmbProductColor.SelectedValuePath = "Id";
                        cmbProductColor.SelectedIndex = -1;
                    }
                    else
                    {
                        var colors = selectedProduct.ProductColors
                            .Where(pc => pc.Color == null)
                            .Select(pc => new { pc.Id, Name = "لا يوجد" })
                            .ToList();

                        cmbProductColor.ItemsSource = colors;
                        cmbProductColor.DisplayMemberPath = "Name";
                        cmbProductColor.SelectedValuePath = "Id";
                        cmbProductColor.SelectedIndex = 0;
                        cmbProductColor_SelectionChanged(sender, e);
                    }

                    // التحقق من وجود تخفيض ساري للمنتج
                    decimal originalPrice = selectedProduct.SellPrice;
                    var (hasDiscount, discountPercentage) = await _discountManager.CheckProductDiscount(selectedProduct.Id, DateTime.Now);

                    if (hasDiscount)
                    {
                        // حساب السعر بعد التخفيض
                        decimal discountedPrice = _discountManager.CalculateDiscountedPrice(originalPrice, discountPercentage);

                        // عرض السعر بعد التخفيض
                        txtProductPrice.Text = discountedPrice.ToString("N3");

                        // إظهار رسالة للمستخدم بوجود تخفيض
                        lblProductDiscount.Visibility = Visibility.Visible;
                        lblProductDiscount.Text = $"يوجد تخفيض {discountPercentage}% - السعر الأصلي: {originalPrice:N3}";
                    }
                    else
                    {
                        // عرض السعر الأصلي
                        txtProductPrice.Text = originalPrice.ToString("N3") ?? "0.000";

                        // إخفاء رسالة التخفيض
                        lblProductDiscount.Visibility = Visibility.Collapsed;
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء تغيير المنتج: {ex.Message}");
            }
        }

        private void btnAddProduct_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (RdbProduct != null && RdbProduct.IsChecked == true)
                {
                    AddProduct();
                }
                else if (RdbService != null && RdbService.IsChecked == true)
                {
                    AddService();
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء إضافة العنصر: {ex.Message}");
            }
            finally
            {
                EnableAllButtons();
            }
        }
        private void AddProduct()
        {
            // Validate inputs
            if (cmbProductName.SelectedItem == null)
            {
                ShowMessage("بيانات ناقصة", "الرجاء اختيار المنتج");
                return;
            }

            if (string.IsNullOrEmpty(txtProductQuantity.Text) ||
                !int.TryParse(txtProductQuantity.Text, out int quantity) ||
                quantity <= 0)
            {
                ShowMessage("بيانات ناقصة", "الرجاء إدخال كمية صحيحة (أكبر من صفر)");
                return;
            }

            if (string.IsNullOrEmpty(txtProductPrice.Text) ||
                !decimal.TryParse(txtProductPrice.Text, out decimal price) ||
                price < 0)
            {
                ShowMessage("بيانات ناقصة", "الرجاء إدخال سعر صحيح");
                return;
            }

            // Get selected product
            var product = cmbProductName.SelectedItem as Product;
            if (product == null) return;
            // Handle product color
            string colorName = string.Empty;

            // التحقق من اختيار اللون إذا كان المنتج يحتوي على ألوان
            if (product.Color)
            {
                if (cmbProductColor.SelectedItem == null)
                {
                    ShowMessage("بيانات ناقصة", "الرجاء اختيار اللون المناسب للمنتج");
                    return;
                }
                colorName = cmbProductColor.Text;
            }
            int? productQuantityId = null;
            if (product.Exp)
            {
                if (CmbProductExpireOn.SelectedItem == null)
                {
                    ShowMessage("بيانات ناقصة", "الرجاء اختيار تاريخ انتهاء الصلاحية");
                    return;
                }
                productQuantityId = (int)CmbProductExpireOn.SelectedValue;
            }
            else
            {
                productQuantityId = product.ProductColors
                    .First(x => x.Id == (int)cmbProductColor.SelectedValue).ProductQuantity
                    .Select(x => x.Id).FirstOrDefault();
                if (productQuantityId == null)
                {
                    ShowMessage("بيانات ناقصة", "يبدو ان الصنف لم يتم ادخاله عن طريق فاتورة شراء يجب ادخاله في فاتورة شراء اولا.");
                    return;
                }
            }


            // التحقق من اختيار تاريخ الصلاحية إذا كان المنتج يحتوي على تاريخ صلاحية
            if (product.Exp && CmbProductExpireOn.Visibility == Visibility.Visible)
            {
                if (CmbProductExpireOn.SelectedItem == null)
                {
                    ShowMessage("بيانات ناقصة", "الرجاء اختيار تاريخ الصلاحية للمنتج");
                    return;
                }
            }

            // التحقق من اختيار اللون حتى لو لم يكن المنتج يحتوي على ألوان متعددة
            if (cmbProductColor.SelectedItem == null && product.ProductColors != null && product.ProductColors.Any())
            {
                ShowMessage("بيانات ناقصة", "الرجاء اختيار اللون المناسب للمنتج");
                return;
            }


            // Check for existing item
            var existingItem = _saleItems.FirstOrDefault(item =>
                item.Type == "منتج" &&
                item.Name == product.Name &&
                item.ProductQuantityId == productQuantityId);

            if (existingItem != null)
            {
                // Ask to merge quantities
                if (QuestionBox.Show(
                    "دمج العناصر المتشابهة",
                    "المنتج موجود مسبقاً بنفس البيانات. هل تريد إضافة الكمية الجديدة للكمية الموجودة؟") == MessageBoxResult.Yes)
                {
                    existingItem.Quantity += quantity;
                    CollectionViewSource.GetDefaultView(_saleItems).Refresh();
                    UpdateTotalPrice();
                    return;
                }
                else
                {
                    return;
                }
            }

            // Get discount information
            decimal originalPrice = product.SellPrice;
            decimal discount = 0;

            // Check if there's a discount
            if (lblProductDiscount.Visibility == Visibility.Visible)
            {
                discount = originalPrice - price;
            }

            // Create new sale item
            var saleItem = new SaleItemVM
            {
                Type = "منتج",
                Name = product.Name,
                OriginalPrice = originalPrice,
                Discount = discount,
                SellPrice = price,
                CostPrice = product.CostPrice,
                Quantity = quantity,
                ProductQuantityId = productQuantityId,
                ColorName = colorName
            };

            // Add to collection
            _saleItems.Add(saleItem);

            // Update total price
            UpdateTotalPrice();

            // Clear input fields
            ClearProductInputs();
        }
        private void AddService()
        {
            // Validate inputs
            if (cmbServiceName.SelectedItem == null)
            {
                ShowMessage("بيانات ناقصة", "الرجاء اختيار الخدمة");
                return;
            }
            if (string.IsNullOrEmpty(txtServicePrice.Text) ||
                !decimal.TryParse(txtServicePrice.Text, out decimal price) ||
                price < 0)
            {
                ShowMessage("بيانات ناقصة", "الرجاء إدخال سعر صحيح");
                return;
            }

            // Validate quantity
            if (string.IsNullOrEmpty(txtServiceQuantity.Text) ||
                !int.TryParse(txtServiceQuantity.Text, out int quantity) ||
                quantity <= 0)
            {
                ShowMessage("بيانات ناقصة", "الرجاء إدخال كمية صحيحة");
                return;
            }

            // Get selected service
            var service = cmbServiceName.SelectedItem as Service;
            if (service == null) return;

            // Check for existing item
            var existingItem = _saleItems.FirstOrDefault(item =>
                item.Type == "خدمة" &&
                item.Name == service.Name &&
                item.ServiceId == service.Id);

            if (existingItem != null)
            {
                // Ask to merge quantities
                if (QuestionBox.Show(
                    "دمج العناصر المتشابهة",
                    "الخدمة موجودة مسبقاً في الفاتورة. هل تريد إضافة الكمية الجديدة للكمية الموجودة؟") == MessageBoxResult.Yes)
                {
                    existingItem.Quantity += quantity;
                    CollectionViewSource.GetDefaultView(_saleItems).Refresh();
                    UpdateTotalPrice();
                    return;
                }
                else
                {
                    return;
                }
            }

            // Get discount information
            decimal originalPrice = service.Price;
            decimal discount = 0;

            // Check if there's a discount
            if (lblServiceDiscount.Visibility == Visibility.Visible)
            {
                discount = originalPrice - price;
            }

            // Create new sale item
            var saleItem = new SaleItemVM
            {
                Type = "خدمة",
                Name = service.Name,
                OriginalPrice = originalPrice,
                Discount = discount,
                SellPrice = price,
                Quantity = quantity,
                ServiceId = service.Id
            };

            // Add to collection
            _saleItems.Add(saleItem);

            // Update total price
            UpdateTotalPrice();

            // Clear input fields
            ClearServiceInputs();
        }
        private void btnNewItem_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (RdbProduct != null && RdbProduct.IsChecked == true)
                {
                    ClearProductInputs();
                }
                else if (RdbService != null && RdbService.IsChecked == true)
                {
                    ClearServiceInputs();
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء تنظيف الحقول: {ex.Message}");
            }
            finally
            {
                EnableAllButtons();
            }
        }
        private async void cmbServiceName_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (cmbServiceName == null || txtServicePrice == null || txtServiceQuantity == null) return;

                if (cmbServiceName.SelectedItem is Service selectedService)
                {
                    // التحقق من وجود تخفيض ساري للخدمة
                    decimal originalPrice = selectedService.Price;
                    var (hasDiscount, discountPercentage) = await _discountManager.CheckServiceDiscount(selectedService.Id, DateTime.Now);

                    if (hasDiscount)
                    {
                        // حساب السعر بعد التخفيض
                        decimal discountedPrice = _discountManager.CalculateDiscountedPrice(originalPrice, discountPercentage);

                        // عرض السعر بعد التخفيض
                        txtServicePrice.Text = discountedPrice.ToString("N3");

                        // إظهار رسالة للمستخدم بوجود تخفيض
                        lblServiceDiscount.Visibility = Visibility.Visible;
                        lblServiceDiscount.Text = $"يوجد تخفيض {discountPercentage}% - السعر الأصلي: {originalPrice:N3}";
                    }
                    else
                    {
                        // عرض السعر الأصلي
                        txtServicePrice.Text = originalPrice.ToString("N3");

                        // إخفاء رسالة التخفيض
                        lblServiceDiscount.Visibility = Visibility.Collapsed;
                    }

                    // Make sure quantity is set to at least 1
                    if (string.IsNullOrEmpty(txtServiceQuantity.Text) || !int.TryParse(txtServiceQuantity.Text, out int quantity) || quantity < 1)
                    {
                        txtServiceQuantity.Text = "1";
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء تغيير الخدمة: {ex.Message}");
            }
        }
        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnSaveAndPrint.IsEnabled = false;
            btnNesw.IsEnabled = false;
            btnAddProduct.IsEnabled = false;
            btnNewItem.IsEnabled = false;
            btnAddLensess.IsEnabled = false;
            btnBack.IsEnabled = false;
            btnAddReceipt.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnSaveAndPrint.IsEnabled = true;
            btnNesw.IsEnabled = true;
            btnAddProduct.IsEnabled = true;
            btnNewItem.IsEnabled = true;
            btnAddLensess.IsEnabled = true;
            btnBack.IsEnabled = true;
            btnAddReceipt.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private async void btnSaveAndPrint_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق مما إذا كانت هذه فاتورة موجودة بالفعل
                if (_saleId > 0)
                {
                    // التحقق من صلاحيات المستخدم
                    bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditSalesRole");

                    // إذا كان المستخدم لا يملك صلاحية التعديل، نقوم بطباعة الفاتورة فقط
                    if (!canEdit)
                    {
                        Print(_saleId);
                        ShowMessage("تم بنجاح", "تمت طباعة الفاتورة بنجاح");
                        return;
                    }
                }
                else
                {
                    // التحقق من صلاحية الإضافة للفواتير الجديدة
                    bool canAdd = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("SalesRole");
                    if (!canAdd)
                    {
                        ErrorBox.Show("لا تملك صلاحية إضافة فواتير المبيعات", "خطأ في الصلاحيات", true);
                        return;
                    }
                }

                if (cmbClient == null || DtpSaleDate == null) return;

                // Validate inputs
                if (cmbClient.SelectedIndex == -1 && (string.IsNullOrEmpty(cmbClient.Text.Trim()) || string.IsNullOrEmpty(txtphone.Text.Trim())))
                {
                    ShowMessage("بيانات ناقصة", "الرجاء اختيار العميل أو إدخال اسم العميل ورقم الهاتف لإنشاء عميل جديد");
                    return;
                }

                if (_saleItems.Count == 0)
                {
                    ShowMessage("بيانات ناقصة", "الرجاء إضافة أصناف للفاتورة");
                    return;
                }
                decimal? paid = null;
                byte? treasuryId = null;
                if (_saleId == 0)
                {
                    decimal.TryParse(txtPaidPrice.Text, out decimal paidAmount);
                    if (cmbTreasury.SelectedItem == null && paidAmount > 0)
                    {
                        ShowMessage("بيانات ناقصة", "الرجاء اختيار طريقة دفع");
                        return;
                    }
                    paid = paidAmount;
                    treasuryId = (byte?)cmbTreasury.SelectedValue;
                }
                // التحقق من اختيار المخزن
                if (cmbWarehouse.SelectedValue == null)
                {
                    ShowMessage("بيانات ناقصة", "الرجاء اختيار المخزن");
                    return;
                }

                // Create sale object with items
                var sale = new Sale
                {
                    Id = _saleId,
                    ClientId = cmbClient.SelectedIndex == -1 ? 0 : (int)cmbClient.SelectedValue,
                    // إنشاء عميل جديد إذا لم يتم اختيار عميل موجود
                    Client = cmbClient.SelectedIndex != -1 ? null : new Client()
                    {
                        Name = cmbClient.Text.Trim(),
                        Phone = txtphone.Text.Trim(),
                        Balance = TotalAmount,
                        IsCustomer = true,
                        WarehouseId = (int)cmbWarehouse.SelectedValue, // ربط العميل الجديد بالمخزن المختار
                        CreatedById = CurrentUser.Id,
                        ModifiedById = CurrentUser.Id
                    },
                    SaleDate = DtpSaleDate.SelectedDate ?? DateTime.Now,
                    TotalBeforeDiscount = TotalBeforeDiscount,
                    TotalDiscount = TotalDiscount,
                    TotalAmount = TotalAmount,
                    Notes = txtNotes.Text,
                    WarehouseId = (int)cmbWarehouse.SelectedValue,
                    SaleItems = _saleItems.Select(item => new SaleItem
                    {
                        Id = item.Id,
                        ProductQuantityId = item.ProductQuantityId,
                        LensQuantityRightId = item.LensQuantityRightId,
                        LensQuantityLeftId = item.LensQuantityLeftId,
                        ServiceId = item.ServiceId,
                        PairId = item.PairId, // إضافة معرف الزوج
                        CostPrice = item.CostPrice,
                        OriginalPrice = item.OriginalPrice,
                        Discount = item.Discount,
                        SellPrice = item.SellPrice,
                        Quantity = item.Quantity,
                        Axis = item.Axis
                    }).ToList()
                };
                bool success; string message; int saleId, invoiceNo = 0;
                // Save sale with inventory management
                if (sale.Id == 0) (success, message, saleId, invoiceNo) = await _saleService.AddSaleAsync(sale, paid, treasuryId);
                else (success, message, saleId, invoiceNo) = await _saleService.EditSaleAsync(sale);
                if (success)
                {
                    txtSaleNo.Text = invoiceNo.ToString();

                    // إعادة تحميل بيانات العملاء إذا تم إنشاء عميل جديد
                    if (cmbClient.SelectedIndex == -1 && !string.IsNullOrEmpty(cmbClient.Text.Trim()))
                    {
                        await LoadClientsAsync();

                        // البحث عن العميل الجديد واختياره
                        var newClientName = cmbClient.Text.Trim();
                        var newClient = _clients?.FirstOrDefault(c => c.Name.Equals(newClientName, StringComparison.OrdinalIgnoreCase));
                        if (newClient != null)
                        {
                            cmbClient.SelectedItem = newClient;
                        }
                    }

                    // Show success message
                    ShowMessage("تم بنجاح", $"تم حفظ الفاتورة رقم {invoiceNo} بنجاح");

                    // Print receipt if needed
                    Print(saleId);

                    // Clear form for new sale
                    btnNew_MouseLeftButtonDown(null, null);

                    // Load the saved sale
                    await GetSale(saleId);
                }
                else
                {
                    ShowError(message);
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء حفظ الفاتورة: {ex.Message}");
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void RdbProduct_Checked(object sender, RoutedEventArgs e)
        {
            if (ProductLayout == null)
                return;
            RdbProduct.IsChecked = true;
            ProductLayout.Visibility = Visibility.Visible;
            ServiceLayout.Visibility = Visibility.Collapsed;
            LensessLayout.Visibility = Visibility.Collapsed;

            // إظهار زر الإضافة العادي وإخفاء زر إضافة العدسات
            if (btnAddProduct != null) btnAddProduct.Visibility = Visibility.Visible;
            if (btnAddLensess != null) btnAddLensess.Visibility = Visibility.Collapsed;

            await LoadProductsAsync();
        }
        private void RdbLenses_Checked(object sender, RoutedEventArgs e)
        {
            if (LensessLayout == null)
                return;

            RdbLenses.IsChecked = true;
            LensessLayout.Visibility = Visibility.Visible;
            ProductLayout.Visibility = Visibility.Collapsed;
            ServiceLayout.Visibility = Visibility.Collapsed;

            // إخفاء زر الإضافة العادي وإظهار زر إضافة العدسات
            if (btnAddProduct != null) btnAddProduct.Visibility = Visibility.Collapsed;
            if (btnAddLensess != null) btnAddLensess.Visibility = Visibility.Visible;
        }
        private async void RdbService_Checked(object sender, RoutedEventArgs e)
        {
            if (ServiceLayout == null)
                return;
            RdbService.IsChecked = true;
            ServiceLayout.Visibility = Visibility.Visible;
            ProductLayout.Visibility = Visibility.Collapsed;
            LensessLayout.Visibility = Visibility.Collapsed;

            // إظهار زر الإضافة العادي وإخفاء زر إضافة العدسات
            if (btnAddProduct != null) btnAddProduct.Visibility = Visibility.Visible;
            if (btnAddLensess != null) btnAddLensess.Visibility = Visibility.Collapsed;

            // Initialize quantity field to 1
            if (txtServiceQuantity != null) txtServiceQuantity.Text = "1";

            await LoadServicesAsync();
        }
        // Helper methods
        private void ClearProductInputs()
        {
            // تفريغ حقل الباركود
            if (txtProductBarcode != null) txtProductBarcode.Clear();

            // إعادة تعيين الكمية إلى 1
            if (txtProductQuantity != null) txtProductQuantity.Text = "1";

            // تفريغ حقل السعر
            if (txtProductPrice != null) txtProductPrice.Clear();

            // إعادة تعيين اختيار المنتج
            if (cmbProductName != null) cmbProductName.SelectedIndex = -1;

            // تفريغ قائمة الألوان وإعادة تعيينها
            if (cmbProductColor != null)
            {
                cmbProductColor.SelectedIndex = -1;
                cmbProductColor.ItemsSource = null;
            }

            // تفريغ قائمة تواريخ الصلاحية وإخفاؤها
            if (CmbProductExpireOn != null)
            {
                CmbProductExpireOn.SelectedIndex = -1;
                CmbProductExpireOn.ItemsSource = null;
                CmbProductExpireOn.Visibility = Visibility.Collapsed;
            }

            // إخفاء رسالة التخفيض
            if (lblProductDiscount != null) lblProductDiscount.Visibility = Visibility.Collapsed;
        }
        private void ClearServiceInputs()
        {
            // تفريغ حقل السعر
            if (txtServicePrice != null) txtServicePrice.Clear();

            // إعادة تعيين اختيار الخدمة
            if (cmbServiceName != null) cmbServiceName.SelectedIndex = -1;

            // إعادة تعيين الكمية إلى 1
            if (txtServiceQuantity != null) txtServiceQuantity.Text = "1";

            // إخفاء رسالة التخفيض
            if (lblServiceDiscount != null) lblServiceDiscount.Visibility = Visibility.Collapsed;
        }
        private async Task LoadClientsAsync()
        {
            try
            {
                if (cmbClient == null) return;

                // Get clients based on selected warehouse
                if (cmbWarehouse.SelectedValue != null)
                {
                    int warehouseId = (int)cmbWarehouse.SelectedValue;
                    _clients = await _clientService.GetCustomersByWarehouseAsync(warehouseId);
                }
                else
                {
                    // Get all customers if no warehouse selected
                    _clients = await _clientService.GetCustomersOnlyAsync();
                }

                // Configure ComboBox
                cmbClient.DisplayMemberPath = "Name";
                cmbClient.SelectedValuePath = "Id";
                cmbClient.ItemsSource = _clients;

                // Select first client if available
                if (_clients.Count > 0)
                {
                    cmbClient.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل بيانات العملاء: {ex.Message}");
            }
        }
        private async Task LoadProductsAsync()
        {
            try
            {
                if (cmbProductName == null) return;

                // Get products based on selected warehouse
                if (cmbWarehouse.SelectedValue != null)
                {
                    int warehouseId = (int)cmbWarehouse.SelectedValue;
                    _products = await _productService.GetProductsForSellByWarehouseAsync(warehouseId);
                }
                else
                {
                    _products = await _productService.GetAllProductsForSellAsync();
                }

                // Configure ComboBox
                cmbProductName.DisplayMemberPath = "Name";
                cmbProductName.SelectedValuePath = "Id";
                cmbProductName.ItemsSource = _products;
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل بيانات المنتجات: {ex.Message}");
            }
        }
        private async Task LoadServicesAsync()
        {
            try
            {
                if (cmbServiceName == null) return;

                // Get all services
                _services = await _serviceService.GetAllServices();

                // Configure ComboBox
                cmbServiceName.DisplayMemberPath = "Name";
                cmbServiceName.SelectedValuePath = "Id";
                cmbServiceName.ItemsSource = _services;
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل بيانات الخدمات: {ex.Message}");
            }
        }
        private async Task LoadTreasuriesAsync()
        {
            try
            {
                if (cmbTreasury == null) return;

                // Get treasuries based on selected warehouse
                if (cmbWarehouse.SelectedValue != null)
                {
                    int warehouseId = (int)cmbWarehouse.SelectedValue;
                    _treasuries = await _treasuryService.GetTreasuriesByWarehouseAsync(warehouseId);
                }
                else
                {
                    // Get all treasuries if no warehouse selected
                    _treasuries = await _treasuryService.GetAllTreasuriesAsync();
                }

                // Configure ComboBox
                cmbTreasury.DisplayMemberPath = "Name";
                cmbTreasury.SelectedValuePath = "Id";
                cmbTreasury.ItemsSource = _treasuries;

                // Select first treasury if available
                cmbTreasury.SelectedIndex = -1;

            }
            catch (Exception ex)
            {
                ShowError($"خطأ في تحميل بيانات الخزائن: {ex.Message}");
            }
        }
        public async Task GetSale(int saleId)
        {
            if (saleId <= 0) return;

            try
            {
                // Fetch the sale by ID
                var saleViewModel = await _saleService.GetSaleByIdAsync(saleId);

                if (saleViewModel == null)
                {
                    ShowError("لم يتم العثور على الفاتورة المطلوبة");
                    return;
                }

                // السماح بعرض اعدادات الراجع
                gtxtNetAmount.Visibility = Visibility.Visible;
                gtxtTotalReturned.Visibility = Visibility.Visible;

                // التحقق من صلاحيات المستخدم
                bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditSalesRole");
                bool canView = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("SalesRole") || CurrentUser.HasRole("EditSalesRole") || CurrentUser.HasRole("DeleteSalesRole");

                // جعل حقل القيمة المدفوعة للقراءة فقط عند التعديل
                txtPaidPrice.IsReadOnly = true;

                // إذا كان المستخدم لا يملك صلاحية التعديل، قم بتعطيل عناصر التحكم الخاصة بالتعديل
                if (!canEdit)
                {
                    DisableEditingControls();

                    // إذا كان المستخدم لديه صلاحية العرض فقط، نتأكد من أن زر الطباعة متاح
                    if (canView)
                    {
                        btnSaveAndPrint.IsEnabled = true;
                    }
                }
                // إظهار زر إضافة وصل فقط
                btnAddReceipt.Visibility = Visibility.Visible;
                cmbTreasury.Visibility = Visibility.Collapsed;
                // Set sale info
                _saleId = saleViewModel.Id;
                if (txtSaleNo != null) txtSaleNo.Text = saleViewModel.InvoiceNo.ToString();
                if (DtpSaleDate != null) DtpSaleDate.SelectedDate = saleViewModel.SaleDate;
                if (txtNotes != null) txtNotes.Text = saleViewModel.Sale.Notes;

                // Set warehouse
                if (cmbWarehouse != null && saleViewModel.Sale.WarehouseId > 0)
                {
                    cmbWarehouse.SelectedValue = saleViewModel.Sale.WarehouseId;
                }

                // Set client
                if (cmbClient != null)
                {
                    var client = _clients?.FirstOrDefault(c => c.Name == saleViewModel.ClientName);
                    if (client != null)
                    {
                        cmbClient.SelectedValue = client.Id;
                    }
                }

                // Clear existing items and add sale items
                _saleItems.Clear();
                foreach (var item in saleViewModel.SaleItems)
                {
                    _saleItems.Add(item);
                }

                // Actualizar los valores de la factura desde el modelo
                if (txtTotalBeforeDiscount != null) txtTotalBeforeDiscount.Text = saleViewModel.TotalBeforeDiscount.ToString("N3");
                if (txtTotalPrice != null) txtTotalPrice.Text = saleViewModel.TotalAmount.ToString("N3");
                if (txtTotalReturned != null) txtTotalReturned.Text = saleViewModel.TotalReturned.ToString("N3");
                if (txtNetAmount != null) txtNetAmount.Text = saleViewModel.NetAmount.ToString("N3");

                // Si hay un monto pagado, actualizarlo
                if (txtPaidPrice != null && saleViewModel.PaidAmount > 0)
                {
                    txtPaidPrice.Text = saleViewModel.PaidAmount.ToString("N3");
                }

                // Actualizar el monto restante
                UpdateRemainingAmount();

                // إظهار زر إضافة وصل إذا كان هناك مبلغ متبقي
                if (decimal.TryParse(txtRemaining.Text, out decimal remainingAmount) && remainingAmount > 0)
                {
                    btnAddReceipt.Visibility = Visibility.Visible;
                    cmbTreasury.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء تحميل بيانات الفاتورة: {ex.Message}");
            }
        }
        private void ShowMessage(string title, string message)
        {
            DialogBox.Show(title, message);
        }
        private void ShowError(string message, string title = "خطأ")
        {
            ErrorBox.Show(message, title);
        }
        private async void btnDeleteItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على العنصر المرتبط بالزر من سياق البيانات
                if (sender is Button button)
                {
                    var selectedItem = button.DataContext as SaleItemVM;

                    if (selectedItem != null)
                    {
                        // التحقق مما إذا كان المستخدم لا يملك صلاحية التعديل
                        if (_saleId > 0 && !(CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditSalesRole")))
                        {
                            ShowError("لا يمكن حذف الصنف لأنك لا تملك صلاحية تعديل الفاتورة");
                            return;
                        }

                        // التحقق مما إذا كان هناك استرجاعات في الصنف
                        if (selectedItem.ReturnedQuantity > 0)
                        {
                            ShowError("لا يمكن حذف الصنف لأن هناك استرجاعات مرتبطة به");
                            return;
                        }

                        // عرض رسالة تأكيد قبل الحذف
                        if (QuestionBox.Show(
                            "تأكيد الحذف",
                            "هل أنت متأكد من حذف هذا العنصر؟") == MessageBoxResult.Yes)
                        {
                            // إذا كان العنصر عدسة وله معرف زوج وتم حفظه في قاعدة البيانات
                            if (selectedItem.Type == "عدسة" && selectedItem.PairId.HasValue && selectedItem.Id > 0)
                            {
                                // تحديث معرف الزوج للعدسة المقابلة
                                var (success, message) = await _saleService.UpdatePairIdOnDeleteAsync(selectedItem.Id);
                                if (!success)
                                {
                                    ShowError($"حدث خطأ أثناء تحديث معرف الزوج: {message}");
                                }
                            }

                            // حذف العنصر من القائمة
                            _saleItems.Remove(selectedItem);

                            // تحديث إجمالي السعر
                            UpdateTotalPrice();

                            ShowMessage("تم بنجاح", "تم حذف العنصر من القائمة");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء حذف العنصر: {ex.Message}");
            }
        }
        private void cmbProductColor_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // Ensure the ComboBox and its selected item are valid
                if (cmbProductColor.SelectedItem is not null)
                {
                    if (cmbProductName.SelectedItem is not Product selectedProduct)
                    {
                        ShowError("الرجاء اختيار منتج أولاً");
                        return;
                    }
                    if (selectedProduct.Exp)
                    {
                        var productColor = selectedProduct.ProductColors
                             .FirstOrDefault(x => x.Id == (int)cmbProductColor.SelectedValue);

                        if (productColor != null)
                        {
                            // Filter quantities by selected warehouse
                            var quantities = productColor.ProductQuantity.AsEnumerable();

                            if (cmbWarehouse.SelectedValue != null)
                            {
                                int warehouseId = (int)cmbWarehouse.SelectedValue;
                                quantities = quantities.Where(pq => pq.WarehouseId == warehouseId);
                            }

                            var Exps = quantities
                                .Select(x => new { x.Id, Exp = x.Exp.ToString() })
                                .ToList();

                            CmbProductExpireOn.ItemsSource = Exps;
                            CmbProductExpireOn.DisplayMemberPath = "Exp";
                            CmbProductExpireOn.SelectedValuePath = "Id";
                            CmbProductExpireOn.SelectedIndex = -1;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء تغيير اللون: {ex.Message}");
            }
        }

        private async void btnAddReceipt_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                AddReceiptWindow add = new AddReceiptWindow(decimal.Parse(txtRemaining.Text), saleId: _saleId);
                if (add.ShowDialog() == true) await GetSale(_saleId);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (list.View is GridView gridView && gridView.Columns.Count >= 7)
            {
                double actionColumnWidth = 100; // Fixed width for operations
                double padding = 10;
                double scrollbarWidth = SystemParameters.VerticalScrollBarWidth;

                double availableWidth = list.ActualWidth - actionColumnWidth - scrollbarWidth - padding;

                double totalWeight = 0.5 + 3.0 + 0.5 + 0.5 + 1.0 + 1.0 + 1.0 + 0.8;
                double unitWidth = availableWidth / totalWeight;

                gridView.Columns[0].Width = unitWidth * 0.5; // Index
                gridView.Columns[1].Width = unitWidth * 3.0; // name
                gridView.Columns[2].Width = unitWidth * 0.5; // quantity
                gridView.Columns[3].Width = unitWidth * 0.5; // returned quantity
                gridView.Columns[4].Width = unitWidth * 1.0; // price
                gridView.Columns[5].Width = unitWidth * 1.0; // discount
                gridView.Columns[6].Width = unitWidth * 1.0; // sell price
                gridView.Columns[7].Width = unitWidth * 0.8; // total
                gridView.Columns[8].Width = actionColumnWidth; // Operations
            }
        }

        private void TextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                textBox.SelectAll();
            }
        }


        void Print(int id)
        {
            try
            {
                var printReceipt = QuestionBox.Show("هل تريد طباعة الفاتورة؟", "تأكيد الطباعة");
                var printerNames = GetDefaultPrinterName();
                if (printReceipt == MessageBoxResult.Yes)
                {


                    var generator = new PdfGenerator<SaleItemVM>
                    {
                        HeaderPage = new SalesHeader(int.Parse(txtSaleNo.Text), DateTime.Parse(DtpSaleDate.SelectedDate.Value.ToString()).ToString(), txtphone.Text, cmbClient.Text),
                        ListHeaderPage = new ListerHeader(),
                        FooterPage = new SalesFooter(txtTotalPrice.Text, txtPaidPrice.Text, txtRemaining.Text, txtNotes.Text),
                        ListItemPageBuilder = (item, index) => new PageLister(item, index)
                    };

                    generator.ExportToTallPdf(_saleItems.ToList(), @"C:\PDFs", "RecieptReport", printerNames, false);
                }

                // Ask to print the lenses


                var printer = new SinglePagePdfPrinter();
                int index = 1;

                var lensItems = _saleItems
         .Where(item => item.Type == "عدسة" && (item.Name.Contains("SPH") || item.Name.Contains("CYL") || item.Name.Contains("ADD")))
         .ToList();
                if (lensItems.Count == 0)
                {
                    return;
                }
                var printLenses = QuestionBox.Show("هل تريد طباعة كشف العدسات؟", "تأكيد الطباعة");
                if (printLenses != MessageBoxResult.Yes)
                {
                    return;
                }
                // Group items with PairId
                var pairedGroups = lensItems
                    .Where(i => i.PairId.HasValue)
                    .GroupBy(i => i.PairId.Value)
                    .ToList();

                // Handle paired items
                foreach (var group in pairedGroups)
                {
                    var right = group.FirstOrDefault(i => i.Name.Contains("يمين"));
                    var left = group.FirstOrDefault(i => i.Name.Contains("يسار"));



                    var receipt = new LensesReceiptPrintableVM
                    {
                        Id = right?.Id ?? left?.Id ?? 0,
                        SaleDate = DtpSaleDate.SelectedDate ?? DateTime.Now,
                        ClientName = cmbClient.Text,
                        PhoneNumber = txtphone.Text,
                        TotalPrice = txtTotalPrice.Text,
                        Paid = txtPaidPrice.Text
                    };

                    if (right != null)
                    {
                        receipt.RightSPH = GetLensValue(right.Name, @"SPH:\s*(-?\d+(\.\d+)?|PL)");
                        receipt.RightCYL = GetLensValue(right.Name, @"CYL:\s*(-?\d+(\.\d+)?|PL)");
                        receipt.RightADD = GetLensValue(right.Name, @"ADD[:\s]*(-?\d+(\.\d+)?|PL)");
                        receipt.RightAxis = GetAxisValue(right.Name, @"Axis[:\s]*(\d+(\.\d+)?)");
                    }

                    if (left != null)
                    {
                        receipt.LeftSPH = GetLensValue(left.Name, @"SPH:\s*(-?\d+(\.\d+)?|PL)");
                        receipt.LeftCYL = GetLensValue(left.Name, @"CYL:\s*(-?\d+(\.\d+)?|PL)");
                        receipt.LeftADD = GetLensValue(left.Name, @"ADD[:\s]*(-?\d+(\.\d+)?|PL)");
                        receipt.LeftAxis = GetAxisValue(left.Name, @"Axis[:\s]*(\d+(\.\d+)?)");
                    }

                    string side = (right != null && left != null) ? "Both" : right != null ? "Right" : "Left";
                    string fileName = $"EyeReport_{side}_{index++}_{DateTime.Now:yyyyMMdd_HHmmss}";

                    var printable = new LensesReceiptPrintable(receipt);
                    printer.ExportAndPrint(printable, @"C:\PDFs", fileName, printerNames);
                }

                // Handle unpaired items (PairId == null)
                var unpairedItems = lensItems
                    .Where(i => !i.PairId.HasValue)
                    .ToList();

                foreach (var item in unpairedItems)
                {
                    var isRight = item.Name.Contains("يمين");
                    var isLeft = item.Name.Contains("يسار");



                    var receipt = new LensesReceiptPrintableVM
                    {
                        Id = item.Id,
                        SaleDate = DtpSaleDate.SelectedDate ?? DateTime.Now,
                        ClientName = cmbClient.Text,
                        PhoneNumber = txtphone.Text,
                        TotalPrice = txtTotalPrice.Text,
                        Paid = txtPaidPrice.Text
                    };

                    if (isRight)
                    {
                        receipt.RightSPH = GetLensValue(item.Name, @"SPH:\s*(-?\d+(\.\d+)?|PL)");
                        receipt.RightCYL = GetLensValue(item.Name, @"CYL:\s*(-?\d+(\.\d+)?|PL)");
                        receipt.RightADD = GetLensValue(item.Name, @"ADD[:\s]*(-?\d+(\.\d+)?|PL)");
                        receipt.RightAxis = GetAxisValue(item.Name, @"Axis[:\s]*(\d+(\.\d+)?)");
                    }
                    else if (isLeft)
                    {
                        receipt.LeftSPH = GetLensValue(item.Name, @"SPH:\s*(-?\d+(\.\d+)?|PL)");
                        receipt.LeftCYL = GetLensValue(item.Name, @"CYL:\s*(-?\d+(\.\d+)?|PL)");
                        receipt.LeftADD = GetLensValue(item.Name, @"ADD[:\s]*(-?\d+(\.\d+)?|PL)");
                        receipt.LeftAxis = GetAxisValue(item.Name, @"Axis[:\s]*(\d+(\.\d+)?)");
                    }

                    string side = isRight ? "Right" : "Left";
                    string fileName = $"EyeReport_{side}_{index++}_{DateTime.Now:yyyyMMdd_HHmmss}";

                    var printable = new LensesReceiptPrintable(receipt);
                    printer.ExportAndPrint(printable, @"C:\PDFs", fileName, printerNames);
                }

            }
            catch (Exception ex)
            {
                ErrorBox.Show($"فشل في إنشاء تقرير PDF:\n{ex.Message}", "خطأ", true);
                // Consider more detailed logging
            }
        }


        // Helper
        string GetLensValue(string input, string pattern)
        {
            var match = Regex.Match(input, pattern, RegexOptions.IgnoreCase);
            if (!match.Success) return "";

            var value = match.Groups[1].Value.Trim().ToUpper();

            if (value == "PL") return "PL";

            if (decimal.TryParse(value, System.Globalization.NumberStyles.Any,
                System.Globalization.CultureInfo.InvariantCulture, out var num))
            {
                return num == 0 ? "" : num.ToString("+#0.00;-#0.00");
            }

            return "";
        }


        string GetAxisValue(string input, string pattern)
        {
            var match = Regex.Match(input, pattern, RegexOptions.IgnoreCase);
            if (!match.Success) return "";

            if (decimal.TryParse(match.Groups[1].Value, System.Globalization.NumberStyles.Any,
                System.Globalization.CultureInfo.InvariantCulture, out var num))
            {
                return num == 0 ? "" : Math.Round(num).ToString(); // Remove decimals like 90.0
            }

            return "";
        }


        public string GetDefaultPrinterName()
        {
            try
            {
                string defaultPrinter = Properties.Settings.Default.DefaultPrinter;
                return defaultPrinter;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على الطابعة الافتراضية: {ex.Message}");
                return string.Empty; // Return empty if error occurs
            }
        }

        /// <summary>
        /// تحميل المخازن
        /// </summary>
        private async Task LoadWarehousesAsync()
        {
            try
            {
                _warehouses = await _warehouseService.GetAllWarehousesAsync();

                if (_warehouses != null && _warehouses.Any())
                {
                    cmbWarehouse.ItemsSource = _warehouses;
                    cmbWarehouse.DisplayMemberPath = "Name";
                    cmbWarehouse.SelectedValuePath = "Id";

                    // تحديد المخزن الافتراضي بناءً على المستخدم الحالي

                    if (CurrentUser.WarehouseId.HasValue)
                    {
                        cmbWarehouse.SelectedValue = CurrentUser.WarehouseId.Value;
                    }
                    else
                    {
                        // إذا لم يتم العثور على مخزن افتراضي، اختر الأول
                        cmbWarehouse.SelectedIndex = 0;
                    }

                    // تطبيق منطق الصلاحيات لتغيير المخزن
                    // إذا لم يكن المستخدم مديراً أو لا يملك صلاحية تغيير المخزن، يتم تعطيل الكومبو
                    cmbWarehouse.IsEnabled = CurrentUser.CanChangeWarehouse;
                }
                else
                {
                    ShowError("لا توجد مخازن متاحة. يرجى إضافة مخزن أولاً.");
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء تحميل المخازن: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج تغيير المخزن المختار
        /// </summary>
        private async void cmbWarehouse_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (cmbWarehouse.SelectedValue != null)
                {
                    int selectedWarehouseId = (int)cmbWarehouse.SelectedValue;

                    // إعادة تحميل العملاء حسب المخزن المختار
                    await LoadClientsAsync();

                    // إعادة تحميل طرق الدفع حسب المخزن المختار
                    await LoadTreasuriesAsync();

                    // إعادة تحميل المنتجات حسب المخزن المختار
                    if (RdbProduct != null && RdbProduct.IsChecked == true)
                    {
                        await LoadProductsAsync();
                    }

                    // مسح الحقول الحالية
                    ClearProductInputs();
                    ClearServiceInputs();
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء تغيير المخزن: {ex.Message}");
            }
        }

        private void cmbClient_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (cmbClient.SelectedItem is not null)
            {
                if (cmbClient.SelectedItem is Client selectedClient) txtphone.Text = selectedClient.Phone;
            }
            else txtphone.Text = "";

        }

        /// <summary>
        /// معالجة حدث النقر على زر استرجاع العنصر
        /// </summary>
        private async void btnReturnItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صلاحية المستخدم لإجراء استرجاع العناصر
                if (!(CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ReturnSalesItemRole")))
                {
                    ShowError("ليس لديك صلاحية استرجاع عناصر المبيعات");
                    return;
                }
                // الحصول على العنصر المرتبط بالزر من سياق البيانات
                if (sender is Button button)
                {
                    var selectedItem = button.DataContext as SaleItemVM;

                    if (selectedItem != null)
                    {
                        // التحقق من أن العنصر له معرف صالح (تم حفظه في قاعدة البيانات)
                        if (selectedItem.Id <= 0)
                        {
                            ShowError("لا يمكن استرجاع عنصر غير محفوظ في قاعدة البيانات");
                            return;
                        }

                        // التحقق من أن هناك كمية متبقية يمكن استرجاعها
                        if (selectedItem.RemainingQuantity <= 0)
                        {
                            ShowError("لا توجد كمية متبقية لاسترجاعها");
                            return;
                        }

                        // طلب الكمية المراد استرجاعها من المستخدم
                        var inputDialog = new InputDialog("استرجاع كمية", "أدخل الكمية المراد استرجاعها:", selectedItem.RemainingQuantity.ToString());
                        if (inputDialog.ShowDialog() == true)
                        {
                            // التحقق من صحة الإدخال
                            if (int.TryParse(inputDialog.Answer, out int returnQuantity) && returnQuantity > 0)
                            {
                                // التحقق من أن الكمية المدخلة لا تتجاوز الكمية المتبقية
                                if (returnQuantity > selectedItem.RemainingQuantity)
                                {
                                    ShowError($"الكمية المدخلة ({returnQuantity}) تتجاوز الكمية المتبقية ({selectedItem.RemainingQuantity})");
                                    return;
                                }

                                // تأكيد العملية
                                if (QuestionBox.Show(
                                    "تأكيد الاسترجاع",
                                    $"هل أنت متأكد من استرجاع {returnQuantity} من {selectedItem.Name}؟") == MessageBoxResult.Yes)
                                {
                                    // استدعاء خدمة الاسترجاع
                                    var (success, message) = await _saleService.ReturnItemAsync(selectedItem.Id, returnQuantity);

                                    if (success)
                                    {
                                        // تحديث العنصر في الواجهة
                                        selectedItem.ReturnedQuantity += returnQuantity;

                                        // تحديث إجمالي السعر
                                        UpdateTotalPrice();

                                        ShowMessage("تم بنجاح", "تم استرجاع الكمية بنجاح");

                                        // إعادة تحميل الفاتورة لتحديث جميع البيانات
                                        await GetSale(_saleId);
                                    }
                                    else
                                    {
                                        ShowError(message);
                                    }
                                }
                            }
                            else
                            {
                                ShowError("الرجاء إدخال كمية صحيحة أكبر من صفر");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء استرجاع العنصر: {ex.Message}");
            }
        }

        private void btnBack_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // Close the page by navigating back if possible
                if (NavigationService != null && NavigationService.CanGoBack)
                {
                    NavigationService.GoBack();
                }
                else
                {
                    // Optionally, navigate to a default page or clear the content
                    if (this.Parent is Frame frame)
                    {
                        frame.Content = null;
                    }
                }
            }
            finally
            {
                EnableAllButtons();
            }
        }
    }
}
