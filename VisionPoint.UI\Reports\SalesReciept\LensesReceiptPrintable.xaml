﻿<Page
    x:Class="VisionPoint.UI.Reports.SalesReciept.LensesReceiptPrintable"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.Reports.SalesReciept"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="LensesReceiptPrintable"
    Width="800"
    Height="450"
    Background="White"
    FlowDirection="RightToLeft"
    mc:Ignorable="d">
    <Grid Margin="16">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <TextBlock
            x:Name="CNameAr"
            Grid.Row="0"
            Grid.Column="0"
            Grid.ColumnSpan="5"
            VerticalAlignment="Center"
            FontSize="21"
            FontWeight="Bold"
            Text="مجموعة النمارق للبصريات"
            TextAlignment="Center" />

        <TextBlock
            Grid.Row="1"
            Grid.Column="0"
            Grid.ColumnSpan="5"
            VerticalAlignment="Top"
            FontSize="14"
            Text="للنظارات الطبية والشمسية والعدسات اللاصقة والكشف بالكمبيوتر"
            TextAlignment="Center"
            TextWrapping="Wrap" />

        <TextBlock
            x:Name="ArabicAddress"
            Grid.Row="2"
            Grid.Column="0"
            Grid.ColumnSpan="5"
            VerticalAlignment="Top"
            FontSize="14"
            Text=""
            TextAlignment="Center"
            TextWrapping="Wrap" />
        <DockPanel
            Grid.Row="3"
            Grid.Column="0"
            Grid.ColumnSpan="4"
            Margin="16,0"
            HorizontalAlignment="Center"
            VerticalAlignment="Center">
            <TextBlock
                VerticalAlignment="Top"
                FontSize="14"
                Text="هاتف : "
                TextAlignment="Center"
                TextWrapping="Wrap" />
            <TextBlock
                x:Name="Phone"
                VerticalAlignment="Top"
                d:Text="092000000"
                FontSize="14"
                TextAlignment="Left"
                TextWrapping="Wrap" />
        </DockPanel>



        <Image
            x:Name="LogoImageElement"
            Grid.Row="0"
            Grid.RowSpan="3"
            Grid.Column="5"
            Grid.ColumnSpan="3"
            MaxHeight="80"
            VerticalAlignment="Center"
            Source="{x:Null}"
            Stretch="Uniform" />



        <TextBlock
            x:Name="CNameEn"
            Grid.Row="0"
            Grid.Column="8"
            Grid.ColumnSpan="5"
            VerticalAlignment="Center"
            FlowDirection="LeftToRight"
            FontSize="21"
            FontWeight="Bold"
            Text="Namariq Optics Group"
            TextAlignment="Center" />

        <TextBlock
            Grid.Row="1"
            Grid.Column="8"
            Grid.ColumnSpan="5"
            VerticalAlignment="Top"
            FlowDirection="LeftToRight"
            FontSize="14"
            Text="Optics, Sunglasses, Contact Lenses ,and Computer Eye Examination"
            TextAlignment="Center"
            TextWrapping="Wrap" />

        <TextBlock
            x:Name="EnglishAddress"
            Grid.Row="2"
            Grid.Column="8"
            Grid.ColumnSpan="5"
            VerticalAlignment="Top"
            FlowDirection="LeftToRight"
            FontSize="14"
            Text=""
            TextAlignment="Center"
            TextWrapping="Wrap" />

        <DockPanel
            Grid.Row="3"
            Grid.Column="8"
            Grid.ColumnSpan="5"
            Margin="16,0"
            VerticalAlignment="Center"
            FlowDirection="LeftToRight">
            <TextBlock
                VerticalAlignment="Top"
                FontSize="14"
                Text="Phone : "
                TextAlignment="Center"
                TextWrapping="Wrap" />
            <TextBlock
                x:Name="EnglishPhone"
                VerticalAlignment="Top"
                d:Text="092000000"
                FontSize="14"
                TextAlignment="Left"
                TextWrapping="Wrap" />
        </DockPanel>

        <DockPanel
            Grid.Row="4"
            Grid.Column="0"
            Grid.ColumnSpan="4"
            Margin="16,8"
            VerticalAlignment="Center"
            LastChildFill="True">
            <TextBlock
                VerticalAlignment="Top"
                DockPanel.Dock="Left"
                FontSize="18"
                Text="التاريخ"
                TextAlignment="Center"
                TextWrapping="Wrap" />

            <TextBlock
                VerticalAlignment="Top"
                DockPanel.Dock="Right"
                FontSize="18"
                Text="Date"
                TextAlignment="Center"
                TextWrapping="Wrap" />
            <TextBlock
                x:Name="ReceiptDate"
                VerticalAlignment="Top"
                d:Text="24/10/2025"
                DockPanel.Dock="Left"
                FontSize="18"
                TextAlignment="Center"
                TextWrapping="Wrap" />

        </DockPanel>

        <TextBlock
            Grid.Row="4"
            Grid.Column="7"
            Grid.ColumnSpan="5"
            VerticalAlignment="Bottom"
            FlowDirection="LeftToRight"
            FontSize="18"
            FontWeight="Bold"
            Text="وصفة نظارة طبية"
            TextAlignment="Center"
            TextWrapping="Wrap" />

        <Border
            Grid.Row="5"
            Grid.RowSpan="4"
            Grid.Column="6"
            Grid.ColumnSpan="3"
            Margin="8"
            Background="#FFE81F25"
            BorderBrush="#FFE81F25"
            BorderThickness="1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition />
                    <ColumnDefinition />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>

                <DockPanel
                    Grid.ColumnSpan="3"
                    Margin="16,0"
                    HorizontalAlignment="Stretch"
                    LastChildFill="False">

                    <TextBlock
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Center"
                        DockPanel.Dock="Left"
                        FontSize="18"
                        Foreground="White"
                        Text="شمال"
                        TextAlignment="Center" />

                    <TextBlock
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Center"
                        DockPanel.Dock="Right"
                        FontSize="18"
                        Foreground="White"
                        Text="Left"
                        TextAlignment="Center" />
                </DockPanel>

                <Border
                    Grid.Row="1"
                    Grid.Column="0"
                    VerticalAlignment="Stretch">
                    <Border.Background>
                        <SolidColorBrush Opacity="0.6" Color="White" />
                    </Border.Background>
                    <TextBlock
                        VerticalAlignment="Center"
                        Text="Axis"
                        TextAlignment="Center" />
                </Border>

                <Border
                    Grid.Row="1"
                    Grid.Column="1"
                    VerticalAlignment="Stretch">
                    <Border.Background>
                        <SolidColorBrush Opacity="0.6" Color="White" />
                    </Border.Background>
                    <TextBlock
                        VerticalAlignment="Center"
                        Text="CYL"
                        TextAlignment="Center" />
                </Border>

                <Border
                    Grid.Row="1"
                    Grid.Column="2"
                    VerticalAlignment="Stretch">
                    <Border.Background>
                        <SolidColorBrush Opacity="0.6" Color="White" />
                    </Border.Background>
                    <TextBlock
                        VerticalAlignment="Center"
                        Text="SPH"
                        TextAlignment="Center" />
                </Border>

                <!--  Duplicate row with 1 added  -->
                <Border
                    Grid.Row="2"
                    Grid.Column="0"
                    VerticalAlignment="Stretch"
                    FlowDirection="LeftToRight">
                    <Border.Background>
                        <SolidColorBrush Color="White" />
                    </Border.Background>
                    <TextBlock
                        x:Name="LeftAxis"
                        VerticalAlignment="Center"
                        d:Text="180"
                        FontSize="16"
                        TextAlignment="Center" />
                </Border>

                <Border
                    Grid.Row="2"
                    Grid.Column="1"
                    VerticalAlignment="Stretch"
                    FlowDirection="LeftToRight">
                    <Border.Background>
                        <SolidColorBrush Color="White" />
                    </Border.Background>
                    <TextBlock
                        x:Name="LeftCYL"
                        VerticalAlignment="Center"
                        d:Text="-1.5"
                        FontSize="16"
                        TextAlignment="Center" />
                </Border>

                <Border
                    Grid.Row="2"
                    Grid.Column="2"
                    VerticalAlignment="Stretch"
                    FlowDirection="LeftToRight">
                    <Border.Background>
                        <SolidColorBrush Color="White" />
                    </Border.Background>
                    <TextBlock
                        x:Name="LeftSPH"
                        VerticalAlignment="Center"
                        d:Text="+1.5"
                        FontSize="16"
                        TextAlignment="Center" />
                </Border>

                <!--  Final row additions  -->
                <Border
                    Grid.Row="3"
                    Grid.Column="0"
                    Grid.ColumnSpan="2"
                    VerticalAlignment="Stretch"
                    FlowDirection="LeftToRight">
                    <Border.Background>
                        <SolidColorBrush Color="White" />
                    </Border.Background>
                    <TextBlock
                        x:Name="LeftADD"
                        VerticalAlignment="Center"
                        d:Text="+1.5"
                        FontSize="16"
                        TextAlignment="Center" />
                </Border>

                <Border
                    Grid.Row="3"
                    Grid.Column="2"
                    VerticalAlignment="Stretch">
                    <Border.Background>
                        <SolidColorBrush Color="White" />
                    </Border.Background>
                    <TextBlock
                        VerticalAlignment="Center"
                        Text="ADD"
                        TextAlignment="Center" />
                </Border>
            </Grid>
        </Border>

        <Border
            Grid.Row="5"
            Grid.RowSpan="4"
            Grid.Column="10"
            Grid.ColumnSpan="3"
            Margin="8"
            Background="#FFE81F25"
            BorderBrush="#FFE81F25"
            BorderThickness="1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition />
                    <ColumnDefinition />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>

                <DockPanel
                    Grid.ColumnSpan="3"
                    Margin="16,0"
                    HorizontalAlignment="Stretch"
                    LastChildFill="False">

                    <TextBlock
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Center"
                        DockPanel.Dock="Left"
                        FontSize="18"
                        Foreground="White"
                        Text="يمين"
                        TextAlignment="Center" />

                    <TextBlock
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Center"
                        DockPanel.Dock="Right"
                        FontSize="18"
                        Foreground="White"
                        Text="Right"
                        TextAlignment="Center" />
                </DockPanel>

                <Border
                    Grid.Row="1"
                    Grid.Column="0"
                    VerticalAlignment="Stretch">
                    <Border.Background>
                        <SolidColorBrush Opacity="0.6" Color="White" />
                    </Border.Background>
                    <TextBlock
                        VerticalAlignment="Center"
                        Text="Axis"
                        TextAlignment="Center" />
                </Border>

                <Border
                    Grid.Row="1"
                    Grid.Column="1"
                    VerticalAlignment="Stretch">
                    <Border.Background>
                        <SolidColorBrush Opacity="0.6" Color="White" />
                    </Border.Background>
                    <TextBlock
                        VerticalAlignment="Center"
                        Text="CYL"
                        TextAlignment="Center" />
                </Border>

                <Border
                    Grid.Row="1"
                    Grid.Column="2"
                    VerticalAlignment="Stretch">
                    <Border.Background>
                        <SolidColorBrush Opacity="0.6" Color="White" />
                    </Border.Background>
                    <TextBlock
                        VerticalAlignment="Center"
                        Text="SPH"
                        TextAlignment="Center" />
                </Border>

                <!--  Duplicate row with 1 added  -->
                <Border
                    Grid.Row="2"
                    Grid.Column="0"
                    VerticalAlignment="Stretch"
                    FlowDirection="LeftToRight">
                    <Border.Background>
                        <SolidColorBrush Color="White" />
                    </Border.Background>
                    <TextBlock
                        x:Name="RightAxis"
                        VerticalAlignment="Center"
                        d:Text="180"
                        FontSize="16"
                        TextAlignment="Center" />
                </Border>

                <Border
                    Grid.Row="2"
                    Grid.Column="1"
                    VerticalAlignment="Stretch"
                    FlowDirection="LeftToRight">
                    <Border.Background>
                        <SolidColorBrush Color="White" />
                    </Border.Background>
                    <TextBlock
                        x:Name="RightCYL"
                        VerticalAlignment="Center"
                        d:Text="-1.5"
                        FontSize="16"
                        TextAlignment="Center" />
                </Border>

                <Border
                    Grid.Row="2"
                    Grid.Column="2"
                    VerticalAlignment="Stretch"
                    FlowDirection="LeftToRight">
                    <Border.Background>
                        <SolidColorBrush Color="White" />
                    </Border.Background>
                    <TextBlock
                        x:Name="RightSPH"
                        VerticalAlignment="Center"
                        d:Text="+1.5"
                        FontSize="16"
                        TextAlignment="Center" />
                </Border>

                <!--  Final row additions  -->
                <Border
                    Grid.Row="3"
                    Grid.Column="0"
                    Grid.ColumnSpan="2"
                    VerticalAlignment="Stretch"
                    FlowDirection="LeftToRight">
                    <Border.Background>
                        <SolidColorBrush Color="White" />
                    </Border.Background>
                    <TextBlock
                        x:Name="RightADD"
                        VerticalAlignment="Center"
                        d:Text="+1.5"
                        FontSize="16"
                        TextAlignment="Center" />
                </Border>

                <Border
                    Grid.Row="3"
                    Grid.Column="2"
                    VerticalAlignment="Stretch">
                    <Border.Background>
                        <SolidColorBrush Color="White" />
                    </Border.Background>
                    <TextBlock
                        VerticalAlignment="Center"
                        FontSize="16"
                        Text="ADD"
                        TextAlignment="Center" />
                </Border>
            </Grid>
        </Border>


        <DockPanel
            Grid.Row="5"
            Grid.RowSpan="4"
            Grid.Column="0"
            Grid.ColumnSpan="4"
            Margin="16,0"
            VerticalAlignment="Center"
            LastChildFill="True">
            <TextBlock
                VerticalAlignment="Top"
                DockPanel.Dock="Top"
                FontSize="18"
                Text="رقم الفاتورة"
                TextAlignment="Center"
                TextWrapping="Wrap" />

            <Image
                x:Name="imgBarcode"
                Grid.Row="2"
                Grid.RowSpan="3"
                Grid.Column="5"
                Grid.ColumnSpan="3"
                MaxHeight="80"
                VerticalAlignment="Center"
                DockPanel.Dock="Top"
                Stretch="Uniform" />




        </DockPanel>

        <!--  Info lines with dotted space for handwritten/printed input  -->
        <!--  Row 9  -->
        <!--  Row 9: الاسم  -->
        <TextBlock
            Grid.Row="9"
            Grid.Column="0"
            Grid.ColumnSpan="2"
            Margin="8,0"
            VerticalAlignment="Center"
            FontSize="14"
            Text="الاسم"
            TextWrapping="NoWrap" />
        <TextBlock
            Grid.Row="9"
            Grid.Column="2"
            Grid.ColumnSpan="6"
            Margin="8,0"
            VerticalAlignment="Bottom"
            FontSize="14"
            Text="......................................................................................................................................................................................................."
            TextWrapping="NoWrap" />
        <TextBlock
            x:Name="txtCustomerName"
            Grid.Row="9"
            Grid.Column="2"
            Grid.ColumnSpan="6"
            Margin="8,0"
            VerticalAlignment="Center"
            FontSize="14"
            Text=""
            TextWrapping="NoWrap" />

        <!--  Row 9: هاتف الزبون  -->
        <TextBlock
            Grid.Row="9"
            Grid.Column="8"
            Grid.ColumnSpan="2"
            Margin="8,0"
            VerticalAlignment="Center"
            FontSize="14"
            Text="هاتف الزبون"
            TextWrapping="NoWrap" />
        <TextBlock
            Grid.Row="9"
            Grid.Column="10"
            Grid.ColumnSpan="4"
            Margin="8,0"
            VerticalAlignment="Bottom"
            FontSize="14"
            Text="......................................................................................................................................................................................................."
            TextWrapping="NoWrap" />
        <TextBlock
            x:Name="txtCustomerPhone"
            Grid.Row="9"
            Grid.Column="10"
            Grid.ColumnSpan="4"
            Margin="8,0"
            VerticalAlignment="Center"
            FontSize="14"
            Text=""
            TextWrapping="NoWrap" />


        <!--  Row 10: اجمالي المبلغ  -->
        <TextBlock
            Grid.Row="10"
            Grid.Column="0"
            Grid.ColumnSpan="2"
            Margin="8,0"
            VerticalAlignment="Center"
            FontSize="14"
            Text="اجمالي المبلغ"
            TextWrapping="NoWrap" />
        <TextBlock
            Grid.Row="10"
            Grid.Column="2"
            Grid.ColumnSpan="2"
            Margin="8,0"
            VerticalAlignment="Bottom"
            FontSize="14"
            Text="......................................................................................................................................................................................................."
            TextWrapping="NoWrap" />
        <TextBlock
            x:Name="txtTotalAmount"
            Grid.Row="10"
            Grid.Column="2"
            Grid.ColumnSpan="2"
            Margin="8,0"
            VerticalAlignment="Center"
            FontSize="14"
            Text=""
            TextWrapping="NoWrap" />

        <!--  Row 10: المدفوع  -->
        <TextBlock
            Grid.Row="10"
            Grid.Column="4"
            Grid.ColumnSpan="2"
            Margin="8,0"
            VerticalAlignment="Center"
            FontSize="14"
            Text="المدفوع"
            TextWrapping="NoWrap" />
        <TextBlock
            Grid.Row="10"
            Grid.Column="6"
            Grid.ColumnSpan="2"
            Margin="8,0"
            VerticalAlignment="Bottom"
            FontSize="14"
            Text="......................................................................................................................................................................................................."
            TextWrapping="NoWrap" />
        <TextBlock
            x:Name="txtPaidAmount"
            Grid.Row="10"
            Grid.Column="6"
            Grid.ColumnSpan="2"
            Margin="8,0"
            VerticalAlignment="Center"
            FontSize="14"
            Text=""
            TextWrapping="NoWrap" />

        <!--  Row 10: الباقي  -->
        <TextBlock
            Grid.Row="10"
            Grid.Column="8"
            Grid.ColumnSpan="2"
            Margin="8,0"
            VerticalAlignment="Center"
            FontSize="14"
            Text="الباقي"
            TextWrapping="NoWrap" />
        <TextBlock
            Grid.Row="10"
            Grid.Column="10"
            Grid.ColumnSpan="4"
            Margin="8,0"
            VerticalAlignment="Bottom"
            FontSize="14"
            Text="......................................................................................................................................................................................................."
            TextWrapping="NoWrap" />
        <TextBlock
            x:Name="txtRemainingAmount"
            Grid.Row="10"
            Grid.Column="10"
            Grid.ColumnSpan="4"
            Margin="8,0"
            VerticalAlignment="Center"
            FontSize="14"
            Text=""
            TextWrapping="NoWrap" />


        <!--  Row 11: تاريخ الاستلام  -->
        <TextBlock
            Grid.Row="11"
            Grid.Column="0"
            Grid.ColumnSpan="2"
            Margin="8,0"
            VerticalAlignment="Center"
            FontSize="14"
            Text="تاريخ الاستلام"
            TextWrapping="NoWrap" />
        <TextBlock
            Grid.Row="11"
            Grid.Column="2"
            Grid.ColumnSpan="2"
            Margin="8,0"
            VerticalAlignment="Bottom"
            FontSize="14"
            Text="......................................................................................................................................................................................................."
            TextWrapping="NoWrap" />

        <!--  Row 11: ملاحظة  -->
        <TextBlock
            Grid.Row="11"
            Grid.Column="4"
            Grid.ColumnSpan="2"
            Margin="8,0"
            VerticalAlignment="Center"
            FontSize="14"
            Text="ملاحظة"
            TextWrapping="NoWrap" />
        <TextBlock
            Grid.Row="11"
            Grid.Column="6"
            Grid.ColumnSpan="10"
            Margin="8,0"
            VerticalAlignment="Bottom"
            FontSize="14"
            Text="......................................................................................................................................................................................................."
            TextWrapping="NoWrap" />
        <TextBlock
            Grid.Row="11"
            Grid.Column="6"
            Grid.ColumnSpan="10"
            Margin="8,0"
            VerticalAlignment="Center"
            FontSize="14"
            Text=""
            TextWrapping="NoWrap" />


    </Grid>
</Page>