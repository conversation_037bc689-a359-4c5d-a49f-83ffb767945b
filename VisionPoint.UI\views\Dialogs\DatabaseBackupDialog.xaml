﻿<Window
    x:Class="VisionPoint.UI.views.Dialogs.DatabaseBackupDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:VisionPoint.UI"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Dialogs"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="النسخ الاحتياطي لقاعدة البيانات"
    AllowsTransparency="True"
    Background="Transparent"
    FlowDirection="RightToLeft"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">
        <Grid Width="1920" Height="1080">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="9*" />
                <ColumnDefinition Width="5*" />
                <ColumnDefinition Width="9*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="16*" />
                <RowDefinition Height="11*" />
                <RowDefinition Height="16*" />
            </Grid.RowDefinitions>

            <!--  Centered 425x275 element  -->
            <Border
                Grid.Row="1"
                Grid.Column="1"
                Padding="8"
                Background="{StaticResource backgroundColor}"
                CornerRadius="24">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <TextBlock
                        Grid.Row="0"
                        Margin="20,20,20,10"
                        HorizontalAlignment="Center"
                        FontSize="16"
                        FontWeight="Bold"
                        Text="جاري إنشاء نسخة احتياطية..." />

                    <StackPanel
                        Grid.Row="1"
                        Margin="20"
                        VerticalAlignment="Center">
                        <ProgressBar
                            x:Name="progressBar"
                            Height="20"
                            Margin="0,10"
                            IsIndeterminate="True" />

                        <TextBlock
                            x:Name="statusText"
                            Margin="0,10"
                            Text="جاري تنفيذ العملية..."
                            TextAlignment="Center"
                            TextWrapping="Wrap" />
                    </StackPanel>



                    <Grid Grid.Row="2" Height="50">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="2*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="2*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Border
                            x:Name="btnCancel"
                            Grid.Column="1"
                            Background="{StaticResource errorColor}"
                            CornerRadius="16"
                            IsEnabled="False"
                            MouseLeftButtonDown="btnCancel_Click">
                            <Label
                                HorizontalContentAlignment="Center"
                                VerticalContentAlignment="Center"
                                Content="إلغاء"
                                Foreground="White" />
                        </Border>
                        <Border
                            x:Name="btnClose"
                            Grid.Column="3"
                            Background="Transparent"
                            BorderBrush="{StaticResource errorColor}"
                            BorderThickness="1"
                            CornerRadius="16"
                            MouseLeftButtonDown="btnClose_Click">
                            <Label
                                HorizontalContentAlignment="Center"
                                VerticalContentAlignment="Center"
                                Content="إغلاق"
                                Foreground="{StaticResource errorColor}" />
                        </Border>
                    </Grid>

                </Grid>
            </Border>
        </Grid>
    </Viewbox>
</Window>

