﻿<Window
    x:Class="VisionPoint.UI.views.Pages.MinimumQuantity.MinimumQuantityWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:VisionPoint.UI"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.MinimumQuantity"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils"
    Title="MinimumQuantityWindow"
    AllowsTransparency="True"
    Background="Transparent"
    FlowDirection="RightToLeft"
    Loaded="Window_Loaded"
    ResizeMode="NoResize"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">
        <Border
            Width="1920"
            Height="1080"
            Background="{StaticResource backgroundColor}"
            BorderBrush="LightGray"
            BorderThickness="1.5"
            CornerRadius="16">



            <Grid Margin="16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition />
                    <ColumnDefinition />
                    <ColumnDefinition />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>

                <Border
                    x:Name="btnclose"
                    Grid.ColumnSpan="4"
                    Width="24"
                    Height="24"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Top"
                    Background="Red"
                    CornerRadius="50"
                    MouseLeftButtonDown="btnclose_MouseLeftButtonDown" />


                <TextBlock
                    Grid.ColumnSpan="8"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="24"
                    FontWeight="Bold"
                    TextAlignment="Center">
                    المنتجات التي وصلت للحد الأدنى
                </TextBlock>


                <Grid
                    Grid.Row="1"
                    Grid.ColumnSpan="4"
                    Margin="0,10,0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition />
                        <ColumnDefinition />
                        <ColumnDefinition />
                    </Grid.ColumnDefinitions>

                    <RadioButton
                        x:Name="RdbProductSelect"
                        Grid.Column="0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Checked="Rdb_Checked"
                        Content="المنتجات"
                        GroupName="ItemSelect" />

                    <RadioButton
                        x:Name="RdbLensesSelect"
                        Grid.Column="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Checked="Rdb_Checked"
                        Content="العدسات"
                        GroupName="ItemSelect" />

                </Grid>

                <!-- إضافة ComboBox لاختيار المخزن -->
                <Grid
                    Grid.Row="1"
                    Grid.Column="3"
                    Grid.ColumnSpan="4"
                    Margin="0,10,0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <TextBlock
                        Grid.Column="0"
                        Margin="0,0,10,0"
                        VerticalAlignment="Center"
                        FontSize="18"
                        FontWeight="Bold"
                        Text="المخزن:" />

                    <ComboBox
                        x:Name="cmbWarehouse"
                        Grid.Column="1"
                        Height="50"
                        FontSize="18"
                        DisplayMemberPath="Name"
                        SelectedValuePath="Id"
                        SelectionChanged="cmbWarehouse_SelectionChanged" />
                </Grid>

                <ListView
                    x:Name="list"
                    Grid.Row="2"
                    Grid.RowSpan="12"
                    Grid.ColumnSpan="4"
                    Background="{DynamicResource PageColor}"
                    BorderThickness="1"
                    FontFamily="pack://application:,,,/Assets/#Cairo"
                    ItemsSource="{Binding}"
                    ScrollViewer.HorizontalScrollBarVisibility="Hidden"
                    SizeChanged="list_SizeChanged">
                    <ListView.BorderBrush>
                        <SolidColorBrush Opacity="0.42" Color="Black" />
                    </ListView.BorderBrush>

                    <ListView.ItemContainerStyle>
                        <Style TargetType="ListViewItem">
                            <Style.Triggers>
                                <Trigger Property="Control.IsMouseOver" Value="True">
                                    <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                                    <Setter Property="FontWeight" Value="Bold" />
                                    <Setter Property="Foreground" Value="Black" />
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                                    <Setter Property="Foreground" Value="Black" />
                                </Trigger>

                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="FontWeight" Value="Bold" />
                                    <Setter Property="Foreground" Value="Black" />
                                </Trigger>

                                <MultiTrigger>
                                    <MultiTrigger.Conditions>
                                        <Condition Property="IsSelected" Value="False" />
                                        <Condition Property="IsMouseOver" Value="False" />
                                    </MultiTrigger.Conditions>
                                    <Setter Property="FontWeight" Value="Thin" />
                                    <Setter Property="Foreground" Value="{DynamicResource PrimaryTextColor}" />
                                </MultiTrigger>

                            </Style.Triggers>
                            <Setter Property="Control.Background" Value="{DynamicResource PageColor}" />
                            <Setter Property="Control.HorizontalContentAlignment" Value="Stretch" />
                        </Style>

                    </ListView.ItemContainerStyle>
                    <ListView.View>
                        <GridView AllowsColumnReorder="False">
                            <GridView.ColumnHeaderContainerStyle>
                                <Style BasedOn="{StaticResource ListViewHeader}" TargetType="{x:Type GridViewColumnHeader}">
                                    <Setter Property="IsEnabled" Value="False" />
                                    <Setter Property="Height" Value="60" />
                                    <Style.Triggers>
                                        <Trigger Property="IsEnabled" Value="False">
                                            <Setter Property="TextElement.Foreground" Value="Black" />
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </GridView.ColumnHeaderContainerStyle>

                            <GridViewColumn Width="Auto" Header="الاسم">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="45"
                                            MinWidth="250"
                                            HorizontalAlignment="Center"
                                            Background="Transparent"
                                            Text="{Binding FullName, FallbackValue='غير معروف', TargetNullValue='غير معروف'}"
                                            TextAlignment="Center" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>

                            <GridViewColumn Width="Auto" Header="الكمية الحالية">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="45"
                                            MinWidth="100"
                                            HorizontalAlignment="Center"
                                            Background="Transparent"
                                            Text="{Binding CurrentQuantity, FallbackValue='0', TargetNullValue='0'}"
                                            TextAlignment="Center" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>

                            <GridViewColumn Width="Auto" Header="الحد الأدنى">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="45"
                                            MinWidth="100"
                                            HorizontalAlignment="Center"
                                            Background="Transparent"
                                            Text="{Binding MinimumQuantity, FallbackValue='0', TargetNullValue='0'}"
                                            TextAlignment="Center" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>

                            <GridViewColumn Width="Auto" Header="النوع">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="45"
                                            MinWidth="60"
                                            HorizontalAlignment="Center"
                                            Background="Transparent"
                                            Text="{Binding ItemType, FallbackValue='غير محدد', TargetNullValue='غير محدد'}"
                                            TextAlignment="Center" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>

                            <GridViewColumn Width="Auto" Header="المخزن">
                                <GridViewColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Height="45"
                                            MinWidth="120"
                                            HorizontalAlignment="Center"
                                            Background="Transparent"
                                            Text="{Binding WarehouseName, FallbackValue='غير محدد', TargetNullValue='غير محدد'}"
                                            TextAlignment="Center" />
                                    </DataTemplate>
                                </GridViewColumn.CellTemplate>
                            </GridViewColumn>

                        </GridView>
                    </ListView.View>
                </ListView>

            </Grid>
        </Border>
    </Viewbox>
</Window>