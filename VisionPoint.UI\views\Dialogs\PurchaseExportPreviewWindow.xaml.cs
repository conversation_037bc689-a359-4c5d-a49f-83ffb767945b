using Microsoft.Win32;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.ViewModel;

namespace VisionPoint.UI.views.Dialogs
{
    /// <summary>
    /// نافذة معاينة تصدير المشتريات
    /// </summary>
    public partial class PurchaseExportPreviewWindow : Window
    {
        private readonly ExportPreviewModel _model;
        private readonly PurchaseService _purchaseService;
        private string _selectedSavePath = string.Empty;
        private bool _isExporting = false;

        public bool ExportCompleted { get; private set; } = false;

        public PurchaseExportPreviewWindow(List<PurchaseViewModel> purchasesData, int? clientId = null, int? warehouseId = null)
        {
            InitializeComponent();

            _purchaseService = new PurchaseService();
            _model = new ExportPreviewModel
            {
                PurchasesData = purchasesData,
                ClientId = clientId,
                WarehouseId = warehouseId,
                FileName = $"تقرير_المشتريات_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
            };

            DataContext = _model;

            // تهيئة النافذة
            InitializeWindow();
        }

        private async void InitializeWindow()
        {
            try
            {
                // تحميل بيانات الأصناف
                await LoadPurchaseItemsData();

                // تهيئة حقول التصدير
                InitializeExportFields();

                // تطبيق التفضيلات المحفوظة
                ApplyPreferences();

                // تحديث المعاينة
                UpdatePreview();

                // تحديث الملخص
                UpdateSummary();

                // تحديث حالة التبويبات
                UpdateTabsState();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تهيئة النافذة: {ex.Message}", "خطأ", true);
            }
        }

        private async Task LoadPurchaseItemsData()
        {
            try
            {
                // جلب جميع معرفات فواتير المشتريات
                var purchaseIds = _model.PurchasesData.Select(p => p.Id).ToList();

                // جلب جميع الأصناف في استعلام واحد محسن
                var allItems = await _purchaseService.GetPurchaseItemsByPurchaseIdsAsync(purchaseIds);

                _model.PurchaseItemsData = allItems;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل بيانات الأصناف: {ex.Message}", "خطأ", true);
            }
        }

        private void InitializeExportFields()
        {
            // حقول الفواتير
            _model.InvoiceFields = new List<ExportFieldOption>
            {
                new() { FieldKey = "Index", DefaultName = "الرقم التسلسلي", IsSelected = true, IsRequired = false },
                new() { FieldKey = "InvoiceNumber", DefaultName = "رقم الفاتورة", IsSelected = true, IsRequired = true },
                new() { FieldKey = "PurchaseDate", DefaultName = "تاريخ الفاتورة", IsSelected = true, IsRequired = false },
                new() { FieldKey = "ClientName", DefaultName = "المورد", IsSelected = true, IsRequired = false },
                new() { FieldKey = "WarehouseName", DefaultName = "المخزن", IsSelected = true, IsRequired = false },
                new() { FieldKey = "TotalBeforeDiscount", DefaultName = "الإجمالي قبل الخصم", IsSelected = true, IsRequired = false },
                new() { FieldKey = "TotalDiscount", DefaultName = "إجمالي الخصم", IsSelected = true, IsRequired = false },
                new() { FieldKey = "TotalAmount", DefaultName = "إجمالي الفاتورة", IsSelected = true, IsRequired = false },
                new() { FieldKey = "PaidAmount", DefaultName = "المدفوع", IsSelected = true, IsRequired = false },
                new() { FieldKey = "RemainingAmount", DefaultName = "المتبقي", IsSelected = true, IsRequired = false }
            };

            // حقول الأصناف
            _model.ItemFields = new List<ExportFieldOption>
            {
                new() { FieldKey = "Index", DefaultName = "الرقم التسلسلي", IsSelected = true, IsRequired = false },
                new() { FieldKey = "PurchaseId", DefaultName = "رقم الفاتورة", IsSelected = true, IsRequired = true },
                new() { FieldKey = "ClientName", DefaultName = "المورد", IsSelected = true, IsRequired = false },
                new() { FieldKey = "WarehouseName", DefaultName = "المخزن", IsSelected = true, IsRequired = false },
                new() { FieldKey = "Type", DefaultName = "نوع الصنف", IsSelected = true, IsRequired = false },
                new() { FieldKey = "Name", DefaultName = "اسم الصنف", IsSelected = true, IsRequired = true },
                new() { FieldKey = "ColorName", DefaultName = "اللون", IsSelected = true, IsRequired = false },
                new() { FieldKey = "Quantity", DefaultName = "الكمية", IsSelected = true, IsRequired = false },
                new() { FieldKey = "Price", DefaultName = "سعر الوحدة", IsSelected = true, IsRequired = false },
                new() { FieldKey = "TotalPrice", DefaultName = "إجمالي السعر", IsSelected = true, IsRequired = false },
                new() { FieldKey = "Exp", DefaultName = "تاريخ الانتهاء", IsSelected = true, IsRequired = false }
            };

            // ربط البيانات
            invoiceFieldsList.ItemsSource = _model.InvoiceFields;
            itemFieldsList.ItemsSource = _model.ItemFields;

            // ربط أحداث تغيير الحقول
            foreach (var field in _model.InvoiceFields)
            {
                field.PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(ExportFieldOption.IsSelected))
                        UpdateInvoicesPreview();
                };
            }

            foreach (var field in _model.ItemFields)
            {
                field.PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(ExportFieldOption.IsSelected))
                        UpdateItemsPreview();
                };
            }
        }

        private void ApplyPreferences()
        {
            // يمكن إضافة تطبيق التفضيلات المحفوظة هنا لاحقاً
            // مماثل لما تم في SalesExportPreviewWindow
        }

        private void UpdatePreview()
        {
            // تحديث معاينة الفواتير
            if (_model.ExportInvoices)
            {
                UpdateInvoicesPreview();
            }

            // تحديث معاينة الأصناف
            if (_model.ExportItems)
            {
                UpdateItemsPreview();
            }
        }

        private void UpdateInvoicesPreview()
        {
            invoicesDataGrid.Columns.Clear();

            var selectedFields = _model.InvoiceFields.Where(f => f.IsSelected).ToList();

            // تعيين الرقم التسلسلي للفواتير
            for (int i = 0; i < _model.PurchasesData.Count; i++)
            {
                _model.PurchasesData[i].Index = i + 1;
            }

            foreach (var field in selectedFields)
            {
                var column = new DataGridTextColumn
                {
                    Header = field.CustomHeader,
                    Binding = new Binding(field.FieldKey)
                };

                invoicesDataGrid.Columns.Add(column);
            }

            invoicesDataGrid.ItemsSource = _model.PurchasesData;
        }

        private void UpdateItemsPreview()
        {
            itemsDataGrid.Columns.Clear();

            var selectedFields = _model.ItemFields.Where(f => f.IsSelected).ToList();

            // تعيين الرقم التسلسلي للأصناف
            for (int i = 0; i < _model.PurchaseItemsData.Count; i++)
            {
                _model.PurchaseItemsData[i].Index = i + 1;
            }

            foreach (var field in selectedFields)
            {
                var column = new DataGridTextColumn
                {
                    Header = field.CustomHeader,
                    Binding = new Binding(field.FieldKey)
                };

                itemsDataGrid.Columns.Add(column);
            }

            itemsDataGrid.ItemsSource = _model.PurchaseItemsData;
        }

        private void UpdateSummary()
        {
            var invoicesCount = _model.ExportInvoices ? _model.PurchasesData.Count : 0;
            var itemsCount = _model.ExportItems ? _model.PurchaseItemsData.Count : 0;

            txtSummary.Text = $"إجمالي الفواتير: {invoicesCount} | إجمالي الأصناف: {itemsCount}";
        }

        private void UpdateTabsState()
        {
            // تفعيل/تعطيل التبويبات بناءً على خيارات التصدير
            tabInvoices.IsEnabled = _model.ExportInvoices;
            tabItems.IsEnabled = _model.ExportItems;

            // تغيير شفافية التبويبات المعطلة
            tabInvoices.Opacity = _model.ExportInvoices ? 1.0 : 0.5;
            tabItems.Opacity = _model.ExportItems ? 1.0 : 0.5;
        }

        private void ExportOption_Changed(object sender, RoutedEventArgs e)
        {
            if (!IsLoaded) return;

            _model.ExportInvoices = chkExportInvoices.IsChecked == true;
            _model.ExportItems = chkExportItems.IsChecked == true;

            // التحقق من أن خيار واحد على الأقل مختار
            if (!_model.ExportInvoices && !_model.ExportItems)
            {
                ErrorBox.Show("يجب اختيار خيار واحد على الأقل للتصدير", "تنبيه", false);

                // إعادة تفعيل الخيار الذي تم إلغاؤه
                if (sender == chkExportInvoices)
                    chkExportInvoices.IsChecked = true;
                else
                    chkExportItems.IsChecked = true;

                return;
            }

            UpdatePreview();
            UpdateSummary();
            UpdateTabsState();
        }

        private void btnSelectPath_Click(object sender, RoutedEventArgs e)
        {
            var saveFileDialog = new SaveFileDialog
            {
                Filter = "Excel Files|*.xlsx",
                FileName = _model.FileName,
                Title = "حفظ تقرير المشتريات"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                _selectedSavePath = saveFileDialog.FileName;
                txtSelectedPath.Text = _selectedSavePath;
                _model.FileName = System.IO.Path.GetFileNameWithoutExtension(_selectedSavePath);
            }
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // تطبيق تأثيرات التحميل إذا لزم الأمر
        }

        private void btnClose_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            btnCancel_Click(sender, e);
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnExportNow.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnExportNow.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private async void btnExportNow_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_isExporting) return;
                DisableAllButtons();

                // التحقق من المتطلبات
                if (string.IsNullOrEmpty(_selectedSavePath))
                {
                    ErrorBox.Show("يرجى اختيار مسار حفظ الملف أولاً", "تنبيه", false);
                    return;
                }

                if (!_model.ExportInvoices && !_model.ExportItems)
                {
                    ErrorBox.Show("يجب اختيار خيار واحد على الأقل للتصدير", "تنبيه", false);
                    return;
                }

                // تأكيد التصدير
                var result = QuestionBox.Show("تأكيد التصدير", "هل أنت متأكد من بدء عملية التصدير؟");
                if (result != MessageBoxResult.Yes) return;

                await StartExportProcess();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء التصدير: {ex.Message}", "خطأ في التصدير", true);
            }
            finally
            {
                EnableAllButtons();

                _isExporting = false;
                progressSection.Visibility = Visibility.Collapsed;
                btnExportNow.IsEnabled = true;
            }
        }

        private async Task StartExportProcess()
        {
            _isExporting = true;
            btnExportNow.IsEnabled = false;
            progressSection.Visibility = Visibility.Visible;

            var progress = new Progress<int>(percentage =>
            {
                Dispatcher.Invoke(() =>
                {
                    progressBar.Value = percentage;
                    txtProgress.Text = $"{percentage}%";
                });
            });

            var exportService = new ExportService();

            // تحديد الحقول المختارة
            var selectedInvoiceFields = _model.InvoiceFields.Where(f => f.IsSelected).Select(f => f.FieldKey).ToList();
            var selectedItemFields = _model.ItemFields.Where(f => f.IsSelected).Select(f => f.FieldKey).ToList();

            // تحديد رؤوس الأعمدة المخصصة
            var invoiceHeaders = _model.InvoiceFields.Where(f => f.IsSelected).ToDictionary(f => f.FieldKey, f => f.CustomHeader);
            var itemHeaders = _model.ItemFields.Where(f => f.IsSelected).ToDictionary(f => f.FieldKey, f => f.CustomHeader);

            await exportService.ExportPurchasesWithPreviewAsync(
                _selectedSavePath,
                _model.PurchasesData,
                _model.PurchaseItemsData,
                _model.ExportInvoices,
                _model.ExportItems,
                selectedInvoiceFields,
                selectedItemFields,
                invoiceHeaders,
                itemHeaders,
                _model.ClientId,
                _model.WarehouseId,
                progress);

            // حفظ التفضيلات للاستخدام المستقبلي (يمكن إضافة هذا لاحقاً)
            // ExportPreferencesHelper.SavePreferences(_model);

            ExportCompleted = true;
            DialogBox.Show("تم بنجاح", "تم تصدير البيانات بنجاح");
            Close();
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            if (_isExporting)
            {
                var result = QuestionBox.Show("تأكيد الإلغاء", "هل أنت متأكد من إلغاء عملية التصدير؟");
                if (result != MessageBoxResult.Yes) return;
            }

            ExportCompleted = false;
            Close();
        }

        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            if (_isExporting)
            {
                var result = QuestionBox.Show("تأكيد الإغلاق", "عملية التصدير جارية. هل أنت متأكد من إغلاق النافذة؟");
                if (result != MessageBoxResult.Yes)
                {
                    e.Cancel = true;
                    return;
                }
            }

            base.OnClosing(e);
        }
    }
}
