using Microsoft.EntityFrameworkCore;
using System;

namespace VisionPoint.UI.ViewModel;

/// <summary>
/// نموذج ملخص كشف حساب المستخدم
/// </summary>
public class UserStatementSummaryVM
{
    /// <summary>
    /// اسم المستخدم
    /// </summary>
    public string UserName { get; set; }
    
    /// <summary>
    /// مجموع المدين (مجموع ما على المستخدم)
    /// </summary>
    [Precision(18, 3)] public decimal TotalDebit { get; set; } = 0;
    
    /// <summary>
    /// مجموع الدائن (مجموع ما للمستخدم)
    /// </summary>
    [Precision(18, 3)] public decimal TotalCredit { get; set; } = 0;
    
    /// <summary>
    /// الرصيد النهائي
    /// </summary>
    [Precision(18, 3)] public decimal FinalBalance { get; set; } = 0;
    
    /// <summary>
    /// توضيح حالة الرصيد (مدين/دائن)
    /// </summary>
    public string BalanceStatus { get; set; }
}
