using System.Windows;

namespace VisionPoint.UI.views.Dialogs
{
    /// <summary>
    /// نافذة حوار لإدخال قيمة
    /// </summary>
    public partial class InputDialog : Window
    {
        /// <summary>
        /// القيمة المدخلة
        /// </summary>
        public string Answer { get; private set; }

        /// <summary>
        /// إنشاء نافذة حوار جديدة
        /// </summary>
        /// <param name="title">عنوان النافذة</param>
        /// <param name="question">السؤال المطروح</param>
        /// <param name="defaultAnswer">القيمة الافتراضية</param>
        public InputDialog(string title, string question, string defaultAnswer = "")
        {
            InitializeComponent();
            Title = title;
            txtAnswer.Tag = question;
            txtAnswer.Text = defaultAnswer;
            txtAnswer.SelectAll();
            txtAnswer.Focus();
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnOk.IsEnabled = false;
            btnCancel.IsEnabled = false;
            btnclose.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnOk.IsEnabled = true;
            btnCancel.IsEnabled = true;
            btnclose.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private void btnOk_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                Answer = txtAnswer.Text;
                DialogResult = true;
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                DialogResult = false;
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnclose_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                DialogResult = false;
            }
            finally
            {
                EnableAllButtons();
            }
        }
    }
}
