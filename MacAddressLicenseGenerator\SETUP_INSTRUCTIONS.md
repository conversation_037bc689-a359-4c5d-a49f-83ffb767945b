# تعليمات الإعداد السريع

## خطوات الإعداد

### 1. إعداد Google Sheets API

1. **إنشاء مشروع في Google Cloud:**
   - اذهب إلى https://console.cloud.google.com/
   - أنشئ مشروع جديد أو اختر مشروع موجود

2. **تفعيل Google Sheets API:**
   - في القائمة الجانبية، اختر "APIs & Services" > "Library"
   - ابحث عن "Google Sheets API"
   - انقر عليه ثم انقر "Enable"

3. **إنشاء Service Account:**
   - اذهب إلى "APIs & Services" > "Credentials"
   - انقر "Create Credentials" > "Service Account"
   - أدخل اسم للحساب (مثل: visionpoint-device-collector)
   - انقر "Create and Continue"
   - تخطى الخطوات الاختيارية وانقر "Done"

4. **إنشاء مفتاح JSON:**
   - انقر على Service Account الذي أنشأته
   - اذهب إلى تبويب "Keys"
   - انقر "Add Key" > "Create New Key"
   - اختر "JSON" وانقر "Create"
   - سيتم تحميل ملف JSON - احفظه باسم `credentials.json` في مجلد البرنامج

### 2. إعداد Google Sheets

1. **إنشاء جدول بيانات:**
   - اذهب إلى https://sheets.google.com/
   - أنشئ جدول بيانات جديد

2. **الحصول على معرف الجدول:**
   - من رابط الجدول، انسخ الجزء بين `/d/` و `/edit`
   - مثال: في الرابط `https://docs.google.com/spreadsheets/d/1ABC123XYZ/edit`
   - معرف الجدول هو: `1ABC123XYZ`

3. **مشاركة الجدول:**
   - انقر "Share" في الجدول
   - أضف البريد الإلكتروني للـ Service Account (موجود في credentials.json)
   - أعطه صلاحية "Editor"

### 3. تحديث الكود

1. **تحديث معرف الجدول:**
   - افتح ملف `Program.cs`
   - ابحث عن السطر:
     ```csharp
     private const string SpreadsheetId = "YOUR_SPREADSHEET_ID_HERE";
     ```
   - استبدل `YOUR_SPREADSHEET_ID_HERE` بمعرف جدولك الفعلي

2. **التأكد من وجود ملف الاعتماد:**
   - تأكد من وجود ملف `credentials.json` في مجلد البرنامج
   - تأكد من أن اسم الملف صحيح تماماً

### 4. تشغيل البرنامج

```bash
dotnet run
```

## ملاحظات مهمة

- **الأمان:** لا تشارك ملف `credentials.json` مع أحد
- **الصلاحيات:** قد تحتاج لتشغيل البرنامج كمدير للحصول على بعض معلومات النظام
- **الشبكة:** تأكد من اتصال الإنترنت لرفع البيانات إلى Google Sheets

## استكشاف الأخطاء الشائعة

### "Credentials file not found"
- تأكد من وجود ملف `credentials.json` في نفس مجلد البرنامج
- تأكد من صحة اسم الملف

### "The caller does not have permission"
- تأكد من مشاركة الجدول مع Service Account
- تأكد من إعطاء صلاحية "Editor"

### "Spreadsheet not found"
- تأكد من صحة معرف الجدول في الكود
- تأكد من أن الجدول موجود ومتاح

## الدعم

إذا واجهت أي مشاكل، تأكد من:
1. تفعيل Google Sheets API
2. صحة ملف credentials.json
3. مشاركة الجدول مع Service Account
4. صحة معرف الجدول في الكود
