﻿using Microsoft.EntityFrameworkCore;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Models;
using VisionPoint.UI.Services;
using VisionPoint.UI.ViewModel;
using static VisionPoint.UI.PL.SeedData;

namespace VisionPoint.UI.PL;

public class TreasuryService : IDisposable
{
    private readonly AppDbContext _context = ServiceLocator.GetService<AppDbContext>();
    private bool _disposed = false;

    public TreasuryService()
    {
    }

    public async Task<List<Treasury>> GetAllTreasuriesAsync()
    {
        return await _context.Treasuries
            .Include(t => t.Warehouse)
            .ToListAsyncWithBusy("GetAllTreasuries");
    }

    public async Task<List<Treasury>> GetTreasuriesByWarehouseAsync(int warehouseId)
    {
        return await _context.Treasuries
            .Include(t => t.Warehouse)
            .Where(t => t.WarehouseId == warehouseId)
            .ToListAsyncWithBusy("GetTreasuriesByWarehouse");
    }

    public async Task<Treasury> GetTreasuryByIdAsync(byte id)
    {
        return await _context.Treasuries.FindAsyncWithBusy(id);
    }

    public async Task<(bool State, string Message)> AddTreasuryAsync(Treasury treasury)
    {
        treasury.CreatedAt = DateTime.Now;
        treasury.UpdatedAt = DateTime.Now;
        await _context.Treasuries.AddAsyncWithBusy(treasury);
        return await _context.SaveWithTransactionAndBusy("AddTreasury");
    }

    public async Task<(bool State, string Message)> UpdateTreasuryAsync(Treasury treasury)
    {
        treasury.UpdatedAt = DateTime.Now;
        _context.Treasuries.UpdateWithBusy(treasury);
        return await _context.SaveWithTransactionAndBusy("UpdateTreasury");
    }

    public async Task<(bool State, string Message)> DeleteTreasuryAsync(byte id)
    {
        var treasury = await _context.Treasuries.FindAsyncWithBusy(id);
        if (treasury == null)
        {
            return (false, "الخزينة غير موجودة");
        }

        // التحقق من وجود إيصالات مرتبطة بالخزينة
        bool hasReceipts = await _context.Receipts.AnyAsyncWithBusy(r => r.TreasuryId == id, "CheckTreasuryReceipts");

        if (hasReceipts)
        {
            return (false, "لا يمكن حذف الخزينة لأنها مرتبطة بإيصالات");
        }

        _context.Treasuries.RemoveWithBusy(treasury);
        return await _context.SaveWithTransactionAndBusy("DeleteTreasury");
    }
    public async Task<(bool State, string Message)> IncreaseBalanceAsync(byte id, decimal amount)
    {
        var treasury = await _context.Treasuries.FindAsyncWithBusy(id);
        if (treasury != null)
        {
            treasury.Balance += amount;
            return await _context.SaveWithTransactionAndBusy("IncreaseTreasuryBalance");
        }
        return (false, "الخزينة غير موجودة");
    }

    public async Task<(bool State, string Message)> DecreaseBalanceAsync(byte id, decimal amount)
    {
        var treasury = await _context.Treasuries.FindAsyncWithBusy(id);
        if (treasury != null)
        {
            treasury.Balance -= amount;
            return await _context.SaveWithTransactionAndBusy("DecreaseTreasuryBalance");
        }
        return (false, "الخزينة غير موجودة");
    }

    public async Task<List<Treasury>> SearchTreasuriesAsync(string searchTerm)
    {
        return await _context.Treasuries
            .Include(t => t.Warehouse)
            .Where(t => t.Name.Contains(searchTerm))
            .ToListAsyncWithBusy("SearchTreasuries");
    }

    /// <summary>
    /// استرجاع كشف حساب الخزينة
    /// </summary>
    /// <param name="treasuryId">معرف الخزينة</param>
    /// <param name="fromDate">تاريخ البداية (اختياري)</param>
    /// <param name="toDate">تاريخ النهاية (اختياري)</param>
    /// <returns>قائمة بعمليات الخزينة</returns>
    public async Task<(List<TreasuryStatementVM> Transactions, TreasuryStatementSummaryVM Summary)> GetTreasuryStatementAsync(byte treasuryId, DateTime? fromDate = null, DateTime? toDate = null)
    {
        // التحقق من وجود الخزينة
        var treasury = await _context.Treasuries
            .Include(t => t.Warehouse)
            .FirstOrDefaultAsyncWithBusy(t => t.Id == treasuryId);

        if (treasury == null)
        {
            return (new List<TreasuryStatementVM>(), new TreasuryStatementSummaryVM { TreasuryName = "غير موجود" });
        }

        var result = new List<TreasuryStatementVM>();

        // استعلام الإيصالات المرتبطة بهذه الخزينة
        // يشمل الإيصالات العادية وعمليات التحويل (كمصدر أو هدف)
        var receiptsQuery = _context.Receipts
            .Include(r => r.Client)
            .Include(r => r.Financial)
            .Include(r => r.Sale)
            .Include(r => r.Purchase)
            .Include(r => r.Expense)
            .Include(r => r.Employee)
            .Include(r => r.SourceTreasury)
            .Include(r => r.TargetTreasury)
            .Where(r => r.TreasuryId == treasuryId ||
                       r.SourceTreasuryId == treasuryId ||
                       r.TargetTreasuryId == treasuryId);

        // تطبيق فلتر التاريخ إذا تم تحديده
        if (fromDate.HasValue)
        {
            receiptsQuery = receiptsQuery.Where(r => r.Date >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            var endDate = toDate.Value.AddDays(1).AddSeconds(-1); // نهاية اليوم
            receiptsQuery = receiptsQuery.Where(r => r.Date <= endDate);
        }

        // جمع كل العمليات
        var receipts = await receiptsQuery.ToListAsyncWithBusy("GetReceiptsForTreasuryStatement");

        // جمع كل العمليات في قائمة واحدة للترتيب حسب التاريخ
        var allTransactions = new List<TreasuryStatementVM>();

        // تحويل الإيصالات إلى نموذج كشف الحساب
        foreach (var receipt in receipts)
        {
            string operationType;
            decimal incoming = 0;
            decimal outgoing = 0;
            string partyName = "غير محدد"; // الطرف المتعامل
            string description = receipt.Statement ?? "";

            // تحديد نوع العملية والمبالغ بناءً على نوع الإيصال والمراجع
            if (receipt.FinancialId.HasValue)
            {
                var financialId = (FinancialId)receipt.FinancialId.Value;

                switch (financialId)
                {
                    case FinancialId.Sale:
                        incoming = receipt.Value;
                        operationType = "تحصيل فاتورة مبيعات";
                        partyName = receipt.Client?.Name ?? "عميل";
                        if (receipt.SaleId.HasValue)
                            description = $"فاتورة مبيعات رقم {receipt.SaleId}";
                        break;

                    case FinancialId.Purchase:
                        outgoing = receipt.Value;
                        operationType = "دفع فاتورة مشتريات";
                        partyName = receipt.Client?.Name ?? "مورد";
                        if (receipt.PurchaseId.HasValue)
                            description = $"فاتورة مشتريات رقم {receipt.PurchaseId}";
                        break;

                    case FinancialId.Expense:
                        outgoing = receipt.Value;
                        operationType = "مصروف";
                        partyName = receipt.Expense?.Name ?? "مصروف عام";
                        if (string.IsNullOrEmpty(description))
                            description = receipt.Expense?.Name ?? "مصروف";
                        break;

                    case FinancialId.Client:
                        if (receipt.IsExchange == true)
                        {
                            outgoing = receipt.Value;
                            operationType = "دفع للعميل";
                        }
                        else
                        {
                            incoming = receipt.Value;
                            operationType = "قبض من العميل";
                        }
                        partyName = receipt.Client?.Name ?? "عميل";
                        break;

                    case FinancialId.Employee:
                        if (receipt.IsExchange == true)
                        {
                            outgoing = receipt.Value;
                            operationType = "دفع للموظف";
                        }
                        else
                        {
                            incoming = receipt.Value;
                            operationType = "قبض من الموظف";
                        }
                        partyName = receipt.Employee?.Name ?? "موظف";
                        break;

                    case FinancialId.Deposit:
                        incoming = receipt.Value;
                        operationType = "إيداع";
                        partyName = receipt.Client?.Name ?? receipt.Employee?.Name ?? "إيداع نقدي";
                        break;

                    case FinancialId.Withdrawal:
                        outgoing = receipt.Value;
                        operationType = "سحب";
                        partyName = receipt.Client?.Name ?? receipt.Employee?.Name ?? "سحب نقدي";
                        break;

                    case FinancialId.SalaryPayment:
                        outgoing = receipt.Value;
                        operationType = "صرف راتب";
                        partyName = receipt.Employee?.Name ?? "موظف";
                        break;

                    case FinancialId.Transfer:
                        // التحويل بين الخزائن
                        if (receipt.SourceTreasuryId.HasValue && receipt.TargetTreasuryId.HasValue)
                        {
                            operationType = "تحويل بين الخزائن";

                            // تحديد ما إذا كانت الخزينة الحالية هي المصدر أم الهدف
                            if (receipt.SourceTreasuryId == treasuryId)
                            {
                                // الخزينة الحالية هي المصدر - مبلغ خارج
                                outgoing = receipt.Value;
                                incoming = 0;
                                partyName = $"تحويل إلى {receipt.TargetTreasury?.Name ?? "خزينة"}";
                            }
                            else if (receipt.TargetTreasuryId == treasuryId)
                            {
                                // الخزينة الحالية هي الهدف - مبلغ داخل
                                incoming = receipt.Value;
                                outgoing = 0;
                                partyName = $"تحويل من {receipt.SourceTreasury?.Name ?? "خزينة"}";
                            }
                            else
                            {
                                // لا يجب أن نصل هنا، لكن للأمان
                                incoming = 0;
                                outgoing = 0;
                                partyName = $"من {receipt.SourceTreasury?.Name ?? "خزينة"} إلى {receipt.TargetTreasury?.Name ?? "خزينة"}";
                            }
                        }
                        else
                        {
                            operationType = "تحويل";
                            partyName = "تحويل مالي";
                            incoming = 0;
                            outgoing = 0;
                        }
                        break;

                    case FinancialId.OpeningBalanceForClient:
                        if (receipt.Value >= 0)
                        {
                            incoming = receipt.Value;
                            operationType = "رصيد افتتاحي للعميل (دائن)";
                        }
                        else
                        {
                            outgoing = Math.Abs(receipt.Value);
                            operationType = "رصيد افتتاحي للعميل (مدين)";
                        }
                        partyName = receipt.Client?.Name ?? "عميل";
                        break;

                    case FinancialId.OpeningBalanceForEmployee:
                        if (receipt.Value >= 0)
                        {
                            incoming = receipt.Value;
                            operationType = "رصيد افتتاحي للموظف (دائن)";
                        }
                        else
                        {
                            outgoing = Math.Abs(receipt.Value);
                            operationType = "رصيد افتتاحي للموظف (مدين)";
                        }
                        partyName = receipt.Employee?.Name ?? "موظف";
                        break;

                    case FinancialId.AutomaticSalary:
                        if (receipt.IsExchange == true)
                        {
                            outgoing = receipt.Value;
                            operationType = "خصم راتب تلقائي";
                        }
                        else
                        {
                            incoming = receipt.Value;
                            operationType = "إضافة راتب تلقائي";
                        }
                        partyName = receipt.Employee?.Name ?? "موظف";
                        break;

                    default:
                        // للأنواع الأخرى، استخدم اسم النوع المالي
                        operationType = receipt.Financial?.Name ?? "عملية مالية";
                        if (receipt.IsExchange == true)
                        {
                            outgoing = receipt.Value;
                        }
                        else
                        {
                            incoming = receipt.Value;
                        }
                        partyName = receipt.Client?.Name ?? receipt.Employee?.Name ?? "غير محدد";
                        break;
                }
            }
            else
            {
                // إيصالات بدون نوع مالي محدد - استخدم IsExchange
                if (receipt.IsExchange == true)
                {
                    outgoing = receipt.Value;
                    operationType = "إيصال صرف";
                }
                else
                {
                    incoming = receipt.Value;
                    operationType = "إيصال قبض";
                }
                partyName = receipt.Client?.Name ?? receipt.Employee?.Name ?? "غير محدد";
            }

            allTransactions.Add(new TreasuryStatementVM
            {
                Id = receipt.Id,
                Date = receipt.Date,
                OperationType = operationType,
                OperationNumber = receipt.ReceiptNo,
                ClientName = partyName, // استخدام الطرف المتعامل الجديد
                Description = description,
                Incoming = incoming,
                Outgoing = outgoing,
                Balance = 0 // سيتم حسابه لاحقاً
            });
        }

        // ترتيب العمليات حسب التاريخ
        allTransactions = allTransactions.OrderBy(t => t.Date).ThenBy(t => t.Id).ToList();

        // حساب الرصيد التراكمي
        decimal currentBalance = 0;

        // إضافة صف الرصيد السابق إذا تم تحديد تاريخ "من" والرصيد ليس صفر
        if (fromDate.HasValue)
        {
            // حساب الرصيد السابق من العمليات قبل تاريخ "من"
            var previousBalance = await CalculateTreasuryBalanceBeforeDateAsync(treasuryId, fromDate.Value);

            // إضافة صف الرصيد السابق فقط إذا لم يكن صفر
            if (previousBalance != 0)
            {
                result.Add(new TreasuryStatementVM
                {
                    Id = 0,
                    Date = null, // لا يوجد تاريخ محدد للرصيد السابق
                    OperationType = "الرصيد السابق",
                    OperationNumber = null,
                    ClientName = "",
                    Description = $"رصيد الخزينة قبل {fromDate.Value:yyyy-MM-dd}",
                    Incoming = previousBalance > 0 ? previousBalance : 0,
                    Outgoing = previousBalance < 0 ? Math.Abs(previousBalance) : 0,
                    Balance = previousBalance,
                    IsSpecialRow = true,
                    SpecialRowType = "PreviousBalance"
                });
            }

            currentBalance = previousBalance;
        }

        // إضافة العمليات مع حساب الرصيد التراكمي
        foreach (var transaction in allTransactions)
        {
            currentBalance += transaction.Incoming - transaction.Outgoing;
            transaction.Balance = currentBalance;
            result.Add(transaction);
        }

        // إضافة صف الرصيد اللاحق إذا تم تحديد تاريخ "إلى"
        if (toDate.HasValue)
        {
            // حساب الرصيد اللاحق من العمليات بعد تاريخ "إلى"
            var nextBalance = await CalculateTreasuryBalanceAfterDateAsync(treasuryId, toDate.Value);

            // إضافة الصف فقط إذا كان هناك عمليات بعد التاريخ المحدد والرصيد ليس صفر
            if (nextBalance != currentBalance && nextBalance != 0)
            {
                result.Add(new TreasuryStatementVM
                {
                    Id = 0,
                    Date = null, // لا يوجد تاريخ محدد للرصيد اللاحق
                    OperationType = "الرصيد اللاحق",
                    OperationNumber = null,
                    ClientName = "",
                    Description = $"رصيد الخزينة بعد {toDate.Value:yyyy-MM-dd}",
                    Incoming = nextBalance > currentBalance ? (nextBalance - currentBalance) : 0,
                    Outgoing = nextBalance < currentBalance ? (currentBalance - nextBalance) : 0,
                    Balance = nextBalance,
                    IsSpecialRow = true,
                    SpecialRowType = "NextBalance"
                });
            }
        }

        // إنشاء ملخص كشف الحساب
        var summary = new TreasuryStatementSummaryVM
        {
            TreasuryName = treasury.Name,
            // حساب المجاميع فقط للعمليات العادية (غير الصفوف الخاصة)
            TotalIncoming = result.Where(t => !t.IsSpecialRow).Sum(t => t.Incoming),
            TotalOutgoing = result.Where(t => !t.IsSpecialRow).Sum(t => t.Outgoing),
            // استخدام الرصيد النهائي من آخر صف في النتائج
            FinalBalance = result.LastOrDefault()?.Balance ?? 0
        };

        // تحديد حالة الرصيد
        if (summary.FinalBalance > 0)
        {
            summary.BalanceStatus = "رصيد موجب";
        }
        else if (summary.FinalBalance < 0)
        {
            summary.BalanceStatus = "رصيد سالب";
        }
        else
        {
            summary.BalanceStatus = "رصيد متوازن";
        }

        return (result, summary);
    }

    /// <summary>
    /// حساب رصيد الخزينة قبل تاريخ معين
    /// </summary>
    private async Task<decimal> CalculateTreasuryBalanceBeforeDateAsync(byte treasuryId, DateTime beforeDate)
    {
        decimal balance = 0;

        // حساب مجموع الإيصالات قبل التاريخ المحدد (يشمل عمليات التحويل)
        var receipts = await _context.Receipts
            .Where(r => (r.TreasuryId == treasuryId ||
                        r.SourceTreasuryId == treasuryId ||
                        r.TargetTreasuryId == treasuryId) &&
                       r.Date < beforeDate)
            .ToListAsyncWithBusy("CalculateTreasuryBalanceBeforeDate");

        foreach (var receipt in receipts)
        {
            // معالجة عمليات التحويل
            if (receipt.FinancialId.HasValue && (FinancialId)receipt.FinancialId.Value == FinancialId.Transfer)
            {
                if (receipt.SourceTreasuryId == treasuryId)
                {
                    // الخزينة الحالية هي المصدر - مبلغ خارج
                    balance -= receipt.Value;
                }
                else if (receipt.TargetTreasuryId == treasuryId)
                {
                    // الخزينة الحالية هي الهدف - مبلغ داخل
                    balance += receipt.Value;
                }
            }
            else
            {
                // معالجة الإيصالات العادية
                if (receipt.IsExchange == true)
                {
                    // إيصال صرف - مبلغ خارج من الخزينة
                    balance -= receipt.Value;
                }
                else if (receipt.IsExchange == false)
                {
                    // إيصال قبض - مبلغ داخل للخزينة
                    balance += receipt.Value;
                }
            }
        }

        return balance;
    }

    /// <summary>
    /// حساب رصيد الخزينة بعد تاريخ معين (يشمل جميع العمليات حتى ذلك التاريخ وما بعده)
    /// </summary>
    private async Task<decimal> CalculateTreasuryBalanceAfterDateAsync(byte treasuryId, DateTime afterDate)
    {
        decimal balance = 0;

        // حساب مجموع جميع الإيصالات (يشمل عمليات التحويل)
        var receipts = await _context.Receipts
            .Where(r => r.TreasuryId == treasuryId ||
                       r.SourceTreasuryId == treasuryId ||
                       r.TargetTreasuryId == treasuryId)
            .ToListAsyncWithBusy("CalculateTreasuryBalanceAfterDate");

        foreach (var receipt in receipts)
        {
            // معالجة عمليات التحويل
            if (receipt.FinancialId.HasValue && (FinancialId)receipt.FinancialId.Value == FinancialId.Transfer)
            {
                if (receipt.SourceTreasuryId == treasuryId)
                {
                    // الخزينة الحالية هي المصدر - مبلغ خارج
                    balance -= receipt.Value;
                }
                else if (receipt.TargetTreasuryId == treasuryId)
                {
                    // الخزينة الحالية هي الهدف - مبلغ داخل
                    balance += receipt.Value;
                }
            }
            else
            {
                // معالجة الإيصالات العادية
                if (receipt.IsExchange == true)
                {
                    // إيصال صرف - مبلغ خارج من الخزينة
                    balance -= receipt.Value;
                }
                else if (receipt.IsExchange == false)
                {
                    // إيصال قبض - مبلغ داخل للخزينة
                    balance += receipt.Value;
                }
            }
        }

        return balance;
    }

    // Implement IDisposable pattern
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // Dispose managed resources
                _context?.Dispose();
            }

            // Free unmanaged resources
            _disposed = true;
        }
    }

    // Destructor
    ~TreasuryService()
    {
        Dispose(false);
    }
}

