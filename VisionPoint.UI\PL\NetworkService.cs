﻿﻿using System.IO;
using System.Net.NetworkInformation;
using System.Security.Cryptography;
using System.Text;

namespace VisionPoint.UI.PL;

public class NetworkService : IDisposable
{
    // Default encryption key and IV (Initialization Vector)
    private const string DefaultSecretKey = "VisionPointSecure2024KeyForEncryption";
    // Fixed IV for consistent encryption results - 16 bytes for AES
    private static readonly byte[] FixedIV = new byte[]
    {
        0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
        0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10
    };
    private bool _disposed = false;
    /// <summary>
    /// Retrieves all MAC addresses from network interfaces in the current device
    /// </summary>
    /// <returns>A list of MAC addresses as strings</returns>
    public List<string> GetAllMacAddresses()
    {
        var result = new List<string>();

        // Get all network interfaces from the system
        var networkInterfaces = System.Net.NetworkInformation.NetworkInterface.GetAllNetworkInterfaces();

        foreach (var netInterface in networkInterfaces.OrderBy(x=>x.Name))
        {
            // Get the MAC address
            string macAddress = netInterface.GetPhysicalAddress().ToString();

            // Only add non-empty MAC addresses
            if (!string.IsNullOrEmpty(macAddress))
            {
                result.Add(macAddress);
            }
        }

        return result;
    }

    /// <summary>
    /// Gets all MAC addresses as a single string separated by hyphens
    /// </summary>
    /// <param name="encrypt">Whether to encrypt the content</param>
    /// <param name="secretKey">The secret key for encryption. If null, the default key will be used.</param>
    /// <returns>MAC addresses as a string, optionally encrypted</returns>
    public string GetMacAddressesAsString(bool encrypt = false, string? secretKey = null)
    {
        // Get all MAC addresses
        var macAddresses = GetAllMacAddresses();

        // Join all MAC addresses with a hyphen separator
        string content = string.Join("-", macAddresses);

        // Encrypt the content if requested
        if (encrypt)
        {
            content = Encrypt(content, secretKey ?? DefaultSecretKey);
        }

        return content;
    }

    /// <summary>
    /// Compares MAC addresses with content from a file
    /// </summary>
    /// <param name="fileContent">Content to compare with</param>
    /// <param name="secretKey">The secret key for encryption. If null, the default key will be used.</param>
    /// <returns>True if MAC addresses match the file content after encryption</returns>
    public bool CompareMacAddressesWithContent(string fileContent, string? secretKey = null)
    {
        if (string.IsNullOrEmpty(fileContent))
            return false;

        // Get encrypted MAC addresses
        string encryptedMacAddresses = GetMacAddressesAsString(true, secretKey ?? DefaultSecretKey);

        // Compare with file content
        return encryptedMacAddresses.Equals(fileContent.Trim());
    }

    /// <summary>
    /// Reads a file from the user's AppData\Local directory
    /// </summary>
    /// <param name="fileName">Name of the file to read</param>
    /// <returns>Content of the file, or empty string if file doesn't exist</returns>
    public string ReadFileFromAppDataLocal(string fileName)
    {
        try
        {
            string localAppDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            string filePath = Path.Combine(localAppDataPath, fileName);

            if (File.Exists(filePath))
            {
                return File.ReadAllText(filePath);
            }

            return string.Empty;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error reading file: {ex.Message}");
            return string.Empty;
        }
    }

    /// <summary>
    /// Encrypts a string using AES encryption with the provided secret key
    /// </summary>
    /// <param name="plainText">The text to encrypt</param>
    /// <param name="secretKey">The secret key for encryption</param>
    /// <returns>The encrypted text as a Base64 string</returns>
    public string Encrypt(string plainText, string secretKey)
    {
        if (string.IsNullOrEmpty(plainText))
            return string.Empty;

        byte[] encryptedBytes;
        byte[] keyBytes = GetKeyBytes(secretKey);

        using (Aes aes = Aes.Create())
        {
            aes.Key = keyBytes;
            aes.IV = FixedIV; // Use fixed IV for consistent encryption results

            using (ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV))
            using (MemoryStream ms = new MemoryStream())
            {
                using (CryptoStream cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                using (StreamWriter sw = new StreamWriter(cs))
                {
                    sw.Write(plainText);
                }

                encryptedBytes = ms.ToArray();
            }
        }

        // Convert the encrypted bytes to a Base64 string for storage
        return Convert.ToBase64String(encryptedBytes);
    }

    /// <summary>
    /// Decrypts a string that was encrypted using the Encrypt method
    /// </summary>
    /// <param name="encryptedText">The encrypted text as a Base64 string</param>
    /// <param name="secretKey">The secret key for decryption</param>
    /// <returns>The decrypted text</returns>
    public string Decrypt(string encryptedText, string secretKey)
    {
        if (string.IsNullOrEmpty(encryptedText))
            return string.Empty;

        string plainText;
        byte[] keyBytes = GetKeyBytes(secretKey);
        byte[] encryptedBytes = Convert.FromBase64String(encryptedText);

        using (Aes aes = Aes.Create())
        {
            aes.Key = keyBytes;
            aes.IV = FixedIV; // Use the same fixed IV as in encryption

            using (ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV))
            using (MemoryStream ms = new MemoryStream(encryptedBytes))
            using (CryptoStream cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
            using (StreamReader sr = new StreamReader(cs))
            {
                plainText = sr.ReadToEnd();
            }
        }

        return plainText;
    }

    /// <summary>
    /// Reads and decrypts a file that was encrypted using the SaveMacAddressesToFile method with encryption
    /// </summary>
    /// <param name="filePath">Path to the encrypted file</param>
    /// <param name="secretKey">The secret key for decryption</param>
    /// <returns>The decrypted content of the file</returns>
    public string ReadAndDecryptFile(string filePath, string? secretKey = null)
    {
        if (!File.Exists(filePath))
            throw new FileNotFoundException("The specified file was not found.", filePath);

        string encryptedContent = File.ReadAllText(filePath);
        return Decrypt(encryptedContent, secretKey ?? DefaultSecretKey);
    }

    /// <summary>
    /// Converts a string key to a valid AES key byte array
    /// </summary>
    private byte[] GetKeyBytes(string key)
    {
        // Ensure the key is exactly 32 bytes (256 bits) for AES-256
        using (SHA256 sha256 = SHA256.Create())
        {
            return sha256.ComputeHash(Encoding.UTF8.GetBytes(key));
        }
    }

    // Implement IDisposable pattern
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // NetworkService doesn't have specific resources to dispose
                // but we implement the pattern for consistency
            }

            // Free unmanaged resources
            _disposed = true;
        }
    }

    // Destructor
    ~NetworkService()
    {
        Dispose(false);
    }
}
