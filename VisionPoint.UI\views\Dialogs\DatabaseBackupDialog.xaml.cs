using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using VisionPoint.UI.Services;

namespace VisionPoint.UI.views.Dialogs
{
    /// <summary>
    /// Interaction logic for DatabaseBackupDialog.xaml
    /// </summary>
    public partial class DatabaseBackupDialog : Window
    {
        private readonly DatabaseBackupService _backupService;
        private readonly bool _isRestore;
        private readonly string _backupPath;
        private CancellationTokenSource _cancellationTokenSource;
        private bool _operationCompleted = false;

        /// <summary>
        /// Creates a new instance of the DatabaseBackupDialog
        /// </summary>
        /// <param name="isRestore">True if this is a restore operation, false for backup</param>
        /// <param name="backupPath">Optional path for backup/restore file. If null, a dialog will be shown.</param>
        public DatabaseBackupDialog(bool isRestore = false, string backupPath = null)
        {
            InitializeComponent();
            _backupService = new DatabaseBackupService();
            _isRestore = isRestore;
            _backupPath = backupPath;
            _cancellationTokenSource = new CancellationTokenSource();

            // Update UI based on operation type
            if (_isRestore)
            {
                Title = "استعادة قاعدة البيانات";
                statusText.Text = "جاري استعادة قاعدة البيانات...";
            }
            else
            {
                Title = "النسخ الاحتياطي لقاعدة البيانات";
                statusText.Text = "جاري إنشاء نسخة احتياطية...";
            }

            Loaded += DatabaseBackupDialog_Loaded;
        }

        private async void DatabaseBackupDialog_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_isRestore)
                {
                    await PerformRestoreAsync();
                }
                else
                {
                    await PerformBackupAsync();
                }
            }
            catch (OperationCanceledException)
            {
                statusText.Text = "تم إلغاء العملية";
                progressBar.IsIndeterminate = false;
                btnClose.IsEnabled = true;
                btnCancel.IsEnabled = false;
            }
            catch (Exception ex)
            {
                statusText.Text = $"حدث خطأ: {ex.Message}";
                progressBar.IsIndeterminate = false;
                btnClose.IsEnabled = true;
                btnCancel.IsEnabled = false;
            }
        }

        private async Task PerformBackupAsync()
        {
            var result = await _backupService.CreateBackupAsync(_backupPath);
            _operationCompleted = true;

            statusText.Text = result.Message;
            progressBar.IsIndeterminate = false;
            btnClose.IsEnabled = true;
            btnCancel.IsEnabled = false;

            if (result.Success)
            {
                DialogBox.Show("نجاح", result.Message);
            }
            else
            {
                ErrorBox.Show(result.Message, "خطأ", true);
            }
        }

        private async Task PerformRestoreAsync()
        {
            var result = await _backupService.RestoreBackupAsync(_backupPath);
            _operationCompleted = true;

            statusText.Text = result.Message;
            progressBar.IsIndeterminate = false;
            btnClose.IsEnabled = true;
            btnCancel.IsEnabled = false;

            if (result.Success)
            {
                DialogBox.Show("نجاح", result.Message);
            }
            else
            {
                ErrorBox.Show(result.Message, "خطأ", true);
            }
        }

        private void btnClose_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                DialogResult = _operationCompleted;
                Close();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnCancel.IsEnabled = false;
            btnClose.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnCancel.IsEnabled = true;
            btnClose.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (!_operationCompleted)
                {
                    _cancellationTokenSource.Cancel();
                    statusText.Text = "جاري إلغاء العملية...";
                    btnCancel.IsEnabled = false;
                }
                else
                {
                    Close();
                }
            }
            finally
            {
                EnableAllButtons();
            }
        }
    }
}
