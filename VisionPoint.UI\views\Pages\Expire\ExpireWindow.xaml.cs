﻿using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.Expire
{
    /// <summary>
    /// Interaction logic for ExpireWindow.xaml
    /// </summary>
    public partial class ExpireWindow : Window
    {
        private ObservableCollection<ExpiredItemViewModel> _expiredItems;
        private int? _selectedWarehouseId; // Changed to nullable to support "All Warehouses"
        private bool _isProductsSelected = true; // Default to products selected
        private bool _isLoaded = false; // Flag to track if the window has been loaded
        public ExpireWindow(bool isProductsSelected)
        {
            _expiredItems = new ObservableCollection<ExpiredItemViewModel>();
            InitializeComponent();
            _isLoaded = true;
            list.ItemsSource = _expiredItems;
            _isProductsSelected = isProductsSelected;
        }
        private bool _isInitializing = false;
        // Create a new ProductService with a shared DbContext
        private ProductService CreateProductService()
        {
            return new ProductService();
        }

        // Create a new LensService with a shared DbContext
        private LensService CreateLensService()
        {
            return new LensService();
        }



        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            _isInitializing = true;
            try
            {
                // Load the license remaining days from settings
                txtDefaultExpireDate.Text = GetLicenseRemainingDays().ToString();

                // Load warehouses
                await LoadWarehousesAsync();
                RdbLensesSelect.IsChecked = !_isProductsSelected;
                RdbProductSelect.IsChecked = _isProductsSelected;
                Rdb_Checked(sender, e); // Load initial data based on selected type
            }
            finally
            {
                _isInitializing = false;
            }
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnclose.IsEnabled = false;
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnclose.IsEnabled = true;
        }

        private void btnclose_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                Close();
            }
            finally
            {
                EnableAllButtons();
            }
        }



        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (list.View is GridView gridView && gridView.Columns.Count >= 6)
            {
                double scrollbarWidth = SystemParameters.VerticalScrollBarWidth;

                double availableWidth = list.ActualWidth - scrollbarWidth;

                double totalWeight = 3.0 + 2.0 + 1.0 + 1.5 + 1.0 + 1.5 + 2.0; // Name, ExpirationDate, Quantity, RemainingDays, ItemType, Warehouse, Status
                double unitWidth = availableWidth / totalWeight;

                gridView.Columns[0].Width = unitWidth * 3.0; // Name
                gridView.Columns[1].Width = unitWidth * 2.0; // ExpirationDate
                gridView.Columns[2].Width = unitWidth * 1.0; // Quantity
                gridView.Columns[3].Width = unitWidth * 1.5; // RemainingDays
                gridView.Columns[4].Width = unitWidth * 1.0; // ItemType
                gridView.Columns[5].Width = unitWidth * 1.5; // Warehouse
                gridView.Columns[6].Width = unitWidth * 2.0; // Status
            }
        }
        private async void Rdb_Checked(object sender, RoutedEventArgs e)
        {
            if (!_isLoaded) return;
            if (RdbLensesSelect.IsChecked == true) await LoadExpiredLenses();
            else if (RdbProductSelect.IsChecked == true) await LoadExpiredProducts();
        }
        private void TextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                textBox.SelectAll();
            }
        }

        private async Task LoadWarehousesAsync()
        {
            try
            {
                using var warehouseService = new WarehouseService();
                var warehouses = await warehouseService.GetAllWarehousesAsync();

                // Check user permissions for warehouse access
                cmbWarehouse.IsEnabled = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ChangeWarehouseRole");
                // User can see all warehouses - add "All Warehouses" option
                var allWarehouses = new List<Warehouse>
                    {
                        new Warehouse { Id = 0, Name = "جميع المخازن" }
                    };
                allWarehouses.AddRange(warehouses);

                cmbWarehouse.ItemsSource = allWarehouses;
                cmbWarehouse.SelectedValue = CurrentUser.WarehouseId;
                _selectedWarehouseId = CurrentUser.WarehouseId;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل المخازن: {ex.Message}", "خطأ", true);
            }
        }

        private async void cmbWarehouse_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_isInitializing || cmbWarehouse.SelectedValue == null)
                return;

            try
            {
                // Handle "All Warehouses" option (Id = 0) vs specific warehouse
                int selectedId = (int)cmbWarehouse.SelectedValue;
                _selectedWarehouseId = selectedId == 0 ? null : selectedId;

                // Reload data for the selected warehouse
                if (RdbProductSelect.IsChecked == true)
                {
                    await LoadExpiredProducts();
                }
                else if (RdbLensesSelect.IsChecked == true)
                {
                    await LoadExpiredLenses();
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تغيير المخزن: {ex.Message}", "خطأ", true);
            }
        }

        private async Task LoadExpiredProducts()
        {
            try
            {
                _expiredItems.Clear();

                int remainingDays = GetLicenseRemainingDays();
                var today = DateOnly.FromDateTime(DateTime.Today);

                using (var productService = CreateProductService())
                {
                    _expiredItems = new(await productService.GetExpiredProductsViewModelAsync(_selectedWarehouseId, remainingDays, today));
                    list.ItemsSource = _expiredItems;
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل المنتجات منتهية الصلاحية: {ex.Message}", "خطأ", true);
            }
        }

        private async Task LoadExpiredLenses()
        {
            try
            {
                _expiredItems.Clear();

                int remainingDays = GetLicenseRemainingDays();
                var today = DateOnly.FromDateTime(DateTime.Today);

                using (var lensService = CreateLensService())
                {
                    _expiredItems = new(await lensService.GetExpiredLensesViewModelAsync(_selectedWarehouseId, remainingDays, today));
                    list.ItemsSource = _expiredItems;
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل العدسات منتهية الصلاحية: {ex.Message}", "خطأ", true);
            }
        }

        // Get license remaining days from settings
        private int GetLicenseRemainingDays()
        {
            try
            {
                return Properties.Settings.Default.LicenseRemainingDays;
            }
            catch (Exception ex)
            {
                return 0;
            }
        }

        // Set license remaining days in settings
        private void SetLicenseRemainingDays(int days)
        {
            try
            {
                Properties.Settings.Default.LicenseRemainingDays = days;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تعيين الأيام المتبقية للصلاحية: {ex.Message}");
            }
        }

        private async void txtDefaultExpireDate_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isInitializing)
                return;

            // حفظ الأيام المتبقية للصلاحية عند تغيير النص
            if (!string.IsNullOrEmpty(txtDefaultExpireDate.Text) && int.TryParse(txtDefaultExpireDate.Text, out int days))
            {
                SetLicenseRemainingDays(days);
            }
            else
            {
                // إذا كانت القيمة غير صالحة، نضع القيمة صفر
                SetLicenseRemainingDays(0);
                txtDefaultExpireDate.Text = "0";
            }
            if (RdbProductSelect.IsChecked == true)
            {
                await LoadExpiredProducts();
            }
            else if (RdbLensesSelect.IsChecked == true)
            {
                await LoadExpiredLenses();
            }
        }
    }

    // View model for expired items
    public class ExpiredItemViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public string ExpirationDate { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public string ItemType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public int RemainingDays { get; set; }
        public string WarehouseName { get; set; } = string.Empty;

        // Override Equals and GetHashCode to help with duplicate detection
        public override bool Equals(object obj)
        {
            if (obj is not ExpiredItemViewModel other)
                return false;

            // Consider items equal if they have the same name, color, expiration date, item type, and warehouse
            return Name == other.Name &&
                   Color == other.Color &&
                   ExpirationDate == other.ExpirationDate &&
                   ItemType == other.ItemType &&
                   WarehouseName == other.WarehouseName;
        }

        public override int GetHashCode()
        {
            // Create a hash code based on the properties we're comparing
            return HashCode.Combine(Name, Color, ExpirationDate, ItemType, WarehouseName);
        }
    }

    // Custom comparer for ExpiredItemViewModel to use with LINQ's Distinct() method
    public class ExpiredItemViewModelComparer : IEqualityComparer<ExpiredItemViewModel>
    {
        public bool Equals(ExpiredItemViewModel x, ExpiredItemViewModel y)
        {
            if (x == null && y == null)
                return true;
            if (x == null || y == null)
                return false;

            // Consider items equal if they have the same name, color, expiration date, item type, and warehouse
            return x.Name == y.Name &&
                   x.Color == y.Color &&
                   x.ExpirationDate == y.ExpirationDate &&
                   x.ItemType == y.ItemType &&
                   x.WarehouseName == y.WarehouseName;
        }

        public int GetHashCode(ExpiredItemViewModel obj)
        {
            if (obj == null)
                return 0;

            // Create a hash code based on the properties we're comparing
            return HashCode.Combine(obj.Name, obj.Color, obj.ExpirationDate, obj.ItemType, obj.WarehouseName);
        }
    }
}
