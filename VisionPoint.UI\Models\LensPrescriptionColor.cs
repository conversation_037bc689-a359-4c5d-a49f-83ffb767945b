﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VisionPoint.UI.Models;

[Index(nameof(Barcode), IsUnique = true, IsDescending =[ false])]
[Index(nameof(LensPrescriptionId))]
[Index(nameof(ColorId))]
public class LensPrescriptionColor
{
    [Key]
    public int Id { get; set; }
    public int? LensPrescriptionId { get; set; }
    public LensPrescription? LensPrescription { get; set; }
    public byte? ColorId { get; set; }
    public Color? Color { get; set; }
    [StringLength(30, ErrorMessage = "الباركود يجب ألا يتجاوز 30 حرف")]
    public string? Barcode { get; set; }
    public ICollection<LensQuantity>? LensQuantity { get; set; } = new List<LensQuantity>();

    /// <summary>
    /// Row GUID for SQL Server Merge Replication - managed by database
    /// </summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid RowGuid { get; set; }
}
