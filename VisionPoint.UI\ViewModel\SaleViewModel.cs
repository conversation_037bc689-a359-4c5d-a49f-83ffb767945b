using VisionPoint.UI.Models;

namespace VisionPoint.UI.ViewModel
{
    public class SaleViewModel
    {
        public int Index { get; set; }
        public int Id { get; set; }
        /// <summary>
        /// رقم الفاتورة
        /// </summary>
        public int InvoiceNo { get; set; }

        /// <summary>
        /// رقم الفاتورة مع رمز المخزن (مثل: MAIN-001)
        /// </summary>
        public string? InvoiceNumber { get; set; }

        public DateTime SaleDate { get; set; }
        public string ClientName { get; set; }
        public string WarehouseName { get; set; }
        public decimal TotalBeforeDiscount { get; set; }
        public decimal TotalDiscount { get; set; }
        public decimal TotalAmount { get; set; }
        /// <summary>
        /// إجمالي قيمة المنتجات المسترجعة
        /// </summary>
        public decimal TotalReturned { get; set; }
        /// <summary>
        /// إجمالي القيمة بعد خصم المسترجعات
        /// </summary>
        public decimal NetAmount => TotalAmount - TotalReturned;
        public decimal PaidAmount { get; set; }
        /// <summary>
        /// المبلغ المتبقي بعد خصم المسترجعات والمدفوعات
        /// </summary>
        public decimal RemainingAmount => NetAmount - PaidAmount;
        public Sale Sale { get; set; }
        public ICollection<SaleItemVM>? SaleItems { get; set; }
    }
}