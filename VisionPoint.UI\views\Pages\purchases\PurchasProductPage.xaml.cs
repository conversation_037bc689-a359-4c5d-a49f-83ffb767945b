using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Dialogs;
using VisionPoint.UI.views.Pages.Receipts;
using Color = VisionPoint.UI.Models.Color;

namespace VisionPoint.UI.views.Pages.purchases
{
    /// <summary>
    /// Interaction logic for PurchasProductPage.xaml
    /// </summary>
    public partial class PurchasProductPage : Page
    {
        private readonly ClientService _clientService;
        private readonly ProductService _productService;
        private readonly PurchaseService _purchaseService;
        private readonly LensService _lensService;
        private readonly TreasuryService _treasuryService;
        private readonly WarehouseService _warehouseService;
        int _purchaseId = 0;
        private List<Client> _suppliers;
        private List<Product> _products;
        private List<Lens> _lenses;
        private List<LensCategory> _lensCategories;
        private List<LensPrescription> _lensPrescriptions;
        private List<LensPrescriptionColor> _lensPrescriptionColors;
        private List<Models.Treasury> _treasuries;
        private List<Warehouse> _warehouses;
        // Property removed - using BusyService in services instead

        // إضافة مجموعة لتخزين عناصر المشتريات
        private ObservableCollection<PurchaseItemVM> _purchaseItems;

        // إضافة خاصية لحساب المجموع الكلي
        private decimal TotalAmount => _purchaseItems?.Sum(item => item.Price * item.Quantity) ?? 0;

        public PurchasProductPage(PurchaseService purchaseService, int purchaseId = 0)
        {
            InitializeComponent();

            // تهيئة المجموعة
            _purchaseItems = new ObservableCollection<PurchaseItemVM>();

            // تعيين مصدر البيانات للقائمة
            list.ItemsSource = _purchaseItems;

            // الحصول على الخدمات من حاوية DI
            _clientService = new ClientService();
            _productService = new ProductService();
            _lensService = new LensService();
            _purchaseService = purchaseService;
            _purchaseId = purchaseId;
            _treasuryService = new TreasuryService();
            _warehouseService = new WarehouseService();
        }

        // تعديل طريقة إضافة العناصر
        private void btnAdd_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (RdbProduct.IsChecked == true)
                {
                    AddProduct();
                }
                else if (RdbLenses.IsChecked == true)
                {
                    AddLens();
                }

                UpdateTotalPrice();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء إضافة العنصر: {ex.Message}", "خطأ في الإضافة", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }


        private void AddProduct()
        {
            // التحقق من المدخلات
            if (cmbProductName.SelectedItem == null)
            {
                DialogBox.Show("بيانات ناقصة", "الرجاء اختيار المنتج لإكمال العملية");
                return;
            }

            if (string.IsNullOrEmpty(txtProductQuantity.Text) || !int.TryParse(txtProductQuantity.Text, out int quantity) || quantity <= 0)
            {
                DialogBox.Show("بيانات ناقصة", "الرجاء إدخال كمية صحيحة (أكبر من صفر)");
                return;
            }
            if (string.IsNullOrEmpty(txtProductPrice.Text) || !decimal.TryParse(txtProductPrice.Text, out decimal price) || price < 0)
            {
                DialogBox.Show("بيانات ناقصة", "الرجاء إدخال سعر صحيح (أكبر من أو يساوي صفر)");
                return;
            }
            if (cmbProductColor.SelectedItem == null)
            {
                DialogBox.Show("بيانات ناقصة", "الرجاء اختيار اللون المناسب للمنتج");
                return;
            }
            // الحصول على المنتج المحدد
            var product = cmbProductName.SelectedItem as Product;
            if (product == null) return;
            int productColorId = (int)cmbProductColor.SelectedValue;
            DateOnly? expirationDate = null;
            if (product.Exp)
            {
                if (!DtpProductExpireOn.SelectedDate.HasValue)
                {
                    DialogBox.Show("بيانات ناقصة", "الرجاء تحديد تاريخ الصلاحية للمنتج");
                    return;
                }
                expirationDate = DateOnly.FromDateTime(DtpProductExpireOn.SelectedDate.Value);
            }
            // إضافة إلى المجموعة
            var existingItem = _purchaseItems.FirstOrDefault(item =>
                item.Name == product.Name &&
                item.ProductColorId == productColorId &&
                ((item.Exp == null && expirationDate == null) ||
                 (item.Exp != null && expirationDate != null && item.Exp.Value == expirationDate.Value)));

            if (existingItem != null)
            {
                // Ask user if they want to merge quantities
                var result = QuestionBox.Show(
                    "دمج العناصر المتشابهة",
                    "المنتج موجود مسبقاً بنفس البيانات. هل تريد إضافة الكمية الجديدة للكمية الموجودة؟");

                if (result == MessageBoxResult.Yes)
                {
                    // Update existing item quantity
                    existingItem.Quantity += quantity;

                    // Refresh ListView to show updated quantity
                    CollectionViewSource.GetDefaultView(_purchaseItems).Refresh();

                    // Update total price
                    UpdateTotalPrice();
                    return;
                }
                else
                {
                    return;
                }
            }

            // Create new purchase item
            var purchaseItem = new PurchaseItemVM
            {
                Name = product.Name,
                Price = price,
                Quantity = quantity,
                Exp = expirationDate,
                ProductColorId = productColorId,
                ColorName = cmbProductColor.Text
            };

            // Add to collection
            _purchaseItems.Add(purchaseItem);

            // Update total price
            UpdateTotalPrice();

            // مسح المدخلات
            ClearItem();
        }

        private void AddLens()
        {
            // التحقق من المدخلات
            if (cmbLensesName.SelectedItem == null)
            {
                DialogBox.Show("بيانات ناقصة", "الرجاء اختيار العدسة لإكمال العملية");
                return;
            }

            if (string.IsNullOrEmpty(txtLensQuantity.Text) || !int.TryParse(txtLensQuantity.Text, out int quantity) || quantity <= 0)
            {
                DialogBox.Show("بيانات ناقصة", "الرجاء إدخال كمية صحيحة (أكبر من صفر)");
                return;
            }
            if (string.IsNullOrEmpty(txtLensPrice.Text) || !decimal.TryParse(txtLensPrice.Text, out decimal price) || price < 0)
            {
                DialogBox.Show("بيانات ناقصة", "الرجاء إدخال سعر صحيح (أكبر من أو يساوي صفر)");
                return;
            }
            // الحصول على العدسة المحددة
            var lens = cmbLensesName.SelectedItem as Lens;
            if (lens == null) return;

            // الحصول على الوصفة المحددة
            var prescription = cmbPrescription.SelectedItem as PrescriptionDisplayItem;
            if (prescription == null && (lens.Sphere || lens.Cylinder || lens.Power))
            {
                DialogBox.Show("بيانات ناقصة", "الرجاء اختيار الوصفة لإكمال العملية");
                return;
            }

            // إنشاء عنصر شراء جديد
            // Validate color if lens supports it
            string colorName = string.Empty;
            int? lensPrescriptionColorId = null;
            //if (lens.Color)
            //{
            if (cmbLensColor.SelectedItem == null)
            {
                DialogBox.Show("بيانات ناقصة", "الرجاء اختيار اللون المناسب للعدسة");
                return;
            }
            lensPrescriptionColorId = _lensPrescriptionColors.First(x => x.ColorId == (byte)cmbLensColor.SelectedValue).Id;
            colorName = (cmbLensColor.SelectedItem as Color).Name;
            //}

            // Validate expiration date if lens supports it
            DateOnly? expirationDate = null;
            if (lens.Exp)
            {
                if (!DtpLensExpireOn.SelectedDate.HasValue)
                {
                    DialogBox.Show("بيانات ناقصة", "الرجاء تحديد تاريخ الصلاحية للعدسة");
                    return;
                }
                else if (DtpLensExpireOn.SelectedDate.Value < DateTime.Today)
                {
                    if (QuestionBox.Show(
                     "تحذير: تاريخ منتهي",
                     "تاريخ الصلاحية منتهي. هل تريد الاستمرار بالإضافة رغم ذلك؟") == MessageBoxResult.No) return;
                }
                expirationDate = DateOnly.FromDateTime(DtpLensExpireOn.SelectedDate.Value);
            }

            // Check for duplicate items
            var existingItem = _purchaseItems.FirstOrDefault(item =>
                item.Name == lens.Name &&
                item.LensPrescriptionColorId == lensPrescriptionColorId &&
                ((item.Exp == null && expirationDate == null) ||
                 (item.Exp != null && expirationDate != null && item.Exp.Value == expirationDate.Value)));

            if (existingItem != null)
            {
                // Ask user if they want to merge quantities
                var result = QuestionBox.Show(
                    "دمج العناصر المتشابهة",
                    "العدسة موجودة مسبقاً بنفس البيانات. هل تريد إضافة الكمية الجديدة للكمية الموجودة؟");

                if (result == MessageBoxResult.Yes)
                {
                    // Update existing item quantity
                    existingItem.Quantity += quantity;

                    // Refresh ListView to show updated quantity
                    CollectionViewSource.GetDefaultView(_purchaseItems).Refresh();

                    // Update total price
                    UpdateTotalPrice();
                    return;
                }
                else
                {
                    return;
                }
            }

            // Create new purchase item
            var purchaseItem = new PurchaseItemVM
            {
                Name = lens.Name,
                Price = price,
                Quantity = quantity,
                Exp = expirationDate,
                LensPrescriptionColorId = lensPrescriptionColorId,
                ColorName = colorName
            };

            // Add to collection
            _purchaseItems.Add(purchaseItem);

            // Update total price
            UpdateTotalPrice();

            // مسح المدخلات
            ClearItem();
        }


        private void btnDeleteItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على العنصر المرتبط بالزر من سياق البيانات
                if (sender is Button button)
                {
                    var item = button.DataContext as PurchaseItemVM;
                    if (item != null)
                    {
                        // عرض رسالة تأكيد قبل الحذف
                        if (QuestionBox.Show("تأكيد الحذف", "هل أنت متأكد من حذف هذا الصنف من قائمة المشتريات؟") == MessageBoxResult.Yes)
                        {
                            // إزالة العنصر
                            _purchaseItems.Remove(item);

                            // تحديث السعر الإجمالي والمبلغ المتبقي
                            UpdateTotalPrice();

                            DialogBox.Show("تم بنجاح", "تم حذف العنصر من قائمة المشتريات");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء حذف العنصر: {ex.Message}", "خطأ في الحذف", true);
            }
        }

        // إضافة طريقة لتحديث السعر الإجمالي
        private void UpdateTotalPrice()
        {
            txtTotalPrice.Text = TotalAmount.ToString("N3");
            UpdateRemainingAmount();
        }

        // إضافة طريقة لمعالجة تغييرات السعر المدفوع
        private void txtPaidPrice_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateRemainingAmount();
        }

        // تعديل طريقة تحديث المبلغ المتبقي
        private void UpdateRemainingAmount()
        {
            if (decimal.TryParse(txtTotalPrice.Text, out decimal totalAmount) &&
                decimal.TryParse(txtPaidPrice.Text, out decimal paidAmount))
            {
                // التأكد من أن المبلغ المدفوع لا يتجاوز الإجمالي
                if (paidAmount > totalAmount)
                {
                    paidAmount = totalAmount;
                    txtPaidPrice.Text = paidAmount.ToString("N3");
                }

                decimal remainingAmount = totalAmount - paidAmount;
                txtRemaining.Text = remainingAmount.ToString("N3");
            }
            else
            {
                txtRemaining.Text = "0.000";
            }
        }

        // تعديل طريقة إنشاء فاتورة جديدة
        private void btnNew_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                EnableEditingControls();
                // مسح جميع الحقول
                txtPurchaseNo.Clear();
                DtpPurchaseDate.SelectedDate = DateTime.Now;

                // Reset warehouse to first item
                if (_warehouses != null && _warehouses.Count > 0)
                {
                    cmbWarehouse.SelectedIndex = 0;
                }

                cmbClient.SelectedIndex = -1;

                // مسح حقول المنتج
                txtProductBarcode.Clear();
                txtProductQuantity.Text = "0";
                cmbProductName.SelectedIndex = -1;
                cmbProductColor.SelectedIndex = -1;

                // مسح حقول العدسة
                txtLensBarcode.Clear();
                txtLensQuantity.Text = "0";
                cmbLensCategory.SelectedIndex = 0; // Reset to "All Categories"
                cmbLensesName.SelectedIndex = -1;
                cmbPrescription.SelectedIndex = -1;
                cmbLensColor.SelectedIndex = -1;
                _purchaseItems.Clear();
                // مسح المجاميع
                txtTotalPrice.Text = "0.000";
                txtPaidPrice.Text = "0.000";
                txtRemaining.Text = "0.000";
                _purchaseId = 0;
                // مسح مجموعة عناصر الشراء
                txtPaidPrice.IsReadOnly = false; // إعادة تعيين خاصية القراءة فقط عند إنشاء فاتورة جديدة
                ClearItem();
                cmbClient.SelectedValue = 2;
                cmbTreasury.Visibility = Visibility.Visible;
                btnAddReceipt.Visibility = Visibility.Collapsed;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء إنشاء فاتورة جديدة: {ex.Message}", "خطأ", true);
            }
        }
        async Task GetPurchase(int purchaseId)
        {
            try
            {
                var savedPurchase = await _purchaseService.GetPurchaseByIdAsync(purchaseId);

                if (savedPurchase != null)
                {
                    _purchaseId = purchaseId;
                    // Fill in purchase details on the form
                    txtPurchaseNo.Text = savedPurchase.InvoiceNo.ToString();
                    DtpPurchaseDate.SelectedDate = savedPurchase.PurchaseDate;
                    cmbWarehouse.SelectedValue = savedPurchase.Purchase.WarehouseId;
                    cmbClient.SelectedValue = savedPurchase.Purchase.ClientId;
                    txtNotes.Text = savedPurchase.Purchase.Notes;

                    // Fill in purchase items
                    _purchaseItems.Clear();
                    foreach (var item in savedPurchase.PurchaseItems)
                    {
                        _purchaseItems.Add(item);
                    }

                    // Update totals
                    txtTotalPrice.Text = savedPurchase.TotalAmount.ToString("N3");
                    txtPaidPrice.Text = savedPurchase.PaidAmount.ToString("N3");
                    txtRemaining.Text = savedPurchase.RemainingAmount.ToString("N3");
                    txtPaidPrice.IsReadOnly = true; // جعل حقل القيمة المدفوعة للقراءة فقط عند التعديل
                    cmbTreasury.Visibility = Visibility.Collapsed;

                    // التحقق من صلاحيات المستخدم
                    bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditPurchaseRole");
                    bool canView = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("PurchaseRole") || CurrentUser.HasRole("EditPurchaseRole") || CurrentUser.HasRole("DeletePurchaseRole");

                    // إذا كان المستخدم لا يملك صلاحية التعديل، قم بتعطيل عناصر التحكم الخاصة بالتعديل
                    if (!canEdit)
                    {
                        DisableEditingControls();
                    }

                    // إظهار زر إضافة وصل إذا كان هناك مبلغ متبقي وكان المستخدم لديه صلاحية العرض على الأقل
                    if (decimal.TryParse(txtRemaining.Text, out decimal remainingAmount) && remainingAmount > 0 && canView)
                    {
                        btnAddReceipt.Visibility = Visibility.Visible;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"خطأ في تحميل بيانات الفاتورة: {ex.Message}", "خطأ في الاسترجاع", true);
            }
        }
        // تعديل طريقة حفظ الفاتورة
        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnSave.IsEnabled = false;
            btnNew.IsEnabled = false;
            btnAdd.IsEnabled = false;
            btnNewItem.IsEnabled = false;
            btnBack.IsEnabled = false;
            btnAddReceipt.IsEnabled = false;
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnSave.IsEnabled = true;
            btnNew.IsEnabled = true;
            btnAdd.IsEnabled = true;
            btnNewItem.IsEnabled = true;
            btnBack.IsEnabled = true;
            btnAddReceipt.IsEnabled = true;
        }

        private async void btnSave_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق من الصلاحيات
                if (_purchaseId > 0)
                {
                    // التحقق من صلاحية التعديل للفواتير الموجودة
                    bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditPurchaseRole");
                    if (!canEdit)
                    {
                        ErrorBox.Show("لا تملك صلاحية تعديل فواتير المشتريات", "خطأ في الصلاحيات", true);
                        return;
                    }
                }
                else
                {
                    // التحقق من صلاحية الإضافة للفواتير الجديدة
                    bool canAdd = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("PurchaseRole");
                    if (!canAdd)
                    {
                        ErrorBox.Show("لا تملك صلاحية إضافة فواتير المشتريات", "خطأ في الصلاحيات", true);
                        return;
                    }
                }

                if (cmbWarehouse.SelectedItem == null)
                {
                    DialogBox.Show("بيانات ناقصة", "الرجاء اختيار المخزن لإكمال عملية الحفظ");
                    return;
                }

                if (cmbClient.SelectedItem == null)
                {
                    DialogBox.Show("بيانات ناقصة", "الرجاء اختيار المورد لإكمال عملية الحفظ");
                    return;
                }

                if (_purchaseItems.Count == 0)
                {
                    DialogBox.Show("بيانات ناقصة", "الرجاء إضافة عناصر للفاتورة قبل الحفظ");
                    return;
                }
                decimal? paid = null;
                byte? treasuryId = null;
                if (_purchaseId == 0)
                {
                    decimal.TryParse(txtPaidPrice.Text, out decimal paidAmount);
                    if (cmbTreasury.SelectedItem == null && paidAmount > 0)
                    {
                        ErrorBox.Show("الرجاء اختيار الخزينة", "بيانات ناقصة");
                        return;
                    }
                    paid = paidAmount;
                    treasuryId = (byte?)cmbTreasury.SelectedValue;
                }
                // Create purchase object
                var purchase = new Purchase
                {
                    Id = _purchaseId,
                    WarehouseId = (int)cmbWarehouse.SelectedValue,
                    ClientId = (int)cmbClient.SelectedValue,
                    PurchaseDate = DtpPurchaseDate.SelectedDate ?? DateTime.Now,
                    TotalAmount = TotalAmount,
                    Notes = txtNotes.Text,
                    PurchaseItems = _purchaseItems.Select(item => new PurchaseItem
                    {
                        ProductColorId = item.ProductColorId,
                        LensPrescriptionColorId = item.LensPrescriptionColorId,
                        Price = item.Price,
                        Quantity = item.Quantity,
                        Exp = item.Exp
                    }).ToList()
                };

                bool success;
                string message;
                int purchaseId, invoiceNo = 0;

                // For new purchases, use the payment functionality
                if (purchase.Id == 0)
                {
                    // Save purchase with payment
                    (success, message, purchaseId, invoiceNo) = await _purchaseService.AddPurchaseAsync(purchase, paid, treasuryId);
                }
                else
                {
                    // For existing purchases, use the regular save method
                    (success, message, purchaseId, invoiceNo) = await _purchaseService.EditPurchaseAsync(purchase);
                }

                if (success)
                {
                    // Get the invoice number
                    txtPurchaseNo.Text = invoiceNo.ToString();

                    DialogBox.Show("تم بنجاح", $"تم حفظ الفاتورة رقم {invoiceNo} بنجاح");
                    btnNew_MouseLeftButtonDown(null, null); // Clear form
                    await GetPurchase(purchaseId);
                }
                else
                {
                    ErrorBox.Show(message, "خطأ في عملية الحفظ", true);
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ في النظام", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                txtProductQuantity.Text = "0";
                txtProductPrice.Text = "0";
                txtTotalPrice.Text = "0.000";
                txtPaidPrice.Text = "0.000";
                txtRemaining.Text = "0.000";
                // توليد رقم الفاتورة التالي

                // Load warehouses first
                await LoadWarehousesAsync();
                // Load suppliers
                // await LoadSuppliersAsync();
                // await LoadTreasuriesAsync();
                // Load products or lenses based on selected radio button
                if (RdbProduct.IsChecked == true)
                {
                    await LoadProductsAsync();
                }
                else if (RdbLenses.IsChecked == true)
                {
                    await LoadLensCategoriesAsync();
                }
                cmbTreasury.Visibility = Visibility.Visible;
                btnAddReceipt.Visibility = Visibility.Collapsed;

                // إذا كان هناك رقم فاتورة، قم بتحميل الفاتورة
                if (_purchaseId > 0)
                {
                    await GetPurchase(_purchaseId);

                    // التحقق من صلاحيات المستخدم
                    bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditPurchaseRole");
                    bool canView = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("PurchaseRole") || CurrentUser.HasRole("EditPurchaseRole") || CurrentUser.HasRole("DeletePurchaseRole");

                    // إذا كان المستخدم لا يملك صلاحية التعديل، قم بتعطيل عناصر التحكم الخاصة بالتعديل
                    if (!canEdit)
                    {
                        DisableEditingControls();

                        // إظهار زر إضافة وصل فقط إذا كان المستخدم لديه صلاحية العرض
                        if (canView)
                        {
                            btnAddReceipt.Visibility = Visibility.Visible;
                        }
                    }
                }

                // تعيين التاريخ الحالي
                DtpPurchaseDate.SelectedDate = DateTime.Now;

                // التحقق من صلاحية تغيير التاريخ
                bool canChangeDate = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ChangePurchaseDateRole");
                DtpPurchaseDate.IsEnabled = canChangeDate;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ في التحميل", true);
            }
        }

        private async void cmbWarehouse_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // Reload suppliers when warehouse changes
                await LoadSuppliersAsync();
                await LoadTreasuriesAsync();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تغيير المخزن: {ex.Message}", "خطأ", true);
            }
        }

        private async void cmbProductName_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (cmbProductName.SelectedItem is Product selectedProduct)
                {
                    // Show/hide color control based on product's Color property
                    if (selectedProduct.Color)
                    {
                        // Show color control
                        txtProductBarcode.Clear();

                        // Load colors for this product
                        if (selectedProduct != null && selectedProduct.ProductColors != null)
                        {
                            var colors = selectedProduct.ProductColors
                                .Where(pc => pc.Color != null)
                                .Select(pc => new { pc.Id, pc.Color.Name })
                                .ToList();

                            cmbProductColor.ItemsSource = colors;
                            cmbProductColor.DisplayMemberPath = "Name";
                            cmbProductColor.SelectedValuePath = "Id";
                            cmbProductColor.SelectedIndex = -1;
                        }
                    }
                    else
                    {
                        // Hide color control
                        var colors = selectedProduct.ProductColors
                             .Select(pc => new { pc.Id, Name = "لايوجد" })
                             .ToList();

                        cmbProductColor.ItemsSource = colors;
                        cmbProductColor.DisplayMemberPath = "Name";
                        cmbProductColor.SelectedValuePath = "Id";
                        cmbProductColor.SelectedIndex = 0;
                    }

                    // Show/hide expiration control based on product's Exp property
                    if (selectedProduct.Exp)
                    {
                        // Show expiration control
                        DtpProductExpireOn.Visibility = Visibility.Visible;
                    }
                    else
                    {
                        // Hide expiration control
                        DtpProductExpireOn.Visibility = Visibility.Collapsed;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تغيير المنتج: {ex.Message}", "خطأ", true);
            }
        }

        private async Task LoadWarehousesAsync()
        {
            try
            {
                // Get all warehouses
                _warehouses = await _warehouseService.GetAllWarehousesAsync();

                // Configure ComboBox
                cmbWarehouse.DisplayMemberPath = "Name";
                cmbWarehouse.SelectedValuePath = "Id";
                cmbWarehouse.ItemsSource = _warehouses;

                // تحديد المخزن الافتراضي بناءً على المستخدم الحالي

                if (CurrentUser.WarehouseId.HasValue)
                {
                    cmbWarehouse.SelectedValue = CurrentUser.WarehouseId.Value;
                }
                else if (_warehouses.Count > 0)
                {
                    // إذا لم يتم العثور على مخزن افتراضي، اختر الأول
                    cmbWarehouse.SelectedIndex = 0;
                }

                // تطبيق منطق الصلاحيات لتغيير المخزن
                // إذا لم يكن المستخدم مديراً أو لا يملك صلاحية تغيير المخزن، يتم تعطيل الكومبو
                cmbWarehouse.IsEnabled = CurrentUser.CanChangeWarehouse;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"خطأ في تحميل بيانات المخازن: {ex.Message}", "خطأ في الاتصال بالبيانات", true);
            }
        }

        private async Task LoadSuppliersAsync()
        {
            try
            {
                // Get suppliers based on selected warehouse
                if (cmbWarehouse.SelectedValue != null)
                {
                    int warehouseId = (int)cmbWarehouse.SelectedValue;
                    _suppliers = await _clientService.GetSuppliersByWarehouseAsync(warehouseId);
                }
                else
                {
                    // Get all suppliers if no warehouse selected
                    _suppliers = await _clientService.GetSuppliersOnlyAsync();
                }

                // Configure ComboBox
                cmbClient.DisplayMemberPath = "Name";
                cmbClient.SelectedValuePath = "Id";
                cmbClient.ItemsSource = _suppliers;

                // Select first supplier if available
                if (_suppliers.Count > 0)
                {
                    cmbClient.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"خطأ في تحميل بيانات الموردين: {ex.Message}", "خطأ في الاتصال بالبيانات", true);
            }
        }

        private async Task LoadProductsAsync()
        {
            try
            {
                // Get all products
                _products = await _productService.GetAllProductsForPurchaseAsync();

                // Configure ComboBox
                cmbProductName.DisplayMemberPath = "Name";
                cmbProductName.SelectedValuePath = "Id";
                cmbProductName.ItemsSource = _products;
                cmbProductName.SelectedIndex = -1;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"خطأ في تحميل بيانات المنتجات: {ex.Message}", "خطأ في الاتصال بالبيانات", true);
            }
        }



        private async Task LoadLensCategoriesAsync()
        {
            try
            {
                // Get all lens categories
                _lensCategories = await _lensService.GetAllLensCategoriesAsync();

                // Configure ComboBox
                cmbLensCategory.DisplayMemberPath = "Name";
                cmbLensCategory.SelectedValuePath = "Id";
                cmbLensCategory.ItemsSource = _lensCategories;

                // Add "All Categories" option
                var allCategoriesItem = new LensCategory { Id = 0, Name = "كل أنواع العدسات" };
                var categories = new List<LensCategory> { allCategoriesItem };
                categories.AddRange(_lensCategories);

                cmbLensCategory.ItemsSource = categories;
                cmbLensCategory.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"خطأ في تحميل بيانات أنواع العدسات: {ex.Message}", "خطأ في الاتصال بالبيانات", true);
            }
        }

        private async void RdbType_Checked(object sender, RoutedEventArgs e)
        {
            try
            {
                // Check which radio button is checked
                if (LensesLayout == null || ProductLayout == null)
                {
                    return;
                }

                if (RdbProduct.IsChecked == true)
                {
                    LensesLayout.Visibility = Visibility.Collapsed;
                    ProductLayout.Visibility = Visibility.Visible;

                    // Load products if not already loaded
                    if (_products == null || _products.Count == 0)
                    {
                        await LoadProductsAsync();
                    }
                }
                else if (RdbLenses.IsChecked == true)
                {
                    ProductLayout.Visibility = Visibility.Collapsed;
                    LensesLayout.Visibility = Visibility.Visible;

                    // Load lens categories and lenses if not already loaded
                    if (_lensCategories == null || _lensCategories.Count == 0)
                    {
                        await LoadLensCategoriesAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تغيير نوع العنصر: {ex.Message}", "خطأ", true);
            }
        }


        private async void txtProductBarcode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                try
                {
                    // Check if the barcode is unique
                    if (!string.IsNullOrEmpty(txtProductBarcode.Text))
                    {
                        string barcode = txtProductBarcode.Text.Trim();
                        var product = _products.FirstOrDefault(p => p.ProductColors.Any(pc => pc.Barcode == (barcode)));
                        if (product != null)
                        {
                            cmbProductName.SelectedItem = product;
                            cmbProductName_SelectionChanged(null, null);
                            // الآن نختار اللون المناسب
                            var productColor = product.ProductColors.FirstOrDefault(pc => pc.Barcode == (barcode));
                            if (productColor != null)
                            {
                                cmbProductColor.SelectedValue = productColor.Id;
                            }
                            txtProductBarcode.Text = barcode;
                        }
                        else
                        {
                            ErrorBox.Show("لم يتم العثور على منتج بهذا الباركود", "غير متوفر", true);
                        }
                    }
                    else
                    {
                        DialogBox.Show("بيانات ناقصة", "يرجى إدخال الباركود للبحث");
                    }
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"حدث خطأ أثناء البحث عن الباركود: {ex.Message}", "خطأ", true);
                }
            }
        }

        private async void cmbLensCategory_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (cmbLensCategory.SelectedItem is LensCategory selectedCategory)
                {
                    // If "All Categories" is selected (Id = 0), load all lenses
                    if (selectedCategory.Id == 0)
                    {
                        _lenses = await _lensService.GetAllLensesAsync();
                    }
                    else
                    {
                        // Otherwise, load lenses by category
                        _lenses = await _lensService.GetLensesByCategoryIdAsync(selectedCategory.Id);
                    }

                    // Update the lenses ComboBox
                    cmbLensesName.DisplayMemberPath = "Name";
                    cmbLensesName.SelectedValuePath = "Id";
                    cmbLensesName.ItemsSource = _lenses;
                    cmbLensesName.SelectedIndex = -1;

                    // Clear related fields
                    cmbPrescription.ItemsSource = null;
                    cmbLensColor.ItemsSource = null;
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"خطأ في تحميل بيانات العدسات: {ex.Message}", "خطأ في الاتصال بالبيانات", true);
            }
        }

        private async void cmbLensesName_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (cmbLensesName.SelectedItem is Lens selectedLens)
                {
                    VisibilityLens(selectedLens);
                    if (selectedLens.Sphere || selectedLens.Cylinder || selectedLens.Power)
                    {
                        // Load combined prescription values
                        await GetPrescriptionValuesAsync(selectedLens.Id);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تغيير العدسة: {ex.Message}", "خطأ", true);
            }
        }

        void VisibilityLens(Lens selectedLens)
        {
            // Handle numeric properties (Axis, BC, Dia, Addition)
            Axis.Visibility = selectedLens.Axis.HasValue ? Visibility.Visible : Visibility.Collapsed;
            BC.Visibility = selectedLens.BC.HasValue ? Visibility.Visible : Visibility.Collapsed;
            Dia.Visibility = selectedLens.Dia.HasValue ? Visibility.Visible : Visibility.Collapsed;
            Add.Visibility = selectedLens.Addtion.HasValue ? Visibility.Visible : Visibility.Collapsed;

            // Handle Color and Expiration
            cmbLensColor.Visibility = /*selectedLens.Color ?*/ Visibility.Visible /*: Visibility.Collapsed*/;
            DtpLensExpireOn.Visibility = selectedLens.Exp ? Visibility.Visible : Visibility.Collapsed;

            // Set values for displayed fields
            if (selectedLens.Axis.HasValue) txtAxis.Text = selectedLens.Axis.Value.ToString();
            if (selectedLens.BC.HasValue) txtBC.Text = selectedLens.BC.Value.ToString();
            if (selectedLens.Dia.HasValue) TxtDia.Text = selectedLens.Dia.Value.ToString();
            if (selectedLens.Addtion.HasValue) txtAdd.Text = selectedLens.Addtion.Value.ToString();

            // Handle combined prescription ComboBox
            cmbPrescription.Visibility = (selectedLens.Sphere || selectedLens.Cylinder || selectedLens.Power) ? Visibility.Visible : Visibility.Collapsed;
        }

        private async void cmbPrescription_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (cmbPrescription.SelectedItem is PrescriptionDisplayItem selectedPrescription)
                {
                    // Get the selected lens
                    if (cmbLensesName.SelectedItem is Lens selectedLens /*&& selectedLens.Color*/)
                    {
                        // Get the lens prescription ID
                        int prescriptionId = selectedPrescription.LensPrescription.Id;

                        try
                        {
                            // Get colors for this prescription
                            _lensPrescriptionColors = await _lensService.GetLensPrescriptionColorsByPrescriptionIdAsync(prescriptionId);

                            if (_lensPrescriptionColors != null && _lensPrescriptionColors.Any())
                            {
                                GetColorLens(_lensPrescriptionColors);
                            }
                            else
                            {
                                // No colors found for this prescription
                                cmbLensColor.ItemsSource = null;
                                DialogBox.Show("معلومات هامة", "لا توجد ألوان متاحة لهذه الوصفة");
                            }
                        }
                        catch (Exception ex)
                        {
                            ErrorBox.Show($"حدث خطأ أثناء تحميل بيانات الألوان: {ex.Message}", "خطأ في الاتصال بالبيانات", true);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تغيير الوصفة: {ex.Message}", "خطأ", true);
            }
        }

        void GetColorLens(List<LensPrescriptionColor> prescriptionColors)
        {
            // Extract unique colors and set to ComboBox
            var colors = prescriptionColors
                .Where(pc => pc.Color != null)
                .Select(pc => pc.Color)
                .Distinct()
                .ToList();

            cmbLensColor.ItemsSource = colors;
            cmbLensColor.DisplayMemberPath = "Name";
            cmbLensColor.SelectedValuePath = "Id";
            cmbLensColor.SelectedIndex = -1;

            // Make sure color ComboBox is visible
            cmbLensColor.Visibility = Visibility.Visible;
        }

        void FillCombLensPrescriptions(List<LensPrescription> lensPrescriptions)
        {
            if (lensPrescriptions != null)
            {
                // Create a list of combined prescription display items
                var prescriptionItems = new List<PrescriptionDisplayItem>();

                foreach (var prescription in lensPrescriptions)
                {
                    string displayText = "";

                    // Add Sphere value if available
                    if (prescription.Sphere != null)
                    {
                        displayText += $"Sph: {prescription.Sphere.Value.ToString("F2")}";
                    }

                    // Add Cylinder value if available
                    if (prescription.Cylinder != null)
                    {
                        // Add separator if needed
                        if (!string.IsNullOrEmpty(displayText))
                            displayText += " | ";

                        displayText += $"Cyl: {prescription.Cylinder.Value.ToString("F2")}";
                    }

                    // Add Power value if available
                    if (prescription.Pow != null)
                    {
                        // Add separator if needed
                        if (!string.IsNullOrEmpty(displayText))
                            displayText += " | ";

                        displayText += $"Pow: {prescription.Pow.Value.ToString("F2")}";
                    }

                    // Only add if there's something to display
                    if (!string.IsNullOrEmpty(displayText))
                    {
                        prescriptionItems.Add(new PrescriptionDisplayItem
                        {
                            Id = prescription.Id,
                            DisplayText = displayText,
                            LensPrescription = prescription
                        });
                    }
                }

                // Set to ComboBox
                cmbPrescription.ItemsSource = prescriptionItems;
                cmbPrescription.DisplayMemberPath = "DisplayText";
                cmbPrescription.SelectedValuePath = "Id";
                cmbPrescription.SelectedIndex = -1;
            }
        }

        private async Task GetPrescriptionValuesAsync(int lensId)
        {
            try
            {
                _lensPrescriptions = await _lensService.GetLensPrescriptionsByLensIdAsync(lensId);
                FillCombLensPrescriptions(_lensPrescriptions);
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل بيانات الوصفات: {ex.Message}", "خطأ في الاتصال بالبيانات", true);
            }
        }

        private async void txtLensBarcode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                try
                {
                    string barcode = txtLensBarcode.Text.Trim();

                    if (string.IsNullOrEmpty(barcode))
                    {
                        DialogBox.Show("بيانات ناقصة", "يرجى إدخال الباركود للبحث");
                        return;
                    }

                    try
                    {
                        // استخدام الطريقة الجديدة للبحث عن العدسة بالباركود
                        var lensPrescriptionColor = await _lensService.GetLensPrescriptionColorByBarcodeAsync(barcode);

                        if (lensPrescriptionColor == null)
                        {
                            ErrorBox.Show("لم يتم العثور على عدسة بهذا الباركود", "غير متوفر", true);
                            return;
                        }

                        // الحصول على معلومات العدسة والوصفة
                        var lens = lensPrescriptionColor.LensPrescription?.Lens;
                        var prescription = lensPrescriptionColor.LensPrescription;

                        if (lens == null || prescription == null)
                        {
                            ErrorBox.Show("بيانات العدسة غير مكتملة", "خطأ", true);
                            return;
                        }


                        cmbLensesName.SelectedValue = lens.Id;
                        _lensPrescriptions = lens.LensPrescriptions.ToList();
                        VisibilityLens(lens);
                        FillCombLensPrescriptions(_lensPrescriptions);
                        cmbPrescription.SelectedValue = prescription.Id;
                        _lensPrescriptionColors = lens.LensPrescriptions.First(x => x.Id == prescription.Id).LensPrescriptionColors.ToList();
                        //if (lens.Color)
                        //{
                        GetColorLens(_lensPrescriptionColors);
                        cmbLensColor.SelectedValue = lensPrescriptionColor.ColorId;
                        //}

                        // تعيين التركيز إلى حقل الكمية
                        txtLensQuantity.Focus();
                    }
                    catch (Exception ex)
                    {
                        ErrorBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ في عملية البحث", true);
                    }
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"حدث خطأ أثناء معالجة الباركود: {ex.Message}", "خطأ", true);
                }
            }
        }

        private void btnNewItem_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                ClearItem();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تفريغ الحقول: {ex.Message}", "خطأ في النظام", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }
        void ClearItem()
        {
            // Clear Product Layout fields
            txtProductBarcode.Clear();
            txtProductQuantity.Text = "0";
            txtProductPrice.Text = "0";
            cmbProductName.SelectedIndex = -1;
            cmbProductColor.SelectedIndex = -1;
            DtpProductExpireOn.SelectedDate = null;

            // Clear Lens Layout fields
            txtLensBarcode.Clear();
            txtLensQuantity.Text = "0";
            txtLensPrice.Text = "0";
            cmbLensCategory.SelectedIndex = 0; // Reset to "All Categories"
            cmbLensesName.SelectedIndex = -1;
            cmbPrescription.SelectedIndex = -1;
            cmbLensColor.SelectedIndex = -1;
            DtpLensExpireOn.SelectedDate = null;

            // Clear numeric fields
            txtAxis.Text = "0";
            txtBC.Text = "0";
            TxtDia.Text = "0";
            txtAdd.Text = "0";
        }

        // طريقة لتعطيل عناصر التحكم الخاصة بالتعديل
        private void DisableEditingControls()
        {
            // تعطيل حقول الإدخال
            cmbClient.IsEnabled = false;
            DtpPurchaseDate.IsEnabled = false;
            txtNotes.IsEnabled = false;

            // تعطيل أزرار الإضافة والتعديل
            btnAdd.IsEnabled = false;
            btnSave.IsEnabled = false;
            btnNewItem.IsEnabled = false;

            // تعطيل حقول المنتجات
            RdbProduct.IsEnabled = false;
            RdbLenses.IsEnabled = false;
            txtProductBarcode.IsEnabled = false;
            cmbProductName.IsEnabled = false;
            cmbProductColor.IsEnabled = false;
            DtpProductExpireOn.IsEnabled = false;
            txtProductQuantity.IsEnabled = false;
            txtProductPrice.IsEnabled = false;

            // تعطيل حقول العدسات
            txtLensBarcode.IsEnabled = false;
            cmbLensCategory.IsEnabled = false;
            cmbLensesName.IsEnabled = false;
            cmbPrescription.IsEnabled = false;
            cmbLensColor.IsEnabled = false;
            DtpLensExpireOn.IsEnabled = false;
            txtLensQuantity.IsEnabled = false;
            txtLensPrice.IsEnabled = false;

            // تعطيل الحقول الإضافية للعدسات
            txtAxis.IsEnabled = false;
            txtBC.IsEnabled = false;
            TxtDia.IsEnabled = false;
            txtAdd.IsEnabled = false;

            // تعطيل أزرار الحذف في القائمة
            foreach (var item in list.Items)
            {
                var container = list.ItemContainerGenerator.ContainerFromItem(item) as ListViewItem;
                if (container != null)
                {
                    var button = FindVisualChild<Button>(container);
                    if (button != null)
                    {
                        button.IsEnabled = false;
                    }
                }
            }

            // تعطيل حقل المبلغ المدفوع
            txtPaidPrice.IsEnabled = false;
            cmbTreasury.IsEnabled = false;
        }

        /// <summary>
        /// تمكين عناصر التحكم للتحرير (عكس وظيفة DisableEditingControls)
        /// </summary>
        private void EnableEditingControls()
        {
            // تمكين حقول الإدخال
            cmbClient.IsEnabled = true;
            DtpPurchaseDate.IsEnabled = true;
            txtNotes.IsEnabled = true;

            // تمكين أزرار الإضافة والتعديل
            btnAdd.IsEnabled = true;
            btnSave.IsEnabled = true;
            btnNewItem.IsEnabled = true;

            // تمكين حقول المنتجات
            RdbProduct.IsEnabled = true;
            RdbLenses.IsEnabled = true;
            txtProductBarcode.IsEnabled = true;
            cmbProductName.IsEnabled = true;
            cmbProductColor.IsEnabled = true;
            DtpProductExpireOn.IsEnabled = true;
            txtProductQuantity.IsEnabled = true;
            txtProductPrice.IsEnabled = true;

            // تمكين حقول العدسات
            txtLensBarcode.IsEnabled = true;
            cmbLensCategory.IsEnabled = true;
            cmbLensesName.IsEnabled = true;
            cmbPrescription.IsEnabled = true;
            cmbLensColor.IsEnabled = true;
            DtpLensExpireOn.IsEnabled = true;
            txtLensQuantity.IsEnabled = true;
            txtLensPrice.IsEnabled = true;

            // تمكين الحقول الإضافية للعدسات
            txtAxis.IsEnabled = true;
            txtBC.IsEnabled = true;
            TxtDia.IsEnabled = true;
            txtAdd.IsEnabled = true;

            // تمكين أزرار الحذف في القائمة
            foreach (var item in list.Items)
            {
                var container = list.ItemContainerGenerator.ContainerFromItem(item) as ListViewItem;
                if (container != null)
                {
                    var button = FindVisualChild<Button>(container);
                    if (button != null)
                    {
                        button.IsEnabled = true;
                    }
                }
            }

            // تمكين حقل المبلغ المدفوع
            txtPaidPrice.IsEnabled = true;
            cmbTreasury.IsEnabled = true;
        }

        // طريقة مساعدة للبحث عن عنصر في شجرة العناصر المرئية
        private T FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(parent, i);

                if (child != null && child is T)
                    return (T)child;

                T childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }

            return null;
        }

        private async Task LoadTreasuriesAsync()
        {
            try
            {
                if (cmbTreasury == null) return;
                int warehouseId = (int)cmbWarehouse.SelectedValue;
                // Get all treasuries
                _treasuries = await _treasuryService.GetTreasuriesByWarehouseAsync(warehouseId);

                // Configure ComboBox
                cmbTreasury.DisplayMemberPath = "Name";
                cmbTreasury.SelectedValuePath = "Id";
                cmbTreasury.ItemsSource = _treasuries;

                // Select first treasury if available

                cmbTreasury.SelectedIndex = -1;

            }
            catch (Exception ex)
            {
                ErrorBox.Show($"خطأ في تحميل بيانات الخزائن: {ex.Message}", "خطأ");
            }
        }

        // Helper class for displaying combined prescription values
        private class PrescriptionDisplayItem
        {
            public int Id { get; set; }
            public string DisplayText { get; set; }
            public LensPrescription LensPrescription { get; set; }
        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (list.View is GridView gridView && gridView.Columns.Count >= 8)
            {
                double fixedActionColumn = 70;
                double scrollbarWidth = SystemParameters.VerticalScrollBarWidth;
                double padding = 10;

                double availableWidth = list.ActualWidth - scrollbarWidth - padding - fixedActionColumn;

                // Total ratio units (sum of all ratios except action column)
                double totalRatio = 0.5 + 2.0 + 1.5 + 1.0 + 1.0 + 1.5 + 1.5;

                // Calculate per-ratio width
                double unitWidth = availableWidth / totalRatio;

                gridView.Columns[0].Width = unitWidth * 0.5;  // #
                gridView.Columns[1].Width = unitWidth * 2.0;  // Name
                gridView.Columns[2].Width = unitWidth * 1.5;  // Color
                gridView.Columns[3].Width = unitWidth * 1.0;  // Price
                gridView.Columns[4].Width = unitWidth * 1.0;  // Quantity
                gridView.Columns[5].Width = unitWidth * 1.5;  // Expiry
                gridView.Columns[6].Width = unitWidth * 1.5;  // Total

                gridView.Columns[7].Width = fixedActionColumn;  // Action buttons
            }
        }

        private async void btnAddReceipt_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                AddReceiptWindow add = new AddReceiptWindow(decimal.Parse(txtRemaining.Text), purchaseId: _purchaseId);
                if (add.ShowDialog() == true) await GetPurchase(_purchaseId);
            }
            finally
            {
                EnableAllButtons();
            }
        }


        private void TextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                textBox.SelectAll();
            }
        }

        private void btnBack_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // Close the page by navigating back if possible
                if (NavigationService != null && NavigationService.CanGoBack)
                {
                    NavigationService.GoBack();
                }
                else
                {
                    // Optionally, navigate to a default page or clear the content
                    if (this.Parent is Frame frame)
                    {
                        frame.Content = null;
                    }
                }
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void Control_VisibilityChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            UpdateGridColumns();
        }

        private void UpdateGridColumns()
        {
            bool isColorVisible = cmbProductColor.Visibility == Visibility.Visible;
            bool isExpireVisible = DtpProductExpireOn.Visibility == Visibility.Visible;

            if (isColorVisible && isExpireVisible)
            {
                // Both visible – split equally
                ColColor.Width = new GridLength(1, GridUnitType.Star);
                ColExpire.Width = new GridLength(1, GridUnitType.Star);
            }
            else if (isColorVisible && !isExpireVisible)
            {
                ColColor.Width = new GridLength(1, GridUnitType.Star);
                ColExpire.Width = new GridLength(0);
            }
            else if (!isColorVisible && isExpireVisible)
            {
                ColColor.Width = new GridLength(0);
                ColExpire.Width = new GridLength(1, GridUnitType.Star);
            }
            else
            {
                // Both collapsed – optional behavior
                ColColor.Width = new GridLength(0);
                ColExpire.Width = new GridLength(0);
            }
        }


    }
}
