﻿using Microsoft.Win32;
using System.Drawing.Printing;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using VisionPoint.UI.views.Dialogs;
using VisionPoint.UI.views.Pages.Expire;
using VisionPoint.UI.views.Pages.MinimumQuantity;
using VisionPoint.UI.views.Pages.Users;

namespace VisionPoint.UI.views.Pages.Settings
{
    /// <summary>
    /// Interaction logic for SettingsPage.xaml
    /// </summary>
    public partial class SettingsPage : Page
    {
        public SettingsPage()
        {
            InitializeComponent();
        }



        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            var printers = GetAllPrinterNames();
            if (printers != null)
            {
                cmbDefaultPrinter.ItemsSource = printers;
                cmbDefaultPrinter.SelectedItem = GetDefaultPrinterName();
            }
            else
            {
                ErrorBox.Show("لا توجد طابعات متاحة", "خطأ", true);
            }

            txtArabicAddress.Text = GetArabicAddress();
            txtEnglishAddress.Text = GetEnglishAddress();
            txtPhoneNumber.Text = GetPhoneNumber();
            txtBranch.Text = GetBranchName();

            // عرض الأيام المتبقية للصلاحية
            int remainingDays = GetLicenseRemainingDays();
            txtDefaultExpireDate.Text = remainingDays.ToString();

            // تحميل إعداد السماح بالكميات بالسالب
            chkAllowNegativeQuantities.IsChecked = GetAllowNegativeQuantities();



            // تحميل إعداد تفعيل فحص الحد الأدنى للكمية
            chkEnableMinimumQuantityCheck.IsChecked = GetEnableMinimumQuantityCheck();



            // تحميل إعدادات النسخ الاحتياطي التلقائي
            chkAutoBackupEnabled.IsChecked = GetAutoBackupEnabled();
            txtAutoBackupPath.Text = GetAutoBackupPath();
            txtAutoBackupMaxCount.Text = GetAutoBackupMaxCount().ToString();
        }

        private void btnBorrowMoney_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (!CheckRole(new[] { "Admin", "User" }))
                return;
            BorrowMoneyPage page = new BorrowMoneyPage();
            page.ShowDialog();
        }


        public string GetDefaultPrinterName()
        {
            try
            {
                string defaultPrinter = Properties.Settings.Default.DefaultPrinter;
                return defaultPrinter;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على الطابعة الافتراضية: {ex.Message}");
                return string.Empty; // Return empty if error occurs
            }
        }


        public void SetDefaultPrinterName(string printerName)
        {
            try
            {
                if (printerName == Properties.Settings.Default.DefaultPrinter)
                {
                    return;
                }
                Properties.Settings.Default.DefaultPrinter = printerName;
                Properties.Settings.Default.Save(); // Save to user config
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تعيين الطابعة الافتراضية: {ex.Message}");
            }
        }

        // English Address
        public string GetEnglishAddress()
        {
            try
            {
                return Properties.Settings.Default.EnglishAddress;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على العنوان الإنجليزي: {ex.Message}");
                return string.Empty;
            }
        }

        public void SetEnglishAddress(string address)
        {
            try
            {
                Properties.Settings.Default.EnglishAddress = address;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تعيين العنوان الإنجليزي: {ex.Message}");
            }
        }

        // Arabic Address
        public string GetArabicAddress()
        {
            try
            {
                return Properties.Settings.Default.ArabicAddress;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على العنوان العربي: {ex.Message}");
                return string.Empty;
            }
        }

        public void SetArabicAddress(string address)
        {
            try
            {
                Properties.Settings.Default.ArabicAddress = address;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تعيين العنوان العربي: {ex.Message}");
            }
        }

        // Phone Number
        public string GetPhoneNumber()
        {
            try
            {
                return Properties.Settings.Default.PhoneNumber;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على رقم الهاتف: {ex.Message}");
                return string.Empty;
            }
        }

        public void SetPhoneNumber(string phone)
        {
            try
            {
                Properties.Settings.Default.PhoneNumber = phone;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تعيين رقم الهاتف: {ex.Message}");
            }
        }


        public string GetBranchName()
        {
            try
            {
                return Properties.Settings.Default.BranchName;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على اسم الفرع: {ex.Message}");
                return string.Empty;
            }
        }

        public void SetBranchName(string Name)
        {
            try
            {
                Properties.Settings.Default.BranchName = Name;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تعيين اسم الفرع: {ex.Message}");
            }
        }

        // الأيام المتبقية للصلاحية
        public int GetLicenseRemainingDays()
        {
            try
            {
                return Properties.Settings.Default.LicenseRemainingDays;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على الأيام المتبقية للصلاحية: {ex.Message}");
                return 0;
            }
        }

        public void SetLicenseRemainingDays(int days)
        {
            try
            {
                Properties.Settings.Default.LicenseRemainingDays = days;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تعيين الأيام المتبقية للصلاحية: {ex.Message}");
            }
        }

        // Get all available printers
        public string[]? GetAllPrinterNames()
        {
            try
            {
                PrinterSettings.StringCollection printers = PrinterSettings.InstalledPrinters;
                return printers.Cast<string>().ToArray();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على الطابعات: {ex.Message}");
                return null;
            }
        }

        private void btnSaveSettings_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (!CheckRole(new[] { "Admin", "SystemConfigRole" }))
                return;

            if (cmbDefaultPrinter.SelectedItem == null)
            {
                ErrorBox.Show("الرجاء اختيار طابعة افتراضية", "خطأ", true);
                return;
            }
            SetDefaultPrinterName((string)cmbDefaultPrinter.SelectedItem);

            if (string.IsNullOrEmpty(txtArabicAddress.Text))
            {
                ErrorBox.Show("الرجاء إدخال عنوان باللغة العربية", "خطأ", true);
                return;
            }
            SetArabicAddress(txtArabicAddress.Text);
            if (string.IsNullOrEmpty(txtEnglishAddress.Text))
            {
                ErrorBox.Show("الرجاء إدخال عنوان باللغة الإنجليزية", "خطأ", true);
                return;
            }
            SetEnglishAddress(txtEnglishAddress.Text);
            if (string.IsNullOrEmpty(txtPhoneNumber.Text))
            {
                ErrorBox.Show("الرجاء إدخال رقم الهاتف", "خطأ", true);
                return;
            }
            SetPhoneNumber(txtPhoneNumber.Text);
            SetBranchName(txtBranch.Text);
            // الأيام المتبقية للصلاحية يتم حفظها تلقائيًا عند تغيير النص
            // لذلك لا نحتاج لحفظها هنا مرة أخرى

            // إعداد السماح بالكميات بالسالب يتم حفظه تلقائيًا عند تغيير حالة الـ Checkbox
            // لذلك لا نحتاج لحفظه هنا مرة أخرى



            // عرض رسالة نجاح
            DialogBox.Show("تم الحفظ", "تم حفظ الإعدادات بنجاح");
        }



        private void btnDiscount_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (!CheckRole(new[] { "Admin" }))
                return;
            DiscountWindow discountWindow = new DiscountWindow();
            discountWindow.ShowDialog();
        }

        private void TextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                textBox.SelectAll();
            }
        }

        private void btnExipres_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (!CheckRole(new[] { "Admin", "ProductRole", "EditProductRole" }))
                return;
            ExpireWindow expireWindow = new ExpireWindow(true);
            expireWindow.ShowDialog();
            txtDefaultExpireDate.Text = GetLicenseRemainingDays().ToString();
        }

        private void btnMinimumQuantity_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (!CheckRole(new[] { "Admin" }))
                return;

            MinimumQuantityWindow minimumQuantityWindow = new MinimumQuantityWindow(true);
            minimumQuantityWindow.ShowDialog();
        }

        private void txtDefaultExpireDate_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (!CheckRole(new[] { "Admin", "ProductRole", "EditProductRole" }))
                return;
            // حفظ الأيام المتبقية للصلاحية عند تغيير النص
            if (!string.IsNullOrEmpty(txtDefaultExpireDate.Text) && int.TryParse(txtDefaultExpireDate.Text, out int days))
            {
                SetLicenseRemainingDays(days);
            }
            else if (string.IsNullOrEmpty(txtDefaultExpireDate.Text))
            {
                // إذا كان الحقل فارغًا، لا نقوم بأي إجراء حتى يكمل المستخدم الإدخال
                return;
            }
            else
            {
                // إذا كانت القيمة غير صالحة، نضع القيمة صفر
                SetLicenseRemainingDays(0);
                txtDefaultExpireDate.Text = "0";
            }
        }

        // السماح بالكميات بالسالب
        public bool GetAllowNegativeQuantities()
        {
            try
            {
                return Properties.Settings.Default.AllowNegativeQuantities;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على إعداد السماح بالكميات بالسالب: {ex.Message}");
                return false;
            }
        }

        public void SetAllowNegativeQuantities(bool allow)
        {
            if (!CheckRole(new[] { "Admin" }))
                return;
            try
            {
                Properties.Settings.Default.AllowNegativeQuantities = allow;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تعيين إعداد السماح بالكميات بالسالب: {ex.Message}");
            }
        }

        private void chkAllowNegativeQuantities_Checked(object sender, RoutedEventArgs e)
        {
            SetAllowNegativeQuantities(true);
        }

        private void chkAllowNegativeQuantities_Unchecked(object sender, RoutedEventArgs e)
        {
            SetAllowNegativeQuantities(false);
        }



        // تفعيل فحص الحد الأدنى للكمية
        public bool GetEnableMinimumQuantityCheck()
        {
            try
            {
                return Properties.Settings.Default.EnableMinimumQuantityCheck;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على إعداد تفعيل فحص الحد الأدنى للكمية: {ex.Message}");
                return true; // القيمة الافتراضية هي تفعيل الفحص
            }
        }

        public void SetEnableMinimumQuantityCheck(bool enable)
        {
            if (!CheckRole(new[] { "Admin" }))
                return;
            try
            {
                Properties.Settings.Default.EnableMinimumQuantityCheck = enable;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تعيين إعداد تفعيل فحص الحد الأدنى للكمية: {ex.Message}");
            }
        }

        private void chkEnableMinimumQuantityCheck_Checked(object sender, RoutedEventArgs e)
        {
            SetEnableMinimumQuantityCheck(true);
        }

        private void chkEnableMinimumQuantityCheck_Unchecked(object sender, RoutedEventArgs e)
        {
            SetEnableMinimumQuantityCheck(false);
        }



        bool CheckRole(string[] roles)
        {
            if (!CurrentUser.HasAnyRole(roles))
            {
                ErrorBox.Show("لا تملك صلاحية الوصول لهذه الصفحة", "خطأ في الصلاحيات", true);
                return false;
            }
            return true;
        }

        private void btnBackup_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (!CheckRole(new[] { "Admin", "BackupRole" }))
                return;

            try
            {
                // Show the backup dialog
                var backupDialog = new DatabaseBackupDialog(isRestore: false);
                backupDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", true);
            }
        }

        // Auto Backup Enabled
        public bool GetAutoBackupEnabled()
        {
            try
            {
                return Properties.Settings.Default.AutoBackupEnabled;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على إعداد تفعيل النسخ الاحتياطي التلقائي: {ex.Message}");
                return false;
            }
        }

        public void SetAutoBackupEnabled(bool enabled)
        {
            if (!CheckRole(new[] { "Admin" }))
                return;
            try
            {
                Properties.Settings.Default.AutoBackupEnabled = enabled;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تعيين إعداد تفعيل النسخ الاحتياطي التلقائي: {ex.Message}");
            }
        }

        private void chkAutoBackupEnabled_Checked(object sender, RoutedEventArgs e)
        {
            SetAutoBackupEnabled(true);
        }

        private void chkAutoBackupEnabled_Unchecked(object sender, RoutedEventArgs e)
        {
            SetAutoBackupEnabled(false);
        }

        // Auto Backup Path
        public string GetAutoBackupPath()
        {
            try
            {
                return Properties.Settings.Default.AutoBackupPath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على مسار النسخ الاحتياطي التلقائي: {ex.Message}");
                return string.Empty;
            }
        }

        public void SetAutoBackupPath(string path)
        {
            if (!CheckRole(new[] { "Admin" }))
                return;
            try
            {
                Properties.Settings.Default.AutoBackupPath = path;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تعيين مسار النسخ الاحتياطي التلقائي: {ex.Message}");
            }
        }

        private void btnBrowseAutoBackupPath_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (!CheckRole(new[] { "Admin" }))
                return;

            // Use WPF's folder browser dialog
            var dialog = new SaveFileDialog
            {
                Title = "اختر مجلد النسخ الاحتياطي التلقائي",
                FileName = "اختر هذا المجلد", // This is a placeholder
                CheckPathExists = true,
                ValidateNames = false,
                CheckFileExists = false,
                OverwritePrompt = false
            };

            // Set initial directory if one is already set
            if (!string.IsNullOrEmpty(txtAutoBackupPath.Text) && Directory.Exists(txtAutoBackupPath.Text))
            {
                dialog.InitialDirectory = txtAutoBackupPath.Text;
            }

            if (dialog.ShowDialog() == true)
            {
                // Get the directory path from the selected file path
                string directoryPath = Path.GetDirectoryName(dialog.FileName);
                txtAutoBackupPath.Text = directoryPath;
                SetAutoBackupPath(directoryPath);
            }
        }

        // Auto Backup Max Count
        public int GetAutoBackupMaxCount()
        {
            try
            {
                return Properties.Settings.Default.AutoBackupMaxCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على عدد النسخ الاحتياطية التلقائية: {ex.Message}");
                return 5; // Default value
            }
        }

        public void SetAutoBackupMaxCount(int count)
        {
            if (!CheckRole(new[] { "Admin" }))
                return;
            try
            {
                Properties.Settings.Default.AutoBackupMaxCount = count;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تعيين عدد النسخ الاحتياطية التلقائية: {ex.Message}");
            }
        }

        private void txtAutoBackupMaxCount_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (!CheckRole(new[] { "Admin" }))
                return;

            if (!string.IsNullOrEmpty(txtAutoBackupMaxCount.Text) && int.TryParse(txtAutoBackupMaxCount.Text, out int count))
            {
                if (count < 1)
                {
                    count = 1;
                    txtAutoBackupMaxCount.Text = "1";
                }
                SetAutoBackupMaxCount(count);
            }
            else if (string.IsNullOrEmpty(txtAutoBackupMaxCount.Text))
            {
                // Do nothing until user completes input
                return;
            }
            else
            {
                // Invalid value, set to default
                SetAutoBackupMaxCount(5);
                txtAutoBackupMaxCount.Text = "5";
            }
        }

        private void btnRestore_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (!CheckRole(new[] { "Admin", "BackupRole" }))
                return;

            // Show confirmation dialog
            var result = QuestionBox.Show(
                "تأكيد استعادة النسخة الاحتياطية",
                "سيتم استبدال قاعدة البيانات الحالية بالنسخة الاحتياطية. هل أنت متأكد من المتابعة؟");

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    // Show the restore dialog
                    var restoreDialog = new DatabaseBackupDialog(isRestore: true);
                    if (restoreDialog.ShowDialog() == true)
                    {
                        // If restore was successful, inform the user that the application will restart
                        DialogBox.Show(
                            "استعادة ناجحة",
                            "تم استعادة قاعدة البيانات بنجاح. يرجى إعادة تشغيل التطبيق لتطبيق التغييرات.");

                        // Close the application
                        Application.Current.Shutdown();
                    }
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"حدث خطأ أثناء استعادة النسخة الاحتياطية: {ex.Message}", "خطأ", true);
                }
            }
        }
    }
}
