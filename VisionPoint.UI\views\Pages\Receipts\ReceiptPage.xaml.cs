using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using Microsoft.Extensions.DependencyInjection;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Controls;
using VisionPoint.UI.views.Dialogs;
using VisionPoint.UI.Utils;
using VisionPoint.UI.Helper;
using VisionPoint.UI.Reports.Reciept;

namespace VisionPoint.UI.views.Pages.Receipts
{
    /// <summary>
    /// صفحة إدارة الوصلات
    /// </summary>
    public partial class ReceiptPage : Page
    {
        private readonly ClientService _clientService;
        private readonly UserService _userService;
        private readonly TreasuryService _treasuryService;
        private readonly PurchaseService _purchaseService;
        private readonly SaleService _saleService;
        private readonly ReceiptService _receiptService;
        private readonly ExpenseService _expenseService;
        private readonly WarehouseService _warehouseService;

        private List<Client> _clients;
        private List<User> _employees;
        private List<Models.Treasury> _treasuries;
        private List<Financial> _financials;
        private List<Receipt> _receipts;
        private List<Expense> _expenses;
        private List<Warehouse> _warehouses;

        // لتخزين معلومات الفواتير المحملة
        private Purchase _loadedPurchase;
        private Sale _loadedSale;

        private Receipt _currentReceipt;
        private bool _isEditMode = false;

        public ReceiptPage()
        {
            InitializeComponent();

            // الحصول على الخدمات
            _clientService = ServiceLocator.GetService<ClientService>();
            _userService = ServiceLocator.GetService<UserService>();
            _treasuryService = ServiceLocator.GetService<TreasuryService>();
            _purchaseService = ServiceLocator.GetService<PurchaseService>();
            _saleService = ServiceLocator.GetService<SaleService>();
            _receiptService = ServiceLocator.GetService<ReceiptService>();
            _expenseService = ServiceLocator.GetService<ExpenseService>();
            _warehouseService = ServiceLocator.GetService<WarehouseService>();

            // تهيئة البيانات
            _currentReceipt = new Receipt();

            // ربط حدث الضغط على مفتاح في حقل رقم الفاتورة
            txtInvoiceNo.KeyDown += TxtInvoiceNo_KeyDown;
        }

        private async void TxtInvoiceNo_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && cmbType.SelectedItem is Financial selectedFinancial)
            {
                if (int.TryParse(txtInvoiceNo.Text, out int invoiceNo))
                {
                    if (selectedFinancial.Id == (byte)FinancialId.Purchase)
                    { // Si no se encuentra por número de factura, intentar buscar por ID (para compatibilidad)
                            await LoadPurchaseInvoice(invoiceNo);
                    }
                    else if (selectedFinancial.Id == (byte)FinancialId.Sale)
                    {  // Si no se encuentra por número de factura, intentar buscar por ID (para compatibilidad)
                            await LoadSaleInvoice(invoiceNo);
                    }
                }
            }
        }

        private async Task LoadPurchaseInvoice(int invoice)
        {
            try
            {
                // الحصول على المخزن المختار
                int? selectedWarehouseId = null;
                if (cmbWarehouse.SelectedValue != null && cmbWarehouse.SelectedValue is int warehouseId && warehouseId != -1)
                {
                    selectedWarehouseId = warehouseId;
                }

                // البحث عن الفاتورة بالرقم مباشرة
                var purchaseViewModel = await _purchaseService.GetPurchaseByInvoiceNoAsync(invoice);

                if (purchaseViewModel != null)
                {
                    // التحقق من المخزن إذا كان محدد
                    if (selectedWarehouseId.HasValue && purchaseViewModel.Purchase.WarehouseId != selectedWarehouseId.Value)
                    {
                        string warehouseName = _warehouses.FirstOrDefault(w => w.Id == selectedWarehouseId.Value)?.Name ?? "المخزن المحدد";
                        ErrorBox.Show($"فاتورة المشتريات رقم {invoice} لا تنتمي إلى {warehouseName}", "خطأ");
                        _loadedPurchase = null;
                        cmbClient.IsEnabled = true;
                        return;
                    }
                }

                if (purchaseViewModel != null)
                {
                    _loadedPurchase = purchaseViewModel.Purchase;

                    if (_loadedPurchase.ClientId > 0)
                    {
                        cmbClient.SelectedValue = _loadedPurchase.ClientId;
                        cmbClient.IsEnabled = false;
                    }

                    decimal paidAmount = _loadedPurchase.Receipts?.Sum(r => r.Value) ?? 0;
                    decimal remainingAmount = _loadedPurchase.TotalAmount - paidAmount;

                    txtValue.Text = remainingAmount.ToString("N2");
                    txtStatment.Text = "فاتورة مشتريات رقم " + _loadedPurchase.InvoiceNo;
                    DialogBox.Show("تم تحميل الفاتورة", $"المبلغ المتبقي: {remainingAmount:N2}");
                }
                else
                {
                    ErrorBox.Show("لم يتم العثور على الفاتورة", "خطأ");
                    _loadedPurchase = null;
                    cmbClient.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل الفاتورة: {ex.Message}", "خطأ");
                _loadedPurchase = null;
                cmbClient.IsEnabled = true;
            }
        }

        private async Task LoadSaleInvoice(int invoice)
        {
            try
            {
                // الحصول على المخزن المختار
                int? selectedWarehouseId = null;
                if (cmbWarehouse.SelectedValue != null && cmbWarehouse.SelectedValue is int warehouseId && warehouseId != -1)
                {
                    selectedWarehouseId = warehouseId;
                }

                // البحث عن الفاتورة بالرقم مباشرة
                var saleViewModel = await _saleService.GetSaleByInvoiceNoAsync(invoice);

                if (saleViewModel != null)
                {
                    // التحقق من المخزن إذا كان محدد
                    if (selectedWarehouseId.HasValue && saleViewModel.Sale.WarehouseId != selectedWarehouseId.Value)
                    {
                        string warehouseName = _warehouses.FirstOrDefault(w => w.Id == selectedWarehouseId.Value)?.Name ?? "المخزن المحدد";
                        ErrorBox.Show($"فاتورة المبيعات رقم {invoice} لا تنتمي إلى {warehouseName}", "خطأ");
                        _loadedSale = null;
                        cmbClient.IsEnabled = true;
                        return;
                    }

                    _loadedSale = saleViewModel.Sale;

                    if (_loadedSale.ClientId > 0)
                    {
                        cmbClient.SelectedValue = _loadedSale.ClientId;
                        cmbClient.IsEnabled = false;
                    }

                    decimal paidAmount = _loadedSale.Receipts?.Sum(r => r.Value) ?? 0;
                    decimal remainingAmount = _loadedSale.TotalAmount - paidAmount;

                    txtValue.Text = remainingAmount.ToString("N2");
                    txtStatment.Text = "فاتورة بيع رقم " + _loadedSale.InvoiceNo;
                    DialogBox.Show("تم تحميل الفاتورة", $"المبلغ المتبقي: {remainingAmount:N2}");
                }
                else
                {
                    ErrorBox.Show("لم يتم العثور على الفاتورة", "خطأ");
                    _loadedSale = null;
                    cmbClient.IsEnabled = true;
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل الفاتورة: {ex.Message}", "خطأ");
                _loadedSale = null;
                cmbClient.IsEnabled = true;
            }
        }

        private async void Page_Loaded(object sender, RoutedEventArgs e)
        {
            // ضبط تاريخ اليوم كقيمة افتراضية
            DtpGeneralExpireOn.SelectedDate = DateTime.Today;

            // التحقق من صلاحية تغيير التاريخ
            bool canChangeDate = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ChangeReceiptDateRole");
            DtpGeneralExpireOn.IsEnabled = canChangeDate;

            // التحقق من صلاحيات المستخدم للتعديل والحذف
            bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditReceiptRole");
            bool canDelete = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("DeleteReceiptRole");

            // إخفاء زر الحذف إذا لم يكن المستخدم يملك صلاحية الحذف
            btnDelete.Visibility = canDelete ? Visibility.Visible : Visibility.Collapsed;

            await LoadData();
            await LoadReceiptsList();
        }

        private async Task LoadData()
        {
            try
            {
                // تحميل البيانات الأساسية فقط
                txtValue.Text = "0";

                // تحميل المخازن والأنواع المالية فقط
                _warehouses = await _warehouseService.GetAllWarehousesAsync();
                _financials = await _receiptService.GetFinancials();

                // تكوين كومبوبوكس المخازن
                var warehouseItems = new List<object>();
                warehouseItems.Add(new { Id = -1, Name = "الكل" }); // خيار "الكل"
                warehouseItems.AddRange(_warehouses.Select(w => new { Id = w.Id, Name = w.Name }));

                cmbWarehouse.ItemsSource = warehouseItems;
                cmbWarehouse.DisplayMemberPath = "Name";
                cmbWarehouse.SelectedValuePath = "Id";

                // تكوين كومبوبوكس نوع العملية المالية
                cmbType.ItemsSource = _financials;
                cmbType.DisplayMemberPath = "Name";
                cmbType.SelectedValuePath = "Id";

                // جعل الاختيار الأول افتراضياً
                if (_financials.Count > 0)
                {
                    cmbType.SelectedIndex = 0;
                }

                // تحديد المخزن الافتراضي بناءً على المستخدم الحالي
                if (CurrentUser.WarehouseId.HasValue && _warehouses.Any(w => w.Id == CurrentUser.WarehouseId.Value))
                {
                    // اختيار مخزن المستخدم الحالي
                    cmbWarehouse.SelectedValue = CurrentUser.WarehouseId.Value;
                }
                else
                {
                    // اختيار "الكل" افتراضياً
                    cmbWarehouse.SelectedIndex = 0;
                }

                // تطبيق منطق الصلاحيات لتغيير المخزن
                // إذا لم يكن المستخدم مديراً أو لا يملك صلاحية تغيير المخزن، يتم تعطيل الكومبو
                cmbWarehouse.IsEnabled = CurrentUser.CanChangeWarehouse;

            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ");
            }
        }

        private async Task LoadReceiptsList()
        {
            _receipts = await _receiptService.GetAllReceipts();
            list.ItemsSource = _receipts;
        }

        private bool _isLoadingData = false;

        /// <summary>
        /// تحميل طرق الدفع والعملاء بناءً على المخزن المختار
        /// </summary>
        private async Task LoadTreasuriesAndClients()
        {
            if (_isLoadingData) return; // منع التحميل المتزامن

            try
            {
                _isLoadingData = true;

                // الحصول على المخزن المختار
                int? selectedWarehouseId = null;
                if (cmbWarehouse.SelectedValue != null && cmbWarehouse.SelectedValue is int warehouseId && warehouseId != -1)
                {
                    selectedWarehouseId = warehouseId;
                }

                // تحميل طرق الدفع حسب المخزن
                if (selectedWarehouseId.HasValue && selectedWarehouseId.Value != -1)
                {
                    _treasuries = await _treasuryService.GetTreasuriesByWarehouseAsync(selectedWarehouseId.Value);
                }
                else
                {
                    _treasuries = await _treasuryService.GetAllTreasuriesAsync();
                }

                // التحقق من أن الصفحة لا تزال مفتوحة
                if (cmbTreasury != null)
                {
                    cmbTreasury.ItemsSource = _treasuries;
                    cmbTreasury.DisplayMemberPath = "Name";
                    cmbTreasury.SelectedValuePath = "Id";
                    cmbTreasury.SelectedIndex = _treasuries.Count > 0 ? 0 : -1;
                }

                // تحميل العملاء حسب المخزن
                _clients = await _clientService.GetClientsByWarehouseAsync(selectedWarehouseId);

                // التحقق من أن الصفحة لا تزال مفتوحة
                if (cmbClient != null)
                {
                    cmbClient.ItemsSource = _clients;
                    cmbClient.DisplayMemberPath = "Name";
                    cmbClient.SelectedValuePath = "Id";
                    // جعل الاختيار الأول افتراضياً إذا كان العميل مرئياً ومطلوباً
                    if (cmbClient.Visibility == Visibility.Visible && _clients.Count > 0)
                    {
                        cmbClient.SelectedIndex = 0;
                    }
                    else
                    {
                        cmbClient.SelectedIndex = -1;
                    }
                }
            }
            catch (Exception ex)
            {
                if (this.IsLoaded) // التحقق من أن الصفحة لا تزال محملة
                {
                    ErrorBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ");
                }
            }
            finally
            {
                _isLoadingData = false;
            }
        }

        /// <summary>
        /// تحميل بيانات الموظفين عند الحاجة
        /// </summary>
        private async Task LoadEmployees()
        {
            if (_employees == null)
            {
                try
                {
                    _employees = await _userService.GetAllUsersAsync();

                    if (cmbEmploye != null)
                    {
                        cmbEmploye.ItemsSource = _employees;
                        cmbEmploye.DisplayMemberPath = "UserName";
                        cmbEmploye.SelectedValuePath = "Id";
                        cmbEmploye.SelectedIndex = _employees.Count > 0 ? 0 : -1;
                    }
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"حدث خطأ أثناء تحميل بيانات الموظفين: {ex.Message}", "خطأ");
                }
            }
            else
            {
                // إذا كانت البيانات محملة مسبقاً، فقط اختر الأول
                if (cmbEmploye != null && _employees.Count > 0)
                {
                    cmbEmploye.SelectedIndex = 0;
                }
            }
        }

        /// <summary>
        /// تحميل بيانات المصروفات عند الحاجة
        /// </summary>
        private async Task LoadExpenses()
        {
            if (_expenses == null)
            {
                try
                {
                    _expenses = await _expenseService.GetAllExpensesAsync();

                    if (cmbExpense != null)
                    {
                        cmbExpense.ItemsSource = _expenses;
                        cmbExpense.DisplayMemberPath = "Name";
                        cmbExpense.SelectedValuePath = "Id";
                        cmbExpense.SelectedIndex = _expenses.Count > 0 ? 0 : -1;
                    }
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"حدث خطأ أثناء تحميل بيانات المصروفات: {ex.Message}", "خطأ");
                }
            }
            else
            {
                // إذا كانت البيانات محملة مسبقاً، فقط اختر الأول
                if (cmbExpense != null && _expenses.Count > 0)
                {
                    cmbExpense.SelectedIndex = 0;
                }
            }
        }

        /// <summary>
        /// تحميل جميع طرق الدفع من جميع المخازن للتحويل
        /// </summary>
        private async Task LoadAllTreasuriesForTransfer()
        {
            try
            {
                // جلب جميع طرق الدفع من جميع المخازن
                var allTreasuries = await _treasuryService.GetAllTreasuriesAsync();

                // تعيين البيانات لطريقة الدفع المصدر
                cmbSourceTreasury.ItemsSource = allTreasuries;
                cmbSourceTreasury.DisplayMemberPath = "Name";
                cmbSourceTreasury.SelectedValuePath = "Id";
                cmbSourceTreasury.SelectedIndex = allTreasuries.Count > 0 ? 0 : -1;

                // تعيين البيانات لطريقة الدفع الهدف
                cmbTargetTreasury.ItemsSource = allTreasuries;
                cmbTargetTreasury.DisplayMemberPath = "Name";
                cmbTargetTreasury.SelectedValuePath = "Id";
                cmbTargetTreasury.SelectedIndex = allTreasuries.Count > 1 ? 1 : -1; // اختيار طريقة دفع مختلفة
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل طرق الدفع: {ex.Message}", "خطأ في التحميل", true);
            }
        }

        /// <summary>
        /// حدث تغيير المخزن المختار
        /// </summary>
        private async void cmbWarehouse_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (!this.IsLoaded) return; // تجنب التنفيذ أثناء التحميل الأولي

            try
            {
                await LoadTreasuriesAndClients();

                // إعادة تعيين الحقول المرتبطة بالفواتير
                txtInvoiceNo.Text = "";
                _loadedPurchase = null;
                _loadedSale = null;
                cmbClient.IsEnabled = true;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تغيير المخزن: {ex.Message}", "خطأ");
            }
        }

        private async Task ConfigureUIByType()
        {
            if (cmbType.SelectedItem is Financial selectedFinancial)
            {
                // إعادة تعيين عناصر التحكم إلى حالتها الافتراضية
                cmbClient.IsEnabled = true;
                cmbEmploye.IsEnabled = true;
                cmbExpense.IsEnabled = true;
                RdbCatch.IsEnabled = true;
                RdbExchange.IsEnabled = true;
                txtInvoiceNo.IsEnabled = true;
                cmbTreasury.IsEnabled = true;
                cmbTreasury.Visibility = Visibility.Visible;
                cmbExpense.Visibility = Visibility.Collapsed;

                // إخفاء عناصر التحويل بشكل افتراضي
                cmbSourceTreasury.Visibility = Visibility.Collapsed;
                cmbTargetTreasury.Visibility = Visibility.Collapsed;

                // إعادة تعيين خاصية السماح بالقيم السالبة وإخفاء التوضيح
                NumericInputControl.SetAllowNegative(txtValue, false);
                txtValueHint.Visibility = Visibility.Collapsed;

                // تكوين واجهة المستخدم حسب نوع العملية المالية
                switch (selectedFinancial.Id)
                {
                    case (byte)FinancialId.Client: // 7. عميل
                        cmbClient.Visibility = Visibility.Visible;
                        cmbEmploye.Visibility = Visibility.Collapsed;
                        txtInvoiceNo.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Visible;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;
                        // تحميل العملاء إذا لم يتم تحميلهم
                        if (_clients == null || _clients.Count == 0)
                        {
                            await LoadTreasuriesAndClients();
                        }
                        // جعل الاختيار الأول افتراضياً للعميل
                        if (cmbClient.Items.Count > 0 && cmbClient.SelectedIndex == -1)
                        {
                            cmbClient.SelectedIndex = 0;
                        }
                        break;

                    case (byte)FinancialId.Purchase: // 1. فاتورة مشتريات
                        cmbClient.Visibility = Visibility.Visible;
                        cmbEmploye.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Visible;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;

                        // التحقق من المخزن المختار
                        bool isAllWarehouses = cmbWarehouse.SelectedValue == null ||
                                             (cmbWarehouse.SelectedValue is int whId && whId == -1);

                        if (isAllWarehouses)
                        {
                            // منع إدخال رقم الفاتورة عندما يكون المخزن "الكل"
                            txtInvoiceNo.Visibility = Visibility.Collapsed;
                            ErrorBox.Show("لا يمكن إصدار إيصال للفواتير عندما يكون المخزن 'الكل'. يرجى اختيار مخزن محدد.", "تنبيه");
                        }
                        else
                        {
                            txtInvoiceNo.Visibility = Visibility.Visible;
                            txtInvoiceNo.Tag = "رقم فاتورة المشتريات";
                        }

                        // تحميل العملاء إذا لم يتم تحميلهم
                        if (_clients == null || _clients.Count == 0)
                        {
                            await LoadTreasuriesAndClients();
                        }

                        // تعطيل اختيار العميل عند اختيار فاتورة
                        cmbClient.IsEnabled = false;

                        // دائماً يكون نوع مصروف (صرف) ولا يمكن تغييره
                        RdbCatch.IsChecked = true;
                        RdbCatch.IsEnabled = false;
                        RdbExchange.IsEnabled = false;
                        break;

                    case (byte)FinancialId.Sale: // 2. فاتورة مبيعات
                        cmbClient.Visibility = Visibility.Visible;
                        cmbEmploye.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Visible;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;

                        // التحقق من المخزن المختار
                        bool isAllWarehousesSale = cmbWarehouse.SelectedValue == null ||
                                                 (cmbWarehouse.SelectedValue is int whIdSale && whIdSale == -1);

                        if (isAllWarehousesSale)
                        {
                            // منع إدخال رقم الفاتورة عندما يكون المخزن "الكل"
                            txtInvoiceNo.Visibility = Visibility.Collapsed;
                            ErrorBox.Show("لا يمكن إصدار إيصال للفواتير عندما يكون المخزن 'الكل'. يرجى اختيار مخزن محدد.", "تنبيه");
                        }
                        else
                        {
                            txtInvoiceNo.Visibility = Visibility.Visible;
                            txtInvoiceNo.Tag = "رقم فاتورة المبيعات";
                        }

                        // تحميل العملاء إذا لم يتم تحميلهم
                        if (_clients == null || _clients.Count == 0)
                        {
                            await LoadTreasuriesAndClients();
                        }

                        // تعطيل اختيار العميل عند اختيار فاتورة
                        cmbClient.IsEnabled = false;

                        // دائماً يكون نوع إيراد (قبض) ولا يمكن تغييره
                        RdbExchange.IsChecked = true;
                        RdbCatch.IsEnabled = false;
                        RdbExchange.IsEnabled = false;
                        break;

                    case (byte)FinancialId.Employee: // 3. موظف
                        cmbClient.Visibility = Visibility.Collapsed;
                        cmbEmploye.Visibility = Visibility.Visible;
                        txtInvoiceNo.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Visible;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;
                        // تحميل الموظفين عند الحاجة
                        await LoadEmployees();
                        break;

                    case (byte)FinancialId.OpeningBalanceForEmployee: // 4. رصيد افتتاحي للموظف
                        cmbClient.Visibility = Visibility.Collapsed;
                        cmbEmploye.Visibility = Visibility.Visible;
                        txtInvoiceNo.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Collapsed;
                        cmbTreasury.SelectedValue = null;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;
                        // تحميل الموظفين عند الحاجة
                        await LoadEmployees();
                        // ليس إيراد ولا مصروف - تعطيل كليهما
                        RdbCatch.IsEnabled = false;
                        RdbExchange.IsEnabled = false;
                        RdbCatch.IsChecked = null;
                        RdbExchange.IsChecked = null;
                        // السماح بالقيم السالبة وإظهار التوضيح
                        NumericInputControl.SetAllowNegative(txtValue, true);
                        txtValueHint.Visibility = Visibility.Visible;
                        break;

                    case (byte)FinancialId.OpeningBalanceForClient: // 5. رصيد افتتاحي للعميل
                        cmbClient.Visibility = Visibility.Visible;
                        cmbEmploye.Visibility = Visibility.Collapsed;
                        txtInvoiceNo.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Collapsed;
                        cmbTreasury.SelectedValue = null;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;
                        // تحميل العملاء إذا لم يتم تحميلهم
                        if (_clients == null || _clients.Count == 0)
                        {
                            await LoadTreasuriesAndClients();
                        }
                        // جعل الاختيار الأول افتراضياً للعميل
                        if (cmbClient.Items.Count > 0 && cmbClient.SelectedIndex == -1)
                        {
                            cmbClient.SelectedIndex = 0;
                        }
                        // ليس إيراد ولا مصروف - تعطيل كليهما
                        RdbCatch.IsEnabled = false;
                        RdbExchange.IsEnabled = false;
                        RdbCatch.IsChecked = null;
                        RdbExchange.IsChecked = null;
                        // السماح بالقيم السالبة وإظهار التوضيح
                        NumericInputControl.SetAllowNegative(txtValue, true);
                        txtValueHint.Visibility = Visibility.Visible;
                        break;

                    case (byte)FinancialId.Withdrawal: // 8. سحب
                        cmbClient.Visibility = Visibility.Collapsed;
                        cmbEmploye.Visibility = Visibility.Collapsed;
                        txtInvoiceNo.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Visible;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;
                        // ليس إيراد ولا مصروف - تعطيل كليهما
                        RdbCatch.IsEnabled = false;
                        RdbExchange.IsEnabled = false;
                        RdbCatch.IsChecked = null;
                        RdbExchange.IsChecked = null;
                        break;

                    case (byte)FinancialId.Deposit: // 9. إيداع
                        cmbClient.Visibility = Visibility.Collapsed;
                        cmbEmploye.Visibility = Visibility.Collapsed;
                        txtInvoiceNo.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Visible;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;
                        // ليس إيراد ولا مصروف - تعطيل كليهما
                        RdbCatch.IsEnabled = false;
                        RdbExchange.IsEnabled = false;
                        RdbCatch.IsChecked = null;
                        RdbExchange.IsChecked = null;
                        break;

                    case (byte)FinancialId.Expense: // مصروفات
                        cmbClient.Visibility = Visibility.Collapsed;
                        cmbEmploye.Visibility = Visibility.Collapsed;
                        cmbExpense.Visibility = Visibility.Visible;
                        txtInvoiceNo.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Visible;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;
                        // تحميل المصروفات عند الحاجة
                        await LoadExpenses();
                        // دائماً يكون مصروف (صرف) ولا يمكن تغييره
                        RdbCatch.IsChecked = true;
                        RdbCatch.IsEnabled = false;
                        RdbExchange.IsEnabled = false;
                        break;

                    case (byte)FinancialId.SalaryPayment: // صرف راتب
                        cmbClient.Visibility = Visibility.Collapsed;
                        cmbEmploye.Visibility = Visibility.Visible;
                        cmbExpense.Visibility = Visibility.Collapsed;
                        txtInvoiceNo.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Visible;
                        cmbSourceTreasury.Visibility = Visibility.Collapsed;
                        cmbTargetTreasury.Visibility = Visibility.Collapsed;
                        // تحميل الموظفين عند الحاجة
                        await LoadEmployees();
                        // دائماً يكون مصروف (صرف) ولا يمكن تغييره
                        RdbCatch.IsChecked = true;
                        RdbCatch.IsEnabled = false;
                        RdbExchange.IsEnabled = false;
                        break;

                    case (byte)FinancialId.Transfer: // تحويل بين طرق الدفع
                        cmbClient.Visibility = Visibility.Collapsed;
                        cmbEmploye.Visibility = Visibility.Collapsed;
                        cmbExpense.Visibility = Visibility.Collapsed;
                        txtInvoiceNo.Visibility = Visibility.Collapsed;
                        cmbTreasury.Visibility = Visibility.Collapsed;
                        cmbSourceTreasury.Visibility = Visibility.Visible;
                        cmbTargetTreasury.Visibility = Visibility.Visible;

                        // تحميل جميع طرق الدفع من جميع المخازن
                        await LoadAllTreasuriesForTransfer();

                        // ليس إيراد ولا مصروف - تعطيل كليهما
                        RdbCatch.IsEnabled = false;
                        RdbExchange.IsEnabled = false;
                        RdbCatch.IsChecked = null;
                        RdbExchange.IsChecked = null;
                        break;

                }
            }
        }

        private async void btnSave_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                if (_isEditMode)
                {
                    // التحقق من صلاحية التعديل
                    bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditReceiptRole");
                    if (!canEdit)
                    {
                        ErrorBox.Show("لا تملك صلاحية تعديل الإيصالات", "خطأ في الصلاحيات");
                        return;
                    }

                    if (QuestionBox.Show("تأكيد التعديل", "هل أنت متأكد من تعديل هذا الواصل؟") == MessageBoxResult.No)
                        return;
                }

                // إنشاء أو تحديث الوصل
                byte? financialId = null;
                int? clientId = null;
                int? saleId = null;
                int? purchaseId = null;
                int? userId = null;
                bool? isExchange = null;

                if (cmbType.SelectedItem is Financial selectedFinancial)
                {
                    financialId = selectedFinancial.Id;

                    // التحقق من القواعد حسب النوع
                    switch (selectedFinancial.Id)
                    {
                        case (byte)FinancialId.Client:
                        case (byte)FinancialId.OpeningBalanceForClient:
                            clientId = cmbClient.SelectedValue != null ? (int)cmbClient.SelectedValue : null;
                            if (selectedFinancial.Id != (byte)FinancialId.OpeningBalanceForClient)
                            {
                                isExchange = RdbCatch.IsChecked ?? false;
                            }
                            break;

                        case (byte)FinancialId.Employee:
                        case (byte)FinancialId.OpeningBalanceForEmployee:
                            userId = cmbEmploye.SelectedValue != null ? (int)cmbEmploye.SelectedValue : null;
                            if (selectedFinancial.Id != (byte)FinancialId.OpeningBalanceForEmployee)
                            {
                                isExchange = RdbCatch.IsChecked ?? false;
                            }
                            break;

                        case (byte)FinancialId.Expense:
                            // للمصروفات، دائماً يكون صرف
                            isExchange = true;
                            break;

                        case (byte)FinancialId.SalaryPayment:
                            // لصرف الراتب، دائماً يكون صرف
                            isExchange = true;
                            userId = cmbEmploye.SelectedValue != null ? (int)cmbEmploye.SelectedValue : null;
                            break;

                        case (byte)FinancialId.Sale:
                            // للمبيعات، دائماً يكون قبض
                            isExchange = false;

                            if (!string.IsNullOrEmpty(txtInvoiceNo.Text) && int.TryParse(txtInvoiceNo.Text, out int saleInvoiceNo))
                            {
                                // Buscar por número de factura
                                var saleViewModel = await _saleService.GetSaleByInvoiceNoAsync(saleInvoiceNo);
                                if (saleViewModel != null)
                                {
                                    saleId = saleViewModel.Id;
                                }
                                else
                                {
                                    // Si no se encuentra, intentar usar el valor como ID (para compatibilidad)
                                    saleId = saleInvoiceNo;
                                }

                                // إذا كانت هناك فاتورة مبيعات محملة، التحقق من الحدود
                                if (_loadedSale != null)
                                {
                                    clientId = _loadedSale.ClientId;

                                    // التحقق من عدم تجاوز المبلغ المتبقي
                                    decimal paidAmount = _loadedSale.Receipts?.Sum(r => r.Value) ?? 0;
                                    decimal remainingAmount = _loadedSale.TotalAmount - paidAmount;

                                    if (decimal.Parse(txtValue.Text) > remainingAmount)
                                    {
                                        ErrorBox.Show($"القيمة المدخلة ({txtValue.Text}) تتجاوز المبلغ المستحق ({remainingAmount:N2})", "خطأ");
                                        return;
                                    }
                                }
                                else
                                {
                                    clientId = cmbClient.SelectedValue != null ? (int)cmbClient.SelectedValue : null;
                                }
                            }
                            else
                            {
                                clientId = cmbClient.SelectedValue != null ? (int)cmbClient.SelectedValue : null;
                            }
                            break;

                        case (byte)FinancialId.Purchase:
                            // للمشتريات، دائماً يكون صرف
                            isExchange = true;

                            if (!string.IsNullOrEmpty(txtInvoiceNo.Text) && int.TryParse(txtInvoiceNo.Text, out int purchaseInvoiceNo))
                            {
                                // Buscar por número de factura
                                var purchaseViewModel = await _purchaseService.GetPurchaseByInvoiceNoAsync(purchaseInvoiceNo);
                                if (purchaseViewModel != null)
                                {
                                    purchaseId = purchaseViewModel.Id;
                                }
                                else
                                {
                                    // Si no se encuentra, intentar usar el valor como ID (para compatibilidad)
                                    purchaseId = purchaseInvoiceNo;
                                }

                                // إذا كانت هناك فاتورة مشتريات محملة، التحقق من الحدود
                                if (_loadedPurchase != null)
                                {
                                    clientId = _loadedPurchase.ClientId;

                                    // التحقق من عدم تجاوز المبلغ المتبقي
                                    decimal paidAmount = _loadedPurchase.Receipts?.Sum(r => r.Value) ?? 0;
                                    decimal remainingAmount = _loadedPurchase.TotalAmount - paidAmount;

                                    if (decimal.Parse(txtValue.Text) > remainingAmount)
                                    {
                                        ErrorBox.Show($"القيمة المدخلة ({txtValue.Text}) تتجاوز المبلغ المستحق ({remainingAmount:N2})", "خطأ");
                                        return;
                                    }
                                }
                                else
                                {
                                    clientId = cmbClient.SelectedValue != null ? (int)cmbClient.SelectedValue : null;
                                }
                            }
                            else
                            {
                                clientId = cmbClient.SelectedValue != null ? (int)cmbClient.SelectedValue : null;
                            }
                            break;

                        case (byte)FinancialId.Transfer:
                            // للتحويل بين طرق الدفع، ليس إيراد ولا مصروف
                            isExchange = null;
                            break;
                    }
                }

                decimal value = decimal.Parse(txtValue.Text);
                var receipt = new Receipt
                {
                    Id = _isEditMode ? _currentReceipt.Id : 0,
                    Statement = txtStatment.Text,
                    Date = DtpGeneralExpireOn.SelectedDate ?? DateTime.Today,
                    IsExchange = isExchange,
                    Value = value,
                    TreasuryId = financialId == (byte)FinancialId.Transfer ? null : (cmbTreasury.SelectedValue != null ? (byte)cmbTreasury.SelectedValue : null),
                    ClientId = clientId,
                    SaleId = saleId,
                    PurchaseId = purchaseId,
                    EmployeeId = userId,
                    FinancialId = financialId,
                    ExpenseId = financialId == (byte)FinancialId.Expense && cmbExpense.SelectedValue != null ? (int)cmbExpense.SelectedValue : null,
                    SourceTreasuryId = financialId == (byte)FinancialId.Transfer && cmbSourceTreasury.SelectedValue != null ? (byte)cmbSourceTreasury.SelectedValue : null,
                    TargetTreasuryId = financialId == (byte)FinancialId.Transfer && cmbTargetTreasury.SelectedValue != null ? (byte)cmbTargetTreasury.SelectedValue : null
                };

                // If in edit mode, preserve the receipt number
                if (_isEditMode && _currentReceipt != null)
                {
                    receipt.ReceiptNo = _currentReceipt.ReceiptNo;
                }

                (bool success, string message, int receiptId) result;
                if (_isEditMode)
                {
                    result = await _receiptService.UpdateReceipt(receipt);
                }
                else
                {
                    result = await _receiptService.CreateReceipt(receipt);
                }

                var (success, message) = (result.success, result.message);

                if (result.success)
                {
                    DialogBox.Show("نجاح", _isEditMode ? "تم تعديل الواصل بنجاح" : "تم حفظ الواصل بنجاح");
                    ClearFields();
                    await LoadReceiptsList();
                }
                else
                {
                    ErrorBox.Show(result.message, "خطأ");
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء حفظ الواصل: {ex.Message}", "خطأ");
            }
        }

        private bool ValidateInput()
        {
            // التحقق من القيمة
            if (string.IsNullOrEmpty(txtValue.Text) || !decimal.TryParse(txtValue.Text, out decimal value))
            {
                ErrorBox.Show("الرجاء إدخال قيمة صحيحة", "خطأ في الإدخال");
                return false;
            }

            // التحقق من القيمة حسب نوع العملية
            if (cmbType.SelectedItem is Financial selectedFinancial)
            {
                // السماح بالقيم السالبة فقط للرصيد الافتتاحي
                bool isOpeningBalance = selectedFinancial.Id == (byte)FinancialId.OpeningBalanceForClient ||
                                       selectedFinancial.Id == (byte)FinancialId.OpeningBalanceForEmployee;

                if (!isOpeningBalance && value <= 0)
                {
                    ErrorBox.Show("الرجاء إدخال قيمة صحيحة أكبر من الصفر", "خطأ في الإدخال");
                    return false;
                }
                else if (isOpeningBalance && value == 0)
                {
                    ErrorBox.Show("الرجاء إدخال قيمة رصيد افتتاحي (موجبة أو سالبة)", "خطأ في الإدخال");
                    return false;
                }
            }

            // التحقق من اختيار المصروف إذا كان نوع العملية مصروفات
            if (cmbType.SelectedItem is Financial financial && financial.Id == (byte)FinancialId.Expense)
            {
                if (cmbExpense.SelectedValue == null)
                {
                    ErrorBox.Show("الرجاء اختيار المصروف", "خطأ في الإدخال");
                    return false;
                }
            }

            // التحقق من التاريخ
            if (DtpGeneralExpireOn.SelectedDate == null)
            {
                ErrorBox.Show("الرجاء اختيار التاريخ", "بيانات ناقصة");
                return false;
            }

            if (cmbType.SelectedItem is Financial selectedFinancia)
            {
                // التحقق من القيم حسب النوع
                switch (selectedFinancia.Id)
                {
                    case (byte)FinancialId.Client: // 7. عميل
                        if (cmbTreasury.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار الخزينة", "بيانات ناقصة");
                            return false;
                        }
                        if (cmbClient.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار العميل", "بيانات ناقصة");
                            return false;
                        }
                        if (RdbCatch.IsChecked == null && RdbExchange.IsChecked == null)
                        {
                            ErrorBox.Show("الرجاء اختيار نوع العملية (قبض/صرف)", "بيانات ناقصة");
                            return false;
                        }
                        break;

                    case (byte)FinancialId.Purchase: // 1. فاتورة مشتريات
                        if (cmbTreasury.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار الخزينة", "بيانات ناقصة");
                            return false;
                        }
                        if (string.IsNullOrEmpty(txtInvoiceNo.Text))
                        {
                            ErrorBox.Show("الرجاء إدخال رقم فاتورة المشتريات", "بيانات ناقصة");
                            return false;
                        }
                        if (!int.TryParse(txtInvoiceNo.Text, out _))
                        {
                            ErrorBox.Show("رقم فاتورة المشتريات غير صحيح", "خطأ في الإدخال");
                            return false;
                        }
                        break;

                    case (byte)FinancialId.Sale: // 2. فاتورة مبيعات
                        if (cmbTreasury.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار الخزينة", "بيانات ناقصة");
                            return false;
                        }
                        if (string.IsNullOrEmpty(txtInvoiceNo.Text))
                        {
                            ErrorBox.Show("الرجاء إدخال رقم فاتورة المبيعات", "بيانات ناقصة");
                            return false;
                        }
                        if (!int.TryParse(txtInvoiceNo.Text, out _))
                        {
                            ErrorBox.Show("رقم فاتورة المبيعات غير صحيح", "خطأ في الإدخال");
                            return false;
                        }
                        break;

                    case (byte)FinancialId.Employee: // 3. موظف
                        if (cmbTreasury.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار الخزينة", "بيانات ناقصة");
                            return false;
                        }
                        if (cmbEmploye.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار الموظف", "بيانات ناقصة");
                            return false;
                        }
                        if (RdbCatch.IsChecked == null && RdbExchange.IsChecked == null)
                        {
                            ErrorBox.Show("الرجاء اختيار نوع العملية (قبض/صرف)", "بيانات ناقصة");
                            return false;
                        }
                        break;

                    case (byte)FinancialId.OpeningBalanceForEmployee: // 4. رصيد افتتاحي للموظف
                        if (cmbEmploye.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار الموظف", "بيانات ناقصة");
                            return false;
                        }
                        break;

                    case (byte)FinancialId.OpeningBalanceForClient: // 5. رصيد افتتاحي للعميل
                        if (cmbClient.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار العميل", "بيانات ناقصة");
                            return false;
                        }
                        break;

                    case (byte)FinancialId.Withdrawal: // 8. سحب
                        if (cmbTreasury.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار الخزينة", "بيانات ناقصة");
                            return false;
                        }
                        break;

                    case (byte)FinancialId.Deposit: // 9. إيداع
                        if (cmbTreasury.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار الخزينة", "بيانات ناقصة");
                            return false;
                        }
                        break;

                    case (byte)FinancialId.Expense: // مصروفات
                        if (cmbTreasury.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار الخزينة", "بيانات ناقصة");
                            return false;
                        }
                        if (cmbExpense.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار المصروف", "بيانات ناقصة");
                            return false;
                        }
                        break;

                    case (byte)FinancialId.SalaryPayment: // صرف راتب
                        if (cmbTreasury.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار الخزينة", "بيانات ناقصة");
                            return false;
                        }
                        if (cmbEmploye.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار الموظف", "بيانات ناقصة");
                            return false;
                        }
                        break;

                    case (byte)FinancialId.Transfer: // تحويل بين طرق الدفع
                        if (cmbSourceTreasury.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار طريقة الدفع المصدر", "بيانات ناقصة");
                            return false;
                        }
                        if (cmbTargetTreasury.SelectedValue == null)
                        {
                            ErrorBox.Show("الرجاء اختيار طريقة الدفع الهدف", "بيانات ناقصة");
                            return false;
                        }
                        if (cmbSourceTreasury.SelectedValue.Equals(cmbTargetTreasury.SelectedValue))
                        {
                            ErrorBox.Show("لا يمكن التحويل من وإلى نفس طريقة الدفع", "بيانات ناقصة");
                            return false;
                        }
                        break;

                }
            }
            else
            {
                ErrorBox.Show("الرجاء اختيار نوع العملية", "بيانات ناقصة");
                return false;
            }

            return true;
        }

        private void ClearFields()
        {
            txtValue.Text = string.Empty;
            txtStatment.Text = string.Empty;
            txtInvoiceNo.Text = string.Empty;
            DtpGeneralExpireOn.SelectedDate = DateTime.Today;

            // إعادة تعيين ComboBoxes إلى حالة فارغة
            cmbClient.SelectedIndex = -1;
            cmbEmploye.SelectedIndex = -1;
            cmbExpense.SelectedIndex = -1;
            cmbTreasury.SelectedIndex = -1;
            cmbSourceTreasury.SelectedIndex = -1;
            cmbTargetTreasury.SelectedIndex = -1;

            // إعادة تعيين نوع الإيصال إلى الأول
            if (_financials != null && _financials.Count > 0)
            {
                cmbType.SelectedIndex = 0;
            }
            else
            {
                cmbType.SelectedIndex = -1;
            }

            RdbCatch.IsChecked = null;
            RdbExchange.IsChecked = null;
            RdbCatch.IsEnabled = true;
            RdbExchange.IsEnabled = true;
            txtReceiptNo.Text = string.Empty;

            // التحقق من صلاحية تغيير التاريخ
            bool canChangeDate = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ChangeReceiptDateRole");
            DtpGeneralExpireOn.IsEnabled = canChangeDate;

            // إعادة تعيين حالة التحرير
            _isEditMode = false;
            _currentReceipt = new Receipt();

            // إعادة تعيين الفواتير المحملة
            _loadedPurchase = null;
            _loadedSale = null;

            // إعادة تعيين حالة العناصر
            cmbClient.IsEnabled = true;
            cmbEmploye.IsEnabled = true;
            txtInvoiceNo.IsEnabled = true;
            cmbTreasury.IsEnabled = true;
            list.SelectedIndex = -1;

            // إعادة تعيين الرؤية
            cmbClient.Visibility = Visibility.Visible;
            cmbEmploye.Visibility = Visibility.Collapsed;
            cmbExpense.Visibility = Visibility.Collapsed;
            txtInvoiceNo.Visibility = Visibility.Collapsed;
            cmbTreasury.Visibility = Visibility.Visible;
            cmbSourceTreasury.Visibility = Visibility.Collapsed;
            cmbTargetTreasury.Visibility = Visibility.Collapsed;

            // إعادة تعيين خاصية السماح بالقيم السالبة
            NumericInputControl.SetAllowNegative(txtValue, false);
            txtValueHint.Visibility = Visibility.Collapsed;
        }

        private async void btnNew_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            ClearFields();
            await LoadReceiptsList();
        }

        private async void btnDelete_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // التحقق من صلاحية الحذف
            bool canDelete = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("DeleteReceiptRole");
            if (!canDelete)
            {
                ErrorBox.Show("لا تملك صلاحية حذف الإيصالات", "خطأ في الصلاحيات");
                return;
            }

            if (_currentReceipt == null || _currentReceipt.Id == 0)
            {
                ErrorBox.Show("يرجى اختيار ايصال للحذف", "خطأ");
                return;
            }

            if (QuestionBox.Show("تأكيد الحذف", "هل أنت متأكد من حذف هذا الواصل؟") == MessageBoxResult.No)
                return;

            try
            {
                var (success, message) = await _receiptService.DeleteReceipt(_currentReceipt.Id);
                if (success)
                {
                    DialogBox.Show("نجاح", "تم حذف الواصل بنجاح");
                    ClearFields();
                    await LoadReceiptsList();
                }
                else
                {
                    ErrorBox.Show(message, "خطأ");
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء حذف الواصل: {ex.Message}", "خطأ");
            }
        }

        private async void btnSearch_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                // جمع القيم المدخلة من واجهة المستخدم
                int? receiptNo = null;
                if (!string.IsNullOrEmpty(txtReceiptNo.Text) && int.TryParse(txtReceiptNo.Text, out int parsedReceiptNo))
                {
                    receiptNo = parsedReceiptNo;
                }

                byte? financialId = null;
                if (cmbType.SelectedItem is Financial searchFinancial)
                {
                    financialId = searchFinancial.Id;
                }

                DateTime? date = DtpGeneralExpireOn.SelectedDate;

                int? clientId = null;
                if (cmbClient.Visibility == Visibility.Visible && cmbClient.SelectedValue != null)
                {
                    clientId = (int)cmbClient.SelectedValue;
                }

                int? employeeId = null;
                if (cmbEmploye.Visibility == Visibility.Visible && cmbEmploye.SelectedValue != null)
                {
                    employeeId = (int)cmbEmploye.SelectedValue;
                }

                int? expenseId = null;
                if (cmbExpense.Visibility == Visibility.Visible && cmbExpense.SelectedValue != null)
                {
                    expenseId = (int)cmbExpense.SelectedValue;
                }

                byte? treasuryId = null;
                if (cmbTreasury.Visibility == Visibility.Visible && cmbTreasury.SelectedValue != null)
                {
                    treasuryId = (byte)cmbTreasury.SelectedValue;
                }

                bool? isExchange = null;
                if (RdbCatch.IsChecked != null || RdbExchange.IsChecked != null)
                {
                    isExchange = RdbCatch.IsChecked ?? false;
                }


                string statement = null;
                if (!string.IsNullOrEmpty(txtStatment.Text))
                {
                    statement = txtStatment.Text;
                }

                int? purchaseId = null;
                int? saleId = null;
                if (!string.IsNullOrEmpty(txtInvoiceNo.Text) && int.TryParse(txtInvoiceNo.Text, out int invoiceNo))
                {
                    if (financialId.HasValue)
                    {
                        if (financialId.Value == (byte)FinancialId.Purchase)
                        {
                            // Buscar por número de factura
                            var purchaseViewModel = await _purchaseService.GetPurchaseByInvoiceNoAsync(invoiceNo);
                            if (purchaseViewModel != null)
                            {
                                purchaseId = purchaseViewModel.Id;
                            }
                            else
                            {
                                // Si no se encuentra, intentar usar el valor como ID (para compatibilidad)
                                purchaseId = invoiceNo;
                            }
                        }
                        else if (financialId.Value == (byte)FinancialId.Sale)
                        {
                            // Buscar por número de factura
                            var saleViewModel = await _saleService.GetSaleByInvoiceNoAsync(invoiceNo);
                            if (saleViewModel != null)
                            {
                                saleId = saleViewModel.Id;
                            }
                            else
                            {
                                // Si no se encuentra, intentar usar el valor como ID (para compatibilidad)
                                saleId = invoiceNo;
                            }
                        }
                    }
                }

                // استدعاء دالة البحث من الخدمة
                _receipts = await _receiptService.SearchReceipts(
                    receiptNo: receiptNo,
                    financialId: financialId,
                    date: date,
                    clientId: clientId,
                    employeeId: employeeId,
                    treasuryId: treasuryId,
                    isExchange: isExchange,
                    statement: statement,
                    purchaseId: purchaseId,
                    saleId: saleId,
                    expenseId: expenseId
                );

                // تحديث القائمة بنتائج البحث
                list.ItemsSource = _receipts;

                // عرض رسالة بنتائج البحث
                DialogBox.Show("نتائج البحث", $"تم العثور على {_receipts.Count} نتيجة");
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ");
            }
        }


        private async void cmbType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // تجنب التنفيذ أثناء التحميل الأولي
            if (!this.IsLoaded) return;

            await ConfigureUIByType();

            // تنظيف الحقول المتعلقة بالفواتير عند تغيير النوع
            if (cmbType.SelectedItem is Financial typeFinancial)
            {
                if (typeFinancial.Id != (byte)FinancialId.Purchase && typeFinancial.Id != (byte)FinancialId.Sale)
                {
                    txtStatment.Text = string.Empty;
                    txtInvoiceNo.Text = string.Empty;
                    _loadedPurchase = null;
                    _loadedSale = null;
                    cmbClient.IsEnabled = true;
                }
            }
        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (list.View is GridView gridView && gridView.Columns.Count >= 9)
            {
                double padding = 10;
                double scrollbarWidth = SystemParameters.VerticalScrollBarWidth;

                double availableWidth = list.ActualWidth - scrollbarWidth - padding;

                double totalWeight = 1.0 + 1.5 + 2.5 + 1.5 + 1.0 + 1.0 + 1.5 + 1.5 + 2.0 + 1.5;
                double unitWidth = availableWidth / totalWeight;

                gridView.Columns[0].Width = unitWidth * 1.0; // Index
                gridView.Columns[1].Width = unitWidth * 1.5; // date
                gridView.Columns[2].Width = unitWidth * 2.5; // note
                gridView.Columns[3].Width = unitWidth * 1.5; // type
                gridView.Columns[4].Width = unitWidth * 1.0; // G/T
                gridView.Columns[5].Width = unitWidth * 1.0; // amount
                gridView.Columns[6].Width = unitWidth * 1.5; // treasury
                gridView.Columns[7].Width = unitWidth * 2.0; // client
                gridView.Columns[8].Width = unitWidth * 2.0; // user
                gridView.Columns[9].Width = unitWidth * 1.5; // expense
            }
        }
        private void TextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                textBox.SelectAll();
            }
        }

        private async void list_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (list.SelectedItem is Receipt selectedReceipt)
            {
                if (selectedReceipt == null)
                    return;

                try
                {
                    // عرض بيانات الوصل المحدد في الحقول
                    // التحقق من صلاحية التعديل
                    bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditReceiptRole");
                    if (!canEdit)
                    {
                        // إذا لم يكن لديه صلاحية التعديل، نعرض رسالة خطأ ونخرج
                        ErrorBox.Show("لا تملك صلاحية تعديل الإيصالات", "خطأ في الصلاحيات");
                        return;
                    }

                    _currentReceipt = selectedReceipt;
                    _isEditMode = true;

                    // تعبئة الحقول بمعلومات الوصل المحدد
                    txtReceiptNo.Text = selectedReceipt.ReceiptNo.ToString();
                    txtValue.Text = selectedReceipt.Value.ToString();
                    DtpGeneralExpireOn.SelectedDate = selectedReceipt.Date;

                    // تحديد نوع العملية
                    if (selectedReceipt.FinancialId.HasValue)
                    {
                        var financial = _financials.FirstOrDefault(f => f.Id == selectedReceipt.FinancialId.Value);
                        if (financial != null)
                        {
                            cmbType.SelectedItem = financial;
                            // تكوين واجهة المستخدم حسب النوع (سيحمل البيانات المطلوبة)
                            await ConfigureUIByType();
                        }
                    }

                    // تحديد العميل/الموظف/المصروف بعد تحميل البيانات
                    if (selectedReceipt.ClientId.HasValue && _clients != null)
                    {
                        cmbClient.SelectedValue = selectedReceipt.ClientId.Value;
                    }

                    if (selectedReceipt.EmployeeId.HasValue && _employees != null)
                    {
                        cmbEmploye.SelectedValue = selectedReceipt.EmployeeId.Value;
                    }

                    if (selectedReceipt.ExpenseId.HasValue && _expenses != null)
                    {
                        cmbExpense.SelectedValue = selectedReceipt.ExpenseId.Value;
                    }

                    // تحديد الخزينة
                    if (selectedReceipt.TreasuryId.HasValue)
                    {
                        cmbTreasury.SelectedValue = selectedReceipt.TreasuryId.Value;
                    }

                    // تحديد نوع الوصل (قبض/صرف)
                    RdbCatch.IsChecked = selectedReceipt.IsExchange;
                    RdbExchange.IsChecked = !selectedReceipt.IsExchange;

                    // تحديد رقم الفاتورة إذا كان موجوداً
                    if (selectedReceipt.PurchaseId.HasValue)
                    {
                        // Mostrar el número de factura en lugar del ID
                        if (selectedReceipt.Purchase != null)
                        {
                            txtInvoiceNo.Text = selectedReceipt.Purchase.InvoiceNo.ToString();
                        }
                        else
                        {
                            // Si no está cargada la compra, cargarla
                            var purchaseViewModel = await _purchaseService.GetPurchaseByIdAsync(selectedReceipt.PurchaseId.Value);
                            if (purchaseViewModel != null)
                            {
                                txtInvoiceNo.Text = purchaseViewModel.InvoiceNo.ToString();
                                _loadedPurchase = purchaseViewModel.Purchase;
                            }
                            else
                            {
                                txtInvoiceNo.Text = selectedReceipt.PurchaseId.Value.ToString();
                            }
                        }
                        _loadedPurchase = selectedReceipt.Purchase;
                    }
                    else if (selectedReceipt.SaleId.HasValue)
                    {
                        // Mostrar el número de factura en lugar del ID
                        if (selectedReceipt.Sale != null)
                        {
                            txtInvoiceNo.Text = selectedReceipt.Sale.InvoiceNo.ToString();
                        }
                        else
                        {
                            // Si no está cargada la venta, cargarla
                            var saleViewModel = await _saleService.GetSaleByIdAsync(selectedReceipt.SaleId.Value);
                            if (saleViewModel != null)
                            {
                                txtInvoiceNo.Text = saleViewModel.InvoiceNo.ToString();
                                _loadedSale = saleViewModel.Sale;
                            }
                            else
                            {
                                txtInvoiceNo.Text = selectedReceipt.SaleId.Value.ToString();
                            }
                        }
                        _loadedSale = selectedReceipt.Sale;
                    }
                    else
                    {
                        txtInvoiceNo.Text = string.Empty;
                    }

                    // تعيين البيان
                    txtStatment.Text = selectedReceipt.Statement;

                    // التحقق من صلاحية تغيير التاريخ
                    bool canChangeDate = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ChangeReceiptDateRole");
                    DtpGeneralExpireOn.IsEnabled = canChangeDate;
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"خطأ في عرض بيانات الوصل: {ex.Message}", "خطأ");
                }
            }
        }

        private void btnPrint_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {

            if (_currentReceipt == null || _currentReceipt.Id == 0)
            {
                ErrorBox.Show("يرجى اختيار ايصال للطباعة", "خطأ");
                return;
            }
            // Optional: Add field-level validation here if needed (Value, Client, etc.)

            SinglePagePdfPrinter pdfPrinter = new SinglePagePdfPrinter();
            PrintableReciept reciept = new PrintableReciept(_currentReceipt);
            string fileName = $"reciept_{DateTime.Now:yyyyMMdd_HHmmss}";
            var printerNames = GetDefaultPrinterName();

            pdfPrinter.ExportAndPrint(reciept, @"C:\PDFs", "reciept", printerNames);
        }


        public string GetDefaultPrinterName()
        {
            try
            {
                string defaultPrinter = Properties.Settings.Default.DefaultPrinter;
                return defaultPrinter;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على الطابعة الافتراضية: {ex.Message}");
                return string.Empty; // Return empty if error occurs
            }
        }
    }
}
