﻿﻿using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace VisionPoint.UI.Converters
{
    public class BooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                // إذا كانت القيمة صحيحة، أظهر العنصر، وإلا أخفيه
                return boolValue ? Visibility.Visible : Visibility.Collapsed;
            }
            
            // في حالة عدم وجود قيمة، أخفِ العنصر
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                // إذا كان العنصر مرئيًا، فالقيمة صحيحة، وإلا فهي خاطئة
                return visibility == Visibility.Visible;
            }
            
            return false;
        }
    }
}
