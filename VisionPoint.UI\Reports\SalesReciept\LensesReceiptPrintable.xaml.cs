﻿using System.Globalization;
using System.IO;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Dialogs;
using ZXing;
using ZXing.Windows.Compatibility; // Required for BitmapRenderer


namespace VisionPoint.UI.Reports.SalesReciept
{
    /// <summary>
    /// Interaction logic for LensesReceiptPrintable.xaml
    /// </summary>
    public partial class LensesReceiptPrintable : Page
    {
        public LensesReceiptPrintable(LensesReceiptPrintableVM vm)
        {
            InitializeComponent();
            LoadSettingsFromAppConfig(vm);
            LoadInitialLogo(); // Call this here
        }
        private void LoadInitialLogo()
        {
            string initialLogoPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Assets", "company_logo.png");

            if (File.Exists(initialLogoPath))
            {
                try
                {
                    BitmapImage bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri(initialLogoPath);
                    bitmap.CacheOption = BitmapCacheOption.OnLoad; // Important for releasing file lock
                    bitmap.EndInit();
                    LogoImageElement.Source = bitmap; // Assuming LogoImageElement is your Image's x:Name
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"Error loading initial logo: {ex.Message}", "خطأ");
                }
            }
        }

        private void LoadSettingsFromAppConfig(LensesReceiptPrintableVM vm)
        {
            try
            {
                // Access the application settings using the generated static class
                // Make sure the names match exactly what you defined in the Settings tab
                ArabicAddress.Text = Properties.Settings.Default.ArabicAddress;
                EnglishAddress.Text = Properties.Settings.Default.EnglishAddress;
                CNameAr.Text = Properties.Settings.Default.CompanyName;
                CNameEn.Text = Properties.Settings.Default.CompanyNameEng;

                string phoneNumber = Properties.Settings.Default.PhoneNumber;
                Phone.Text = phoneNumber; // Set the TextBlock next to the Arabic label
                EnglishPhone.Text = phoneNumber; // Set the TextBlock next to the English label
                if (vm.LeftAxis != null)
                {
                    LeftADD.Text = (vm.LeftADD);
                    LeftAxis.Text = FormatAxis(vm.LeftAxis);
                    LeftCYL.Text = (vm.LeftCYL);
                    LeftSPH.Text = (vm.LeftSPH);
                }

                if (vm.RightAxis != null)
                {
                    RightADD.Text = (vm.RightADD);
                    RightAxis.Text = FormatAxis(vm.RightAxis);
                    RightCYL.Text = (vm.RightCYL);
                    RightSPH.Text = (vm.RightSPH);
                }


                txtCustomerName.Text = vm.ClientName;
                txtTotalAmount.Text = vm.TotalPrice;
                txtPaidAmount.Text = vm.Paid;
                ReceiptDate.Text = vm.SaleDate.ToString("yyyy/MM/dd");
                txtRemainingAmount.Text = (Convert.ToDecimal(vm.TotalPrice) - Convert.ToDecimal(vm.Paid)).ToString();

                GenerateBarcode(vm.Id.ToString());

            }
            catch (Exception ex)
            {
                // Handle cases where settings might be missing or inaccessible
                ErrorBox.Show($"خطأ في تحميل إعدادات التطبيق: {ex.Message}", "خطأ في الإعدادات", true);
                // Provide default text or leave blank if settings fail
                ArabicAddress.Text = "[عنوان غير موجود]";
                EnglishAddress.Text = "[English Address Missing]";
                Phone.Text = "[رقم هاتف غير موجود]";
                EnglishPhone.Text = "[Phone Number Missing]";
            }
        }


        private string FormatAxis(string mValue)
        {
            if (string.IsNullOrWhiteSpace(mValue))
            {
                // Handle empty or whitespace input gracefully
                return string.Empty;
            }

            var value = decimal.Parse(mValue, CultureInfo.InvariantCulture);

            if (value == 0m) // Handling for zero values
            {
                int scale = GetScale(value);
                if (scale >= 2) // Input was 0.00m (or 0.000m etc.)
                {
                    return "0"; // Output the string "0"
                }
                else // Input was 0m or 0.0m
                {
                    return string.Empty;
                }
            }
            else // Handling for non-zero values
            {
                if (value % 1 == 0)
                {
                    return ((int)value).ToString(CultureInfo.InvariantCulture);
                }
                else
                {
                    return (value / 1.0m).ToString(CultureInfo.InvariantCulture);
                }
            }
        }

        // Helper method to get the scale of a decimal
        private static int GetScale(decimal d)
        {
            int[] bits = decimal.GetBits(d);
            // Bits 16-23 of the last integer in bits[] represent the scale.
            return (bits[3] >> 16) & 0x7F;
        }
        private void GenerateBarcode(string content)
        {
            var writer = new BarcodeWriter<System.Drawing.Bitmap>
            {
                Renderer = new BitmapRenderer(), // Now available
                Format = BarcodeFormat.CODE_128,
                Options = new ZXing.Common.EncodingOptions
                {
                    Width = 300,
                    Height = 100,
                    Margin = 10
                }
            };



            // Generate the bitmap image
            var bitmap = writer.Write(content);

            // Convert the Bitmap to BitmapImage
            using (MemoryStream memory = new MemoryStream())
            {
                bitmap.Save(memory, System.Drawing.Imaging.ImageFormat.Bmp);
                memory.Position = 0;

                BitmapImage bitmapImage = new BitmapImage();
                bitmapImage.BeginInit();
                bitmapImage.StreamSource = memory;
                bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                bitmapImage.EndInit();

                imgBarcode.Source = bitmapImage; // Image control in XAML
            }
        }

    }
}
