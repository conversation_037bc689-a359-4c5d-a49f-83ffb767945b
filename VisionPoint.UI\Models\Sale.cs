using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace VisionPoint.UI.Models;

[Index(nameof(ClientId), nameof(SaleDate))]
[Index(nameof(SaleDate))]
[Index(nameof(InvoiceNo))]
[Index(nameof(WarehouseId))]
[Index(nameof(WarehouseId), nameof(InvoiceNo))]
public class Sale : BaseEntity
{
    /// <summary>
    /// رقم الفاتورة
    /// </summary>
    public int InvoiceNo { get; set; }

    /// <summary>
    /// رقم الفاتورة مع رمز المخزن (مثل: MAIN-001)
    /// </summary>
    [StringLength(50)]
    public string? InvoiceNumber { get; set; }

    public int ClientId { get; set; }
    public Client Client { get; set; }
    /// <summary>
    /// معرف المخزن
    /// </summary>
    public int WarehouseId { get; set; }
    /// <summary>
    /// المخزن
    /// </summary>
    public Warehouse Warehouse { get; set; }
    public DateTime SaleDate { get; set; }
    [Precision(18, 3)] public decimal TotalBeforeDiscount { get; set; }
    [Precision(18, 3)] public decimal TotalDiscount { get; set; }
    [Precision(18, 3)] public decimal TotalAmount { get; set; }
    /// <summary>
    /// إجمالي قيمة المنتجات المسترجعة
    /// </summary>
    [Precision(18, 3)] public decimal TotalReturned { get; set; } = 0;
    /// <summary>
    /// ملاحظات الفاتورة
    /// </summary>
    [StringLength(500, ErrorMessage = "الملاحظات يجب ألا تتجاوز 500 حرف")]
    public string? Notes { get; set; }
    public ICollection<SaleItem> SaleItems { get; set; }
    public ICollection<Receipt>? Receipts { get; set; }
}
