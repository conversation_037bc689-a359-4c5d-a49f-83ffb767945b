﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VisionPoint.UI.Models;

public class Color
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public byte Id { get; set; }
    [StringLength(50, ErrorMessage = "اسم اللون يجب ألا يتجاوز 50 حرف")]
    public string Name { get; set; }

    [StringLength(30, ErrorMessage = "كود اللون يجب ألا يتجاوز 30 حرف")]
    public string HexCode { get; set; }

    /// <summary>
    /// Row GUID for SQL Server Merge Replication - managed by database
    /// </summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid RowGuid { get; set; }
}
