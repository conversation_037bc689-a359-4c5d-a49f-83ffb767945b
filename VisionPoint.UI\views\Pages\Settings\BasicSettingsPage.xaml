﻿<Page
    x:Class="VisionPoint.UI.views.Pages.Settings.BasicSettingsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="BasicSettingsPage"
    d:Background="White"
    d:Height="1080"
    d:Width="1570"
    FlowDirection="RightToLeft"
    Loaded="Page_Loaded"
    mc:Ignorable="d">

    <Grid Margin="32">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="0.5*" />
            <RowDefinition Height="2*" />
            <RowDefinition Height="0.8*" />
            <RowDefinition Height="1*" />
            <RowDefinition Height="1*" />
            <RowDefinition Height="2.5*" />
            <RowDefinition Height="*" />

            <RowDefinition Height="0.6*" />
        </Grid.RowDefinitions>



        <!--  Logo Preview and Upload  -->
        <TextBlock
            Grid.Row="0"
            Grid.Column="0"
            Margin="8"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            FontWeight="Bold"
            Text="شعار الشركة" />
        <Border
            Grid.Row="1"
            Grid.Column="0"
            MaxWidth="250"
            HorizontalAlignment="Center"
            VerticalAlignment="Stretch"
            BorderBrush="Gray"
            BorderThickness="0">
            <Image x:Name="imgLogoPreview" Stretch="Uniform" />
        </Border>
        <Border
            Grid.Row="2"
            Grid.Column="0"
            MinHeight="44"
            MaxWidth="250"
            Margin="8"
            VerticalAlignment="Center"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="16"
            MouseLeftButtonDown="btnUploadLogo_Click">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="21"
                Foreground="White"
                Text="تحديث الشعار" />
        </Border>
        <TextBox
            x:Name="txtCompanyName"
            Grid.Row="3"
            MaxHeight="80"
            Margin="8"
            FontSize="21"
            Tag="اسم الشركة عربي" />
        <TextBox
            x:Name="txtCompanyNameEng"
            Grid.Row="3"
            Grid.Column="1"
            MaxHeight="80"
            Margin="8"
            FontSize="21"
            Tag="اسم الشركة انجليزي" />

        <TextBox
            x:Name="txtArabicAddress"
            Grid.Row="5"
            Margin="8"
            FontSize="21"
            Tag="العنوان بالعربي" />



        <TextBox
            x:Name="txtEnglishAddress"
            Grid.Row="5"
            Grid.Column="1"
            Grid.ColumnSpan="3"
            Margin="8"
            FontSize="21"
            Tag="العنوان بالانجليزي" />

        <Grid Grid.Row="6" Margin="8,0">
            <ComboBox
                x:Name="cmbDefaultPrinter"
                Height="60"
                Padding="42,0"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                IsEditable="True"
                IsReadOnly="False"
                Tag="الطابعة الافتراضية" />
        </Grid>

        <TextBox
            x:Name="txtPhoneNumber"
            Grid.Row="4"
            Grid.Column="1"
            MaxHeight="80"
            Margin="8"
            FontSize="21"
            Tag="رقم الهاتف" />


        <TextBox
            x:Name="txtBranch"
            Grid.Row="4"
            MaxHeight="80"
            Margin="8"
            FontSize="21"
            Tag="اسم الفرع" />



        <!--  Save Button  -->
        <Border
            Grid.Row="7"
            Grid.Column="0"
            Grid.ColumnSpan="4"
            Width="120"
            Height="44"
            HorizontalAlignment="Right"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="12"
            MouseLeftButtonDown="btnSave_Click">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="21"
                Foreground="White"
                Text="حفظ" />
        </Border>
    </Grid>
</Page>
