<Window
    x:Class="VisionPoint.UI.views.Dialogs.SalesExportPreviewWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:VisionPoint.UI.Converters"
    Title="معاينة تصدير المبيعات"
    AllowsTransparency="True"
    Background="Transparent"
    FlowDirection="RightToLeft"
    Loaded="Window_Loaded"
    ResizeMode="NoResize"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!--  Converters  -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter" />

            <!--  أنماط مخصصة  -->
            <Style x:Key="TabItemStyle" TargetType="TabItem">
                <Setter Property="FontSize" Value="22" />
                <Setter Property="FontWeight" Value="Bold" />
                <Setter Property="FontFamily" Value="Segoe UI" />
                <Setter Property="Padding" Value="25,15" />
                <Setter Property="Margin" Value="5,0" />
                <Setter Property="Background" Value="{StaticResource backgroundColor}" />
                <Setter Property="Foreground" Value="{StaticResource PrimaryTextColor}" />
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryColor}" />
                <Setter Property="BorderThickness" Value="1" />

            </Style>

            <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="24" />
                <Setter Property="FontWeight" Value="Bold" />
                <Setter Property="FontFamily" Value="Segoe UI" />
                <Setter Property="Margin" Value="0,0,0,15" />
                <Setter Property="Foreground" Value="{StaticResource PrimaryColor}" />
            </Style>

            <Style x:Key="SectionBorderStyle" TargetType="Border">
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryColor}" />
                <Setter Property="BorderThickness" Value="1" />
                <Setter Property="CornerRadius" Value="8" />
                <Setter Property="Padding" Value="20" />
                <Setter Property="Margin" Value="0,0,0,20" />
                <Setter Property="Background" Value="{StaticResource PageColor}" />
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect
                            Direction="315"
                            Opacity="0.3"
                            ShadowDepth="2"
                            Color="Gray" />
                    </Setter.Value>
                </Setter>
            </Style>


        </ResourceDictionary>
    </Window.Resources>

    <WindowChrome.WindowChrome>
        <WindowChrome
            CaptionHeight="0"
            ResizeBorderThickness="0"
            UseAeroCaptionButtons="False" />
    </WindowChrome.WindowChrome>

    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">

        <Grid
            Width="1920"
            Height="1080"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Stretch"
            Background="Transparent">

            <Border
                Padding="20"
                Background="{DynamicResource backgroundColor}"
                BorderThickness="0"
                CornerRadius="0">

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <!--  رأس النافذة  -->
                    <Border
                        Grid.Row="0"
                        Margin="0,24"
                        CornerRadius="12">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    FontSize="40"
                                    FontWeight="ExtraBold"
                                    Foreground="{StaticResource PrimaryColor}"
                                    Text="معاينة تصدير المبيعات" />
                                <TextBlock
                                    x:Name="txtSummary"
                                    Margin="0,10,0,0"
                                    HorizontalAlignment="Center"
                                    FontSize="22"
                                    FontWeight="SemiBold"
                                    Foreground="{StaticResource PrimaryColor}"
                                    Text="إجمالي الفواتير: 0 | إجمالي الأصناف: 0" />
                            </StackPanel>


                        </Grid>
                    </Border>

                    <!--  المحتوى الرئيسي  -->
                    <Grid Grid.Row="1" Margin="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="400" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!--  لوحة التحكم الجانبية  -->
                        <ScrollViewer
                            Grid.Column="0"
                            Margin="0,0,20,0"
                            VerticalScrollBarVisibility="Auto">
                            <StackPanel>
                                <!--  خيارات التصدير  -->
                                <Border Style="{StaticResource SectionBorderStyle}">
                                    <StackPanel>
                                        <TextBlock  Text="خيارات التصدير" />

                                        <CheckBox
                                            x:Name="chkExportInvoices"
                                            Checked="ExportOption_Changed"
                                            Content="تصدير الفواتير"
                                            IsChecked="True"
                                            Style="{StaticResource CircleCheckboxFL}"
                                            Unchecked="ExportOption_Changed" />

                                        <CheckBox
                                            x:Name="chkExportItems"
                                            Checked="ExportOption_Changed"
                                            Content="تصدير الأصناف"
                                            IsChecked="True"
                                            Style="{StaticResource CircleCheckboxFL}"
                                            Unchecked="ExportOption_Changed" />
                                    </StackPanel>
                                </Border>

                                <!--  إعدادات الملف  -->
                                <Border Style="{StaticResource SectionBorderStyle}">
                                    <StackPanel>
                                        <TextBlock  Text="إعدادات الملف" />

                                        <TextBlock
                                            Margin="0,8,0,5"
                                            FontSize="20"
                                            FontWeight="Bold"
                                            Foreground="{StaticResource PrimaryTextColor}"
                                            Text="اسم الملف:" />
                                        <Border
                                            Height="45"
                                            BorderBrush="{StaticResource PrimaryColor}"
                                            BorderThickness="2"
                                            CornerRadius="6">
                                            <TextBox
                                                x:Name="txtFileName"
                                                Padding="12,8"
                                                Background="Transparent"
                                                BorderThickness="0"
                                                FontSize="18"
                                                FontWeight="SemiBold"
                                                Text="{Binding FileName, UpdateSourceTrigger=PropertyChanged}" />
                                        </Border>

                                        <Border
                                            x:Name="btnSelectPath"
                                            Height="44"
                                            Margin="0,16"
                                            BorderBrush="{StaticResource PrimaryColor}"
                                            BorderThickness="1"
                                            CornerRadius="12"
                                            MouseLeftButtonDown="btnSelectPath_Click">
                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                FontSize="18"
                                                FontWeight="SemiBold"
                                                Foreground="{StaticResource PrimaryColor}"
                                                Text="اختيار مسار الحفظ" />
                                        </Border>

                                        <TextBlock
                                            x:Name="txtSelectedPath"
                                            Margin="0,10,0,0"
                                            FontSize="16"
                                            FontWeight="SemiBold"
                                            Foreground="{StaticResource SecundaryButton}"
                                            Text="لم يتم اختيار مسار"
                                            TextWrapping="Wrap" />
                                    </StackPanel>
                                </Border>

                                <!--  شريط التقدم  -->
                                <Border
                                    x:Name="progressSection"
                                    Style="{StaticResource SectionBorderStyle}"
                                    Visibility="Collapsed">
                                    <StackPanel>
                                        <TextBlock  Text="تقدم التصدير" />
                                        <ProgressBar
                                            x:Name="progressBar"
                                            Height="30"
                                            Margin="0,10"
                                            Background="{StaticResource backgroundColor}"
                                            BorderBrush="{StaticResource PrimaryColor}"
                                            BorderThickness="1"
                                            Foreground="{StaticResource SuccessColor}" />
                                        <TextBlock
                                            x:Name="txtProgress"
                                            HorizontalAlignment="Center"
                                            FontSize="20"
                                            FontWeight="Bold"
                                            Foreground="{StaticResource PrimaryColor}"
                                            Text="0%" />
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </ScrollViewer>

                        <!--  منطقة المعاينة والحقول  -->
                        <TabControl
                            Grid.Column="1"
                            Margin="0"
                            Background="Transparent">
                            <!--  تبويب الفواتير  -->
                            <TabItem
                                x:Name="tabInvoices"
                                Header="فواتير المبيعات"
                                Style="{StaticResource TabItemStyle}">
                                <Grid Margin="20">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="*" />
                                    </Grid.RowDefinitions>

                                    <!--  حقول الفواتير  -->
                                    <Border Grid.Row="0" Style="{StaticResource SectionBorderStyle}">
                                        <StackPanel>
                                            <TextBlock  Text="الحقول المراد تصديرها" />
                                            <ScrollViewer MaxHeight="200" VerticalScrollBarVisibility="Auto">
                                                <ItemsControl x:Name="invoiceFieldsList">
                                                    <ItemsControl.ItemTemplate>
                                                        <DataTemplate>
                                                            <Grid Margin="0,5">
                                                                <Grid.ColumnDefinitions>
                                                                    <ColumnDefinition Width="Auto" />
                                                                    <ColumnDefinition Width="200" />
                                                                    <ColumnDefinition Width="*" />
                                                                </Grid.ColumnDefinitions>

                                                                <CheckBox
                                                                    Grid.Column="0"
                                                                    Margin="0,0,15,0"
                                                                    VerticalAlignment="Center"
                                                                    IsChecked="{Binding IsSelected}"
                                                                    IsEnabled="{Binding IsRequired, Converter={StaticResource InverseBooleanConverter}}"
                                                                    Style="{StaticResource CircleCheckboxFL}" />

                                                                <TextBlock
                                                                    Grid.Column="1"
                                                                    Margin="0,0,15,0"
                                                                    VerticalAlignment="Center"
                                                                    FontSize="18"
                                                                    FontWeight="Bold"
                                                                    Foreground="{StaticResource PrimaryTextColor}"
                                                                    Text="{Binding DefaultName}" />

                                                                <Border
                                                                    Grid.Column="2"
                                                                    Height="35"
                                                                    BorderBrush="{StaticResource PrimaryColor}"
                                                                    BorderThickness="1"
                                                                    CornerRadius="4">
                                                                    <TextBox
                                                                        Padding="8,5"
                                                                        Background="Transparent"
                                                                        BorderThickness="0"
                                                                        FontSize="16"
                                                                        FontWeight="SemiBold"
                                                                        Text="{Binding CustomHeader, UpdateSourceTrigger=PropertyChanged}" />
                                                                </Border>
                                                            </Grid>
                                                        </DataTemplate>
                                                    </ItemsControl.ItemTemplate>
                                                </ItemsControl>
                                            </ScrollViewer>
                                        </StackPanel>
                                    </Border>

                                    <!--  معاينة الفواتير  -->
                                    <Border Grid.Row="1" Style="{StaticResource SectionBorderStyle}">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="*" />
                                            </Grid.RowDefinitions>

                                            <TextBlock
                                                Grid.Row="0"
                                                
                                                Text="معاينة البيانات" />

                                            <DataGrid
                                                x:Name="invoicesDataGrid"
                                                Grid.Row="1"
                                                AutoGenerateColumns="False"
                                                Background="{StaticResource PageColor}"
                                                BorderBrush="{StaticResource PrimaryColor}"
                                                BorderThickness="1"
                                                CanUserReorderColumns="False"
                                                CanUserResizeColumns="True"
                                                CanUserSortColumns="False"
                                                ColumnWidth="*"
                                                FontSize="16"
                                                FontWeight="SemiBold"
                                                GridLinesVisibility="All"
                                                HeadersVisibility="Column"
                                                IsReadOnly="True"
                                                RowHeight="40">

                                                <DataGrid.ColumnHeaderStyle>
                                                    <Style TargetType="DataGridColumnHeader">
                                                        <Setter Property="Background" Value="{StaticResource PrimaryColor}" />
                                                        <Setter Property="Foreground" Value="White" />
                                                        <Setter Property="FontWeight" Value="Bold" />
                                                        <Setter Property="FontSize" Value="18" />
                                                        <Setter Property="FontFamily" Value="Segoe UI" />
                                                        <Setter Property="Padding" Value="12,10" />
                                                        <Setter Property="HorizontalContentAlignment" Value="Center" />
                                                    </Style>
                                                </DataGrid.ColumnHeaderStyle>
                                                <DataGrid.RowStyle>
                                                    <Style TargetType="DataGridRow">
                                                        <Setter Property="Background" Value="White" />
                                                        <Setter Property="FontWeight" Value="SemiBold" />
                                                        <Style.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="{StaticResource backgroundColor}" />
                                                            </Trigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </DataGrid.RowStyle>
                                            </DataGrid>
                                        </Grid>
                                    </Border>
                                </Grid>
                            </TabItem>

                            <!--  تبويب الأصناف  -->
                            <TabItem
                                x:Name="tabItems"
                                Header="أصناف الفواتير"
                                Style="{StaticResource TabItemStyle}">
                                <Grid Margin="20">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="*" />
                                    </Grid.RowDefinitions>

                                    <!--  حقول الأصناف  -->
                                    <Border Grid.Row="0" Style="{StaticResource SectionBorderStyle}">
                                        <StackPanel>
                                            <TextBlock  Text="الحقول المراد تصديرها" />
                                            <ScrollViewer MaxHeight="200" VerticalScrollBarVisibility="Auto">
                                                <ItemsControl x:Name="itemFieldsList">
                                                    <ItemsControl.ItemTemplate>
                                                        <DataTemplate>
                                                            <Grid Margin="0,5">
                                                                <Grid.ColumnDefinitions>
                                                                    <ColumnDefinition Width="Auto" />
                                                                    <ColumnDefinition Width="200" />
                                                                    <ColumnDefinition Width="*" />
                                                                </Grid.ColumnDefinitions>

                                                                <CheckBox
                                                                    Grid.Column="0"
                                                                    Margin="0,0,15,0"
                                                                    VerticalAlignment="Center"
                                                                    IsChecked="{Binding IsSelected}"
                                                                    IsEnabled="{Binding IsRequired, Converter={StaticResource InverseBooleanConverter}}"
                                                                    Style="{StaticResource CircleCheckboxFL}" />

                                                                <TextBlock
                                                                    Grid.Column="1"
                                                                    Margin="0,0,15,0"
                                                                    VerticalAlignment="Center"
                                                                    FontSize="18"
                                                                    FontWeight="Bold"
                                                                    Foreground="{StaticResource PrimaryTextColor}"
                                                                    Text="{Binding DefaultName}" />

                                                                <Border
                                                                    Grid.Column="2"
                                                                    Height="35"
                                                                    BorderBrush="{StaticResource PrimaryColor}"
                                                                    BorderThickness="1"
                                                                    CornerRadius="4">
                                                                    <TextBox
                                                                        Padding="8,5"
                                                                        Background="Transparent"
                                                                        BorderThickness="0"
                                                                        FontSize="16"
                                                                        FontWeight="SemiBold"
                                                                        Text="{Binding CustomHeader, UpdateSourceTrigger=PropertyChanged}" />
                                                                </Border>
                                                            </Grid>
                                                        </DataTemplate>
                                                    </ItemsControl.ItemTemplate>
                                                </ItemsControl>
                                            </ScrollViewer>
                                        </StackPanel>
                                    </Border>

                                    <!--  معاينة الأصناف  -->
                                    <Border Grid.Row="1" Style="{StaticResource SectionBorderStyle}">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="*" />
                                            </Grid.RowDefinitions>

                                            <TextBlock
                                                Grid.Row="0"
                                                
                                                Text="معاينة البيانات" />

                                            <DataGrid
                                                x:Name="itemsDataGrid"
                                                Grid.Row="1"
                                                AutoGenerateColumns="False"
                                                Background="{StaticResource PageColor}"
                                                BorderBrush="{StaticResource PrimaryColor}"
                                                BorderThickness="1"
                                                CanUserReorderColumns="False"
                                                CanUserResizeColumns="True"
                                                CanUserSortColumns="False"
                                                ColumnWidth="*"
                                                FontSize="16"
                                                FontWeight="SemiBold"
                                                GridLinesVisibility="All"
                                                HeadersVisibility="Column"
                                                IsReadOnly="True"
                                                RowHeight="40">
                                                <DataGrid.ColumnHeaderStyle>
                                                    <Style TargetType="DataGridColumnHeader">
                                                        <Setter Property="Background" Value="{StaticResource PrimaryColor}" />
                                                        <Setter Property="Foreground" Value="White" />
                                                        <Setter Property="FontWeight" Value="Bold" />
                                                        <Setter Property="FontSize" Value="18" />
                                                        <Setter Property="FontFamily" Value="Segoe UI" />
                                                        <Setter Property="Padding" Value="12,10" />
                                                        <Setter Property="HorizontalContentAlignment" Value="Center" />
                                                    </Style>
                                                </DataGrid.ColumnHeaderStyle>
                                                <DataGrid.RowStyle>
                                                    <Style TargetType="DataGridRow">
                                                        <Setter Property="Background" Value="White" />
                                                        <Setter Property="FontWeight" Value="SemiBold" />
                                                        <Style.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="{StaticResource backgroundColor}" />
                                                            </Trigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </DataGrid.RowStyle>
                                            </DataGrid>
                                        </Grid>
                                    </Border>
                                </Grid>
                            </TabItem>
                        </TabControl>
                    </Grid>

                    <!--  أزرار التحكم  -->

                    <Border
                        Grid.Row="2"
                        Margin="0,16"
                        Padding="0,24">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="4*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="12" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="4*" />
                            </Grid.ColumnDefinitions>
                            <Border
                                x:Name="btnExportNow"
                                Grid.Column="1"
                                MinHeight="44"
                                Background="{StaticResource PrimaryColor}"
                                CornerRadius="12"
                                Cursor="Hand"
                                MouseLeftButtonDown="btnExportNow_Click">
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FontSize="20"
                                    FontWeight="Bold"
                                    Foreground="White"
                                    Text="تصدير الآن" />
                            </Border>

                            <Border
                                x:Name="btnCancel"
                                Grid.Column="3"
                                MinHeight="44"
                                Background="Transparent"
                                BorderBrush="{StaticResource errorColor}"
                                BorderThickness="1"
                                CornerRadius="12"
                                Cursor="Hand"
                                MouseLeftButtonDown="btnCancel_Click">
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FontSize="20"
                                    FontWeight="Bold"
                                    Foreground="{StaticResource errorColor}"
                                    Text="إلغاء" />
                            </Border>
                        </Grid>
                    </Border>

                </Grid>
            </Border>
        </Grid>
    </Viewbox>
</Window>
