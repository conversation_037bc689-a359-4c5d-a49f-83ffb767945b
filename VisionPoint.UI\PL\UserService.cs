﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using System;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using VisionPoint.UI.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Data.SqlClient;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Services;
using VisionPoint.UI.ViewModel;

namespace VisionPoint.UI.PL;

public class UserService : IDisposable
{
    private readonly UserManager<User> _userManager;
    private readonly RoleManager<Role> _roleManager;
    private readonly AppDbContext _context = ServiceLocator.GetService<AppDbContext>();
    private bool _disposed = false;

    public UserService(
        UserManager<User> userManager,
        RoleManager<Role> roleManager)
    {
        _userManager = userManager;
        _roleManager = roleManager;
    }

    // Create user with roles
    public async Task<(IdentityResult result, User user)> CreateUser(User user, string password, List<string> roles)
    {
        var result = await _userManager.CreateAsync(user, password);

        if (result.Succeeded && roles != null && roles.Any())
        {
            await _userManager.AddToRolesAsync(user, roles);
        }

        return (result, user);
    }

    // Update user with roles
    public async Task<IdentityResult> UpdateUser(User user, List<string> newRoles)
    {
        // التأكد من أن المستخدم غير مُتتبع في DbContext
        var existingUser = await _userManager.FindByIdAsync(user.Id.ToString());
        if (existingUser == null)
        {
            return IdentityResult.Failed(new IdentityError { Description = "المستخدم غير موجود" });
        }

        // تحديث البيانات
        existingUser.UserName = user.UserName;
        existingUser.Name = user.Name;
        existingUser.Salary = user.Salary;
        existingUser.Balance = user.Balance;
        existingUser.WarehouseId = user.WarehouseId;
        existingUser.AllowBalanceOverride = user.AllowBalanceOverride;

        var result = await _userManager.UpdateAsync(existingUser);

        if (result.Succeeded && newRoles != null)
        {
            var currentRoles = await _userManager.GetRolesAsync(existingUser);
            await _userManager.RemoveFromRolesAsync(existingUser, currentRoles);
            await _userManager.AddToRolesAsync(existingUser, newRoles);
        }

        return result;
    }

    // Delete user
    public async Task<(bool Succeeded, string Message)> DeleteUser(User user)
    {
            // التحقق من وجود علاقات مع جداول أخرى باستخدام استعلام SQL مباشر
            // هذا الاستعلام سيتحقق من جميع الجداول التي تحتوي على حقول تشير إلى المستخدم

            try
            {
                
                //// التحقق من الأدوار والمطالبات وتسجيلات الدخول
                //bool hasRoles = (await _userManager.GetRolesAsync(user)).Any();
                //if (hasRoles)
                //{
                //    // حذف الأدوار قبل حذف المستخدم
                //    await _userManager.RemoveFromRolesAsync(user, await _userManager.GetRolesAsync(user));
                //}

                // إذا لم تكن هناك علاقات، قم بحذف المستخدم
                _context.Users.RemoveWithBusy(user);
            var result =await _context.SaveWithTransactionAndBusy("DeleteUser");
            return (result.State, result.Message);
            }
            catch (Exception ex)
            {
                return (false, $"حدث خطأ أثناء الحذف: {ex.Message}");
            }
    }

    // Get user by ID
    public async Task<User> GetUserById(int id)
    {
        return await _context.Users
            .Include(u => u.Warehouse)
            .FirstOrDefaultAsyncWithBusy(u => u.Id == id, "GetUserById");
    }

    // Get all users
    public async Task<List<User>> GetAllUsersAsync()
    { // استخدام الطريقة المتزامنة بدلاً من .Result لتجنب deadlock
            return await _context.Users
                .Include(u => u.Warehouse)
                .AsNoTracking()
                .ToListAsyncWithBusy("GetAllUsers");
    }

    /// <summary>
    /// الحصول على المستخدمين المرتبطين بمخزن معين أو المستخدمين العامين (WarehouseId = null)
    /// </summary>
    /// <param name="warehouseId">معرف المخزن - إذا كان null فسيتم إرجاع المستخدمين العامين فقط</param>
    /// <returns>قائمة المستخدمين</returns>
    public async Task<List<User>> GetUsersByWarehouseAsync(int? warehouseId)
    {
        var query = _context.Users.AsQueryable();

        if (warehouseId.HasValue)
        {
            // إرجاع المستخدمين المرتبطين بالمخزن المحدد أو المستخدمين العامين (WarehouseId = null)
            query = query.Where(u => u.WarehouseId == warehouseId.Value || u.WarehouseId == null);
        }
        else
        {
            // إرجاع المستخدمين العامين فقط (WarehouseId = null)
            query = query.Where(u => u.WarehouseId == null);
        }

        return await query.Include(u => u.Warehouse).AsNoTracking().ToListAsyncWithBusy("GetUsersByWarehouse");
    }

    // Get user roles
    public async Task<List<string>> GetUserRoles(User user)
    {
        // الحصول على المستخدم من قاعدة البيانات لتجنب مشاكل التتبع
        var existingUser = await _userManager.FindByIdAsync(user.Id.ToString());
        if (existingUser == null)
        {
            return new List<string>();
        }

        return (await _userManager.GetRolesAsync(existingUser)).ToList();
    }

    // Login
    public async Task<bool> Login(string name, string password)
    {
        var user = await _userManager.FindByNameAsync(name);
        if (user == null)
            return false;

        var result = await _userManager.CheckPasswordAsync(user, password);
        if (result)
        {
            var userRoles = await _userManager.GetRolesAsync(user);

            // الحصول على معلومات المستخدم مع المخزن
            var userWithWarehouse = await _context.Users
                .Include(u => u.Warehouse)
                .FirstOrDefaultAsyncWithBusy(u => u.Id == user.Id, "GetUserWithWarehouse");

            CurrentUser.Id = user.Id;
            CurrentUser.Name = user.UserName;
            CurrentUser.Role = userRoles.FirstOrDefault(); // للتوافق مع الكود القديم
            CurrentUser.Roles = userRoles.ToList(); // تخزين جميع الأدوار

            // تحديد معلومات المخزن
            CurrentUser.WarehouseId = userWithWarehouse?.WarehouseId;
            CurrentUser.WarehouseName = userWithWarehouse?.Warehouse?.Name;
        }
        return result;
    }

    // Logout
    public async Task Logout()
    {
        CurrentUser.Id = 0;
        CurrentUser.Name = null;
        CurrentUser.Role = null;
        CurrentUser.Roles.Clear();
        CurrentUser.WarehouseId = null;
        CurrentUser.WarehouseName = null;
        await Task.CompletedTask;
    }

    // Change password
    public async Task<IdentityResult> ChangePassword(User user, string currentPassword, string newPassword)
    {
        return await _userManager.ChangePasswordAsync(user, currentPassword, newPassword);
    }

    // Generate password reset token
    public async Task<string> GeneratePasswordResetToken(User user)
    {
        return await _userManager.GeneratePasswordResetTokenAsync(user);
    }

    // Reset password with token
    public async Task<IdentityResult> ResetPassword(User user, string token, string newPassword)
    {
        return await _userManager.ResetPasswordAsync(user, token, newPassword);
    }

    // Reset password directly (for admin use)
    public async Task<IdentityResult> AdminResetPassword(User user, string newPassword)
    {
        // الحصول على المستخدم من قاعدة البيانات لتجنب مشاكل التتبع
        var existingUser = await _userManager.FindByIdAsync(user.Id.ToString());
        if (existingUser == null)
        {
            return IdentityResult.Failed(new IdentityError { Description = "المستخدم غير موجود" });
        }

        // إزالة كلمة المرور الحالية وتعيين الجديدة
        var token = await _userManager.GeneratePasswordResetTokenAsync(existingUser);
        var result = await _userManager.ResetPasswordAsync(existingUser, token, newPassword);

        return result;
    }

    // Helper method to get available roles
    public async Task<List<string>> GetAvailableRoles()
    {
        return await Task.FromResult(_roleManager.Roles.Select(r => r.Name).ToList());
    }

    // Helper method to get a fresh user instance from database to avoid tracking issues
    private async Task<User> GetFreshUserInstance(int userId)
    {
        // استخدام AsNoTracking لتجنب مشاكل التتبع
        return await _context.Users.AsNoTracking().FirstOrDefaultAsyncWithBusy(u => u.Id == userId, "GetFreshUserInstance");
    }

    /// <summary>
    /// استرجاع كشف حساب المستخدم
    /// </summary>
    /// <param name="userId">معرف المستخدم</param>
    /// <param name="fromDate">تاريخ البداية (اختياري)</param>
    /// <param name="toDate">تاريخ النهاية (اختياري)</param>
    /// <returns>قائمة بعمليات المستخدم</returns>
    public async Task<(List<UserStatementVM> Transactions, UserStatementSummaryVM Summary)> GetUserStatementAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null)
    {
            // التحقق من وجود المستخدم
            var user = await _context.Users.FindAsyncWithBusy(userId);
            if (user == null)
            {
                return (new List<UserStatementVM>(), new UserStatementSummaryVM { UserName = "غير موجود" });
            }

            // الحصول على معلومات المستخدم
            var userInfo = user;

            // تحديد نطاق التاريخ
            DateTime startDate = fromDate ?? DateTime.MinValue;
            DateTime endDate = toDate?.Date.AddDays(1).AddSeconds(-1) ?? DateTime.MaxValue;

            // قائمة لتخزين جميع العمليات
            var allTransactions = new List<UserStatementVM>();

            // جلب الإيصالات المرتبطة بالمستخدم
            var receipts = await _context.Receipts
                .Include(r => r.Treasury)
                .Include(r => r.Financial)
                .Where(r => r.EmployeeId == userId)
                .ToListAsyncWithBusy("GetReceiptsForUserStatement");

            // البحث عن الرصيد الافتتاحي للموظف
            var openingBalanceReceipt = receipts
                .FirstOrDefault(r => r.FinancialId == (byte)FinancialId.OpeningBalanceForEmployee);

            // جلب دفعات المرتبات المرتبطة بالمستخدم
            var salaryPayments = await _context.SalaryPayments
                .Include(sp => sp.Receipt)
                .ThenInclude(r => r.Treasury)
                .Where(sp => sp.UserId == userId)
                .ToListAsyncWithBusy("GetSalaryPaymentsForUserStatement");

            // إضافة الرصيد الافتتاحي إذا كان موجوداً
            decimal openingBalance = 0;
            if (openingBalanceReceipt != null)
            {
                // تحديد قيمة الرصيد الافتتاحي
                openingBalance = openingBalanceReceipt.Value;

                // إضافة صف الرصيد الافتتاحي في بداية الكشف
                allTransactions.Add(new UserStatementVM
                {
                    Id = openingBalanceReceipt.Id,
                    Date = openingBalanceReceipt.Date,
                    OperationType = "الرصيد الافتتاحي",
                    OperationNumber = openingBalanceReceipt.ReceiptNo,
                    PaymentMethod = openingBalanceReceipt.Treasury?.Name ?? "\\",
                    Description = openingBalanceReceipt.Statement ?? "رصيد افتتاحي للموظف",
                    Incoming = openingBalanceReceipt.Value, // نعتبر الرصيد الافتتاحي دائماً كمبلغ داخل
                    Outgoing = 0,
                    Balance = 0, // سيتم حسابه لاحقاً
                    IsSpecialRow = true // نعتبره صفاً خاصاً ليظهر بتنسيق مختلف
                });
            }

            // إذا كان هناك تاريخ بداية محدد، نحتاج إلى حساب الرصيد السابق
            decimal previousBalance = openingBalance; // نبدأ من الرصيد الافتتاحي إذا كان موجوداً
            if (fromDate.HasValue)
            {
                // الإيصالات السابقة
                var previousReceipts = await _context.Receipts
                    .Where(r => r.EmployeeId == userId && r.Date < startDate && r.FinancialId != (byte)FinancialId.OpeningBalanceForEmployee)
                    .ToListAsyncWithBusy("GetPreviousReceiptsForUserStatement");

                // دفعات المرتبات السابقة
                var previousSalaryPayments = await _context.SalaryPayments
                    .Where(sp => sp.UserId == userId && sp.PaymentDate < startDate)
                    .ToListAsyncWithBusy("GetPreviousSalaryPaymentsForUserStatement");

                // الرصيد السابق سيتم حسابه في الخطوة التالية

                // إضافة صف الرصيد السابق دائماً عند تحديد تاريخ بداية
                // حساب الرصيد السابق من الإيصالات السابقة
                foreach (var receipt in previousReceipts)
                {
                    if (receipt.FinancialId == (byte)FinancialId.Employee)
                    {
                        if (receipt.IsExchange == true) // صرف للموظف
                        {
                            previousBalance += receipt.Value;
                        }
                        else if (receipt.IsExchange == false) // قبض من الموظف
                        {
                            previousBalance -= receipt.Value;
                        }
                    }
                    else if (receipt.FinancialId == (byte)FinancialId.SalaryPayment)
                    {
                        previousBalance += receipt.Value; // زيادة رصيد الموظف (صرف المرتب)
                    }
                    else if (receipt.FinancialId == (byte)FinancialId.AutomaticSalary)
                    {
                        previousBalance -= receipt.Value; // نقص رصيد الموظف (إضافة المرتب التلقائي)
                    }
                }

                // إضافة صف الرصيد السابق
                allTransactions.Add(new UserStatementVM
                {
                    Id = -1, // معرف خاص للرصيد السابق
                    Date = fromDate.Value.AddSeconds(-1),
                    OperationType = "الرصيد السابق",
                    OperationNumber = 0,
                    PaymentMethod = "-",
                    Description = "الرصيد قبل " + fromDate.Value.ToString("yyyy-MM-dd"),
                    Incoming = 0,
                    Outgoing = 0,
                    Balance = 0, // سيتم حسابه لاحقاً
                    IsSpecialRow = true
                });
            }

            // تحويل الإيصالات إلى نموذج كشف الحساب (باستثناء الرصيد الافتتاحي الذي تمت إضافته بالفعل)
            foreach (var receipt in receipts.Where(r => r.FinancialId != (byte)FinancialId.OpeningBalanceForEmployee))
            {
                decimal incoming = 0;
                decimal outgoing = 0;
                string operationType = "";

                // تحديد نوع العملية والمبالغ بناءً على نوع الإيصال
                if (receipt.FinancialId == (byte)FinancialId.Employee)
                {
                    if (receipt.IsExchange == true) // صرف للموظف
                    {
                        operationType = "صرف للموظف";
                        incoming = receipt.Value; // مبلغ داخل للموظف
                        outgoing = 0;
                    }
                    else if (receipt.IsExchange == false) // قبض من الموظف
                    {
                        operationType = "قبض من الموظف";
                        incoming = 0;
                        outgoing = receipt.Value; // مبلغ خارج من الموظف
                    }
                }
                else if (receipt.FinancialId == (byte)FinancialId.SalaryPayment)
                {
                    operationType = "صرف مرتب";
                    incoming = receipt.Value; // مبلغ داخل للموظف
                    outgoing = 0;
                }
                else if (receipt.FinancialId == (byte)FinancialId.AutomaticSalary)
                {
                    operationType = "إضافة راتب تلقائي";
                    incoming = 0;
                    outgoing = receipt.Value; // مبلغ خارج من الموظف
                }
                // تم نقل معالجة الرصيد الافتتاحي إلى قسم منفصل في بداية الدالة
                else
                {
                    // أنواع أخرى من الإيصالات
                    operationType = receipt.Financial?.Name ?? "إيصال";
                    if (receipt.IsExchange == true) // صرف
                    {
                        incoming = receipt.Value;
                        outgoing = 0;
                    }
                    else if (receipt.IsExchange == false) // قبض
                    {
                        incoming = 0;
                        outgoing = receipt.Value;
                    }
                }

                // تحديد طريقة الدفع للإيصالات
                string paymentMethod;
                if (receipt.Treasury != null)
                {
                    paymentMethod = receipt.Treasury.Name;
                }
                else if (receipt.TreasuryId.HasValue)
                {
                    // محاولة جلب الخزينة إذا كان لدينا معرف الخزينة
                    var treasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TreasuryId.Value);
                    paymentMethod = treasury?.Name ?? "نقدي";
                }
                else
                {
                    paymentMethod = "نقدي";
                }

                allTransactions.Add(new UserStatementVM
                {
                    Id = receipt.Id,
                    Date = receipt.Date,
                    OperationType = operationType,
                    OperationNumber = receipt.ReceiptNo,
                    PaymentMethod = paymentMethod,
                    Description = receipt.Statement ?? "",
                    Incoming = incoming,
                    Outgoing = outgoing,
                    Balance = 0 // سيتم حسابه لاحقاً
                });
            }

            // ترتيب العمليات حسب التاريخ (من الأقدم للأحدث)
            allTransactions = allTransactions.OrderBy(t => t.Date).ToList();

            // تطبيق فلتر التاريخ (تضمين الرصيد الافتتاحي فقط إذا كان تاريخه ضمن النطاق)
            var result = allTransactions
                .Where(t => t.Date >= startDate && t.Date <= endDate ||
                       t.OperationType == "الرصيد السابق" || // دائماً تضمين الرصيد السابق
                       t.OperationType == "الرصيد اللاحق") // دائماً تضمين الرصيد اللاحق
                .ToList();

            // ترتيب العمليات حسب التاريخ فقط
            result = result.OrderBy(t => t.Date).ToList();

            // حساب الرصيد التراكمي بطريقة صحيحة
            decimal currentBalance = 0; // نبدأ من صفر

            // نمر على كل العمليات بالترتيب الزمني
            for (int i = 0; i < result.Count; i++)
            {
                var transaction = result[i];

                // إذا كان الصف هو الرصيد الافتتاحي
                if (transaction.OperationType == "الرصيد الافتتاحي")
                {
                    // نستخدم قيمة الرصيد الافتتاحي كبداية
                    currentBalance = transaction.Incoming;
                    transaction.Balance = currentBalance;
                }
                // إذا كان الصف هو الرصيد السابق
                else if (transaction.OperationType == "الرصيد السابق")
                {
                    // نستخدم قيمة الرصيد السابق كما هي
                    transaction.Balance = previousBalance;
                    currentBalance = previousBalance;
                }
                // للصفوف العادية
                else
                {
                    // حساب الرصيد الجديد بإضافة الوارد وطرح الصادر
                    currentBalance += transaction.Incoming - transaction.Outgoing;
                    transaction.Balance = currentBalance;
                }
            }

            // إذا كان هناك تاريخ نهاية محدد، نحتاج إلى حساب الرصيد اللاحق
            if (toDate.HasValue)
            {
                // الإيصالات اللاحقة (باستثناء الرصيد الافتتاحي)
                var futureReceipts = await _context.Receipts
                    .Where(r => r.EmployeeId == userId &&
                           r.Date > endDate &&
                           r.FinancialId != (byte)FinancialId.OpeningBalanceForEmployee)
                    .ToListAsyncWithBusy("GetFutureReceiptsForUserStatement");

                // دفعات المرتبات اللاحقة
                var futureSalaryPayments = await _context.SalaryPayments
                    .Where(sp => sp.UserId == userId && sp.PaymentDate > endDate)
                    .ToListAsyncWithBusy("GetFutureSalaryPaymentsForUserStatement");

                // تعريف متغير الرصيد اللاحق
                decimal futureBalance = currentBalance;

                // إضافة صف الرصيد اللاحق إذا كانت هناك عمليات لاحقة
                if (futureReceipts.Count > 0 || futureSalaryPayments.Count > 0)
                {
                    // استخدام آخر رصيد محسوب كأساس للرصيد اللاحق
                    var lastTransaction = result.LastOrDefault();
                    decimal latestBalance = lastTransaction?.Balance ?? 0;

                    // استخدام آخر رصيد محسوب
                    futureBalance = latestBalance;

                    // حساب الرصيد اللاحق من الإيصالات المستقبلية
                    foreach (var receipt in futureReceipts)
                    {
                        if (receipt.FinancialId == (byte)FinancialId.Employee)
                        {
                            if (receipt.IsExchange == true) // صرف للموظف
                            {
                                futureBalance += receipt.Value;
                            }
                            else if (receipt.IsExchange == false) // قبض من الموظف
                            {
                                futureBalance -= receipt.Value;
                            }
                        }
                        else if (receipt.FinancialId == (byte)FinancialId.SalaryPayment)
                        {
                            futureBalance += receipt.Value; // زيادة رصيد الموظف (صرف المرتب)
                        }
                        else if (receipt.FinancialId == (byte)FinancialId.AutomaticSalary)
                        {
                            futureBalance -= receipt.Value; // نقص رصيد الموظف (إضافة المرتب التلقائي)
                        }
                    }

                    result.Add(new UserStatementVM
                    {
                        Id = -2, // معرف خاص للرصيد اللاحق
                        Date = endDate.AddSeconds(1),
                        OperationType = "الرصيد اللاحق",
                        OperationNumber = 0,
                        PaymentMethod = "-",
                        Description = "الرصيد بعد " + endDate.ToString("yyyy-MM-dd"),
                        Incoming = 0,
                        Outgoing = 0,
                        Balance = futureBalance,
                        IsSpecialRow = true
                    });
                }
            }

            // إنشاء ملخص كشف الحساب
            var summary = new UserStatementSummaryVM
            {
                UserName = userInfo?.Name ?? "غير معروف",
                // حساب المجاميع فقط للعمليات العادية (غير الصفوف الخاصة)
                TotalDebit = result.Where(t => !t.IsSpecialRow).Sum(t => t.Outgoing),
                TotalCredit = result.Where(t => !t.IsSpecialRow).Sum(t => t.Incoming),
                // استخدام الرصيد النهائي من آخر صف في النتائج
                FinalBalance = result.LastOrDefault()?.Balance ?? 0
            };

            // تحديد حالة الرصيد (مدين/دائن)
            if (user.Balance > 0)
            {
                summary.BalanceStatus = "مدين (الموظف مدين للمتجر)";
            }
            else if (user.Balance < 0)
            {
                summary.BalanceStatus = "دائن (المتجر مدين للموظف)";
            }
            else
            {
                summary.BalanceStatus = "متوازن (لا يوجد دين)";
            }

            return (result, summary);
  
    }

    // Implement IDisposable pattern
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // Dispose managed resources
                _userManager?.Dispose();
                _roleManager?.Dispose();
                _context?.Dispose();
            }

            // Free unmanaged resources
            _disposed = true;
        }
    }

    // Destructor
    ~UserService()
    {
        Dispose(false);
    }
}