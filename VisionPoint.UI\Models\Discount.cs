﻿﻿﻿using Microsoft.EntityFrameworkCore;

namespace VisionPoint.UI.Models;

public class Discount : BaseEntity
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    [Precision(18, 3)] public decimal DiscountPercentage { get; set; }
    
    // Flags to indicate if the discount applies to all items in a category
    public bool ApplyToAllProducts { get; set; }
    public bool ApplyToAllLenses { get; set; }
    public bool ApplyToAllServices { get; set; }
    
    // Navigation properties
    public ICollection<DiscountProduct>? DiscountProducts { get; set; } = new List<DiscountProduct>();
    public ICollection<DiscountLens>? DiscountLenses { get; set; } = new List<DiscountLens>();
    public ICollection<DiscountService>? DiscountServices { get; set; } = new List<DiscountService>();
}
