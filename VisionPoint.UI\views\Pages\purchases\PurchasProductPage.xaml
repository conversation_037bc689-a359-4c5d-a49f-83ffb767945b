﻿<Page
    x:Class="VisionPoint.UI.views.Pages.purchases.PurchasProductPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converter="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.purchases"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils"
    Title="PurchasProductPage"
    d:Background="White"
    d:Height="1080"
    d:Width="1570"
    FlowDirection="RightToLeft"
    Loaded="Window_Loaded"
    mc:Ignorable="d">
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <converter:PriceMultiplierConverter x:Key="PriceMultiplierConverter" />
            <converter:IndexToNumberConverter x:Key="IndexToNumberConverter" />
        </ResourceDictionary>
    </Page.Resources>







    <Grid
        Grid.Row="1"
        Grid.ColumnSpan="2"
        Margin="16">

        <Grid.ColumnDefinitions>
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
        </Grid.RowDefinitions>



        <Grid Grid.ColumnSpan="2" Margin="8,0">


            <TextBox
                x:Name="txtPurchaseNo"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                IsReadOnly="True"
                Tag="رقم الفاتورة" />

        </Grid>

        <DatePicker
            x:Name="DtpPurchaseDate"
            Grid.Column="2"
            Grid.ColumnSpan="2"
            Height="60"
            Margin="8,0"
            BorderBrush="{StaticResource PrimaryTextColor}"
            BorderThickness="1"
            FontSize="18"
            SelectedDateFormat="Short">
            <DatePicker.Resources>
                <Style TargetType="{x:Type DatePickerTextBox}">
                    <Setter Property="Control.Template">
                        <Setter.Value>
                            <ControlTemplate>
                                <TextBox
                                    x:Name="PART_TextBox"
                                    VerticalAlignment="Stretch"
                                    BorderThickness="0"
                                    Foreground="{StaticResource PrimaryTextColor}"
                                    Tag="تاريخ الفاتورة"
                                    Text="{Binding Path=SelectedDate, StringFormat='dd/MM/yyyy', RelativeSource={RelativeSource AncestorType={x:Type DatePicker}}}" />
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </DatePicker.Resources>
        </DatePicker>



        <ComboBox
            x:Name="cmbWarehouse"
            Grid.Column="4"
            Grid.ColumnSpan="2"
            Margin="8,0"
            FontSize="21"
            IsEditable="True"
            IsReadOnly="False"
            SelectionChanged="cmbWarehouse_SelectionChanged"
            Tag="المخزن" />

        <ComboBox
            x:Name="cmbClient"
            Grid.Column="6"
            Grid.ColumnSpan="2"
            Margin="8,0"
            FontSize="21"
            IsEditable="True"
            IsReadOnly="False"
            Tag="المورد" />

        <Border
            x:Name="btnBack"
            Grid.Column="8"
            MaxHeight="44"
            Margin="8,0"
            Background="Transparent"
            BorderBrush="{StaticResource errorColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnBack_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource errorColor}">
                رجوع
            </TextBlock>
        </Border>

        <Grid Grid.Row="1" Margin="8,0">
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition />
            </Grid.RowDefinitions>
            <TextBlock
                Margin="0,0,0,12"
                VerticalAlignment="Bottom"
                FontSize="21"
                TextAlignment="Center">
                اصناف
            </TextBlock>

            <RadioButton
                x:Name="RdbProduct"
                Grid.Row="1"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Checked="RdbType_Checked"
                FlowDirection="LeftToRight"
                GroupName="ProductType"
                IsChecked="False" />
        </Grid>

        <Grid
            Grid.Row="1"
            Grid.Column="1"
            Margin="8,0">
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition />
            </Grid.RowDefinitions>
            <TextBlock
                VerticalAlignment="Center"
                FontSize="21"
                TextAlignment="Center">
                عدسات
            </TextBlock>

            <RadioButton
                x:Name="RdbLenses"
                Grid.Row="1"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Checked="RdbType_Checked"
                FlowDirection="LeftToRight"
                GroupName="ProductType"
                IsChecked="True" />
        </Grid>





        <Grid
            x:Name="LensesLayout"
            Grid.Row="1"
            Grid.RowSpan="3"
            Grid.ColumnSpan="10">

            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition />
                <RowDefinition />
            </Grid.RowDefinitions>

            <Grid
                Grid.Column="2"
                Grid.ColumnSpan="2"
                Margin="8,0">


                <TextBox
                    x:Name="txtLensBarcode"
                    Height="60"
                    VerticalAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    BorderThickness="1"
                    FontSize="21"
                    GotFocus="TextBox_GotFocus"
                    KeyDown="txtLensBarcode_KeyDown"
                    Tag="باركود" />

            </Grid>

            <Grid
                Grid.Column="4"
                Grid.ColumnSpan="2"
                Margin="8,0">


                <ComboBox
                    x:Name="cmbLensCategory"
                    Grid.Row="0"
                    Margin="0,0,0,2"
                    FontSize="16"
                    IsEditable="True"
                    IsReadOnly="False"
                    SelectionChanged="cmbLensCategory_SelectionChanged"
                    Tag="نوع العدسة" />

            </Grid>

            <Grid
                Grid.Column="6"
                Grid.ColumnSpan="2"
                Margin="8,0">




                <ComboBox
                    x:Name="cmbLensesName"
                    Grid.Row="1"
                    Margin="0,2,0,0"
                    FontSize="16"
                    IsEditable="True"
                    IsReadOnly="False"
                    SelectionChanged="cmbLensesName_SelectionChanged"
                    Tag="اسم العدسة" />
            </Grid>
            <DatePicker
                x:Name="DtpLensExpireOn"
                Grid.Row="2"
                Grid.Column="6"
                Grid.ColumnSpan="2"
                Height="60"
                Margin="0,0,16,0"
                BorderBrush="{StaticResource PrimaryTextColor}"
                BorderThickness="1"
                FontSize="18"
                Text="تاريخ الصلاحية">
                <DatePicker.Resources>
                    <Style TargetType="{x:Type DatePickerTextBox}">
                        <Setter Property="Control.Template">
                            <Setter.Value>
                                <ControlTemplate>
                                    <TextBox
                                        x:Name="PART_TextBox"
                                        VerticalAlignment="Stretch"
                                        BorderThickness="0"
                                        Foreground="{StaticResource PrimaryTextColor}"
                                        Tag="تاريخ الصلاحية"
                                        Text="{Binding Path=SelectedDate, RelativeSource={RelativeSource AncestorType={x:Type DatePicker}}}" />
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </DatePicker.Resources>
            </DatePicker>


            <Grid Grid.Row="1" Margin="8,0">


                <TextBox
                    x:Name="txtLensQuantity"
                    Height="60"
                    VerticalAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    utils:NumericInputControl.IsNumericOnly="True"
                    BorderThickness="1"
                    FontSize="21"
                    GotFocus="TextBox_GotFocus"
                    Tag="الكمية" />
            </Grid>
            <Grid
                Grid.Row="1"
                Grid.Column="1"
                Margin="8,0">



                <TextBox
                    x:Name="txtLensPrice"
                    Height="60"
                    VerticalAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    utils:NumericInputControl.IsDecimalOnly="True"
                    BorderThickness="1"
                    FontSize="21"
                    GotFocus="TextBox_GotFocus"
                    Tag="السعر" />
            </Grid>

            <Grid
                x:Name="Axis"
                Grid.Row="1"
                Grid.Column="2"
                Margin="0,8">
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <TextBlock
                    Margin="8,0"
                    VerticalAlignment="Center"
                    FontSize="21"
                    Text="Axis"
                    TextAlignment="Center" />
                <TextBlock
                    x:Name="txtAxis"
                    Grid.Row="1"
                    Margin="8,0"
                    VerticalAlignment="Center"
                    FontSize="21"
                    Text="0"
                    TextAlignment="Center" />
            </Grid>


            <Grid
                x:Name="BC"
                Grid.Row="1"
                Grid.Column="3"
                Margin="0,8">
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <TextBlock
                    Margin="8,0"
                    VerticalAlignment="Center"
                    FontSize="21"
                    Text="BC"
                    TextAlignment="Center" />
                <TextBlock
                    x:Name="txtBC"
                    Grid.Row="1"
                    Margin="8,0"
                    VerticalAlignment="Center"
                    FontSize="21"
                    Text="0"
                    TextAlignment="Center" />
            </Grid>





            <Grid
                x:Name="Dia"
                Grid.Row="1"
                Grid.Column="4"
                Margin="0,8">
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <TextBlock
                    Margin="8,0"
                    VerticalAlignment="Center"
                    FontSize="21"
                    Text="Dia"
                    TextAlignment="Center" />
                <TextBlock
                    x:Name="TxtDia"
                    Grid.Row="1"
                    Margin="8,0"
                    VerticalAlignment="Center"
                    FontSize="21"
                    Text="0"
                    TextAlignment="Center" />
            </Grid>







            <Grid
                x:Name="Add"
                Grid.Row="1"
                Grid.Column="5"
                Margin="0,8">
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <TextBlock
                    Margin="8,0"
                    VerticalAlignment="Center"
                    FontSize="21"
                    Text="Add"
                    TextAlignment="Center" />
                <TextBlock
                    x:Name="txtAdd"
                    Grid.Row="1"
                    Margin="8,0"
                    VerticalAlignment="Center"
                    FontSize="21"
                    Text="0"
                    TextAlignment="Center" />
            </Grid>


            <!--  Replace the three separate ComboBoxes (cmbSph, cmbCyl, cmbPow) with this single one  -->
            <Grid
                Grid.Row="2"
                Grid.ColumnSpan="4"
                Margin="8,0">

                <ComboBox
                    x:Name="cmbPrescription"
                    Height="60"
                    VerticalAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    BorderThickness="1"
                    FontSize="21"
                    IsEditable="True"
                    IsReadOnly="False"
                    SelectionChanged="cmbPrescription_SelectionChanged"
                    Tag="الوصفة (Sph/Cyl/Pow)" />
            </Grid>


            <Grid
                Grid.Row="2"
                Grid.Column="4"
                Grid.ColumnSpan="2"
                Margin="8,0">
                <ComboBox
                    x:Name="cmbLensColor"
                    Grid.Column="0"
                    Grid.ColumnSpan="2"
                    Height="60"
                    BorderBrush="{DynamicResource SecundaryColor}"
                    BorderThickness="2"
                    FontSize="21"
                    IsEditable="True"
                    IsReadOnly="False"
                    Tag="اللون" />

            </Grid>

        </Grid>


        <Grid
            x:Name="ProductLayout"
            Grid.Row="1"
            Grid.RowSpan="3"
            Grid.ColumnSpan="8"
            Visibility="Collapsed">

            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
                <ColumnDefinition />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition />
                <RowDefinition />
            </Grid.RowDefinitions>

            <Grid
                Grid.Column="2"
                Grid.ColumnSpan="2"
                Margin="8,0">


                <TextBox
                    x:Name="txtProductBarcode"
                    Height="60"
                    VerticalAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    BorderThickness="1"
                    FontSize="21"
                    GotFocus="TextBox_GotFocus"
                    KeyDown="txtProductBarcode_KeyDown"
                    Tag="باركود" />

            </Grid>


            <Grid
                Grid.Row="1"
                Grid.Column="0"
                Margin="8,0">


                <TextBox
                    x:Name="txtProductQuantity"
                    Height="60"
                    VerticalAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    utils:NumericInputControl.IsNumericOnly="True"
                    BorderThickness="1"
                    FontSize="21"
                    GotFocus="TextBox_GotFocus"
                    Tag="الكمية" />
            </Grid>
            <Grid
                Grid.Row="1"
                Grid.Column="1"
                Margin="8,0">
                <TextBox
                    x:Name="txtProductPrice"
                    Height="60"
                    VerticalAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    utils:NumericInputControl.IsDecimalOnly="True"
                    BorderThickness="1"
                    FontSize="21"
                    GotFocus="TextBox_GotFocus"
                    Tag="السعر" />
            </Grid>
            <ComboBox
                x:Name="cmbProductName"
                Grid.Column="4"
                Grid.ColumnSpan="2"
                Margin="8,0"
                FontSize="21"
                IsEditable="True"
                IsReadOnly="False"
                SelectionChanged="cmbProductName_SelectionChanged"
                Tag="اسم المنتج" />



            <Grid
                Grid.Row="1"
                Grid.Column="2"
                Grid.ColumnSpan="4"
                Margin="8,0"
                HorizontalAlignment="Stretch">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition x:Name="ColColor" Width="*" />
                    <ColumnDefinition x:Name="ColExpire" Width="*" />
                </Grid.ColumnDefinitions>

                <!--  Product Color  -->
                <ComboBox
                    x:Name="cmbProductColor"
                    Grid.Column="0"
                    Height="60"
                    HorizontalAlignment="Stretch"
                    HorizontalContentAlignment="Stretch"
                    BorderBrush="{DynamicResource SecundaryColor}"
                    BorderThickness="2"
                    FontSize="21"
                    IsEditable="True"
                    IsReadOnly="False"
                    IsVisibleChanged="Control_VisibilityChanged"
                    Tag="اللون"
                    Visibility="Visible" />

                <!--  Expiration Date  -->
                <DatePicker
                    x:Name="DtpProductExpireOn"
                    Grid.Column="1"
                    Height="60"
                    Margin="8,0"
                    HorizontalAlignment="Stretch"
                    BorderBrush="{StaticResource PrimaryTextColor}"
                    BorderThickness="1"
                    FontSize="18"
                    IsVisibleChanged="Control_VisibilityChanged"
                    Visibility="Visible">
                    <DatePicker.Resources>
                        <Style TargetType="{x:Type DatePickerTextBox}">
                            <Setter Property="Control.Template">
                                <Setter.Value>
                                    <ControlTemplate>
                                        <TextBox
                                            x:Name="PART_TextBox"
                                            VerticalAlignment="Stretch"
                                            BorderThickness="0"
                                            Foreground="{StaticResource PrimaryTextColor}"
                                            Tag="تاريخ الصلاحية"
                                            Text="{Binding Path=SelectedDate, RelativeSource={RelativeSource AncestorType={x:Type DatePicker}}}" />
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </DatePicker.Resources>
                </DatePicker>
            </Grid>


        </Grid>


        <Border
            x:Name="btnAdd"
            Grid.Row="4"
            Grid.Column="0"
            MaxHeight="44"
            Margin="8,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnAdd_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White">
                اضافة
            </TextBlock>
        </Border>
        <Border
            x:Name="btnNewItem"
            Grid.Row="4"
            Grid.Column="1"
            MaxHeight="44"
            Margin="8,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnNewItem_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}">
                جديد
            </TextBlock>
        </Border>

        <ComboBox
            x:Name="cmbTreasury"
            Grid.Row="4"
            Grid.Column="2"
            Grid.ColumnSpan="2"
            Margin="8,0"
            FontSize="21"
            IsEditable="True"
            IsReadOnly="False"
            Tag="طريقة الدفع" />

        <Grid
            Grid.Row="4"
            Grid.Column="4"
            Grid.ColumnSpan="4"
            Margin="8,0">

            <TextBox
                x:Name="txtNotes"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                MaxLength="500"
                Tag="ملاحظات" />

        </Grid>
        <ListView
            x:Name="list"
            Grid.Row="5"
            Grid.RowSpan="4"
            Grid.ColumnSpan="10"
            Background="{DynamicResource PageColor}"
            BorderThickness="1"
            FontFamily="pack://application:,,,/Assets/#Cairo"
            ItemsSource="{Binding}"
            ScrollViewer.HorizontalScrollBarVisibility="Hidden"
            SizeChanged="list_SizeChanged">
            <ListView.BorderBrush>
                <SolidColorBrush Opacity="0.42" Color="Black" />
            </ListView.BorderBrush>

            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Style.Triggers>
                        <Trigger Property="Control.IsMouseOver" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="False" />
                                <Condition Property="IsMouseOver" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter Property="FontWeight" Value="Thin" />
                            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextColor}" />
                        </MultiTrigger>
                    </Style.Triggers>
                    <Setter Property="Control.Background" Value="{DynamicResource PageColor}" />
                    <Setter Property="Control.HorizontalContentAlignment" Value="Stretch" />
                </Style>
            </ListView.ItemContainerStyle>
            <ListView.View>
                <GridView AllowsColumnReorder="False">
                    <GridView.ColumnHeaderContainerStyle>
                        <Style BasedOn="{StaticResource ListViewHeader}" TargetType="{x:Type GridViewColumnHeader}">
                            <Setter Property="IsEnabled" Value="False" />
                            <Setter Property="Height" Value="60" />
                            <Style.Triggers>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="TextElement.Foreground" Value="Black" />
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </GridView.ColumnHeaderContainerStyle>
                    <GridViewColumn Width="Auto" Header="#">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Text="{Binding ., Converter={StaticResource IndexToNumberConverter}, ConverterParameter={x:Reference list}}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="الاسم">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding Name, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="اللون">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding ColorName, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="السعر">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding Price, FallbackValue='0', TargetNullValue='0'}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="الكمية">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding Quantity, FallbackValue='0', TargetNullValue='0'}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="تاريخ الصلاحية">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent"
                                    Text="{Binding Exp, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="الإجمالي">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Background="Transparent">
                                    <TextBlock.Text>
                                        <MultiBinding Converter="{StaticResource PriceMultiplierConverter}" StringFormat="{}{0:N3}">
                                            <Binding Path="Price" />
                                            <Binding Path="Quantity" />
                                        </MultiBinding>
                                    </TextBlock.Text>
                                </TextBlock>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="العمليات">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button
                                        Width="30"
                                        Height="30"
                                        Margin="5,0"
                                        Background="Transparent"
                                        BorderBrush="Transparent"
                                        Click="btnDeleteItem_Click"
                                        Cursor="Hand"
                                        ToolTip="حذف الصنف">
                                        <Path
                                            Width="16"
                                            Height="16"
                                            Data="{StaticResource Trash}"
                                            Fill="{StaticResource errorColor}"
                                            Stretch="Uniform" />
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
        </ListView>
        <Border
            x:Name="btnSave"
            Grid.Row="9"
            Grid.Column="0"
            MaxHeight="44"
            Margin="8,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnSave_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White">
                حفظ
            </TextBlock>
        </Border>

        <Border
            x:Name="btnNew"
            Grid.Row="9"
            Grid.Column="1"
            MaxHeight="44"
            Margin="8,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnNew_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}">
                فاتورة جديد
            </TextBlock>
        </Border>

        <Grid
            Grid.Row="9"
            Grid.Column="2"
            Grid.ColumnSpan="2"
            Margin="8,0">


            <TextBox
                x:Name="txtTotalPrice"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                IsReadOnly="True"
                Tag="اجمالي الفاتورة" />

        </Grid>

        <Grid
            Grid.Row="9"
            Grid.Column="4"
            Grid.ColumnSpan="2"
            Margin="8,0">


            <TextBox
                x:Name="txtPaidPrice"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                utils:NumericInputControl.IsDecimalOnly="True"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                Tag="القيمة المدفوعة"
                TextChanged="txtPaidPrice_TextChanged" />

        </Grid>
        <Grid
            Grid.Row="9"
            Grid.Column="6"
            Grid.ColumnSpan="2"
            Margin="8,0">


            <TextBox
                x:Name="txtRemaining"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                utils:NumericInputControl.IsDecimalOnly="True"
                BorderThickness="1"
                FontSize="21"
                IsReadOnly="True"
                Tag="القيمة المتبقية" />

        </Grid>


        <Border
            x:Name="btnAddReceipt"
            Grid.Row="9"
            Grid.Column="8"
            MaxHeight="44"
            Margin="8,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnAddReceipt_MouseLeftButtonDown"
            Visibility="Collapsed">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White">
                اضافة واصل
            </TextBlock>
        </Border>
    </Grid>
</Page>
