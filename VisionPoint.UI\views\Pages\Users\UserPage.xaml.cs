using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Media;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Helper;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.Users
{
    // ViewModel لعرض بيانات المستخدم مع الصلاحيات
    public class UserViewModel
    {
        public int Id { get; set; }
        public string UserName { get; set; }
        public string Name { get; set; }
        public decimal Balance { get; set; }
        public decimal Salary { get; set; }
        public string Roles { get; set; }
        public int? WarehouseId { get; set; }
        public string WarehouseName { get; set; }
        public bool AllowBalanceOverride { get; set; }
        public string AllowBalanceOverrideText => AllowBalanceOverride ? "نعم" : "لا";
        public User OriginalUser { get; set; }
    }

    public partial class UserPage : Page
    {
        private readonly UserService _userService;
        private readonly WarehouseService _warehouseService;
        private ObservableCollection<UserViewModel> _users;
        private List<Warehouse> _warehouses;
        private User _selectedUser;
        private UserViewModel _selectedUserViewModel;
        private bool _isBusy = false;

        public UserPage()
        {
            InitializeComponent();
            _userService = ServiceLocator.GetService<UserService>();
            _warehouseService = ServiceLocator.GetService<WarehouseService>();
            _users = new ObservableCollection<UserViewModel>();
            _warehouses = new List<Warehouse>();
        }

        private async void Page_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("بدء تحميل صفحة المستخدمين...");

                // التحقق من صلاحية الوصول لصفحة المستخدمين
                bool canAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("UserRole") ||
                               CurrentUser.HasRole("EditUserRole") || CurrentUser.HasRole("DeleteUserRole");
                if (!canAccess)
                {
                    ErrorBox.Show("لا تملك صلاحية الوصول لإدارة المستخدمين", "خطأ في الصلاحيات", true);
                    return;
                }

                // التحقق من وجود UserService
                if (_userService == null)
                {
                    ErrorBox.Show("خطأ في تهيئة خدمة المستخدمين", "خطأ في النظام", true);
                    return;
                }

                // إدارة ظهور الأزرار بناءً على الصلاحيات
                SetButtonVisibility();

                await LoadRoles();
                await LoadWarehouses();
                await LoadData();

                System.Diagnostics.Debug.WriteLine("تم تحميل صفحة المستخدمين بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في Page_Loaded: {ex.Message}");
                ErrorBox.Show($"حدث خطأ أثناء تحميل الصفحة: {ex.Message}", "خطأ في التحميل", true);
            }
        }

        private void SetButtonVisibility()
        {
            // فحص الصلاحيات
            bool canAdd = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("UserRole");
            bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditUserRole");
            bool canDelete = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("DeleteUserRole");

            // إدارة ظهور الأزرار
            if (btnSave != null)
                btnSave.Visibility = (canAdd || canEdit) ? Visibility.Visible : Visibility.Collapsed;

            if (btnNew != null)
                btnNew.Visibility = canAdd ? Visibility.Visible : Visibility.Collapsed;

            if (btnDelete != null)
                btnDelete.Visibility = canDelete ? Visibility.Visible : Visibility.Collapsed;

            // إدارة تفعيل الحقول بناءً على الصلاحيات
            bool canModifyFields = canAdd || canEdit;

            txtUserName.IsEnabled = canModifyFields;
            txtFullName.IsEnabled = canModifyFields;
            txtPassword.IsEnabled = canModifyFields;
            txtConfirmPassword.IsEnabled = canModifyFields;
            txtSalary.IsEnabled = canModifyFields;
            lstRoles.IsEnabled = canModifyFields;
            DropDownToggle.IsEnabled = canModifyFields;
            cmbWarehouse.IsEnabled = canModifyFields;

            // إضافة تلميح بصري للمستخدم عند عدم وجود صلاحيات
            if (!canModifyFields)
            {
                txtUserName.ToolTip = "لا تملك صلاحية تعديل بيانات المستخدمين";
                txtFullName.ToolTip = "لا تملك صلاحية تعديل بيانات المستخدمين";
                txtPassword.ToolTip = "لا تملك صلاحية تعديل بيانات المستخدمين";
                txtConfirmPassword.ToolTip = "لا تملك صلاحية تعديل بيانات المستخدمين";
                txtSalary.ToolTip = "لا تملك صلاحية تعديل بيانات المستخدمين";
                DropDownToggle.ToolTip = "لا تملك صلاحية تعديل صلاحيات المستخدمين";
                cmbWarehouse.ToolTip = "لا تملك صلاحية تعديل بيانات المستخدمين";
            }
            else
            {
                // إزالة التلميحات عند وجود الصلاحيات
                txtUserName.ToolTip = null;
                txtFullName.ToolTip = null;
                txtPassword.ToolTip = null;
                txtConfirmPassword.ToolTip = null;
                txtSalary.ToolTip = null;
                DropDownToggle.ToolTip = null;
                cmbWarehouse.ToolTip = null;
            }
        }

        // كلاس لعرض الأدوار بالعربية
        public class RoleDisplayItem
        {
            public string RoleName { get; set; }
            public string DisplayName { get; set; }

            public override string ToString()
            {
                return RoleName;
            }
        }

        private async Task LoadRoles()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("بدء تحميل الأدوار...");

                var roles = await _userService.GetAvailableRoles();
                System.Diagnostics.Debug.WriteLine($"تم جلب {roles?.Count ?? 0} دور من قاعدة البيانات");

                // التأكد من عدم وجود قيم فارغة في قائمة الصلاحيات
                if (roles != null)
                {
                    roles = roles.Where(r => !string.IsNullOrWhiteSpace(r)).ToList();
                }
                else
                {
                    roles = new List<string>();
                }

                if (roles.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("لا توجد أدوار، إضافة دور افتراضي");
                    // إذا لم تكن هناك صلاحيات متوفرة، نضيف صلاحية افتراضية
                    roles.Add("Admin");
                }

                // تحويل الأدوار إلى عناصر عرض مع الترجمة العربية
                var displayItems = roles.Select(r => new RoleDisplayItem
                {
                    RoleName = r,
                    DisplayName = RoleTranslations.GetArabicName(r)
                }).ToList();

                lstRoles.ItemsSource = displayItems;
                System.Diagnostics.Debug.WriteLine($"تم تحميل {displayItems.Count} دور بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في LoadRoles: {ex.Message}");
                ErrorBox.Show($"حدث خطأ أثناء تحميل الصلاحيات: {ex.Message}", "خطأ في التحميل", true);

                // إضافة صلاحية افتراضية في حالة الخطأ
                var defaultRoles = new List<RoleDisplayItem>
                {
                    new RoleDisplayItem
                    {
                        RoleName = "Admin",
                        DisplayName = RoleTranslations.GetArabicName("Admin")
                    }
                };
                lstRoles.ItemsSource = defaultRoles;
                System.Diagnostics.Debug.WriteLine("تم تحميل دور افتراضي بسبب الخطأ");
            }
        }

        private async Task LoadWarehouses()
        {
            try
            {
                _warehouses = await _warehouseService.GetAllWarehousesAsync();

                if (_warehouses != null && _warehouses.Any())
                {
                    // ربط المخازن بـ ComboBox
                    cmbWarehouse.ItemsSource = _warehouses;
                    cmbWarehouse.DisplayMemberPath = "Name";
                    cmbWarehouse.SelectedValuePath = "Id";

                    // تحديد المخزن الافتراضي بناءً على المستخدم الحالي

                    if (CurrentUser.WarehouseId.HasValue)
                    {
                        cmbWarehouse.SelectedValue = CurrentUser.WarehouseId.Value;
                    }
                    else
                    {
                        // إذا لم يتم العثور على مخزن افتراضي، اختر الأول
                        cmbWarehouse.SelectedIndex = 0;
                    }

                    // تطبيق منطق الصلاحيات لتغيير المخزن
                    // إذا لم يكن المستخدم مديراً أو لا يملك صلاحية تغيير المخزن، يتم تعطيل الكومبو
                    cmbWarehouse.IsEnabled = CurrentUser.CanChangeWarehouse;

                    System.Diagnostics.Debug.WriteLine($"تم تحميل {_warehouses.Count} مخزن بنجاح");
                }
                else
                {
                    ErrorBox.Show("لا توجد مخازن متاحة. يرجى إضافة مخزن أولاً.", "تحذير", false);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في LoadWarehouses: {ex.Message}");
                ErrorBox.Show($"حدث خطأ أثناء تحميل المخازن: {ex.Message}", "خطأ في التحميل", true);
            }
        }

        private async Task LoadData()
        {
            try
            {
                _isBusy = true;

                // استخدام النسخة الـ async لتجنب مشاكل التزامن
                var users = await _userService.GetAllUsersAsync();
                _users.Clear();

                if (users == null || users.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("لا توجد مستخدمين في النظام");
                    return;
                }

                foreach (var user in users)
                {
                    try
                    {
                        if (user == null)
                        {
                            System.Diagnostics.Debug.WriteLine("مستخدم فارغ تم تجاهله");
                            continue;
                        }

                        var userRoles = await _userService.GetUserRoles(user);

                        // التأكد من وجود أدوار للمستخدم
                        if (userRoles == null)
                        {
                            userRoles = new List<string>();
                        }

                        // ترجمة أسماء الأدوار إلى العربية
                        var arabicRoles = userRoles.Select(r => RoleTranslations.GetArabicName(r)).ToList();

                        // البحث عن اسم المخزن
                        string warehouseName = "غير محدد";
                        if (user.WarehouseId.HasValue && user.WarehouseId > 0 && _warehouses != null)
                        {
                            var warehouse = _warehouses.FirstOrDefault(w => w.Id == user.WarehouseId.Value);
                            warehouseName = warehouse?.Name ?? "غير محدد";
                        }

                        var viewModel = new UserViewModel
                        {
                            Id = user.Id,
                            UserName = user.UserName ?? "غير محدد",
                            Name = user.Name ?? "غير محدد",
                            Balance = user.Balance,
                            Salary = user.Salary,
                            Roles = arabicRoles.Any() ? string.Join(", ", arabicRoles) : "لا توجد صلاحيات",
                            WarehouseId = user.WarehouseId,
                            WarehouseName = warehouseName,
                            AllowBalanceOverride = user.AllowBalanceOverride,
                            OriginalUser = user
                        };
                        _users.Add(viewModel);
                    }
                    catch (Exception ex)
                    {
                        // لا نريد إيقاف عملية التحميل بسبب خطأ مع مستخدم واحد
                        System.Diagnostics.Debug.WriteLine($"خطأ في تحميل بيانات المستخدم {user?.UserName ?? "غير معروف"}: {ex.Message}");
                        ErrorBox.Show($"خطأ في تحميل بيانات المستخدم {user?.UserName ?? "غير معروف"}: {ex.Message}", "تحذير", false);
                    }
                }

                list.ItemsSource = _users;
                System.Diagnostics.Debug.WriteLine($"تم تحميل {_users.Count} مستخدم بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ عام في LoadData: {ex.Message}");
                ErrorBox.Show($"حدث خطأ أثناء تحميل بيانات المستخدمين: {ex.Message}", "خطأ في التحميل", true);
            }
            finally
            {
                _isBusy = false;
            }
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnSave.IsEnabled = false;
            btnNew.IsEnabled = false;
            btnDelete.IsEnabled = false;
            btnStatement.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnSave.IsEnabled = true;
            btnNew.IsEnabled = true;
            btnDelete.IsEnabled = true;
            btnStatement.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private async void btnSave_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (_isBusy) return;
            DisableAllButtons();
            try
            {

            // التحقق من الصلاحيات
            bool canAdd = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("UserRole");
            bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditUserRole");

            if (_selectedUser == null && !canAdd)
            {
                ErrorBox.Show("لا تملك صلاحية إضافة المستخدمين", "خطأ في الصلاحيات", true);
                return;
            }

            if (_selectedUser != null && !canEdit)
            {
                ErrorBox.Show("لا تملك صلاحية تعديل المستخدمين", "خطأ في الصلاحيات", true);
                return;
            }

            if (string.IsNullOrWhiteSpace(txtUserName.Text))
            {
                ErrorBox.Show("الرجاء إدخال اسم المستخدم", "بيانات ناقصة", false);
                txtUserName.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtFullName.Text))
            {
                ErrorBox.Show("الرجاء إدخال الاسم الكامل", "بيانات ناقصة", false);
                txtFullName.Focus();
                return;
            }

            if (_selectedUser == null && string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                ErrorBox.Show("الرجاء إدخال كلمة المرور", "بيانات ناقصة", false);
                txtPassword.Focus();
                return;
            }

            if (_selectedUser == null && txtPassword.Text != txtConfirmPassword.Text)
            {
                ErrorBox.Show("كلمة المرور وتأكيد كلمة المرور غير متطابقين", "بيانات غير صحيحة", false);
                txtConfirmPassword.Focus();
                return;
            }

            if (lstRoles.SelectedItems.Count == 0)
            {
                ErrorBox.Show("الرجاء اختيار صلاحية واحدة على الأقل", "بيانات ناقصة", false);
                lstRoles.Focus();
                return;
            }

            // التحقق من اختيار المخزن
            if (cmbWarehouse.SelectedValue == null)
            {
                ErrorBox.Show("الرجاء اختيار المخزن", "بيانات ناقصة", false);
                cmbWarehouse.Focus();
                return;
            }

            // التحقق من صحة قيمة المرتب
            decimal salaryValue = 0;
            if (!string.IsNullOrWhiteSpace(txtSalary.Text) && !decimal.TryParse(txtSalary.Text, out salaryValue))
            {
                ErrorBox.Show("الرجاء إدخال قيمة صحيحة للمرتب", "بيانات غير صحيحة", false);
                txtSalary.Focus();
                return;
            }

                _isBusy = true;

                List<string> selectedRoles = new List<string>();
                foreach (var item in lstRoles.SelectedItems)
                {
                    if (item is RoleDisplayItem roleItem)
                    {
                        selectedRoles.Add(roleItem.RoleName);
                    }
                }

                if (_selectedUser == null)
                {
                    // إضافة مستخدم جديد
                    decimal newUserSalary = 0;
                    decimal.TryParse(txtSalary.Text, out newUserSalary);

                    var newUser = new User
                    {
                        UserName = txtUserName.Text,
                        Name = txtFullName.Text,
                        Email = $"{txtUserName.Text}@visionpoint.com",
                        EmailConfirmed = true,
                        Salary = newUserSalary,
                        WarehouseId = (int)cmbWarehouse.SelectedValue,
                        AllowBalanceOverride = chkAllowBalanceOverride.IsChecked ?? false
                    };

                    var result = await _userService.CreateUser(newUser, txtPassword.Text, selectedRoles);

                    if (result.result.Succeeded)
                    {
                        DialogBox.Show("تم بنجاح", "تم إضافة المستخدم بنجاح");
                        ClearForm();
                        await LoadData();
                    }
                    else
                    {
                        string errors = string.Join("\n", result.result.Errors.Select(e => e.Description));
                        ErrorBox.Show($"حدث خطأ أثناء إضافة المستخدم:\n{errors}", "خطأ في الإضافة", true);
                    }
                }
                else
                {
                    // تحديث مستخدم موجود
                    if (_selectedUser != null)
                    {
                        decimal existingUserSalary = 0;
                        decimal.TryParse(txtSalary.Text, out existingUserSalary);

                        _selectedUser.UserName = txtUserName.Text;
                        _selectedUser.Name = txtFullName.Text;
                        _selectedUser.Salary = existingUserSalary;
                        _selectedUser.WarehouseId = (int)cmbWarehouse.SelectedValue;
                        _selectedUser.AllowBalanceOverride = chkAllowBalanceOverride.IsChecked ?? false;

                        var result = await _userService.UpdateUser(_selectedUser, selectedRoles);

                        if (result.Succeeded)
                        {
                            // إذا تم إدخال كلمة مرور جديدة، قم بتحديثها
                            if (!string.IsNullOrWhiteSpace(txtPassword.Text))
                            {
                                try
                                {
                                    // استخدام الطريقة الجديدة المباشرة
                                    var passwordResult = await _userService.AdminResetPassword(_selectedUser, txtPassword.Text);

                                    if (!passwordResult.Succeeded)
                                    {
                                        string errors = string.Join("\n", passwordResult.Errors.Select(e => e.Description));
                                        ErrorBox.Show($"تم تحديث المستخدم لكن حدث خطأ في تغيير كلمة المرور:\n{errors}", "تحذير", true);
                                    }
                                }
                                catch (Exception passwordEx)
                                {
                                    ErrorBox.Show($"تم تحديث المستخدم لكن حدث خطأ في تغيير كلمة المرور:\n{passwordEx.Message}", "تحذير", true);
                                }
                            }

                            DialogBox.Show("تم بنجاح", "تم تحديث المستخدم بنجاح");
                            ClearForm();
                            await LoadData();
                        }
                        else
                        {
                            string errors = string.Join("\n", result.Errors.Select(e => e.Description));
                            ErrorBox.Show($"حدث خطأ أثناء تحديث المستخدم:\n{errors}", "خطأ في التحديث", true);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ: {ex.Message}", "خطأ في النظام", true);
            }
            finally
            {
                _isBusy = false;
                EnableAllButtons();
            }
        }

        private void btnNew_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                ClearForm();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void ClearForm()
        {
            txtUserName.Clear();
            txtFullName.Clear();
            txtPassword.Clear();
            txtConfirmPassword.Clear();
            txtSalary.Text = "0";
            chkAllowBalanceOverride.IsChecked = false;
            _selectedUser = null;
            _selectedUserViewModel = null;
            lstRoles.SelectedItems.Clear();

            // إعادة تعيين المخزن للافتراضي
            if (cmbWarehouse.Items.Count > 0)
            {
                cmbWarehouse.SelectedIndex = 0;
            }
        }

        private async void DisplayUserDetails()
        {
            if (_selectedUserViewModel != null)
            {
                txtUserName.Text = _selectedUserViewModel.UserName;
                txtFullName.Text = _selectedUserViewModel.Name;
                txtSalary.Text = _selectedUserViewModel.Salary.ToString();
                chkAllowBalanceOverride.IsChecked = _selectedUserViewModel.AllowBalanceOverride;
                txtPassword.Clear(); // لا نعرض كلمة المرور
                txtConfirmPassword.Clear(); // لا نعرض تأكيد كلمة المرور

                // تحديد المخزن المحدد للمستخدم
                if (_selectedUserViewModel.WarehouseId.HasValue && _selectedUserViewModel.WarehouseId > 0)
                {
                    cmbWarehouse.SelectedValue = _selectedUserViewModel.WarehouseId.Value;
                }
                else
                {
                    cmbWarehouse.SelectedIndex = 0; // اختيار المخزن الأول كافتراضي
                }

                try
                {
                    // تعطيل الأحداث أثناء تحميل البيانات
                    _isBusy = true;

                    // تحديد الأدوار المحددة للمستخدم
                    var userRoles = await _userService.GetUserRoles(_selectedUserViewModel.OriginalUser);

                    // إلغاء تحديد جميع العناصر أولاً
                    lstRoles.SelectedItems.Clear();

                    // تفعيل جميع العناصر أولاً
                    for (int i = 0; i < lstRoles.Items.Count; i++)
                    {
                        ListBoxItem listBoxItem = (ListBoxItem)lstRoles.ItemContainerGenerator.ContainerFromIndex(i);
                        if (listBoxItem != null)
                        {
                            listBoxItem.IsEnabled = true;
                        }
                    }

                    // تحديد الأدوار التي يملكها المستخدم
                    foreach (var role in userRoles)
                    {
                        for (int i = 0; i < lstRoles.Items.Count; i++)
                        {
                            if (lstRoles.Items[i] is RoleDisplayItem roleItem && roleItem.RoleName == role)
                            {
                                lstRoles.SelectedItems.Add(lstRoles.Items[i]);
                            }
                        }
                    }

                    // تطبيق منطق Admin إذا كان محدد
                    bool adminSelected = lstRoles.SelectedItems.Cast<RoleDisplayItem>().Any(r => r.RoleName == "Admin");
                    if (adminSelected)
                    {
                        var adminRole = lstRoles.SelectedItems.Cast<RoleDisplayItem>().First(r => r.RoleName == "Admin");
                        lstRoles.SelectedItems.Clear();
                        lstRoles.SelectedItems.Add(adminRole);

                        // تعطيل باقي العناصر
                        for (int i = 0; i < lstRoles.Items.Count; i++)
                        {
                            if (lstRoles.Items[i] is RoleDisplayItem item && item.RoleName != "Admin")
                            {
                                ListBoxItem listBoxItem = (ListBoxItem)lstRoles.ItemContainerGenerator.ContainerFromIndex(i);
                                if (listBoxItem != null)
                                {
                                    listBoxItem.IsEnabled = false;
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"حدث خطأ أثناء تحميل صلاحيات المستخدم: {ex.Message}", "خطأ", true);
                }
                finally
                {
                    // تفعيل الأحداث مرة أخرى
                    _isBusy = false;
                }
            }
        }

        private async void btnDelete_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                await DeleteSelectedUser();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async Task DeleteSelectedUser()
        {
            if (_isBusy) return;

            // التحقق من صلاحية الحذف
            bool canDelete = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("DeleteUserRole");
            if (!canDelete)
            {
                ErrorBox.Show("لا تملك صلاحية حذف المستخدمين", "خطأ في الصلاحيات", true);
                return;
            }

            if (_selectedUserViewModel == null || _selectedUser == null)
            {
                ErrorBox.Show("الرجاء اختيار مستخدم للحذف", "تنبيه", false);
                return;
            }

            var result = DeleteBox.Show("هل انت متاكد من حذف المستخدم", _selectedUserViewModel.UserName);
            if (result == true)
            {
                try
                {
                    _isBusy = true;

                    var deleteResult = await _userService.DeleteUser(_selectedUser);

                    if (deleteResult.Succeeded)
                    {
                        _users.Remove(_selectedUserViewModel);
                        ClearForm();
                        DialogBox.Show("تم بنجاح", "تم حذف المستخدم بنجاح");
                    }
                    else
                    {
                        // التحقق مما إذا كانت هناك رسالة خطأ محددة

                            ErrorBox.Show(deleteResult.Message, "لا يمكن حذف المستخدم", true);

                    }
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"حدث خطأ: {ex.Message}", "خطأ في النظام", true);
                }
                finally
                {
                    _isBusy = false;
                }
            }
        }


        private void list_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (list.SelectedItem is UserViewModel selectedViewModel)
            {
                _selectedUserViewModel = selectedViewModel;
                _selectedUser = selectedViewModel.OriginalUser;
                DisplayUserDetails();
            }
        }

        private void btnStatement_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق من اختيار مستخدم
                if (_selectedUser == null)
                {
                    ErrorBox.Show("الرجاء اختيار مستخدم لعرض كشف حسابه", "تنبيه", false);
                    return;
                }

                // فتح نافذة كشف الحساب
                var statementWindow = new UserStatementWindow(_selectedUser);
                statementWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء فتح كشف الحساب: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (sender is ListView listView && listView.View is GridView gridView)
            {
                double workingWidth = listView.ActualWidth - SystemParameters.VerticalScrollBarWidth;

                // Define column percentages
                double col1 = 0.16;  // username
                double col2 = 0.16;  // full name
                double col3 = 0.18;  // role
                double col4 = 0.12;  // balance
                double col5 = 0.12;  // salary
                double col6 = 0.12;  // warehouse
                double col7 = 0.14;  // New column

                // Ensure the sum of percentages is 1 (or very close to it due to floating point arithmetic)
                // You can adjust these values slightly if the sum is not exactly 1.0
                // For example, if you want to be precise, you could calculate the last column's width
                // as 1.0 minus the sum of the others.
                // double col7 = 1.0 - (col1 + col2 + col3 + col4 + col5 + col6);


                // Apply widths to columns
                if (gridView.Columns.Count > 0)
                    gridView.Columns[0].Width = workingWidth * col1;
                if (gridView.Columns.Count > 1)
                    gridView.Columns[1].Width = workingWidth * col2;
                if (gridView.Columns.Count > 2)
                    gridView.Columns[2].Width = workingWidth * col3;
                if (gridView.Columns.Count > 3)
                    gridView.Columns[3].Width = workingWidth * col4;
                if (gridView.Columns.Count > 4)
                    gridView.Columns[4].Width = workingWidth * col5;
                if (gridView.Columns.Count > 5)
                    gridView.Columns[5].Width = workingWidth * col6;
                if (gridView.Columns.Count > 6) // For the new 7th column (index 6)
                    gridView.Columns[6].Width = workingWidth * col7;
            }
        }

        private void txtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtSearch.Text))
            {
                list.ItemsSource = _users;
                return;
            }

            var searchTerm = txtSearch.Text.ToLower();
            var filteredList = _users.Where(u =>
                u.UserName.ToLower().Contains(searchTerm) ||
                u.Name.ToLower().Contains(searchTerm) ||
                u.Roles.ToLower().Contains(searchTerm) ||
                u.Salary.ToString().Contains(searchTerm) ||
                u.WarehouseName.ToLower().Contains(searchTerm)
            ).ToList();

            list.ItemsSource = filteredList;
        }
        private void Border_PreviewMouseWheel(object sender, MouseWheelEventArgs e)
        {
            if (lstRoles == null) return;

            // Create a new MouseWheelEventArgs and raise it on the ListBox
            var args = new MouseWheelEventArgs(e.MouseDevice, e.Timestamp, e.Delta)
            {
                RoutedEvent = UIElement.MouseWheelEvent,
                Source = sender
            };
            lstRoles.RaiseEvent(args);

            e.Handled = true; // Optional: prevent further propagation
        }


        private void DropDownToggle_Click(object sender, RoutedEventArgs e)
        {
            if (sender is ToggleButton toggleButton)
            {
                // Set the Popup's IsOpen state based on the ToggleButton's IsChecked state.
                DropDownPopup.IsOpen = toggleButton.IsChecked.GetValueOrDefault();
            }
        }

        // ADD THIS NEW EVENT HANDLER
        private void DropDownPopup_Closed(object sender, EventArgs e)
        {
            // When the popup closes (either by clicking the button again,
            // clicking outside, or Alt+Tabbing away), ensure the ToggleButton
            // is visually unchecked to keep it in sync.
            DropDownToggle.IsChecked = false;
        }

        private void lstRoles_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // تجنب التنفيذ أثناء تحميل البيانات
            if (_isBusy) return;

            bool adminSelected = false;
            RoleDisplayItem adminRole = null;

            // البحث عن صلاحية Admin في القائمة المحددة
            foreach (var selectedItem in lstRoles.SelectedItems)
            {
                if (selectedItem is RoleDisplayItem roleItem && roleItem.RoleName == "Admin")
                {
                    adminSelected = true;
                    adminRole = roleItem;
                    break;
                }
            }

            // عند اختيار Admin، قم بتعطيل باقي الصلاحيات
            if (adminSelected)
            {
                // حفظ الحالة الحالية للأحداث لتجنب التنفيذ المتكرر
                _isBusy = true;

                // تحديد Admin فقط وإلغاء تحديد باقي الصلاحيات
                lstRoles.SelectedItems.Clear();
                lstRoles.SelectedItems.Add(adminRole);

                // تعطيل باقي العناصر
                for (int i = 0; i < lstRoles.Items.Count; i++)
                {
                    if (lstRoles.Items[i] is RoleDisplayItem item && item.RoleName != "Admin")
                    {
                        // نستخدم القائمة لتعطيل العناصر الأخرى
                        ListBoxItem listBoxItem = (ListBoxItem)lstRoles.ItemContainerGenerator.ContainerFromIndex(i);
                        if (listBoxItem != null)
                        {
                            listBoxItem.IsEnabled = false;
                        }
                    }
                }

                _isBusy = false;
            }
            else
            {
                // عند عدم اختيار Admin، قم بتفعيل جميع الصلاحيات
                // تفعيل جميع العناصر
                for (int i = 0; i < lstRoles.Items.Count; i++)
                {
                    ListBoxItem listBoxItem = (ListBoxItem)lstRoles.ItemContainerGenerator.ContainerFromIndex(i);
                    if (listBoxItem != null)
                    {
                        listBoxItem.IsEnabled = true;
                    }
                }

                // تطبيق منطق الاختيار التلقائي للأدوار المترابطة
                // نحتاج للتحقق من الأدوار المضافة والمحذوفة
                ApplyAutomaticRoleSelection(e.AddedItems, e.RemovedItems);
            }
        }

        /// <summary>
        /// تطبيق منطق الاختيار التلقائي للأدوار المترابطة
        /// عند اختيار التعديل أو الحذف، يتم اختيار العرض والإضافة تلقائياً
        /// </summary>
        private void ApplyAutomaticRoleSelection(System.Collections.IList addedItems, System.Collections.IList removedItems)
        {
            if (_isBusy) return;

            _isBusy = true;

            try
            {
                // قاموس الأدوار المترابطة - عند اختيار التعديل أو الحذف، يتم اختيار العرض والإضافة تلقائياً
                var roleRelationships = new Dictionary<string, string[]>
                {
                    // أدوار المبيعات
                    { "EditSalesRole", new[] { "SalesRole" } },
                    { "DeleteSalesRole", new[] { "SalesRole" } },

                    // أدوار المشتريات
                    { "EditPurchaseRole", new[] { "PurchaseRole" } },
                    { "DeletePurchaseRole", new[] { "PurchaseRole" } },

                    // أدوار المنتجات
                    { "EditProductRole", new[] { "ProductRole" } },
                    { "DeleteProductRole", new[] { "ProductRole" } },

                    // أدوار العدسات
                    { "EditLensRole", new[] { "LensRole" } },
                    { "DeleteLensRole", new[] { "LensRole" } },

                    // أدوار أنواع العدسات
                    { "EditLensCategoryRole", new[] { "LensCategoryRole" } },
                    { "DeleteLensCategoryRole", new[] { "LensCategoryRole" } },

                    
                    // أدوار المخازن
                    { "EditWarehouseRole", new[] { "WarehouseRole" } },
                    { "DeleteWarehouseRole", new[] { "WarehouseRole" } },

                    // أدوار الخدمات
                    { "EditServiceRole", new[] { "ServiceRole" } },
                    { "DeleteServiceRole", new[] { "ServiceRole" } },

                    // أدوار العملاء
                    { "EditClientRole", new[] { "ClientRole" } },
                    { "DeleteClientRole", new[] { "ClientRole" } },

                    // أدوار الإيصالات
                    { "EditReceiptRole", new[] { "ReceiptRole" } },
                    { "DeleteReceiptRole", new[] { "ReceiptRole" } },

                    // أدوار المصروفات
                    { "EditExpenseRole", new[] { "ExpenseRole" } },
                    { "DeleteExpenseRole", new[] { "ExpenseRole" } },

                    // أدوار طرق الدفع
                    { "EditTreasuryRole", new[] { "TreasuryRole" } },
                    { "DeleteTreasuryRole", new[] { "TreasuryRole" } },

                    // أدوار إدارة المستخدمين
                    { "EditUserRole", new[] { "UserRole" } },
                    { "DeleteUserRole", new[] { "UserRole" } },

                    // أدوار الإعدادات والنسخ الاحتياطي
                    { "BackupRole", new[] { "SettingsRole" } },
                    { "SystemConfigRole", new[] { "SettingsRole" } }
                };

                var rolesToAdd = new List<RoleDisplayItem>();

                // التحقق من الأدوار المضافة حديثاً
                if (addedItems != null)
                {
                    foreach (var addedItem in addedItems)
                    {
                        if (addedItem is RoleDisplayItem addedRole && roleRelationships.ContainsKey(addedRole.RoleName))
                        {
                            var requiredRoles = roleRelationships[addedRole.RoleName];

                            foreach (var requiredRoleName in requiredRoles)
                            {
                                // البحث عن الدور المطلوب في القائمة
                                var requiredRole = lstRoles.Items.Cast<RoleDisplayItem>()
                                    .FirstOrDefault(r => r.RoleName == requiredRoleName);

                                if (requiredRole != null && !lstRoles.SelectedItems.Contains(requiredRole))
                                {
                                    rolesToAdd.Add(requiredRole);
                                }
                            }
                        }
                    }
                }

                // إضافة الأدوار المطلوبة
                foreach (var roleToAdd in rolesToAdd)
                {
                    lstRoles.SelectedItems.Add(roleToAdd);
                }

                // التحقق من إزالة الأدوار الأساسية عند إزالة الأدوار المتقدمة
                if (removedItems != null)
                {
                    CheckAndRemoveDependentRoles(removedItems, roleRelationships);
                }
            }
            finally
            {
                _isBusy = false;
            }
        }

        /// <summary>
        /// التحقق من إزالة الأدوار التابعة عند إزالة الأدوار الأساسية
        /// </summary>
        private void CheckAndRemoveDependentRoles(System.Collections.IList removedItems, Dictionary<string, string[]> roleRelationships)
        {
            var rolesToRemove = new List<RoleDisplayItem>();

            foreach (var removedItem in removedItems)
            {
                if (removedItem is RoleDisplayItem removedRole)
                {
                    // البحث عن الأدوار التي تعتمد على الدور المحذوف
                    var dependentRoles = roleRelationships
                        .Where(kvp => kvp.Value.Contains(removedRole.RoleName))
                        .Select(kvp => kvp.Key);

                    foreach (var dependentRoleName in dependentRoles)
                    {
                        var dependentRole = lstRoles.SelectedItems.Cast<RoleDisplayItem>()
                            .FirstOrDefault(r => r.RoleName == dependentRoleName);

                        if (dependentRole != null)
                        {
                            rolesToRemove.Add(dependentRole);
                        }
                    }
                }
            }

            // إزالة الأدوار التابعة
            foreach (var roleToRemove in rolesToRemove)
            {
                lstRoles.SelectedItems.Remove(roleToRemove);
            }
        }
    }
}

