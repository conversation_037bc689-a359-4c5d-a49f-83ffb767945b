﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace VisionPoint.UI.Reports.client
{
    /// <summary>
    /// Interaction logic for ClientReportFooter.xaml
    /// </summary>
    public partial class ClientReportFooter : Page
    {
        public ClientReportFooter(string DebtTotal, string CreditorTotal, string FinalBalance, string Note)
        {
            InitializeComponent();
            txtDebtTotal.Text = ExtractNumber(DebtTotal);
            txtCreditorTotal.Text = ExtractNumber(CreditorTotal);
            txtFinalBalance.Text = ExtractNumber(FinalBalance);
            txtNote.Text = Note;
        }

        private string ExtractNumber(string input)
        {
            var match = System.Text.RegularExpressions.Regex.Match(input, @"[-+]?[0-9]*\.?[0-9]+");
            return match.Success ? match.Value : "0.000"; // Default to "0.000" if no number is found
        }

    }
}
