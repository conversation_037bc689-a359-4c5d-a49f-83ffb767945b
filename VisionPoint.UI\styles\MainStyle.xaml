﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:cc="clr-namespace:VisionPoint.UI.Converters">
    <cc:HalfValueConverter x:Key="HalfValueConverter" />


    <SolidColorBrush
        x:Key="SemiTransparentPrimaryColor"
        Opacity="0.4"
        Color="#134074" />
    <SolidColorBrush x:Key="TextBox.Static.Border" Color="#FFABAdB3" />
    <SolidColorBrush x:Key="TextBox.MouseOver.Border" Color="#134074" />
    <SolidColorBrush x:Key="TextBox.Focus.Border" Color="#134074" />
    <Style x:Key="UserTxt" TargetType="{x:Type TextBox}">
        <Setter Property="Background" Value="White" />
        <Setter Property="FontFamily" Value="El Messiri" />
        <Setter Property="BorderBrush" Value="{StaticResource TextBox.Static.Border}" />
        <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="KeyboardNavigation.TabNavigation" Value="None" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="AllowDrop" Value="true" />
        <Setter Property="ScrollViewer.PanningMode" Value="VerticalFirst" />
        <Setter Property="Stylus.IsFlicksEnabled" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TextBox}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="CW-Inline-input-example">
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="textBlock" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[3].(TranslateTransform.X)">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="-6.667">
                                    <EasingDoubleKeyFrame.EasingFunction>
                                        <QuinticEase EasingMode="EaseInOut" />
                                    </EasingDoubleKeyFrame.EasingFunction>
                                </EasingDoubleKeyFrame>
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="textBlock" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[3].(TranslateTransform.Y)">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="-30.733">
                                    <EasingDoubleKeyFrame.EasingFunction>
                                        <QuinticEase EasingMode="EaseInOut" />
                                    </EasingDoubleKeyFrame.EasingFunction>
                                </EasingDoubleKeyFrame>
                            </DoubleAnimationUsingKeyFrames>
                            <ColorAnimationUsingKeyFrames Storyboard.TargetName="textBlock" Storyboard.TargetProperty="(TextElement.Foreground).(SolidColorBrush.Color)">
                                <EasingColorKeyFrame KeyTime="0:0:0.6" Value="#134074" />
                            </ColorAnimationUsingKeyFrames>
                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="textBlock" Storyboard.TargetProperty="(TextElement.FontWeight)">
                                <DiscreteObjectKeyFrame KeyTime="0:0:0.3">
                                    <DiscreteObjectKeyFrame.Value>
                                        <FontWeight>Bold</FontWeight>
                                    </DiscreteObjectKeyFrame.Value>
                                </DiscreteObjectKeyFrame>
                            </ObjectAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>

                    <Grid>

                        <Border
                            x:Name="border"
                            Grid.Row="1"
                            Background="Transparent"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            SnapsToDevicePixels="True">
                            <Border.BorderBrush>
                                <SolidColorBrush Opacity="0.4" Color="#134074" />
                            </Border.BorderBrush>

                            <ScrollViewer
                                x:Name="PART_ContentHost"
                                Margin="12,0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Focusable="false"
                                HorizontalScrollBarVisibility="Hidden"
                                VerticalScrollBarVisibility="Hidden" />
                        </Border>

                        <TextBlock
                            x:Name="textBlock"
                            Margin="12,0"
                            Padding="8,0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="{TemplateBinding FontSize}"
                            Foreground="Gray"
                            RenderTransformOrigin="0.5,0.5"
                            Text="{TemplateBinding Tag}">
                            <TextBlock.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Offset="0.5" Color="Transparent" />
                                    <GradientStop Offset="0.5" Color="#ffffff" />
                                    <GradientStop Offset="1" Color="#ffffff" />
                                </LinearGradientBrush>
                            </TextBlock.Background>
                            <TextBlock.RenderTransform>
                                <TransformGroup>
                                    <ScaleTransform />
                                    <SkewTransform />
                                    <RotateTransform />
                                    <TranslateTransform />
                                </TransformGroup>
                            </TextBlock.RenderTransform>
                        </TextBlock>

                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="border" Property="Opacity" Value="1" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource TextBox.MouseOver.Border}" />
                        </Trigger>

                        <Trigger Property="IsKeyboardFocused" Value="true">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource TextBox.Focus.Border}" />

                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource CW-Inline-input-example}" />
                            </Trigger.EnterActions>
                            <!--
                          <Trigger.ExitActions>
                              // In case you wanted to do something cool on exit too..
                          </Trigger.ExitActions>
                            -->
                        </Trigger>
                        <DataTrigger Binding="{Binding Text, RelativeSource={RelativeSource Self}}" Value="">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource TextBox.Focus.Border}" />
                            <DataTrigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource CW-Inline-input-example}" />
                            </DataTrigger.EnterActions>
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsInactiveSelectionHighlightEnabled" Value="true" />
                    <Condition Property="IsSelectionActive" Value="false" />
                </MultiTrigger.Conditions>
                <Setter Property="SelectionBrush" Value="{DynamicResource {x:Static SystemColors.InactiveSelectionHighlightBrushKey}}" />
            </MultiTrigger>
        </Style.Triggers>
    </Style>


    <Style x:Key="txtWasDefault" TargetType="{x:Type TextBox}">
        <Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.WindowBrushKey}}" />
        <Setter Property="FontFamily" Value="El Messiri" />
        <Setter Property="BorderBrush" Value="{StaticResource TextBox.Static.Border}" />
        <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="KeyboardNavigation.TabNavigation" Value="None" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="AllowDrop" Value="true" />
        <Setter Property="ScrollViewer.PanningMode" Value="VerticalFirst" />
        <Setter Property="Stylus.IsFlicksEnabled" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TextBox}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="CW-Inline-input-example">
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="textBlock" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[3].(TranslateTransform.X)">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="-6.667">
                                    <EasingDoubleKeyFrame.EasingFunction>
                                        <QuinticEase EasingMode="EaseInOut" />
                                    </EasingDoubleKeyFrame.EasingFunction>
                                </EasingDoubleKeyFrame>
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="textBlock" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[3].(TranslateTransform.Y)">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="-30.733">
                                    <EasingDoubleKeyFrame.EasingFunction>
                                        <QuinticEase EasingMode="EaseInOut" />
                                    </EasingDoubleKeyFrame.EasingFunction>
                                </EasingDoubleKeyFrame>
                            </DoubleAnimationUsingKeyFrames>
                            <ColorAnimationUsingKeyFrames Storyboard.TargetName="textBlock" Storyboard.TargetProperty="(TextElement.Foreground).(SolidColorBrush.Color)">
                                <EasingColorKeyFrame KeyTime="0:0:0.6" Value="#134074" />
                            </ColorAnimationUsingKeyFrames>
                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="textBlock" Storyboard.TargetProperty="(TextElement.FontWeight)">
                                <DiscreteObjectKeyFrame KeyTime="0:0:0.3">
                                    <DiscreteObjectKeyFrame.Value>
                                        <FontWeight>Bold</FontWeight>
                                    </DiscreteObjectKeyFrame.Value>
                                </DiscreteObjectKeyFrame>
                            </ObjectAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>

                    <Grid>

                        <Border
                            x:Name="border"
                            Grid.Row="1"
                            Background="Transparent"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            SnapsToDevicePixels="True">
                            <Border.BorderBrush>
                                <SolidColorBrush Opacity="0.4" Color="#134074" />
                            </Border.BorderBrush>

                            <ScrollViewer
                                x:Name="PART_ContentHost"
                                Margin="12,0"
                                HorizontalAlignment="left"
                                VerticalAlignment="Center"
                                Focusable="false"
                                HorizontalScrollBarVisibility="Hidden"
                                VerticalScrollBarVisibility="Hidden" />
                        </Border>

                        <TextBlock
                            x:Name="textBlock"
                            Margin="12,0"
                            Padding="8,0"
                            HorizontalAlignment="left"
                            VerticalAlignment="Center"
                            Panel.ZIndex="100"
                            FontSize="{TemplateBinding FontSize}"
                            Foreground="Gray"
                            IsEnabled="{TemplateBinding IsEnabled}"
                            RenderTransformOrigin="0.5,0.5"
                            Text="{TemplateBinding Tag}">
                            <TextBlock.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Offset="0.5" Color="Transparent" />
                                    <GradientStop Offset="0.5" Color="#F0F8FF" />
                                    <GradientStop Offset="1" Color="#F0F8FF" />
                                </LinearGradientBrush>
                            </TextBlock.Background>
                            <TextBlock.RenderTransform>
                                <TransformGroup>
                                    <ScaleTransform />
                                    <SkewTransform />
                                    <RotateTransform />
                                    <TranslateTransform />
                                </TransformGroup>
                            </TextBlock.RenderTransform>
                        </TextBlock>

                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="border" Property="Opacity" Value="0.56" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource TextBox.MouseOver.Border}" />
                        </Trigger>

                        <Trigger Property="IsKeyboardFocused" Value="true">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource TextBox.Focus.Border}" />

                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource CW-Inline-input-example}" />
                            </Trigger.EnterActions>
                            <!--
                            <Trigger.ExitActions>
                                // In case you wanted to do something cool on exit too..
                            </Trigger.ExitActions>
                            -->
                        </Trigger>
                        <DataTrigger Binding="{Binding Text, RelativeSource={RelativeSource Self}}" Value="">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource TextBox.Focus.Border}" />
                            <DataTrigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource CW-Inline-input-example}" />
                            </DataTrigger.EnterActions>
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsInactiveSelectionHighlightEnabled" Value="true" />
                    <Condition Property="IsSelectionActive" Value="false" />
                </MultiTrigger.Conditions>
                <Setter Property="SelectionBrush" Value="{DynamicResource {x:Static SystemColors.InactiveSelectionHighlightBrushKey}}" />
            </MultiTrigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="FloatingLabelToggleButton" TargetType="ToggleButton">
        <Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.WindowBrushKey}}" />
        <Setter Property="FontFamily" Value="El Messiri" />
        <Setter Property="BorderBrush" Value="#134074" />
        <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Padding" Value="12,6" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToggleButton">
                    <Grid>
                        <Border
                            x:Name="Border"
                            Grid.ColumnSpan="2"
                            Background="Transparent"
                            BorderBrush="{StaticResource SecundaryColor}"
                            BorderThickness="1"
                            CornerRadius="8" />
                        <ContentPresenter
                            x:Name="content"
                            Margin="12,0"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            Content="{TemplateBinding Content}" />

                        <!--  Floating Label  -->
                        <TextBlock
                            x:Name="textBlock"
                            Margin="12,0"
                            Padding="8,0"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            Panel.ZIndex="99"
                            FontSize="{TemplateBinding FontSize}"
                            Foreground="#134074"
                            RenderTransformOrigin="0.5,0.5"
                            Text="{TemplateBinding Tag}">
                            <TextBlock.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Offset="0.5" Color="Transparent" />
                                    <GradientStop Offset="0.5" Color="#F0F8FF" />
                                    <GradientStop Offset="1" Color="#F0F8FF" />
                                </LinearGradientBrush>
                            </TextBlock.Background>
                            <!--  Apply Static Transform as if animation had occurred  -->
                            <TextBlock.RenderTransform>
                                <TransformGroup>
                                    <ScaleTransform />
                                    <SkewTransform />
                                    <RotateTransform />
                                    <TranslateTransform X="-6.667" Y="-30.733" />
                                </TransformGroup>
                            </TextBlock.RenderTransform>
                            <TextBlock.FontWeight>Bold</TextBlock.FontWeight>
                        </TextBlock>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>


    <Style x:Key="txtDatePick" TargetType="{x:Type TextBox}">
        <Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.WindowBrushKey}}" />
        <Setter Property="FontFamily" Value="El Messiri" />
        <Setter Property="BorderBrush" Value="{StaticResource TextBox.Static.Border}" />
        <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="KeyboardNavigation.TabNavigation" Value="None" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="AllowDrop" Value="true" />
        <Setter Property="ScrollViewer.PanningMode" Value="VerticalFirst" />
        <Setter Property="Stylus.IsFlicksEnabled" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TextBox}">


                    <Grid>

                        <Border
                            x:Name="border"
                            Grid.Row="1"
                            Background="Transparent"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            SnapsToDevicePixels="True">
                            <Border.BorderBrush>
                                <SolidColorBrush Opacity="0.4" Color="#134074" />
                            </Border.BorderBrush>

                            <ScrollViewer
                                x:Name="PART_ContentHost"
                                Margin="12,0"
                                Padding="0,12"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Center"
                                Focusable="false"
                                HorizontalScrollBarVisibility="Hidden"
                                VerticalScrollBarVisibility="Hidden" />

                        </Border>

                        <TextBlock
                            x:Name="textBlock"
                            Margin="6,-20,12,0"
                            HorizontalAlignment="left"
                            VerticalAlignment="Top"
                            Panel.ZIndex="10"
                            FontSize="{TemplateBinding FontSize}"
                            FontWeight="Black"
                            Foreground="{StaticResource PrimaryColor}"
                            RenderTransformOrigin="0.5,0.5"
                            Text="{TemplateBinding Tag}">
                            <TextBlock.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Offset="0.5" Color="Transparent" />
                                    <GradientStop Offset="0.5" Color="#F0F8FF" />
                                    <GradientStop Offset="1" Color="#F0F8FF" />
                                </LinearGradientBrush>
                            </TextBlock.Background>
                            <TextBlock.RenderTransform>
                                <TransformGroup>
                                    <ScaleTransform />
                                    <SkewTransform />
                                    <RotateTransform />
                                    <TranslateTransform />
                                </TransformGroup>
                            </TextBlock.RenderTransform>
                        </TextBlock>

                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="border" Property="Opacity" Value="1" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource TextBox.MouseOver.Border}" />
                        </Trigger>

                        <Trigger Property="IsKeyboardFocused" Value="true">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource TextBox.Focus.Border}" />
                        </Trigger>
                        <DataTrigger Binding="{Binding Text, RelativeSource={RelativeSource Self}}" Value="">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource TextBox.Focus.Border}" />
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsInactiveSelectionHighlightEnabled" Value="true" />
                    <Condition Property="IsSelectionActive" Value="false" />
                </MultiTrigger.Conditions>
                <Setter Property="SelectionBrush" Value="{DynamicResource {x:Static SystemColors.InactiveSelectionHighlightBrushKey}}" />
            </MultiTrigger>
        </Style.Triggers>
    </Style>

    <Style TargetType="{x:Type TextBox}">
        <Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.WindowBrushKey}}" />
        <Setter Property="FontFamily" Value="El Messiri" />
        <Setter Property="BorderBrush" Value="{StaticResource TextBox.Static.Border}" />
        <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="KeyboardNavigation.TabNavigation" Value="None" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="AllowDrop" Value="true" />
        <Setter Property="ScrollViewer.PanningMode" Value="VerticalFirst" />
        <Setter Property="Stylus.IsFlicksEnabled" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TextBox}">


                    <Grid>

                        <Border
                            x:Name="border"
                            Grid.Row="1"
                            Background="Transparent"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            SnapsToDevicePixels="True">
                            <Border.BorderBrush>
                                <SolidColorBrush Opacity="0.4" Color="#134074" />
                            </Border.BorderBrush>

                            <ScrollViewer
                                x:Name="PART_ContentHost"
                                Margin="12,0"
                                Padding="0,16"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Center"
                                Focusable="false"
                                HorizontalScrollBarVisibility="Hidden"
                                VerticalScrollBarVisibility="Hidden" />


                        </Border>

                        <TextBlock
                            x:Name="textBlock"
                            Margin="12,-20,12,0"
                            Padding="8,0"
                            HorizontalAlignment="left"
                            VerticalAlignment="Top"
                            Panel.ZIndex="10"
                            FontSize="{TemplateBinding FontSize}"
                            FontWeight="Black"
                            Foreground="{StaticResource PrimaryColor}"
                            RenderTransformOrigin="0.5,0.5"
                            Text="{TemplateBinding Tag}">
                            <TextBlock.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Offset="0.5" Color="Transparent" />
                                    <GradientStop Offset="0.5" Color="Transparent" />
                                    <GradientStop Offset="1" Color="Transparent" />
                                </LinearGradientBrush>
                            </TextBlock.Background>
                            <TextBlock.RenderTransform>
                                <TransformGroup>
                                    <ScaleTransform />
                                    <SkewTransform />
                                    <RotateTransform />
                                    <TranslateTransform />
                                </TransformGroup>
                            </TextBlock.RenderTransform>
                        </TextBlock>

                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="border" Property="Opacity" Value="1" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource TextBox.MouseOver.Border}" />
                        </Trigger>

                        <Trigger Property="IsKeyboardFocused" Value="true">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource TextBox.Focus.Border}" />
                        </Trigger>
                        <DataTrigger Binding="{Binding Text, RelativeSource={RelativeSource Self}}" Value="">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource TextBox.Focus.Border}" />
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsInactiveSelectionHighlightEnabled" Value="true" />
                    <Condition Property="IsSelectionActive" Value="false" />
                </MultiTrigger.Conditions>
                <Setter Property="SelectionBrush" Value="{DynamicResource {x:Static SystemColors.InactiveSelectionHighlightBrushKey}}" />
            </MultiTrigger>
        </Style.Triggers>
    </Style>





    <Style x:Key="CircleCheckboxFL" TargetType="CheckBox">
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Content" Value="" />
        <Setter Property="FontWeight" Value="Black" />
        <Setter Property="Foreground" Value="{StaticResource PrimaryColor}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type CheckBox}">
                    <StackPanel
                        Margin="2"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        FlowDirection="{TemplateBinding FlowDirection}"
                        Orientation="Horizontal">
                        <!--  Checkbox Circle  -->
                        <Grid Width="20" Height="20">
                            <!--  Outer Circle  -->
                            <Ellipse
                                x:Name="E_Outline"
                                Width="20"
                                Height="20"
                                Stroke="#6A6E71"
                                StrokeThickness="1.5" />

                            <!--  Filling Circle  -->
                            <Ellipse
                                x:Name="E_Filling"
                                Width="16"
                                Height="16"
                                Margin="1.5"
                                Stroke="#0A0C0D"
                                StrokeThickness="1.5">
                                <Ellipse.Fill>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Offset="0" Color="White" />
                                        <GradientStop Offset="0.2" Color="White" />
                                        <GradientStop Offset="1" Color="White" />
                                    </LinearGradientBrush>
                                </Ellipse.Fill>
                            </Ellipse>

                            <!--  Bloom Effect  -->
                            <Ellipse
                                x:Name="E_Bloom"
                                Width="16"
                                Height="16"
                                Margin="2.5"
                                Opacity="1"
                                Stroke="#134074"
                                StrokeThickness="1"
                                Visibility="Hidden">
                                <Ellipse.Effect>
                                    <BlurEffect Radius="3" />
                                </Ellipse.Effect>
                            </Ellipse>

                            <!--  Flare Effect  -->
                            <Ellipse
                                x:Name="E_Flare"
                                Width="16"
                                Height="16"
                                Margin="3.5"
                                StrokeThickness="0.7">
                                <Ellipse.OpacityMask>
                                    <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.5,1">
                                        <GradientStop Offset="0.402" Color="#00000000" />
                                        <GradientStop Offset="0" Color="White" />
                                        <GradientStop Offset="0.076" Color="#A9A9A9A9" />
                                        <GradientStop Offset="0.275" Color="#35353535" />
                                    </LinearGradientBrush>
                                </Ellipse.OpacityMask>
                                <Ellipse.Stroke>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                        <GradientStop Offset="0" Color="#EE858C8F" />
                                        <GradientStop Offset="0.4" Color="#EE858C8F" />
                                        <GradientStop Offset="0.5" Color="#858C8F" />
                                        <GradientStop Offset="0.6" Color="#EE858C8F" />
                                        <GradientStop Offset="1" Color="#EE858C8F" />
                                    </LinearGradientBrush>
                                </Ellipse.Stroke>
                            </Ellipse>

                            <!--  Small center ellipse for spacing or visual  -->
                            <Ellipse
                                x:Name="E_MinMargin"
                                Width="2"
                                Height="2"
                                Margin="9" />
                        </Grid>

                        <!--  Text next to checkbox  -->
                        <ContentPresenter
                            x:Name="content"
                            Margin="6,0,0,0"
                            VerticalAlignment="Center"
                            RecognizesAccessKey="True" />
                    </StackPanel>

                    <!--  Triggers for visual state  -->
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="E_Filling" Property="Fill">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Offset="0" Color="#134074" />
                                        <GradientStop Offset="0.2" Color="#134074" />
                                        <GradientStop Offset="1" Color="#FFFDEACA" />
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="E_Flare" Property="Stroke">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                        <GradientStop Offset="0" Color="#5F9CE3" />
                                        <GradientStop Offset="0.4" Color="#EEFEFCDD" />
                                        <GradientStop Offset="0.5" Color="#FEFCDD" />
                                        <GradientStop Offset="0.6" Color="#EEFEFCDD" />
                                        <GradientStop Offset="1" Color="#5F9CE3" />
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="E_Bloom" Property="Visibility" Value="Visible" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type DataGridCell}">
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextColor}" />
        <Setter Property="FontWeight" Value="Black" />
        <Setter Property="FontSize" Value="16" />

        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DataGridCell}">
                    <Grid Background="{TemplateBinding Background}">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="{TemplateBinding FontSize}"
                            FontWeight="{TemplateBinding FontWeight}"
                            Foreground="{StaticResource PrimaryColor}"
                            Text="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=Content.Text}"
                            TextWrapping="Wrap" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type DataGridColumnHeader}">
        <Setter Property="HorizontalContentAlignment" Value="Center" />
    </Style>



    <Style TargetType="{x:Type PasswordBox}">
        <Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.WindowBrushKey}}" />
        <Setter Property="BorderBrush" Value="{StaticResource TextBox.Static.Border}" />
        <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="KeyboardNavigation.TabNavigation" Value="None" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="AllowDrop" Value="true" />
        <Setter Property="ScrollViewer.PanningMode" Value="VerticalFirst" />
        <Setter Property="Stylus.IsFlicksEnabled" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type PasswordBox}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="CW-Inline-input-example">
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="textBlock" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[3].(TranslateTransform.X)">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="-6.667">
                                    <EasingDoubleKeyFrame.EasingFunction>
                                        <QuinticEase EasingMode="EaseInOut" />
                                    </EasingDoubleKeyFrame.EasingFunction>
                                </EasingDoubleKeyFrame>
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="textBlock" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[3].(TranslateTransform.Y)">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="-30.733">
                                    <EasingDoubleKeyFrame.EasingFunction>
                                        <QuinticEase EasingMode="EaseInOut" />
                                    </EasingDoubleKeyFrame.EasingFunction>
                                </EasingDoubleKeyFrame>
                            </DoubleAnimationUsingKeyFrames>
                            <ColorAnimationUsingKeyFrames Storyboard.TargetName="textBlock" Storyboard.TargetProperty="(TextElement.Foreground).(SolidColorBrush.Color)">
                                <EasingColorKeyFrame KeyTime="0:0:0.6" Value="#134074" />
                            </ColorAnimationUsingKeyFrames>
                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="textBlock" Storyboard.TargetProperty="(TextElement.FontWeight)">
                                <DiscreteObjectKeyFrame KeyTime="0:0:0.3">
                                    <DiscreteObjectKeyFrame.Value>
                                        <FontWeight>Bold</FontWeight>
                                    </DiscreteObjectKeyFrame.Value>
                                </DiscreteObjectKeyFrame>
                            </ObjectAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>

                    <Grid>

                        <Border
                            x:Name="border"
                            Grid.Row="1"
                            Background="{TemplateBinding Background}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            SnapsToDevicePixels="True">
                            <Border.BorderBrush>
                                <SolidColorBrush Opacity="0.4" Color="#134074" />
                            </Border.BorderBrush>
                            <ScrollViewer
                                x:Name="PART_ContentHost"
                                Margin="12,0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FlowDirection="LeftToRight"
                                Focusable="false"
                                HorizontalScrollBarVisibility="Hidden"
                                VerticalScrollBarVisibility="Hidden" />
                        </Border>

                        <TextBlock
                            x:Name="textBlock"
                            Margin="12,0"
                            Padding="8,0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Foreground="Gray"
                            RenderTransformOrigin="0.5,0.5"
                            Text="{TemplateBinding Tag}">
                            <TextBlock.Background>
                                <LinearGradientBrush StartPoint="0,0.5" EndPoint="0,1">
                                    <GradientStop Offset="1" Color="#ffffff" />
                                    <GradientStop Offset="0.5" Color="#ffffff" />
                                    <GradientStop Offset="0.5" Color="Transparent" />
                                    <GradientStop Offset="0" Color="Transparent" />
                                </LinearGradientBrush>
                            </TextBlock.Background>
                            <TextBlock.RenderTransform>
                                <TransformGroup>
                                    <ScaleTransform />
                                    <SkewTransform />
                                    <RotateTransform />
                                    <TranslateTransform />
                                </TransformGroup>
                            </TextBlock.RenderTransform>
                        </TextBlock>

                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="border" Property="Opacity" Value="0.56" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource TextBox.MouseOver.Border}" />
                        </Trigger>

                        <Trigger Property="IsKeyboardFocused" Value="true">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource TextBox.Focus.Border}" />

                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource CW-Inline-input-example}" />
                            </Trigger.EnterActions>
                            <!--
                            <Trigger.ExitActions>
                                // In case you wanted to do something cool on exit too..
                            </Trigger.ExitActions>
                            -->
                        </Trigger>
                        <DataTrigger Binding="{Binding Password, RelativeSource={RelativeSource Self}}" Value="">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource TextBox.Focus.Border}" />
                            <DataTrigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource CW-Inline-input-example}" />
                            </DataTrigger.EnterActions>
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsInactiveSelectionHighlightEnabled" Value="true" />
                    <Condition Property="IsSelectionActive" Value="false" />
                </MultiTrigger.Conditions>
                <Setter Property="SelectionBrush" Value="{DynamicResource {x:Static SystemColors.InactiveSelectionHighlightBrushKey}}" />
            </MultiTrigger>
        </Style.Triggers>
    </Style>


    <Style
        x:Key="DatePickerCalendarStyle"
        BasedOn="{StaticResource {x:Type Calendar}}"
        TargetType="{x:Type Calendar}" />

    <!--  The template for the button that displays the calendar.  -->
    <Style x:Key="DropDownButtonStyle" TargetType="Button">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Grid
                        Width="24"
                        Height="24"
                        Margin="0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Stretch"
                        Background="#11FFFFFF"
                        FlowDirection="RightToLeft">
                        <Path
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Data="{DynamicResource calender}"
                            RenderTransformOrigin="0.5,0.5"
                            Stretch="Fill"
                            Stroke="{DynamicResource SecundaryColor}"
                            StrokeThickness="2" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="hiddenDropDownButtonStyle" TargetType="Button">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Grid
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Stretch"
                        Background="#11FFFFFF"
                        FlowDirection="RightToLeft" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type DatePicker}">
        <Setter Property="Foreground" Value="#FF333333" />
        <Setter Property="IsTodayHighlighted" Value="True" />
        <Setter Property="SelectedDateFormat" Value="Short" />
        <Setter Property="Padding" Value="2" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <!--  Set CalendarStyle to DatePickerCalendarStyle.  -->
        <Setter Property="CalendarStyle" Value="{DynamicResource DatePickerCalendarStyle}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DatePicker}">
                    <Border
                        Padding="{TemplateBinding Padding}"
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Stretch"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="8"
                        IsEnabled="{TemplateBinding IsEnabled}">
                        <Border.BorderBrush>
                            <SolidColorBrush Color="#134074" />
                        </Border.BorderBrush>
                        <Grid
                            x:Name="PART_Root"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Button
                                x:Name="PART_Button"
                                Grid.ColumnSpan="3"
                                Margin="3,0,3,0"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Stretch"
                                Panel.ZIndex="9"
                                Background="Transparent"
                                Cursor="Hand"
                                Focusable="False"
                                Foreground="{TemplateBinding Foreground}"
                                Opacity="0"
                                Style="{StaticResource hiddenDropDownButtonStyle}" />

                            <Button
                                Grid.Row="0"
                                Grid.Column="1"
                                Margin="3,0,3,0"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Stretch"
                                Focusable="False"
                                Foreground="{TemplateBinding Foreground}"
                                Opacity="0.6"
                                Style="{StaticResource DropDownButtonStyle}" />
                            <DatePickerTextBox
                                x:Name="PART_TextBox"
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="6,0,0,0"
                                HorizontalContentAlignment="Stretch"
                                VerticalContentAlignment="Stretch"
                                Focusable="False"
                                Foreground="{TemplateBinding Foreground}"
                                IsReadOnly="True" />
                            <Grid
                                x:Name="PART_DisabledVisual"
                                Grid.Row="0"
                                Grid.Column="0"
                                Grid.ColumnSpan="2"
                                IsHitTestVisible="False"
                                Opacity="0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <Rectangle
                                    Grid.Row="0"
                                    Grid.Column="0"
                                    Fill="#A5FFFFFF"
                                    RadiusX="1"
                                    RadiusY="1" />
                                <Rectangle
                                    Grid.Row="0"
                                    Grid.Column="1"
                                    Width="24"
                                    Height="24"
                                    Margin="3,0,3,0"
                                    Fill="#A5FFFFFF"
                                    RadiusX="1"
                                    RadiusY="1" />
                                <Popup
                                    x:Name="PART_Popup"
                                    AllowsTransparency="True"
                                    Placement="Bottom"
                                    PlacementTarget="{Binding ElementName=PART_TextBox}"
                                    StaysOpen="False" />
                            </Grid>
                        </Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <DoubleAnimation
                                            Storyboard.TargetName="PART_DisabledVisual"
                                            Storyboard.TargetProperty="Opacity"
                                            To="1"
                                            Duration="0" />
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>


    <Style x:Key="DisabledDatePicker" TargetType="{x:Type DatePicker}">
        <Setter Property="Foreground" Value="#FF333333" />
        <Setter Property="IsTodayHighlighted" Value="True" />
        <Setter Property="SelectedDateFormat" Value="Short" />
        <Setter Property="Padding" Value="2" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <!--  Set CalendarStyle to DatePickerCalendarStyle.  -->
        <Setter Property="CalendarStyle" Value="{DynamicResource DatePickerCalendarStyle}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type DatePicker}">
                    <Border
                        Padding="{TemplateBinding Padding}"
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Stretch"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="8">
                        <Border.BorderBrush>
                            <SolidColorBrush Color="#134074" />
                        </Border.BorderBrush>
                        <Grid
                            x:Name="PART_Root"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <Button
                                Grid.Row="0"
                                Grid.Column="1"
                                Margin="3,0,3,0"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Stretch"
                                Focusable="False"
                                Foreground="{TemplateBinding Foreground}"
                                Opacity="0.6"
                                Style="{StaticResource DropDownButtonStyle}" />
                            <DatePickerTextBox
                                x:Name="PART_TextBox"
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="6,0,0,0"
                                HorizontalContentAlignment="Stretch"
                                VerticalContentAlignment="Stretch"
                                Focusable="False"
                                Foreground="{TemplateBinding Foreground}"
                                IsReadOnly="True" />
                            <Grid
                                x:Name="PART_DisabledVisual"
                                Grid.Row="0"
                                Grid.Column="0"
                                Grid.ColumnSpan="2"
                                IsHitTestVisible="False"
                                Opacity="0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <Rectangle
                                    Grid.Row="0"
                                    Grid.Column="0"
                                    Fill="#A5FFFFFF"
                                    RadiusX="1"
                                    RadiusY="1" />
                                <Rectangle
                                    Grid.Row="0"
                                    Grid.Column="1"
                                    Width="24"
                                    Height="24"
                                    Margin="3,0,3,0"
                                    Fill="#A5FFFFFF"
                                    RadiusX="1"
                                    RadiusY="1" />
                                <Popup
                                    x:Name="PART_Popup"
                                    AllowsTransparency="True"
                                    Placement="Bottom"
                                    PlacementTarget="{Binding ElementName=PART_TextBox}"
                                    StaysOpen="False" />
                            </Grid>
                        </Grid>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <DoubleAnimation
                                            Storyboard.TargetName="PART_DisabledVisual"
                                            Storyboard.TargetProperty="Opacity"
                                            To="1"
                                            Duration="0" />
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>





    <!--  DatePicker  -->


    <ControlTemplate x:Key="ComboBoxToggleButton" TargetType="{x:Type ToggleButton}">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition />
                <ColumnDefinition Width="28" />
            </Grid.ColumnDefinitions>
            <Border
                x:Name="Border"
                Grid.ColumnSpan="2"
                Background="Transparent"
                BorderThickness="0"
                CornerRadius="8" />

            <Path
                x:Name="Arrow"
                Grid.Column="1"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Data="M1 1L6.00081 5.58L11 1"
                Stroke="{StaticResource SecundaryColor}"
                StrokeThickness="2" />
        </Grid>
    </ControlTemplate>

    <ControlTemplate x:Key="ComboBoxTextBox" TargetType="{x:Type TextBox}">
        <Border
            x:Name="PART_ContentHost"
            Background="{TemplateBinding Background}"
            Focusable="False" />
    </ControlTemplate>

    <ControlTemplate x:Key="partTextBox" TargetType="{x:Type DatePickerTextBox}">
        <Border
            x:Name="PART_ContentHost"
            Background="{TemplateBinding Background}"
            Focusable="False" />
    </ControlTemplate>

    <!--  Combobox  -->
    <Style x:Key="{x:Type ComboBox}" TargetType="{x:Type ComboBox}">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="OverridesDefaultStyle" Value="true" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.CanContentScroll" Value="true" />
        <Setter Property="MaxHeight" Value="60" />
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextColor}" />
        <Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.WindowBrushKey}}" />
        <Setter Property="BorderBrush" Value="{StaticResource TextBox.Static.Border}" />
        <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="KeyboardNavigation.TabNavigation" Value="None" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="AllowDrop" Value="true" />
        <Setter Property="ScrollViewer.PanningMode" Value="VerticalFirst" />
        <Setter Property="Stylus.IsFlicksEnabled" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ComboBox}">
                    <Grid>

                        <Border
                            x:Name="Border"
                            Background="Transparent"
                            BorderBrush="{StaticResource SecundaryColor}"
                            BorderThickness="1"
                            CornerRadius="8" />
                        <TextBlock
                            x:Name="textBlock"
                            Margin="12,-20,12,0"
                            Padding="8,0"
                            HorizontalAlignment="left"
                            VerticalAlignment="Top"
                            Panel.ZIndex="10"
                            FontSize="21"
                            FontWeight="Black"
                            Foreground="{StaticResource PrimaryColor}"
                            RenderTransformOrigin="0.5,0.5"
                            Text="{TemplateBinding Tag}">
                            <TextBlock.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Offset="0.5" Color="Transparent" />
                                    <GradientStop Offset="0.5" Color="Transparent" />
                                    <GradientStop Offset="1" Color="Transparent" />
                                </LinearGradientBrush>
                            </TextBlock.Background>
                            <TextBlock.RenderTransform>
                                <TransformGroup>
                                    <ScaleTransform />
                                    <SkewTransform />
                                    <RotateTransform />
                                    <TranslateTransform />
                                </TransformGroup>
                            </TextBlock.RenderTransform>
                        </TextBlock>
                        <ToggleButton
                            Name="ToggleButton"
                            Grid.ColumnSpan="2"
                            ClickMode="Press"
                            Focusable="false"
                            IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                            Template="{StaticResource ComboBoxToggleButton}" />
                        <ContentPresenter
                            Name="ContentSite"
                            Margin="16,3"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            Content="{TemplateBinding SelectionBoxItem}"
                            ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                            ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                            IsHitTestVisible="False" />
                        <TextBox
                            x:Name="PART_EditableTextBox"
                            Margin="16,3"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            Background="Transparent"
                            FlowDirection="LeftToRight"
                            Focusable="True"
                            FontSize="{TemplateBinding FontSize}"
                            Foreground="{StaticResource PrimaryTextColor}"
                            IsReadOnly="{TemplateBinding IsReadOnly}"
                            SelectionBrush="{StaticResource PrimaryColor}"
                            Style="{x:Null}"
                            Template="{StaticResource ComboBoxTextBox}"
                            Visibility="Hidden" />
                        <Popup
                            Name="Popup"
                            AllowsTransparency="True"
                            Focusable="False"
                            IsOpen="{TemplateBinding IsDropDownOpen}"
                            Placement="Bottom"
                            PopupAnimation="Slide">

                            <Grid
                                Name="DropDown"
                                MinWidth="{TemplateBinding ActualWidth}"
                                MaxHeight="{TemplateBinding MaxDropDownHeight}"
                                SnapsToDevicePixels="True">
                                <Border
                                    x:Name="DropDownBorder"
                                    Background="{StaticResource SecondaryPageColor}"
                                    BorderBrush="{StaticResource SecundaryColor}"
                                    BorderThickness="1.5"
                                    CornerRadius="12" />
                                <ScrollViewer Margin="4,6,4,6" SnapsToDevicePixels="True">
                                    <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained" />
                                </ScrollViewer>
                            </Grid>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>

                        <Trigger Property="HasItems" Value="false">
                            <Setter TargetName="DropDownBorder" Property="MinHeight" Value="95" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{StaticResource PrimaryTextColor}" />
                        </Trigger>
                        <Trigger Property="IsGrouping" Value="true">
                            <Setter Property="ScrollViewer.CanContentScroll" Value="false" />
                        </Trigger>
                        <Trigger SourceName="Popup" Property="Popup.AllowsTransparency" Value="true">
                            <Setter TargetName="DropDownBorder" Property="CornerRadius" Value="0" />
                            <Setter TargetName="DropDownBorder" Property="Margin" Value="0,0,0,0" />
                        </Trigger>
                        <Trigger Property="IsEditable" Value="true">
                            <Setter Property="IsTabStop" Value="false" />
                            <Setter TargetName="PART_EditableTextBox" Property="Visibility" Value="Visible" />
                            <Setter TargetName="ContentSite" Property="Visibility" Value="Hidden" />
                        </Trigger>

                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ToggleButton" Property="Opacity" Value="0.2" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers />
    </Style>

    <Style x:Key="ComboBoxFloatingLabelAlwaysUpStyle" TargetType="{x:Type ComboBox}">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="OverridesDefaultStyle" Value="true" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.CanContentScroll" Value="true" />
        <Setter Property="MaxHeight" Value="60" />
        <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}" />
        <Setter Property="Background" Value="{DynamicResource {x:Static SystemColors.WindowBrushKey}}" />
        <Setter Property="BorderBrush" Value="{StaticResource TextBox.Static.Border}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="KeyboardNavigation.TabNavigation" Value="None" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="AllowDrop" Value="true" />
        <Setter Property="ScrollViewer.PanningMode" Value="VerticalFirst" />
        <Setter Property="Stylus.IsFlicksEnabled" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ComboBox}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition Width="48" />
                        </Grid.ColumnDefinitions>

                        <Border
                            x:Name="Border"
                            Grid.ColumnSpan="2"
                            Background="Transparent"
                            BorderBrush="{StaticResource SecundaryColor}"
                            BorderThickness="1"
                            CornerRadius="8" />

                        <!--  Always "Up" TextBlock  -->
                        <TextBlock
                            x:Name="textBlock"
                            Margin="12,0"
                            Padding="8,0"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            Panel.ZIndex="99"
                            FontSize="{TemplateBinding FontSize}"
                            Foreground="#134074"
                            RenderTransformOrigin="0.5,0.5"
                            Text="{TemplateBinding Tag}">
                            <TextBlock.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Offset="0.5" Color="Transparent" />
                                    <GradientStop Offset="0.5" Color="#F0F8FF" />
                                    <GradientStop Offset="1" Color="#F0F8FF" />
                                </LinearGradientBrush>
                            </TextBlock.Background>
                            <!--  Apply Static Transform as if animation had occurred  -->
                            <TextBlock.RenderTransform>
                                <TransformGroup>
                                    <ScaleTransform />
                                    <SkewTransform />
                                    <RotateTransform />
                                    <TranslateTransform X="-6.667" Y="-30.733" />
                                </TransformGroup>
                            </TextBlock.RenderTransform>
                            <TextBlock.FontWeight>Bold</TextBlock.FontWeight>
                        </TextBlock>

                        <ToggleButton
                            Name="ToggleButton"
                            Grid.ColumnSpan="2"
                            ClickMode="Press"
                            Focusable="false"
                            IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                            Template="{StaticResource ComboBoxToggleButton}" />

                        <ContentPresenter
                            Name="ContentSite"
                            Margin="16,3"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            Content="{TemplateBinding SelectionBoxItem}"
                            ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                            ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                            IsHitTestVisible="False" />

                        <TextBox
                            x:Name="PART_EditableTextBox"
                            Margin="16,3"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            Background="Transparent"
                            FlowDirection="LeftToRight"
                            Focusable="True"
                            FontSize="{TemplateBinding FontSize}"
                            Foreground="{StaticResource PrimaryTextColor}"
                            IsReadOnly="{TemplateBinding IsReadOnly}"
                            SelectionBrush="{StaticResource PrimaryColor}"
                            Style="{x:Null}"
                            Template="{StaticResource ComboBoxTextBox}"
                            Visibility="Hidden" />

                        <Popup
                            Name="Popup"
                            AllowsTransparency="True"
                            Focusable="False"
                            IsOpen="{TemplateBinding IsDropDownOpen}"
                            Placement="Bottom"
                            PopupAnimation="Slide">

                            <Grid
                                Name="DropDown"
                                MinWidth="{TemplateBinding ActualWidth}"
                                MaxHeight="{TemplateBinding MaxDropDownHeight}"
                                SnapsToDevicePixels="True">
                                <Border
                                    x:Name="DropDownBorder"
                                    Background="{StaticResource SecondaryPageColor}"
                                    BorderBrush="{StaticResource SecundaryColor}"
                                    BorderThickness="1.5"
                                    CornerRadius="12" />
                                <ScrollViewer Margin="4,6,4,6" SnapsToDevicePixels="True">
                                    <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained" />
                                </ScrollViewer>
                            </Grid>
                        </Popup>
                    </Grid>

                    <!--  Remove triggers related to animation  -->
                    <ControlTemplate.Triggers>
                        <Trigger Property="HasItems" Value="false">
                            <Setter TargetName="DropDownBorder" Property="MinHeight" Value="95" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{StaticResource PrimaryTextColor}" />
                        </Trigger>
                        <Trigger Property="IsGrouping" Value="true">
                            <Setter Property="ScrollViewer.CanContentScroll" Value="false" />
                        </Trigger>
                        <Trigger SourceName="Popup" Property="Popup.AllowsTransparency" Value="true">
                            <Setter TargetName="DropDownBorder" Property="CornerRadius" Value="0" />
                            <Setter TargetName="DropDownBorder" Property="Margin" Value="0,0,0,0" />
                        </Trigger>
                        <Trigger Property="IsEditable" Value="true">
                            <Setter Property="IsTabStop" Value="false" />
                            <Setter TargetName="PART_EditableTextBox" Property="Visibility" Value="Visible" />
                            <Setter TargetName="ContentSite" Property="Visibility" Value="Hidden" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ToggleButton" Property="Opacity" Value="0.2" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>


    <Style x:Key="BorderComboBox" TargetType="{x:Type ComboBox}">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="FlowDirection" Value="RightToLeft" />
        <Setter Property="OverridesDefaultStyle" Value="true" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.CanContentScroll" Value="true" />
        <Setter Property="MaxHeight" Value="60" />
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextColor}" />
        <Setter Property="Template">
            <Setter.Value>

                <ControlTemplate TargetType="{x:Type ComboBox}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="CW-Inline-input-example">
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="textBlock" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[3].(TranslateTransform.X)">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="-6.667">
                                    <EasingDoubleKeyFrame.EasingFunction>
                                        <QuinticEase EasingMode="EaseInOut" />
                                    </EasingDoubleKeyFrame.EasingFunction>
                                </EasingDoubleKeyFrame>
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="textBlock" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[3].(TranslateTransform.Y)">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="-30.733">
                                    <EasingDoubleKeyFrame.EasingFunction>
                                        <QuinticEase EasingMode="EaseInOut" />
                                    </EasingDoubleKeyFrame.EasingFunction>
                                </EasingDoubleKeyFrame>
                            </DoubleAnimationUsingKeyFrames>
                            <ColorAnimationUsingKeyFrames Storyboard.TargetName="textBlock" Storyboard.TargetProperty="(TextElement.Foreground).(SolidColorBrush.Color)">
                                <EasingColorKeyFrame KeyTime="0:0:0.6" Value="#134074" />
                            </ColorAnimationUsingKeyFrames>
                            <ObjectAnimationUsingKeyFrames Storyboard.TargetName="textBlock" Storyboard.TargetProperty="(TextElement.FontWeight)">
                                <DiscreteObjectKeyFrame KeyTime="0:0:0.3">
                                    <DiscreteObjectKeyFrame.Value>
                                        <FontWeight>Bold</FontWeight>
                                    </DiscreteObjectKeyFrame.Value>
                                </DiscreteObjectKeyFrame>
                            </ObjectAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>

                    <Grid>
                        <TextBlock
                            x:Name="textBlock"
                            Margin="12,0"
                            Padding="8,0"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            Foreground="Gray"
                            RenderTransformOrigin="0.5,0.5"
                            Text="{TemplateBinding Tag}">
                            <TextBlock.Background>
                                <LinearGradientBrush StartPoint="0,0.5" EndPoint="0,1">
                                    <GradientStop Offset="1" Color="#ffffff" />
                                    <GradientStop Offset="0.5" Color="#ffffff" />
                                    <GradientStop Offset="0.5" Color="Transparent" />
                                    <GradientStop Offset="0" Color="Transparent" />
                                </LinearGradientBrush>
                            </TextBlock.Background>
                            <TextBlock.RenderTransform>
                                <TransformGroup>
                                    <ScaleTransform />
                                    <SkewTransform />
                                    <RotateTransform />
                                    <TranslateTransform />
                                </TransformGroup>
                            </TextBlock.RenderTransform>
                        </TextBlock>


                        <ToggleButton
                            Name="ToggleButton"
                            Grid.ColumnSpan="10"
                            Width="auto"
                            BorderThickness="0"
                            ClickMode="Press"
                            FlowDirection="RightToLeft"
                            Focusable="True"
                            IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                            Opacity="0"
                            Template="{StaticResource ComboBoxToggleButton}" />
                        <ContentPresenter
                            Name="ContentSite"
                            Margin="16,3"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            Content="{TemplateBinding SelectionBoxItem}"
                            ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                            ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                            FlowDirection="LeftToRight"
                            IsHitTestVisible="False" />
                        <TextBox
                            x:Name="PART_EditableTextBox"
                            Margin="12,0"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            Background="Transparent"
                            BorderThickness="0"
                            Focusable="True"
                            FontSize="{TemplateBinding FontSize}"
                            Foreground="{StaticResource PrimaryTextColor}"
                            IsReadOnly="{TemplateBinding IsReadOnly}"
                            SelectionBrush="Transparent"
                            Style="{x:Null}"
                            Template="{StaticResource ComboBoxTextBox}"
                            Visibility="Hidden" />
                        <Popup
                            Name="Popup"
                            AllowsTransparency="True"
                            FlowDirection="RightToLeft"
                            Focusable="False"
                            IsOpen="{TemplateBinding IsDropDownOpen}"
                            Placement="Bottom"
                            PopupAnimation="Slide">

                            <Grid
                                Name="DropDown"
                                MinWidth="{TemplateBinding ActualWidth}"
                                MaxHeight="{TemplateBinding MaxDropDownHeight}"
                                SnapsToDevicePixels="True">
                                <Border
                                    x:Name="DropDownBorder"
                                    Background="{StaticResource SecondaryPageColor}"
                                    BorderBrush="{StaticResource SecundaryColor}"
                                    BorderThickness="1.5"
                                    CornerRadius="12" />
                                <ScrollViewer Margin="4,6,4,6" SnapsToDevicePixels="True">
                                    <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained" />
                                </ScrollViewer>
                            </Grid>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsKeyboardFocused" Value="true">

                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource CW-Inline-input-example}" />
                            </Trigger.EnterActions>
                            <!--
                            <Trigger.ExitActions>
                                // In case you wanted to do something cool on exit too..
                            </Trigger.ExitActions>
                            -->
                        </Trigger>
                        <DataTrigger Binding="{Binding Text, RelativeSource={RelativeSource Self}}" Value="">
                            <DataTrigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource CW-Inline-input-example}" />
                            </DataTrigger.EnterActions>
                        </DataTrigger>
                        <Trigger Property="HasItems" Value="false">
                            <Setter TargetName="DropDownBorder" Property="MinHeight" Value="95" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{StaticResource PrimaryTextColor}" />
                        </Trigger>
                        <Trigger Property="IsGrouping" Value="true">
                            <Setter Property="ScrollViewer.CanContentScroll" Value="false" />
                        </Trigger>
                        <Trigger SourceName="Popup" Property="Popup.AllowsTransparency" Value="true">
                            <Setter TargetName="DropDownBorder" Property="CornerRadius" Value="0" />
                            <Setter TargetName="DropDownBorder" Property="Margin" Value="0,0,0,0" />
                        </Trigger>
                        <Trigger Property="IsEditable" Value="true">
                            <Setter Property="IsTabStop" Value="false" />
                            <Setter TargetName="PART_EditableTextBox" Property="Visibility" Value="Visible" />
                            <Setter TargetName="ContentSite" Property="Visibility" Value="Hidden" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers />
    </Style>



    <!--  SimpleStyles: ComboBoxItem  -->
    <Style x:Key="{x:Type ComboBoxItem}" TargetType="{x:Type ComboBoxItem}">
        <Setter Property="SnapsToDevicePixels" Value="true" />

        <Setter Property="Foreground" Value="{StaticResource PrimaryTextColor}" />
        <Setter Property="Background" Value="{StaticResource SecondaryPageColor}" />
        <Setter Property="OverridesDefaultStyle" Value="true" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ComboBoxItem}">
                    <Border
                        Name="Border"
                        Padding="12"
                        CornerRadius="8"
                        SnapsToDevicePixels="true">
                        <ContentPresenter />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsHighlighted" Value="true">
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource opacitybackgroundHighlight}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{StaticResource SecondaryPageColor}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Combobox  -->



    <!--  btn  -->
    <Style x:Key="noHover" TargetType="{x:Type Button}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Grid Background="{TemplateBinding Background}">
                        <ContentPresenter />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <!--  btn  -->

    <!--  Radio btn  -->
    <Style x:Key="{x:Type RadioButton}" TargetType="{x:Type RadioButton}">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="OverridesDefaultStyle" Value="true" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type RadioButton}">
                    <BulletDecorator Background="Transparent">
                        <BulletDecorator.Bullet>
                            <Grid Width="24" Height="24">
                                <Ellipse x:Name="Border">
                                    <Ellipse.Fill>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                            <LinearGradientBrush.GradientStops>
                                                <GradientStopCollection>
                                                    <GradientStop Offset="0.0" Color="#727370" />
                                                    <GradientStop Offset="1.0" Color="#727370" />
                                                </GradientStopCollection>
                                            </LinearGradientBrush.GradientStops>
                                        </LinearGradientBrush>
                                    </Ellipse.Fill>
                                    <Ellipse.Stroke>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                            <LinearGradientBrush.GradientStops>
                                                <GradientStop Offset="0.0" Color="#727370" />
                                                <GradientStop Offset="1.0" Color="#727370" />
                                            </LinearGradientBrush.GradientStops>
                                        </LinearGradientBrush>
                                    </Ellipse.Stroke>
                                </Ellipse>
                                <Ellipse x:Name="CheckMark" Visibility="Collapsed">
                                    <Ellipse.Fill>
                                        <SolidColorBrush Color="#134074" />
                                    </Ellipse.Fill>
                                </Ellipse>

                            </Grid>
                        </BulletDecorator.Bullet>
                        <ContentPresenter
                            Margin="4,0,0,0"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            RecognizesAccessKey="True" />
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="MouseOver">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="Border" Storyboard.TargetProperty="(Shape.Fill).                     (GradientBrush.GradientStops)[1].(GradientStop.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="#134074" />
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Pressed">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="Border" Storyboard.TargetProperty="(Shape.Fill).                     (GradientBrush.GradientStops)[1].(GradientStop.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="#134074" />
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="Border" Storyboard.TargetProperty="(Shape.Fill).                     (GradientBrush.GradientStops)[1].(GradientStop.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="#40000000" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="Border" Storyboard.TargetProperty="(Shape.Stroke).                     (GradientBrush.GradientStops)[1].(GradientStop.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="#40000000" />
                                        </ColorAnimationUsingKeyFrames>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="Border" Storyboard.TargetProperty="(Shape.Stroke).                     (GradientBrush.GradientStops)[0].(GradientStop.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="#40000000" />
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="CheckStates">
                                <VisualState x:Name="Checked">
                                    <Storyboard>
                                        <ObjectAnimationUsingKeyFrames Storyboard.TargetName="CheckMark" Storyboard.TargetProperty="(UIElement.Visibility)">
                                            <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Visible}" />
                                        </ObjectAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                                <VisualState x:Name="Unchecked" />
                                <VisualState x:Name="Indeterminate" />
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                    </BulletDecorator>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <!--  Radio btn  -->
    <!--  horListStyle  -->
    <Style x:Key="ButtonStyle" TargetType="{x:Type Button}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border
                        x:Name="border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="4"
                        SnapsToDevicePixels="true">
                        <ContentPresenter
                            x:Name="contentPresenter"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            Focusable="False"
                            RecognizesAccessKey="True"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsDefaulted" Value="true">
                            <Setter TargetName="border" Property="BorderBrush" Value="{DynamicResource {x:Static SystemColors.HighlightBrushKey}}" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryColor}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryColor}" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryColor}" />
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryColor}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter TargetName="border" Property="Background" Value="#FFEEEEEE" />
                            <Setter TargetName="border" Property="BorderBrush" Value="#FFAAAAAA" />
                            <Setter TargetName="contentPresenter" Property="TextElement.Foreground" Value="#FF888888" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="FontWeight" Value="SemiBold" />
        <Setter Property="FontSize" Value="14" />
    </Style>


    <Style x:Key="ListContentStyle" TargetType="{x:Type ListViewItem}">
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListViewItem">
                    <Border
                        x:Name="Border"
                        BorderThickness="0"
                        CornerRadius="500">
                        <ContentPresenter />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsSelected" Value="true">
                            <Setter TargetName="Border" Property="Background" Value="Transparent" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="ListContentStyleTrans" TargetType="{x:Type ListViewItem}">
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListViewItem">
                    <Border
                        x:Name="Border"
                        BorderThickness="0"
                        CornerRadius="45">
                        <ContentPresenter />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsSelected" Value="true">
                            <Setter TargetName="Border" Property="Background" Value="Transparent" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="BorderListView" TargetType="Border">
        <Setter Property="CornerRadius" Value="500" />

    </Style>

    <Style x:Key="ListViewHeader" TargetType="{x:Type GridViewColumnHeader}">
        <Setter Property="IsEnabled" Value="False" />
        <Setter Property="Height" Value="60" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Background" Value="#ffffff" />
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="TextElement.Foreground" Value="Black" />
            </Trigger>
        </Style.Triggers>
    </Style>
    <!--  horListStyle  -->



    <!--  Start: Pop-up Button Style  -->
    <Style x:Key="PopupButtonStyle" TargetType="{x:Type Button}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="Foreground" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border
                        x:Name="border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        SnapsToDevicePixels="true">
                        <ContentPresenter
                            x:Name="contentPresenter"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                            Focusable="False"
                            RecognizesAccessKey="True"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="Transparent" />
                            <Setter Property="BorderBrush" Value="Transparent" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="Transparent" />
                            <Setter Property="BorderBrush" Value="Transparent" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <!--  End: Pop-up Button Style  -->

    <!--  LsitView  -->

    <Style TargetType="ListViewItem">
        <Style.Resources>
            <SolidColorBrush x:Key="HighlightBrushKey" Color="Transparent" />
            <SolidColorBrush
                x:Key="HighlightMouseOverBrushKey"
                Opacity="0.3"
                Color="{Binding Source={StaticResource opacitybackgroundHighlight}, Path=Color}" />
        </Style.Resources>

        <Style.Setters>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ListViewItem">
                        <Border
                            Margin="{TemplateBinding Margin}"
                            Padding="{TemplateBinding Padding}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter />
                        </Border>

                        <ControlTemplate.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="{StaticResource opacitybackgroundHighlight}" />
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource opacitybackgroundHighlight}" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style.Setters>
    </Style>

    <!--  LsitView  -->


    <!--  Scroll  -->

    <SolidColorBrush x:Key="StandardBorderBrush" Color="#EEF4ED" />
    <SolidColorBrush x:Key="StandardBackgroundBrush" Color="Black" />
    <SolidColorBrush x:Key="HoverBorderBrush" Color="#DDD" />
    <SolidColorBrush x:Key="SelectedBackgroundBrush" Color="#EEF4ED" />
    <SolidColorBrush x:Key="SelectedForegroundBrush" Color="White" />
    <SolidColorBrush x:Key="DisabledForegroundBrush" Color="#EEF4ED" />
    <SolidColorBrush x:Key="GlyphBrush" Color="#EEF4ED" />
    <SolidColorBrush x:Key="NormalBrush" Color="#EEF4ED" />
    <SolidColorBrush x:Key="NormalBorderBrush" Color="#EEF4ED" />
    <SolidColorBrush x:Key="HorizontalNormalBrush" Color="#EEF4ED" />
    <SolidColorBrush x:Key="HorizontalNormalBorderBrush" Color="#EEF4ED" />

    <LinearGradientBrush x:Key="ListBoxBackgroundBrush" StartPoint="0,0" EndPoint="1,0.001">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Offset="0.0" Color="White" />
                <GradientStop Offset="0.6" Color="White" />
                <GradientStop Offset="1.2" Color="#DDDDDD" />
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="StandardBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Offset="0.0" Color="#FFF" />
                <GradientStop Offset="1.0" Color="#CCC" />
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="PressedBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Offset="0.0" Color="#BBB" />
                <GradientStop Offset="0.1" Color="#EEE" />
                <GradientStop Offset="0.9" Color="#EEE" />
                <GradientStop Offset="1.0" Color="#FFF" />
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>

    <Style x:Key="ScrollBarLineButton" TargetType="{x:Type RepeatButton}">
        <Setter Property="Visibility" Value="Hidden" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="OverridesDefaultStyle" Value="true" />
        <Setter Property="Focusable" Value="false" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type RepeatButton}">
                    <Border
                        Name="Border"
                        Margin="1"
                        Background="{StaticResource NormalBrush}"
                        BorderBrush="{StaticResource NormalBorderBrush}"
                        BorderThickness="1.5"
                        CornerRadius="2">
                        <Path
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Data="{Binding Path=Content, RelativeSource={RelativeSource TemplatedParent}}"
                            Fill="{StaticResource GlyphBrush}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter TargetName="Border" Property="Background" Value="{StaticResource PressedBrush}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{StaticResource DisabledForegroundBrush}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style x:Key="ScrollBarPageButton" TargetType="{x:Type RepeatButton}">
        <Setter Property="Visibility" Value="Hidden" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="OverridesDefaultStyle" Value="true" />
        <Setter Property="IsTabStop" Value="false" />
        <Setter Property="Focusable" Value="false" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type RepeatButton}">
                    <Border Background="#EEF4ED" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="ScrollBarThumb" TargetType="{x:Type Thumb}">
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="OverridesDefaultStyle" Value="true" />
        <Setter Property="IsTabStop" Value="false" />
        <Setter Property="Focusable" Value="false" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <Border
                        Width="8"
                        Margin="0,0,-2,0"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="0"
                        CornerRadius="4" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <ControlTemplate x:Key="VerticalScrollBar" TargetType="{x:Type ScrollBar}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition MaxHeight="0" />
                <RowDefinition Height="0.00001*" />
                <RowDefinition MaxHeight="0" />
            </Grid.RowDefinitions>
            <Border
                Grid.RowSpan="3"
                Background="Transparent"
                CornerRadius="2" />
            <RepeatButton
                Grid.Row="0"
                Height="18"
                Command="ScrollBar.LineUpCommand"
                Content="M 0 4 L 8 4 L 4 0 Z"
                Style="{StaticResource ScrollBarLineButton}" />
            <Track
                Name="PART_Track"
                Grid.Row="1"
                IsDirectionReversed="true">
                <Track.DecreaseRepeatButton>
                    <RepeatButton Command="ScrollBar.PageUpCommand" Style="{StaticResource ScrollBarPageButton}" />
                </Track.DecreaseRepeatButton>
                <Track.Thumb>
                    <Thumb
                        Margin="1,0,1,0"
                        Background="{StaticResource PrimaryColor}"
                        BorderBrush="{StaticResource HorizontalNormalBorderBrush}"
                        Style="{StaticResource ScrollBarThumb}" />
                </Track.Thumb>
                <Track.IncreaseRepeatButton>
                    <RepeatButton Command="ScrollBar.PageDownCommand" Style="{StaticResource ScrollBarPageButton}" />
                </Track.IncreaseRepeatButton>
            </Track>
            <RepeatButton
                Grid.Row="3"
                Height="18"
                Command="ScrollBar.LineDownCommand"
                Content="M 0 0 L 4 4 L 8 0 Z"
                Style="{StaticResource ScrollBarLineButton}" />
        </Grid>
    </ControlTemplate>

    <Style x:Key="ScrollBarThumbHor" TargetType="{x:Type Thumb}">
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="OverridesDefaultStyle" Value="true" />
        <Setter Property="IsTabStop" Value="false" />
        <Setter Property="Focusable" Value="false" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <Border
                        Height="8"
                        Margin="28,0,-2,0"
                        Background="{StaticResource NormalBrush}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="0"
                        CornerRadius="3" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <ControlTemplate x:Key="HorizontalScrollBar" TargetType="{x:Type ScrollBar}">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="0" />
                <ColumnDefinition Width="0.00001*" />
                <ColumnDefinition Width="0" />
            </Grid.ColumnDefinitions>
            <Border
                Grid.ColumnSpan="3"
                Background="Transparent"
                CornerRadius="0" />
            <RepeatButton
                Grid.Column="0"
                Width="18"
                Command="ScrollBar.LineLeftCommand"
                Content="M 4 0 L 4 8 L 0 4 Z"
                Style="{StaticResource ScrollBarLineButton}" />
            <Track
                Name="PART_Track"
                Grid.Column="1"
                IsDirectionReversed="False">
                <Track.DecreaseRepeatButton>
                    <RepeatButton Command="ScrollBar.PageLeftCommand" Style="{StaticResource ScrollBarPageButton}" />
                </Track.DecreaseRepeatButton>
                <Track.Thumb>
                    <Thumb
                        Margin="0,1,0,1"
                        Background="{StaticResource NormalBrush}"
                        BorderBrush="{StaticResource NormalBorderBrush}"
                        Style="{StaticResource ScrollBarThumbHor}" />
                </Track.Thumb>
                <Track.IncreaseRepeatButton>
                    <RepeatButton Command="ScrollBar.PageRightCommand" Style="{StaticResource ScrollBarPageButton}" />
                </Track.IncreaseRepeatButton>
            </Track>
            <RepeatButton
                Grid.Column="3"
                Width="18"
                Command="ScrollBar.LineRightCommand"
                Content="M 0 0 L 4 4 L 0 8 Z"
                Style="{StaticResource ScrollBarLineButton}" />
        </Grid>
    </ControlTemplate>
    <Style x:Key="{x:Type ScrollBar}" TargetType="{x:Type ScrollBar}">
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="OverridesDefaultStyle" Value="true" />
        <Style.Triggers>
            <Trigger Property="Orientation" Value="Horizontal">
                <Setter Property="Width" Value="Auto" />
                <Setter Property="Height" Value="18" />

                <Setter Property="Template" Value="{StaticResource HorizontalScrollBar}" />
            </Trigger>
            <Trigger Property="Orientation" Value="Vertical">
                <Setter Property="Width" Value="10" />
                <Setter Property="Height" Value="Auto" />
                <Setter Property="Template" Value="{StaticResource VerticalScrollBar}" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="FavsScrollViewer" TargetType="{x:Type ScrollViewer}">
        <Setter Property="OverridesDefaultStyle" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ScrollViewer}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <ScrollContentPresenter Grid.Column="1" />
                        <ScrollBar
                            Name="PART_VerticalScrollBar"
                            Maximum="{TemplateBinding ScrollableHeight}"
                            ViewportSize="{TemplateBinding ViewportHeight}"
                            Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                            Value="{TemplateBinding VerticalOffset}" />
                        <ScrollBar
                            Name="PART_HorizontalScrollBar"
                            Grid.Row="1"
                            Grid.Column="1"
                            Maximum="{TemplateBinding ScrollableWidth}"
                            Orientation="Horizontal"
                            ViewportSize="{TemplateBinding ViewportWidth}"
                            Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                            Value="{TemplateBinding HorizontalOffset}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>


    <Style TargetType="{x:Type Page}">
        <Setter Property="FontFamily" Value="El Messiri" />
    </Style>

    <Style TargetType="{x:Type Window}">
        <Setter Property="FontFamily" Value="El Messiri" />
    </Style>

    <Style TargetType="{x:Type UserControl}">
        <Setter Property="FontFamily" Value="El Messiri" />
    </Style>
    <Style TargetType="{x:Type TextBlock}">
        <Setter Property="FontFamily" Value="El Messiri" />
        <Setter Property="FontSize" Value="18" />
        <Setter Property="ToolTip" Value="{Binding Text, RelativeSource={RelativeSource Self}}" />
    </Style>



    <Style x:Key="circleProgressbar" TargetType="ProgressBar">
        <Setter Property="IsIndeterminate" Value="True" />
        <Setter Property="Foreground" Value="DodgerBlue" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ProgressBar">
                    <Grid x:Name="PART_ContainerGrid">
                        <Ellipse
                            Width="{TemplateBinding ActualWidth}"
                            Height="{TemplateBinding ActualHeight}"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Stroke="{TemplateBinding Background}"
                            StrokeThickness="5" />

                        <Grid
                            x:Name="PART_SpinningContainer"
                            Width="{TemplateBinding ActualWidth}"
                            Height="{TemplateBinding ActualHeight}"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center">
                            <Grid.RenderTransform>
                                <RotateTransform x:Name="RotateAnimation" CenterX="{Binding ActualWidth, ElementName=PART_SpinningContainer, Converter={StaticResource HalfValueConverter}}" CenterY="{Binding ActualHeight, ElementName=PART_SpinningContainer, Converter={StaticResource HalfValueConverter}}" />
                            </Grid.RenderTransform>


                            <Canvas Width="100" Height="100">
                                <!--  Green arc (60%)  -->
                                <Path
                                    Fill="Transparent"
                                    RenderTransformOrigin="0.5,0.5"
                                    Stroke="White"
                                    StrokeThickness="10">
                                    <Path.Data>
                                        <PathGeometry>
                                            <PathFigure IsClosed="False" StartPoint="50,5">
                                                <ArcSegment
                                                    IsLargeArc="True"
                                                    Point="5,50"
                                                    Size="45,45"
                                                    SweepDirection="Clockwise" />
                                            </PathFigure>
                                        </PathGeometry>
                                    </Path.Data>
                                </Path>

                                <!--  White arc (40%)  -->
                                <Path
                                    Fill="Transparent"
                                    RenderTransformOrigin="0.5,0.5"
                                    Stroke="Green"
                                    StrokeThickness="10">
                                    <Path.Data>
                                        <PathGeometry>
                                            <PathFigure IsClosed="False" StartPoint="5,50">
                                                <ArcSegment
                                                    IsLargeArc="False"
                                                    Point="50,5"
                                                    Size="45,45"
                                                    SweepDirection="Clockwise" />
                                            </PathFigure>
                                        </PathGeometry>
                                    </Path.Data>
                                </Path>
                            </Canvas>

                        </Grid>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsIndeterminate" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard RepeatBehavior="Forever">
                                        <DoubleAnimation
                                            Storyboard.TargetName="RotateAnimation"
                                            Storyboard.TargetProperty="Angle"
                                            From="0"
                                            To="360"
                                            Duration="0:0:1.5" />
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <StopStoryboard />
                            </Trigger.ExitActions>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--  Scroll  -->
</ResourceDictionary>
