using System;
using System.Collections.Generic;

namespace MacAddressLicenseGenerator
{
    /// <summary>
    /// كلاس لحفظ معلومات الجهاز الشاملة
    /// </summary>
    public class DeviceInformation
    {
        public string ComputerName { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string OperatingSystem { get; set; } = string.Empty;
        public string OSVersion { get; set; } = string.Empty;
        public string OSArchitecture { get; set; } = string.Empty;
        public string ProcessorName { get; set; } = string.Empty;
        public string ProcessorArchitecture { get; set; } = string.Empty;
        public int ProcessorCores { get; set; }
        public string TotalRAM { get; set; } = string.Empty;
        public string AvailableRAM { get; set; } = string.Empty;
        public List<string> MacAddresses { get; set; } = new List<string>();
        public List<NetworkAdapterInfo> NetworkAdapters { get; set; } = new List<NetworkAdapterInfo>();
        public List<DiskInfo> DiskDrives { get; set; } = new List<DiskInfo>();
        public string SystemManufacturer { get; set; } = string.Empty;
        public string SystemModel { get; set; } = string.Empty;
        public string BiosVersion { get; set; } = string.Empty;
        public string SystemSerialNumber { get; set; } = string.Empty;
        public string MotherboardSerialNumber { get; set; } = string.Empty;
        public DateTime CollectionDateTime { get; set; } = DateTime.Now;
        public string TimeZone { get; set; } = string.Empty;
        public string DomainWorkgroup { get; set; } = string.Empty;
        public List<string> InstalledSoftware { get; set; } = new List<string>();
        public string GraphicsCard { get; set; } = string.Empty;
        public string SoundCard { get; set; } = string.Empty;

        // معلومات فريدة إضافية
        public string DeviceUUID { get; set; } = string.Empty;
        public string MachineGUID { get; set; } = string.Empty;
        public string WindowsProductKey { get; set; } = string.Empty;
        public string InstallationID { get; set; } = string.Empty;
        public string SecurityID { get; set; } = string.Empty;
        public string ProcessorID { get; set; } = string.Empty;
        public string GraphicsCardSerial { get; set; } = string.Empty;
        public List<string> MemorySerialNumbers { get; set; } = new List<string>();
        public List<string> HardDriveSerials { get; set; } = new List<string>();
        public string BluetoothMAC { get; set; } = string.Empty;
        public string WiFiMAC { get; set; } = string.Empty;
        public string TPMVersion { get; set; } = string.Empty;
        public string CPUSignature { get; set; } = string.Empty;
        public string BIOSDate { get; set; } = string.Empty;
        public string UEFIVersion { get; set; } = string.Empty;

        // معلومات إضافية فريدة
        public string WindowsInstallDate { get; set; } = string.Empty;
        public string LastBootTime { get; set; } = string.Empty;
        public string SystemUptime { get; set; } = string.Empty;
        public string WindowsBuildNumber { get; set; } = string.Empty;
        public string WindowsEdition { get; set; } = string.Empty;
        public List<string> USBDeviceSerials { get; set; } = new List<string>();
        public string NetworkAdapterPNPIDs { get; set; } = string.Empty;
        public string SystemEnclosureSerial { get; set; } = string.Empty;
        public string ProcessorManufacturer { get; set; } = string.Empty;
        public string ProcessorFamily { get; set; } = string.Empty;
    }

    /// <summary>
    /// معلومات محول الشبكة
    /// </summary>
    public class NetworkAdapterInfo
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string MacAddress { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string NetworkInterfaceType { get; set; } = string.Empty;
        public string Speed { get; set; } = string.Empty;
        public List<string> IPAddresses { get; set; } = new List<string>();
        public string DefaultGateway { get; set; } = string.Empty;
        public List<string> DNSServers { get; set; } = new List<string>();
    }

    /// <summary>
    /// معلومات قرص التخزين
    /// </summary>
    public class DiskInfo
    {
        public string DriveLetter { get; set; } = string.Empty;
        public string Label { get; set; } = string.Empty;
        public string FileSystem { get; set; } = string.Empty;
        public string TotalSize { get; set; } = string.Empty;
        public string FreeSpace { get; set; } = string.Empty;
        public string UsedSpace { get; set; } = string.Empty;
        public string DriveType { get; set; } = string.Empty;
        public string Model { get; set; } = string.Empty;
        public string SerialNumber { get; set; } = string.Empty;
    }
}
