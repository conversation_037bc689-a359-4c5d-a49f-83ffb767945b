﻿using Microsoft.Extensions.DependencyInjection;

namespace VisionPoint.UI.Converters
{
    public static class ServiceLocator
    {
        private static ServiceProvider _serviceProvider;

        public static void Initialize(ServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public static T GetService<T>()
        {
            if (_serviceProvider == null)
            {
                throw new InvalidOperationException("ServiceLocator has not been initialized.");
            }
            return _serviceProvider.GetRequiredService<T>();
        }
    }
}
