﻿using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using VisionPoint.UI.Helper;
using VisionPoint.UI.Helper.ImportExport;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.purchases
{
    /// <summary>
    /// Interaction logic for purchasePage.xaml
    /// </summary>
    public partial class purchasePage : Page
    {
        private readonly PurchaseService _purchaseService;
        private readonly ClientService _clientService;
        private readonly WarehouseService _warehouseService;
        private ObservableCollection<PurchaseViewModel> _purchases;
        private PurchaseViewModel _selectedPurchase;
        bool _firstLoad = true; // متغير لتتبع ما إذا كانت الصفحة قد تم تحميلها لأول مرة
        // فلاتر البحث
        private DateTime? _fromDate;
        private DateTime? _toDate;
        private int? _clientId;
        private int? _warehouseId;
        private bool? _hasRemaining;
        private int? _invoiceId;

        public purchasePage()
        {
            InitializeComponent();
            _purchaseService = new PurchaseService();
            _clientService = new ClientService();
            _warehouseService = new WarehouseService();
            _purchases = new ObservableCollection<PurchaseViewModel>();
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            if (!_firstLoad) return; // إذا كانت الصفحة قد تم تحميلها بالفعل، لا نقوم بإعادة تحميل البيانات

            // تحميل المخازن أولاً
            await LoadWarehousesAsync();

            // تحميل الموردين في القائمة المنسدلة
            // await LoadSuppliersAsync();

            // تعيين تاريخ اليوم كتاريخ افتراضي للبحث
            _fromDate = DateTime.Today;
            dpFromDate.SelectedDate = _fromDate;

            // تعيين حالة عناصر التاريخ
            chkAllPeriods.IsChecked = false;
            UpdateDateControlsState();

            // تحميل فواتير اليوم فقط
            await ApplyFiltersAsync();
        }

        /// <summary>
        /// تحديث حالة عناصر التاريخ بناءً على خيار "كل الفترات"
        /// </summary>
        private void UpdateDateControlsState()
        {
            bool isAllPeriods = chkAllPeriods.IsChecked == true;

            // تعطيل/تفعيل عناصر التاريخ
            dpFromDate.IsEnabled = !isAllPeriods;
            dpToDate.IsEnabled = !isAllPeriods;
            dpFromDate.Opacity = isAllPeriods ? 0.5 : 1;
            dpToDate.Opacity = isAllPeriods ? 0.5 : 1;
        }

        private async Task LoadWarehousesAsync()
        {
            try
            {
                // تحميل المخازن
                var warehouses = await _warehouseService.GetAllWarehousesAsync();

                // إضافة عنصر "الكل" في بداية القائمة
                var allWarehousesItem = new Warehouse { Id = -1, Name = "الكل" };
                var warehousesList = new List<Warehouse> { allWarehousesItem };
                warehousesList.AddRange(warehouses);

                cmbWarehouses.ItemsSource = warehousesList;

                // تحديد المخزن الافتراضي بناءً على المستخدم الحالي
                if (CurrentUser.WarehouseId.HasValue && warehouses.Any(w => w.Id == CurrentUser.WarehouseId.Value))
                {
                    // اختيار مخزن المستخدم الحالي
                    cmbWarehouses.SelectedValue = CurrentUser.WarehouseId.Value;
                }
                else
                {
                    // اختيار "الكل" كقيمة افتراضية
                    cmbWarehouses.SelectedIndex = 0;
                }

                // تطبيق منطق الصلاحيات لتغيير المخزن
                // إذا لم يكن المستخدم مديراً أو لا يملك صلاحية تغيير المخزن، يتم تعطيل الكومبو
                cmbWarehouses.IsEnabled = CurrentUser.CanChangeWarehouse;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل بيانات المخازن: {ex.Message}", "خطأ في التحميل", true);
            }
        }

        private async Task LoadSuppliersAsync()
        {
            try
            {
                List<Client> suppliers;

                // تحديد المخزن المحدد
                if (cmbWarehouses.SelectedValue != null && cmbWarehouses.SelectedValue is int warehouseId && warehouseId != -1)
                {
                    // تحميل الموردين الخاصين بالمخزن المحدد
                    suppliers = await _clientService.GetSuppliersByWarehouseAsync(warehouseId);
                }
                else
                {
                    // تحميل جميع الموردين
                    suppliers = await _clientService.GetSuppliersOnlyAsync();
                }

                // إضافة عنصر "الكل" في بداية القائمة
                var allSuppliersItem = new Client { Id = -1, Name = "الكل" };
                var suppliersList = new List<Client> { allSuppliersItem };
                suppliersList.AddRange(suppliers);

                cmbClients.ItemsSource = suppliersList;
                cmbClients.SelectedIndex = 0; // اختيار "الكل" كقيمة افتراضية
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل بيانات الموردين: {ex.Message}", "خطأ في التحميل", true);
            }
        }

        /// <summary>
        /// تطبيق الفلاتر وتحميل الفواتير المصفاة
        /// </summary>
        private async Task ApplyFiltersAsync()
        {
            try
            {
                // تحقق من رقم الفاتورة
                _invoiceId = null;
                if (!string.IsNullOrWhiteSpace(txtInvoiceNumber.Text) && int.TryParse(txtInvoiceNumber.Text.Trim(), out int invoiceId))
                {
                    _invoiceId = invoiceId;
                }

                // تحقق من المخزن المحدد
                _warehouseId = null;
                if (cmbWarehouses.SelectedValue != null && cmbWarehouses.SelectedValue is int warehouseId && warehouseId != -1)
                {
                    _warehouseId = warehouseId;
                }

                // تحقق من المورد المحدد
                _clientId = null;
                if (cmbClients.SelectedValue != null && cmbClients.SelectedValue is int clientId && clientId != -1)
                {
                    _clientId = clientId;
                }

                // تحقق من خيار "كل الفترات"
                if (chkAllPeriods.IsChecked == true)
                {
                    // إذا تم اختيار "كل الفترات"، نعين قيم التاريخ بـ null
                    _fromDate = null;
                    _toDate = null;
                }

                // تحميل الفواتير المصفاة
                _purchases.Clear();
                _purchases = new ObservableCollection<PurchaseViewModel>(await _purchaseService.GetFilteredPurchasesAsync(
                    _fromDate,
                    _toDate,
                    _clientId,
                    _hasRemaining,
                    _invoiceId,
                    _warehouseId));


                list.ItemsSource = _purchases;

                // لا نظهر أي رسائل في حال عدم وجود فواتير
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تطبيق الفلاتر: {ex.Message}", "خطأ في البحث", true);
            }
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnAdd.IsEnabled = false;
            btnSearch.IsEnabled = false;
            btnExport.IsEnabled = false;
            btnResetFilters.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnAdd.IsEnabled = true;
            btnSearch.IsEnabled = true;
            btnExport.IsEnabled = true;
            btnResetFilters.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private async void btnAdd_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                _firstLoad = false; // تعيين المتغير إلى false بعد تحميل الصفحة لأول مرة
                // التنقل إلى صفحة إضافة المشتريات والانتظار حتى إغلاقها
                await NavigateAction.NavigateToAndWaitAsync(new PurchasProductPage(_purchaseService));
                // إعادة تحميل البيانات بعد الإضافة مع تطبيق الفلاتر الحالية
                await ApplyFiltersAsync();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnSearch_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                await ApplyFiltersAsync();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnResetFilters_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // إعادة تعيين قيم الفلاتر
                _fromDate = DateTime.Today; // تعيين تاريخ اليوم
                _toDate = null;
                _warehouseId = null;
                _clientId = null;
                _hasRemaining = null;
                _invoiceId = null;

                // إعادة تعيين عناصر واجهة المستخدم
                chkAllPeriods.IsChecked = false; // إلغاء اختيار "كل الفترات"
                dpFromDate.SelectedDate = _fromDate; // تعيين تاريخ اليوم
                dpToDate.SelectedDate = null;
                cmbWarehouses.SelectedIndex = 0; // "الكل"
                cmbClients.SelectedIndex = 0; // "الكل"
                chkHasRemaining.IsChecked = null;
                txtInvoiceNumber.Text = string.Empty;

                // تحديث حالة عناصر التاريخ
                UpdateDateControlsState();

                // تطبيق الفلاتر لعرض فواتير اليوم فقط
                await ApplyFiltersAsync();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        // أحداث تغيير قيم الفلاتر

        private void dpFromDate_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            _fromDate = dpFromDate.SelectedDate;
        }

        private void dpToDate_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            _toDate = dpToDate.SelectedDate;
        }

        private async void cmbWarehouses_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (cmbWarehouses.SelectedValue != null && cmbWarehouses.SelectedValue is int warehouseId && warehouseId != -1)
                {
                    _warehouseId = warehouseId;
                }
                else
                {
                    _warehouseId = null;
                }

                // إعادة تحميل الموردين عند تغيير المخزن
                await LoadSuppliersAsync();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تغيير المخزن: {ex.Message}", "خطأ", true);
            }
        }

        private void cmbClients_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (cmbClients.SelectedValue != null && cmbClients.SelectedValue is int clientId && clientId != -1)
            {
                _clientId = clientId;
            }
            else
            {
                _clientId = null;
            }
        }

        private void chkHasRemaining_CheckedChanged(object sender, RoutedEventArgs e)
        {
            _hasRemaining = chkHasRemaining.IsChecked;
        }

        private void chkAllPeriods_CheckedChanged(object sender, RoutedEventArgs e)
        {
            // تحديث حالة عناصر التاريخ
            UpdateDateControlsState();

            if (chkAllPeriods.IsChecked == true)
            {
                // إذا تم اختيار "كل الفترات"، نعين قيم التاريخ بـ null
                _fromDate = null;
                _toDate = null;
            }
            else
            {
                // إذا تم إلغاء اختيار "كل الفترات"، نعيد تعيين تاريخ اليوم كتاريخ بداية افتراضي
                _fromDate = DateTime.Today;
                dpFromDate.SelectedDate = _fromDate;
            }
        }

        private async void btnEdit_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // التحقق من صلاحيات المستخدم
            bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditPurchaseRole");
            bool canView = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("PurchaseRole") || CurrentUser.HasRole("EditPurchaseRole") || CurrentUser.HasRole("DeletePurchaseRole");

            // إذا لم يكن المستخدم يملك صلاحية العرض على الأقل، نمنع الوصول
            if (!canView)
            {
                ErrorBox.Show("لا تملك صلاحية عرض فواتير المشتريات", "خطأ في الصلاحيات", true);
                return;
            }

            if (_selectedPurchase == null)
            {
                ErrorBox.Show("الرجاء اختيار فاتورة للعرض", "تنبيه", false);
                return;
            }

            try
            {
                _firstLoad = false; // تعيين المتغير إلى false بعد تحميل الصفحة لأول مرة
                // فتح صفحة عرض/تعديل المشتريات مع تمرير الفاتورة المحددة والانتظار حتى إغلاقها
                await NavigateAction.NavigateToAndWaitAsync(new PurchasProductPage(_purchaseService, _selectedPurchase.Id));
                // إعادة تحميل البيانات بعد العرض/التعديل مع تطبيق الفلاتر الحالية
                await ApplyFiltersAsync();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء فتح صفحة الفاتورة: {ex.Message}", "خطأ في النظام", true);
            }
        }

        private async void btnDelete_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // التحقق من صلاحية الحذف
            bool canDelete = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("DeletePurchaseRole");
            if (!canDelete)
            {
                ErrorBox.Show("لا تملك صلاحية حذف فواتير المشتريات", "خطأ في الصلاحيات", true);
                return;
            }

            if (_selectedPurchase == null)
            {
                ErrorBox.Show("الرجاء اختيار فاتورة للحذف", "تنبيه", false);
                return;
            }

            var result = QuestionBox.Show("تأكيد الحذف", $"هل أنت متأكد من حذف الفاتورة رقم {_selectedPurchase.InvoiceNo}؟");
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    await _purchaseService.DeletePurchaseAsync(_selectedPurchase.Id);
                    await ApplyFiltersAsync();
                    DialogBox.Show("تم بنجاح", "تم حذف الفاتورة بنجاح");
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"حدث خطأ أثناء حذف الفاتورة: {ex.Message}", "خطأ في الحذف", true);
                }
            }
        }


        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            // Make sure to update the column count check to 9 or more
            if (list.View is GridView gridView && gridView.Columns.Count >= 9)
            {
                double actionColumnWidth = 100; // Fixed width for operations
                double padding = 10;
                double scrollbarWidth = SystemParameters.VerticalScrollBarWidth;

                double availableWidth = list.ActualWidth - actionColumnWidth - scrollbarWidth - padding;

                // Add a weight for your new column (e.g., 1.0 for a balanced width)
                // totalWeight = Index + Invoice ID + Date + Client + Total + Paid + Remaining + NEW_COLUMN_WEIGHT
                double totalWeight = 0.5 + 1.0 + 1.5 + 2.0 + 1.5 + 1.5 + 1.5 + 1.0; // Added 1.0 for the new column
                double unitWidth = availableWidth / totalWeight;

                gridView.Columns[0].Width = unitWidth * 0.5; // Index
                gridView.Columns[1].Width = unitWidth * 1.0; // Invoice ID
                gridView.Columns[2].Width = unitWidth * 1.5; // Date
                gridView.Columns[3].Width = unitWidth * 2.0; // Client
                gridView.Columns[4].Width = unitWidth * 1.5; // Total
                gridView.Columns[5].Width = unitWidth * 1.5; // Paid
                gridView.Columns[6].Width = unitWidth * 1.5; // Remaining

                // This is your new column! Adjust the weight (1.0 here) as needed.
                gridView.Columns[7].Width = unitWidth * 1.0; // <--- Your New Column

                // The "Operations" column now becomes the 9th column (index 8)
                gridView.Columns[8].Width = actionColumnWidth; // Operations
            }
        }




        private async void btnExport_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (_purchases.Count == 0)
                {
                    ErrorBox.Show("لا توجد بيانات لتصديرها", "تنبيه", false);
                    return;
                }

                // فتح نافذة معاينة تصدير المشتريات الجديدة
                var exportWindow = new PurchaseExportPreviewWindow(_purchases.ToList(), _clientId, _warehouseId);
                exportWindow.ShowDialog();

                if (exportWindow.ExportCompleted)
                {
                    // يمكن إضافة أي إجراءات إضافية بعد التصدير الناجح هنا
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء فتح نافذة التصدير: {ex.Message}", "خطأ في التصدير", true);
            }
        }

        private void list_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (list.SelectedItem is PurchaseViewModel selectedItem)
            {
                _selectedPurchase = selectedItem;
                // فتح نافذة العرض/التعديل عند النقر المزدوج
                btnEdit_MouseLeftButtonDown(null, null);
            }
            else
            {
                _selectedPurchase = null;
            }
        }

        private void list_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (list.SelectedItem is PurchaseViewModel selectedItem)
            {
                _selectedPurchase = selectedItem;
            }
            else
            {
                _selectedPurchase = null;
            }
        }

        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            // الحصول على الفاتورة المرتبطة بالزر من سياق البيانات
            if (sender is Button button && button.DataContext is PurchaseViewModel selectedPurchase)
            {
                _selectedPurchase = selectedPurchase;
                btnEdit_MouseLeftButtonDown(null, null);
            }
        }

        private void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            // الحصول على الفاتورة المرتبطة بالزر من سياق البيانات
            if (sender is Button button && button.DataContext is PurchaseViewModel selectedPurchase)
            {
                _selectedPurchase = selectedPurchase;
                btnDelete_MouseLeftButtonDown(null, null);
            }
        }

        private void GridCheckBox_Toggle(object sender, MouseButtonEventArgs e)
        {
            // Make sure the click wasn't directly on a checkbox (to avoid double toggling)
            if (e.OriginalSource is CheckBox)
                return;

            if (sender is Grid grid)
            {
                // Find the first CheckBox inside the grid (you can adjust if you want a specific name or type)
                var checkBox = grid.Children.OfType<CheckBox>().FirstOrDefault();
                if (checkBox != null)
                {
                    checkBox.IsChecked = !checkBox.IsChecked;
                }
            }
        }


        private void TextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox)
            {
                textBox.SelectAll();
            }
        }
    }
}
