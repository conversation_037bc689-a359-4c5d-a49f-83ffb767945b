﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VisionPoint.UI.Models;

namespace VisionPoint.UI.ViewModel;

public class LensPrescriptionColorVM
{
    public int Id { get; set; }
    public int? LensPrescriptionId { get; set; }
    public LensPrescription? LensPrescription { get; set; }
    public byte? ColorId { get; set; }
    public Color? Color { get; set; }
    public string Barcode { get; set; } = string.Empty;
    public int TotalQuantity { get; set; }
}
