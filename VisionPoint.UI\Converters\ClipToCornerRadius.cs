﻿using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace VisionPoint.UI.Converters
{


    public  class ClipToCornerRadius
    {
        public static readonly DependencyProperty IsEnabledProperty =
            DependencyProperty.RegisterAttached(
                "IsEnabled",
                typeof(bool),
                typeof(ClipToCornerRadius),
                new PropertyMetadata(false, OnIsEnabledChanged));

        public  bool GetIsEnabled(DependencyObject obj) => (bool)obj.GetValue(IsEnabledProperty);
        public static void SetIsEnabled(DependencyObject obj, bool value) => obj.SetValue(IsEnabledProperty, value);

        private static void OnIsEnabledChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is Border border && e.NewValue is true)
            {
                border.SizeChanged += (s, args) =>
                {
                    var rect = new Rect(0, 0, border.ActualWidth, border.ActualHeight);
                    var radius = border.CornerRadius.TopLeft;
                    border.Clip = new RectangleGeometry(rect, radius, radius);
                };
            }
        }
    }

}
