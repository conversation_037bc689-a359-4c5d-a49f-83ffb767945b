using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using VisionPoint.UI.Helper;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.Warehouses
{
    public partial class WarehousePage : Page
    {
        private readonly WarehouseService _warehouseService;
        private ObservableCollection<Warehouse> _warehouses;
        private Warehouse _selectedWarehouse;
        private bool _isSearchMode = false;

        public WarehousePage()
        {
            InitializeComponent();
            _warehouseService = new WarehouseService();
            _warehouses = new ObservableCollection<Warehouse>();
        }

        private async void Page_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadWarehousesAsync();
        }

        private async Task LoadWarehousesAsync()
        {
            try
            {
                _warehouses.Clear();
                _warehouses = new ObservableCollection<Warehouse>(await _warehouseService.GetAllWarehousesAsync());
                list.ItemsSource = _warehouses;

                // إظهار عدد المخازن في عنوان النافذة
                Title = $"المخازن - {_warehouses.Count}";
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ في التحميل", true);
            }
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnSave.IsEnabled = false;
            btnNew.IsEnabled = false;
            btnSearch.IsEnabled = false;
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnSave.IsEnabled = true;
            btnNew.IsEnabled = true;
            btnSearch.IsEnabled = true;
        }

        private async void btnSave_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    ErrorBox.Show("يرجى إدخال اسم المخزن", "خطأ في البيانات", false);
                    txtName.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtCode.Text))
                {
                    ErrorBox.Show("يرجى إدخال رمز المخزن", "خطأ في البيانات", false);
                    txtCode.Focus();
                    return;
                }

                // تعديل المخزن أو إضافة مخزن جديد
                if (_selectedWarehouse != null)
                {
                    // تحديث المخزن الموجود
                    _selectedWarehouse.Name = txtName.Text;
                    _selectedWarehouse.Code = txtCode.Text;

                    var result = await _warehouseService.UpdateWarehouseAsync(_selectedWarehouse);
                    if (result.State)
                    {
                        DialogBox.Show("تم بنجاح", $"تم تحديث المخزن {_selectedWarehouse.Name} بنجاح");
                    }
                    else
                    {
                        ErrorBox.Show(result.Message, "خطأ في التحديث", true);
                        return;
                    }
                }
                else
                {
                    // إضافة مخزن جديد
                    var newWarehouse = new Warehouse
                    {
                        Name = txtName.Text,
                        Code = txtCode.Text
                    };

                    var result = await _warehouseService.AddWarehouseAsync(newWarehouse);
                    if (result.State)
                    {
                        DialogBox.Show("تم بنجاح", $"تم إضافة المخزن {newWarehouse.Name} بنجاح");
                    }
                    else
                    {
                        ErrorBox.Show(result.Message, "خطأ في الإضافة", true);
                        return;
                    }
                }

                // إعادة تحميل القائمة وتنظيف الحقول
                await LoadWarehousesAsync();
                ClearForm();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ: {ex.Message}", "خطأ في النظام", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnNew_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                ClearForm();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnSearch_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            await SearchWarehousesAsync();
        }

        private void ClearForm()
        {
            txtName.Text = "";
            txtCode.Text = "";
            _selectedWarehouse = null;
            list.SelectedItem = null;
        }

        private async Task SearchWarehousesAsync()
        {
            try
            {
                string name = txtName.Text?.Trim();
                string code = txtCode.Text?.Trim();

                // If all fields are empty, load all warehouses
                if (string.IsNullOrEmpty(name) && string.IsNullOrEmpty(code))
                {
                    await LoadWarehousesAsync();
                    _isSearchMode = false;
                    return;
                }

                // Combine search terms
                string searchTerm = "";
                if (!string.IsNullOrEmpty(name))
                    searchTerm = name;
                else if (!string.IsNullOrEmpty(code))
                    searchTerm = code;

                // Perform the search
                var searchResults = await _warehouseService.SearchWarehousesAsync(searchTerm);

                // Update the UI
                _warehouses.Clear();
                foreach (var warehouse in searchResults)
                {
                    _warehouses.Add(warehouse);
                }

                list.ItemsSource = _warehouses;
                Title = $"المخازن - نتائج البحث ({_warehouses.Count})";
                _isSearchMode = true;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ في البحث", true);
            }
        }

        private void list_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (list.SelectedItem is Warehouse selectedWarehouse)
            {
                _selectedWarehouse = selectedWarehouse;
                DisplayWarehouseDetails();
            }
        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (list.View is GridView gridView && gridView.Columns.Count >= 3)
            {
                // Define a small tolerance for layout adjustments, often related to borders or padding
                double layoutTolerance = 5;

                // Calculate the total available width for content, accounting for scrollbar and a small tolerance
                double totalWidth = list.ActualWidth - SystemParameters.VerticalScrollBarWidth - layoutTolerance;

                // Define the fixed width for the 'Actions' column (assuming it's at index 2)
                double actionColumnWidth = 120;

                // Calculate the width available for the flexible columns (Name and Code)
                double availableWidthForFlexibleColumns = totalWidth - actionColumnWidth;

                // Ensure availableWidthForFlexibleColumns doesn't go negative
                if (availableWidthForFlexibleColumns < 0)
                {
                    availableWidthForFlexibleColumns = 0;
                }

                // --- Distribute the available width among the flexible columns ---
                // Assuming Name column (index 0) takes 1.5 units and Code column (index 1) takes 1.0 unit.
                // Total units for flexible columns = 1.5 + 1.0 = 2.5
                double totalFlexibleUnits = 2.5;
                double unitWidth = availableWidthForFlexibleColumns / totalFlexibleUnits;

                // --- Assign widths to columns using their indices ---
                // Column 0: Name
                gridView.Columns[0].Width = unitWidth * 1.5;

                // Column 1: Code
                gridView.Columns[1].Width = unitWidth * 1.0;

                // Column 2: Actions
                gridView.Columns[2].Width = actionColumnWidth;
            }
        }

        // عرض بيانات المخزن المحدد في الحقول
        private void DisplayWarehouseDetails()
        {
            if (_selectedWarehouse != null)
            {
                txtName.Text = _selectedWarehouse.Name;
                txtCode.Text = _selectedWarehouse.Code;
            }
        }

        private void TextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (sender is TextBox textBox && !string.IsNullOrEmpty(textBox.Tag?.ToString()))
            {
                if (textBox.Text == textBox.Tag.ToString())
                {
                    textBox.Text = "";
                    textBox.Foreground = System.Windows.Media.Brushes.Black;
                }
            }
        }

        private async void EditButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is Warehouse warehouse)
            {
                _selectedWarehouse = warehouse;
                DisplayWarehouseDetails();
            }
        }

        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is Warehouse warehouse)
            {
                var result = QuestionBox.Show($"هل أنت متأكد من حذف المخزن '{warehouse.Name}'؟",
                    "تأكيد الحذف");

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var deleteResult = await _warehouseService.DeleteWarehouseAsync(warehouse.Id);
                        if (deleteResult.State)
                        {
                            DialogBox.Show("تم حذف المخزن بنجاح", "نجح");
                            await LoadWarehousesAsync();
                            ClearForm();
                        }
                        else
                        {
                            ErrorBox.Show(deleteResult.Message, "خطأ في الحذف", true);
                        }
                    }
                    catch (Exception ex)
                    {
                        ErrorBox.Show($"حدث خطأ أثناء حذف المخزن: {ex.Message}", "خطأ", true);
                    }
                }
            }
        }
    }
}
