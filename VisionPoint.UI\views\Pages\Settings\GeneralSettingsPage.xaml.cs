﻿using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using VisionPoint.UI.Converters;
using VisionPoint.UI.views.Dialogs;
using VisionPoint.UI.views.Pages.Expire;
using VisionPoint.UI.views.Pages.MinimumQuantity;
using VisionPoint.UI.views.Pages.ProductsContent;

namespace VisionPoint.UI.views.Pages.Settings
{
    /// <summary>
    /// Interaction logic for GeneralSettingsPage.xaml
    /// </summary>
    public partial class GeneralSettingsPage : Page
    {
        private ExpensesPage _expensesPage;
        public GeneralSettingsPage()
        {
            InitializeComponent();
        }

        private void Page_Loaded(object sender, RoutedEventArgs e)
        {
            UpdateButtonsVisibility();
            chkAllowNegativeQuantities.IsChecked = GetAllowNegativeQuantities();
            chkEnableMinimumQuantityCheck.IsChecked = GetEnableMinimumQuantityCheck();

        }

        public bool GetEnableMinimumQuantityCheck()
        {
            try
            {
                return Properties.Settings.Default.EnableMinimumQuantityCheck;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على إعداد تفعيل فحص الحد الأدنى للكمية: {ex.Message}");
                return true; // القيمة الافتراضية هي تفعيل الفحص
            }
        }
        public bool GetAllowNegativeQuantities()
        {
            try
            {
                return Properties.Settings.Default.AllowNegativeQuantities;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على إعداد السماح بالكميات بالسالب: {ex.Message}");
                return false;
            }
        }
        private void UpdateButtonsVisibility()
        {
            // التحكم في ظهور الأزرار بناءً على صلاحيات المستخدم
            bool hasExpenseAccess = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ExpenseRole");

            btnExpenses.Visibility = hasExpenseAccess ? System.Windows.Visibility.Visible : System.Windows.Visibility.Collapsed;
            btnDiscount.Visibility = CurrentUser.HasRole("Admin") ? System.Windows.Visibility.Visible : System.Windows.Visibility.Collapsed;
        }



        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnDiscount.IsEnabled = false;
            btnExipres.IsEnabled = false;
            btnMinimumQuantity.IsEnabled = false;
            btnExpenses.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnDiscount.IsEnabled = true;
            btnExipres.IsEnabled = true;
            btnMinimumQuantity.IsEnabled = true;
            btnExpenses.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private void btnDiscount_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (!CheckRole(new[] { "Admin" }))
                    return;
                DiscountWindow discountWindow = new DiscountWindow();
                discountWindow.ShowDialog();
            }
            finally
            {
                EnableAllButtons();
            }
        }


        private void btnExipres_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                ExpireWindow expireWindow = new ExpireWindow(true);
                expireWindow.ShowDialog();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnMinimumQuantity_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                MinimumQuantityWindow minimumQuantityWindow = new MinimumQuantityWindow(true);
                minimumQuantityWindow.ShowDialog();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnExpenses_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (!CheckRole(new[] { "Admin", "ExpenseRole" }))
                    return;
                _expensesPage = new ExpensesPage(ServiceLocator.GetService<PL.ExpenseService>());
                _expensesPage.Closed += (s, args) => _expensesPage = null;
                _expensesPage.ShowDialog();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        bool CheckRole(string[] roles)
        {
            if (!CurrentUser.HasAnyRole(roles))
            {
                ErrorBox.Show("لا تملك صلاحية الوصول لهذه الصفحة", "خطأ في الصلاحيات", true);
                return false;
            }
            return true;
        }
        private void chkAllowNegativeQuantities_Checked(object sender, RoutedEventArgs e)
        {
            SetAllowNegativeQuantities(true);
        }

        private void chkAllowNegativeQuantities_Unchecked(object sender, RoutedEventArgs e)
        {
            SetAllowNegativeQuantities(false);
        }

        public void SetAllowNegativeQuantities(bool allow)
        {
            if (!CheckRole(new[] { "Admin" }))
                return;
            try
            {
                Properties.Settings.Default.AllowNegativeQuantities = allow;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تعيين إعداد السماح بالكميات بالسالب: {ex.Message}");
            }
        }

        public void SetEnableMinimumQuantityCheck(bool enable)
        {
            if (!CheckRole(new[] { "Admin" }))
                return;
            try
            {
                Properties.Settings.Default.EnableMinimumQuantityCheck = enable;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تعيين إعداد تفعيل فحص الحد الأدنى للكمية: {ex.Message}");
            }
        }

        private void chkEnableMinimumQuantityCheck_Checked(object sender, RoutedEventArgs e)
        {
            SetEnableMinimumQuantityCheck(true);
        }

        private void chkEnableMinimumQuantityCheck_Unchecked(object sender, RoutedEventArgs e)
        {
            SetEnableMinimumQuantityCheck(false);
        }

    }
}
