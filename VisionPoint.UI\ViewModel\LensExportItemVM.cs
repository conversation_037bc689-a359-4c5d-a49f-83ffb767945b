using Microsoft.EntityFrameworkCore;

namespace VisionPoint.UI.ViewModel;

/// <summary>
/// نموذج عنصر العدسة المسطح للتصدير والمعاينة
/// يحتوي على جميع البيانات المطلوبة من العدسة والوصفة واللون والكمية
/// </summary>
public class LensExportItemVM
{
    // بيانات العدسة الأساسية
    public int LensId { get; set; }
    public string Name { get; set; } = string.Empty;
    [Precision(18, 3)] public decimal? BC { get; set; }
    [Precision(18, 3)] public decimal? Dia { get; set; }
    [Precision(18, 3)] public decimal? Addtion { get; set; }
    [Precision(18, 3)] public decimal? Axis { get; set; }
    public string CategoryName { get; set; } = string.Empty;

    // بيانات الوصفة
    public int? PrescriptionId { get; set; }
    public decimal? SphereValue { get; set; }
    public decimal? CylinderValue { get; set; }
    public decimal? PowerValue { get; set; }
    [Precision(18, 3)] public decimal CostPrice { get; set; } = decimal.Zero;
    [Precision(18, 3)] public decimal SellPrice { get; set; } = decimal.Zero;

    // بيانات اللون
    public int? ColorId { get; set; }
    public string ColorName { get; set; } = string.Empty;
    public string ColorHexCode { get; set; } = string.Empty;
    public string Barcode { get; set; } = string.Empty;

    // بيانات الكمية
    public int QuantityId { get; set; }
    public int Quantity { get; set; }
    public DateOnly? Expiration { get; set; }
    public string WarehouseName { get; set; } = string.Empty;

    // خصائص محسوبة للعرض
    public string SPH => SphereValue?.ToString("F2") ?? "";
    public string CYL => CylinderValue?.ToString("F2") ?? "";
    public string POW => PowerValue?.ToString("F2") ?? "";
    public string Cost => CostPrice.ToString("F3");
    public string Price => SellPrice.ToString("F3");
    public string HexCode => ColorHexCode;
    public string DIA => Dia?.ToString("F3") ?? "";
    public string ADD => Addtion?.ToString("F3") ?? "";
    public string AXIS => Axis?.ToString("F3") ?? "";
    public string Category => CategoryName;
    public string Warehouse => WarehouseName;
}
