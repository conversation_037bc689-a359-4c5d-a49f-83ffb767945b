﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Microsoft.Win32;
using VisionPoint.UI.views.Dialogs;
using Path = System.IO.Path;

namespace VisionPoint.UI.views.Pages.Settings
{
    /// <summary>
    /// Interaction logic for BackupSettingsPage.xaml
    /// </summary>
    public partial class BackupSettingsPage : Page
    {
        public BackupSettingsPage()
        {
            InitializeComponent();
            LoadBackupSettings();
        }

        private void LoadBackupSettings()
        {
            chkAutoBackupEnabled.IsChecked = GetAutoBackupEnabled();
            txtAutoBackupPath.Text = GetAutoBackupPath();
            txtAutoBackupMaxCount.Text = GetAutoBackupMaxCount().ToString();
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnBackup.IsEnabled = false;
            btnRestore.IsEnabled = false;
            btnBrowseAutoBackupPath.IsEnabled = false;
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnBackup.IsEnabled = true;
            btnRestore.IsEnabled = true;
            btnBrowseAutoBackupPath.IsEnabled = true;
        }

        private void btnBackup_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (!CheckRole(new[] { "Admin", "BackupRole" }))
                    return;

                var backupDialog = new DatabaseBackupDialog(isRestore: false);
                backupDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnRestore_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (!CheckRole(new[] { "Admin", "BackupRole" }))
                    return;

                var result = QuestionBox.Show(
                    "تأكيد استعادة النسخة الاحتياطية",
                    "سيتم استبدال قاعدة البيانات الحالية بالنسخة الاحتياطية. هل أنت متأكد من المتابعة؟");

                if (result == MessageBoxResult.Yes)
                {
                    var restoreDialog = new DatabaseBackupDialog(isRestore: true);
                    if (restoreDialog.ShowDialog() == true)
                    {
                        DialogBox.Show(
                            "استعادة ناجحة",
                            "تم استعادة قاعدة البيانات بنجاح. يرجى إعادة تشغيل التطبيق لتطبيق التغييرات.");
                        Application.Current.Shutdown();
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء استعادة النسخة الاحتياطية: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        public bool GetAutoBackupEnabled()
        {
            try
            {
                return Properties.Settings.Default.AutoBackupEnabled;
            }
            catch
            {
                return false;
            }
        }

        public void SetAutoBackupEnabled(bool enabled)
        {
            if (!CheckRole(new[] { "Admin", "BackupRole" }))
                return;
            try
            {
                Properties.Settings.Default.AutoBackupEnabled = enabled;
                Properties.Settings.Default.Save();
            }
            catch { }
        }

        private void chkAutoBackupEnabled_Checked(object sender, RoutedEventArgs e)
        {
            SetAutoBackupEnabled(true);
        }

        private void chkAutoBackupEnabled_Unchecked(object sender, RoutedEventArgs e)
        {
            SetAutoBackupEnabled(false);
        }

        public string GetAutoBackupPath()
        {
            try
            {
                return Properties.Settings.Default.AutoBackupPath;
            }
            catch
            {
                return string.Empty;
            }
        }

        public void SetAutoBackupPath(string path)
        {
            if (!CheckRole(new[] { "Admin", "BackupRole" }))
                return;
            try
            {
                Properties.Settings.Default.AutoBackupPath = path;
                Properties.Settings.Default.Save();
            }
            catch { }
        }

        private void btnBrowseAutoBackupPath_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (!CheckRole(new[] { "Admin", "BackupRole" }))
                    return;

                var dialog = new SaveFileDialog
                {
                    Title = "اختر مجلد النسخ الاحتياطي التلقائي",
                    FileName = "اختر هذا المجلد",
                    CheckPathExists = true,
                    ValidateNames = false,
                    CheckFileExists = false,
                    OverwritePrompt = false
                };

                if (!string.IsNullOrEmpty(txtAutoBackupPath.Text) && Directory.Exists(txtAutoBackupPath.Text))
                {
                    dialog.InitialDirectory = txtAutoBackupPath.Text;
                }

                if (dialog.ShowDialog() == true)
                {
                    string directoryPath = Path.GetDirectoryName(dialog.FileName);
                    txtAutoBackupPath.Text = directoryPath;
                    SetAutoBackupPath(directoryPath);
                }
            }
            finally
            {
                EnableAllButtons();
            }
        }

        public int GetAutoBackupMaxCount()
        {
            try
            {
                return Properties.Settings.Default.AutoBackupMaxCount;
            }
            catch
            {
                return 5;
            }
        }

        public void SetAutoBackupMaxCount(int count)
        {
            if (!CheckRole(new[] { "Admin", "BackupRole" }))
                return;
            try
            {
                Properties.Settings.Default.AutoBackupMaxCount = count;
                Properties.Settings.Default.Save();
            }
            catch { }
        }

        private void txtAutoBackupMaxCount_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (!CheckRole(new[] { "Admin", "BackupRole" }))
                return;

            if (!string.IsNullOrEmpty(txtAutoBackupMaxCount.Text) && int.TryParse(txtAutoBackupMaxCount.Text, out int count))
            {
                if (count < 1)
                {
                    count = 1;
                    txtAutoBackupMaxCount.Text = "1";
                }
                SetAutoBackupMaxCount(count);
            }
            else if (string.IsNullOrEmpty(txtAutoBackupMaxCount.Text))
            {
                return;
            }
            else
            {
                SetAutoBackupMaxCount(5);
                txtAutoBackupMaxCount.Text = "5";
            }
        }

        private bool CheckRole(string[] roles)
        {
            if (!CurrentUser.HasAnyRole(roles))
            {
                ErrorBox.Show("لا تملك صلاحية الوصول لهذه الصفحة", "خطأ في الصلاحيات", true);
                return false;
            }
            return true;
        }
    }
}
