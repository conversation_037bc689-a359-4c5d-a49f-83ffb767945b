﻿<Window
    x:Class="VisionPoint.UI.views.Windows.LoginWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:VisionPoint.UI"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Windows"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="LoginWindow"
    AllowsTransparency="True"
    Background="Transparent"
    Closing="Window_Closing"
    Loaded="Window_Loaded"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Window.Icon>
        <BitmapImage UriSource="pack://application:,,,/Assets/Logo.png" />
    </Window.Icon>
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">
        <Grid Width="1920" Height="1080">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="0.8*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="0.8*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="0.6*" />
                <RowDefinition Height="*" />
                <RowDefinition Height="0.6*" />
            </Grid.RowDefinitions>



            <Border
                Grid.Row="1"
                Grid.Column="1"
                Background="#EEF4ED"
                CornerRadius="8">
                <Grid>
                    <DockPanel
                        Margin="0,32,0,0"
                        HorizontalAlignment="Center"
                        LastChildFill="False">

                        <Image
                            Width="98"
                            Height="98"
                            Margin="0,13"
                            Panel.ZIndex="10"
                            DockPanel.Dock="Top"
                            Source="pack://application:,,,/VisionPoint.UI;component/Assets/Logo.png"
                            Stretch="Fill" />



                        <TextBlock
                            Margin="0,13"
                            HorizontalAlignment="Center"
                            DockPanel.Dock="Top"
                            FontSize="32"
                            FontWeight="Regular"
                            TextAlignment="Center">
                            تسجيل الدخول
                        </TextBlock>
                        <Border
                            Width="363"
                            Height="60"
                            Margin="0,13"
                            Padding="4"
                            HorizontalAlignment="Center"
                            CornerRadius="8"
                            DockPanel.Dock="Top">
                            <Border.Background>
                                <SolidColorBrush Color="White" />
                            </Border.Background>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="32" />
                                    <ColumnDefinition />
                                </Grid.ColumnDefinitions>
                                <Grid Grid.Column="1">
                                    <TextBox
                                        x:Name="txtUserName"
                                        BorderThickness="0"
                                        FlowDirection="RightToLeft"
                                        FontSize="18"
                                        KeyDown="txtUserName_KeyDown"
                                        SelectionOpacity="0"
                                        Style="{DynamicResource UserTxt}"
                                        Tag="اسم المستخدم"
                                        Text="Admin" />
                                </Grid>
                                <Image Width="24" Source="{StaticResource userLogin}" />

                            </Grid>



                        </Border>
                        <Border
                            Width="363"
                            Height="60"
                            Margin="0,13"
                            Padding="4"
                            HorizontalAlignment="Center"
                            Background="White"
                            CornerRadius="8"
                            DockPanel.Dock="Top">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="32" />
                                    <ColumnDefinition />
                                </Grid.ColumnDefinitions>
                                <Grid Grid.Column="1">
                                    <PasswordBox
                                        x:Name="TxtPassword"
                                        Padding="10,0,10,0"
                                        HorizontalContentAlignment="Right"
                                        VerticalContentAlignment="Stretch"
                                        BorderThickness="0"
                                        FlowDirection="RightToLeft"
                                        FontSize="18"
                                        KeyDown="TxtPassword_KeyDown"
                                        Tag="كلمة المرور" />
                                </Grid>
                                <Image Width="24" Source="{StaticResource PasswordLogin}" />

                            </Grid>
                        </Border>

                        <Grid
                            Height="44"
                            Margin="0,13,0,0"
                            DockPanel.Dock="Top">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition />
                            </Grid.ColumnDefinitions>
                            <Border
                                x:Name="btnClose"
                                Margin="13,0"
                                Background="Transparent"
                                BorderBrush="#134074"
                                BorderThickness="1.5"
                                CornerRadius="8"
                                Cursor="Hand"
                                MouseLeftButtonDown="btnClose_MouseLeftButtonDown">
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FontSize="18"
                                    FontWeight="Bold"
                                    Foreground="{StaticResource PrimaryColor}">
                                    خروج
                                </TextBlock>
                            </Border>

                            <Border
                                x:Name="btnLogin"
                                Grid.Column="1"
                                Margin="13,0"
                                Background="#134074"
                                CornerRadius="8"
                                Cursor="Hand"
                                MouseLeftButtonDown="btnLogin_MouseLeftButtonDown">
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FontSize="18"
                                    FontWeight="Bold"
                                    Foreground="White">
                                    تسجيل الدخول
                                </TextBlock>
                            </Border>

                        </Grid>

                    </DockPanel>

                    <!--  Loading Overlay  -->
                    <Grid
                        x:Name="LoadingOverlay"
                        Background="#80000000"
                        Visibility="Collapsed">
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <ProgressBar
                                Width="100"
                                Height="100"
                                IsIndeterminate="True"
                                Style="{DynamicResource MaterialDesignCircularProgressBar}" />
                            <TextBlock
                                Margin="0,10,0,0"
                                HorizontalAlignment="Center"
                                FontSize="16"
                                Foreground="White"
                                Text="جاري تهيئة البيانات..." />
                        </StackPanel>
                    </Grid>
                </Grid>
            </Border>
        </Grid>
    </Viewbox>
</Window>

