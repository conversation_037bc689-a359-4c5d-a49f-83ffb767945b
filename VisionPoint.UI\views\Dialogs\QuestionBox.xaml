﻿<Window
    x:Class="VisionPoint.UI.views.Dialogs.QuestionBox"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:VisionPoint.UI"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Dialogs"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="QuestionBox"
    AllowsTransparency="True"
    Background="Transparent"
    Closing="Window_Closing"
    FontFamily="Times New Roman"
    ResizeMode="NoResize"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">
        <Grid Width="1920" Height="1080">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Grid
                Grid.Row="1"
                Grid.Column="1"
                Width="auto"
                Height="auto">
                <Border
                    Width="100"
                    Height="100"
                    VerticalAlignment="Top"
                    Panel.ZIndex="10"
                    Background="{StaticResource backgroundColor}"
                    BorderBrush="LightGray"
                    BorderThickness="4"
                    CornerRadius="50">
                    <Image
                        x:Name="iconBox"
                        Height="50"
                        Source="{StaticResource questionIcon}" />
                </Border>
                <Border
                    MinHeight="200"
                    MaxWidth="720"
                    Margin="0,50,0,0"
                    VerticalAlignment="Bottom"
                    Background="{StaticResource backgroundColor}"
                    BorderThickness="1.5"
                    CornerRadius="20">
                    <Border.BorderBrush>
                        <SolidColorBrush Opacity="0.4" Color="Black" />
                    </Border.BorderBrush>
                    <DockPanel LastChildFill="False">

                        <TextBlock
                            x:Name="txtHeader"
                            Margin="8,64,8,24"
                            HorizontalAlignment="Center"
                            DockPanel.Dock="Top"
                            FontSize="20"
                            FontWeight="Bold"
                            Foreground="#333333" />

                        <ScrollViewer
                            MaxHeight="300"
                            Margin="0,0,0,10"
                            DockPanel.Dock="Top"
                            HorizontalScrollBarVisibility="Disabled"
                            VerticalScrollBarVisibility="Auto">
                            <TextBlock
                                x:Name="txtMessage"
                                Margin="15,0,15,5"
                                Padding="5"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Top"
                                FontSize="18"
                                FontWeight="SemiBold"
                                Foreground="#64748B"
                                TextAlignment="Center"
                                TextWrapping="Wrap" />
                        </ScrollViewer>

                        <DockPanel
                            Margin="12,15,12,20"
                            DockPanel.Dock="Bottom"
                            LastChildFill="False">
                            <Border
                                x:Name="btnNo"
                                Width="120"
                                Height="35"
                                Margin="8,0"
                                Background="#E74C3C"
                                CornerRadius="18"
                                Cursor="Hand"
                                DockPanel.Dock="Left"
                                MouseLeftButtonDown="btnNo_MouseLeftButtonUp">
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FontSize="17"
                                    FontWeight="Bold"
                                    Foreground="#fff">
                                    لا
                                </TextBlock>
                            </Border>

                            <Border
                                x:Name="btnYes"
                                Width="120"
                                Height="35"
                                Margin="8,0"
                                Background="#5F9CE3"
                                CornerRadius="18"
                                Cursor="Hand"
                                DockPanel.Dock="Right"
                                MouseLeftButtonDown="btnYes_MouseLeftButtonUp">
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FontSize="17"
                                    FontWeight="Bold"
                                    Foreground="#fff">
                                    نعم
                                </TextBlock>
                            </Border>
                        </DockPanel>
                    </DockPanel>
                </Border>
            </Grid>
        </Grid>
    </Viewbox>
</Window>
