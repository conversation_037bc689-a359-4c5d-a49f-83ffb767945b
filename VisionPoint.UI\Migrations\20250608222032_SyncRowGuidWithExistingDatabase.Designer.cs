﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using VisionPoint.UI.Models;

#nullable disable

namespace VisionPoint.UI.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20250608222032_SyncRowGuidWithExistingDatabase")]
    partial class SyncRowGuidWithExistingDatabase
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.13")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Client", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("AllowedBalance")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal>("Balance")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("int");

                    b.Property<bool>("IsCustomer")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSupplier")
                        .HasColumnType("bit");

                    b.Property<int?>("ModifiedById")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Phone")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("WarehouseId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("IsCustomer");

                    b.HasIndex("IsSupplier");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("Phone");

                    b.HasIndex("WarehouseId");

                    b.ToTable("Clients");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Color", b =>
                {
                    b.Property<byte>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<byte>("Id"));

                    b.Property<string>("HexCode")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.HasKey("Id");

                    b.ToTable("Colors");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Discount", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("ApplyToAllLenses")
                        .HasColumnType("bit");

                    b.Property<bool>("ApplyToAllProducts")
                        .HasColumnType("bit");

                    b.Property<bool>("ApplyToAllServices")
                        .HasColumnType("bit");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("int");

                    b.Property<decimal>("DiscountPercentage")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ModifiedById")
                        .HasColumnType("int");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.ToTable("Discounts");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.DiscountLens", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("DiscountId")
                        .HasColumnType("int");

                    b.Property<int>("LensId")
                        .HasColumnType("int");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.HasKey("Id");

                    b.HasIndex("DiscountId");

                    b.HasIndex("LensId");

                    b.ToTable("DiscountLenses");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.DiscountProduct", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("DiscountId")
                        .HasColumnType("int");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.HasKey("Id");

                    b.HasIndex("DiscountId");

                    b.HasIndex("ProductId");

                    b.ToTable("DiscountProducts");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.DiscountService", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("DiscountId")
                        .HasColumnType("int");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<int>("ServiceId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DiscountId");

                    b.HasIndex("ServiceId");

                    b.ToTable("DiscountServices");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Expense", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedById")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("Name");

                    b.ToTable("Expenses");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Financial", b =>
                {
                    b.Property<byte>("Id")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedById")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Financials");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Lens", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("Addtion")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("Axis")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("BC")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("CategoryId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("int");

                    b.Property<bool>("Cylinder")
                        .HasColumnType("bit");

                    b.Property<decimal?>("Dia")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<bool>("Exp")
                        .HasColumnType("bit");

                    b.Property<int>("MinimumQuantity")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedById")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("Power")
                        .HasColumnType("bit");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<bool>("Sphere")
                        .HasColumnType("bit");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Lenses");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.LensCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int?>("ModifiedById")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("LensCategories");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.LensPrescription", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("CostPrice")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<short?>("CylinderId")
                        .HasColumnType("smallint");

                    b.Property<int?>("LensId")
                        .HasColumnType("int");

                    b.Property<short?>("PowId")
                        .HasColumnType("smallint");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<decimal>("SellPrice")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<short?>("SphereId")
                        .HasColumnType("smallint");

                    b.HasKey("Id");

                    b.HasIndex("CylinderId");

                    b.HasIndex("LensId");

                    b.HasIndex("PowId");

                    b.HasIndex("SphereId");

                    b.HasIndex("LensId", "SphereId", "CylinderId", "PowId");

                    b.ToTable("LensPrescriptions");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.LensPrescriptionColor", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Barcode")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<byte?>("ColorId")
                        .HasColumnType("tinyint");

                    b.Property<int?>("LensPrescriptionId")
                        .HasColumnType("int");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.HasKey("Id");

                    b.HasIndex("Barcode")
                        .IsUnique()
                        .HasFilter("[Barcode] IS NOT NULL");

                    b.HasIndex("ColorId");

                    b.HasIndex("LensPrescriptionId");

                    b.ToTable("LensPrescriptionColors");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.LensQuantity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateOnly?>("Exp")
                        .HasColumnType("date");

                    b.Property<int?>("LensPrescriptionColorId")
                        .HasColumnType("int");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<int>("WarehouseId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Exp");

                    b.HasIndex("LensPrescriptionColorId");

                    b.HasIndex("WarehouseId");

                    b.HasIndex("LensPrescriptionColorId", "Exp");

                    b.HasIndex("LensPrescriptionColorId", "WarehouseId");

                    b.HasIndex("LensPrescriptionColorId", "WarehouseId", "Exp");

                    b.ToTable("LensQuantities");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Prescription", b =>
                {
                    b.Property<short>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("smallint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<short>("Id"));

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<decimal>("Value")
                        .HasPrecision(5, 2)
                        .HasColumnType("decimal(5,2)");

                    b.HasKey("Id");

                    b.ToTable("Prescriptions");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Color")
                        .HasColumnType("bit");

                    b.Property<decimal>("CostPrice")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("int");

                    b.Property<bool>("Exp")
                        .HasColumnType("bit");

                    b.Property<int>("MinimumQuantity")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedById")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<decimal>("SellPrice")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Products");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.ProductColor", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Barcode")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<byte?>("ColorId")
                        .HasColumnType("tinyint");

                    b.Property<int?>("ProductId")
                        .HasColumnType("int");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.HasKey("Id");

                    b.HasIndex("Barcode")
                        .IsUnique()
                        .HasFilter("[Barcode] IS NOT NULL");

                    b.HasIndex("ColorId");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductColors");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.ProductQuantity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateOnly?>("Exp")
                        .HasColumnType("date");

                    b.Property<int?>("ProductColorId")
                        .HasColumnType("int");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<int>("WarehouseId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Exp");

                    b.HasIndex("ProductColorId");

                    b.HasIndex("WarehouseId");

                    b.HasIndex("ProductColorId", "Exp");

                    b.HasIndex("ProductColorId", "WarehouseId");

                    b.HasIndex("ProductColorId", "WarehouseId", "Exp");

                    b.ToTable("ProductQuantities");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Purchase", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("int");

                    b.Property<int>("InvoiceNo")
                        .HasColumnType("int");

                    b.Property<string>("InvoiceNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("ModifiedById")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime>("PurchaseDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<decimal>("TotalAmount")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("WarehouseId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("InvoiceNo");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PurchaseDate");

                    b.HasIndex("WarehouseId");

                    b.HasIndex("ClientId", "PurchaseDate");

                    b.HasIndex("WarehouseId", "InvoiceNo");

                    b.ToTable("Purchases");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.PurchaseItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("int");

                    b.Property<DateOnly?>("Exp")
                        .HasColumnType("date");

                    b.Property<int?>("LensPrescriptionColorId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedById")
                        .HasColumnType("int");

                    b.Property<decimal>("Price")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("ProductColorId")
                        .HasColumnType("int");

                    b.Property<int>("PurchaseId")
                        .HasColumnType("int");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("LensPrescriptionColorId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("ProductColorId");

                    b.HasIndex("PurchaseId");

                    b.ToTable("PurchaseItems");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Receipt", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("ClientId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("int");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<int?>("EmployeeId")
                        .HasColumnType("int");

                    b.Property<int?>("ExpenseId")
                        .HasColumnType("int");

                    b.Property<byte?>("FinancialId")
                        .HasColumnType("tinyint");

                    b.Property<bool?>("IsExchange")
                        .HasColumnType("bit");

                    b.Property<int?>("ModifiedById")
                        .HasColumnType("int");

                    b.Property<int?>("PurchaseId")
                        .HasColumnType("int");

                    b.Property<int>("ReceiptNo")
                        .HasColumnType("int");

                    b.Property<string>("ReceiptNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<int?>("SaleId")
                        .HasColumnType("int");

                    b.Property<byte?>("SourceTreasuryId")
                        .HasColumnType("tinyint");

                    b.Property<string>("Statement")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<byte?>("TargetTreasuryId")
                        .HasColumnType("tinyint");

                    b.Property<byte?>("TreasuryId")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("Value")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("WarehouseId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("Date");

                    b.HasIndex("EmployeeId");

                    b.HasIndex("ExpenseId");

                    b.HasIndex("FinancialId");

                    b.HasIndex("IsExchange");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PurchaseId");

                    b.HasIndex("ReceiptNo");

                    b.HasIndex("SaleId");

                    b.HasIndex("SourceTreasuryId");

                    b.HasIndex("TargetTreasuryId");

                    b.HasIndex("TreasuryId");

                    b.HasIndex("WarehouseId");

                    b.ToTable("Receipts");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("VisionPoint.UI.Models.SalaryPayment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedById")
                        .HasColumnType("int");

                    b.Property<int>("Month")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime>("PaymentDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ReceiptId")
                        .HasColumnType("int");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<int>("Year")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PaymentDate");

                    b.HasIndex("ReceiptId");

                    b.HasIndex("UserId");

                    b.ToTable("SalaryPayments");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Sale", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("int");

                    b.Property<int>("InvoiceNo")
                        .HasColumnType("int");

                    b.Property<string>("InvoiceNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("ModifiedById")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<DateTime>("SaleDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("TotalAmount")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal>("TotalBeforeDiscount")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal>("TotalDiscount")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal>("TotalReturned")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("WarehouseId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("InvoiceNo");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("SaleDate");

                    b.HasIndex("WarehouseId");

                    b.HasIndex("ClientId", "SaleDate");

                    b.HasIndex("WarehouseId", "InvoiceNo");

                    b.ToTable("Sales");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.SaleItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("Axis")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal>("CostPrice")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("int");

                    b.Property<decimal>("Discount")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("LensQuantityLeftId")
                        .HasColumnType("int");

                    b.Property<int?>("LensQuantityRightId")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedById")
                        .HasColumnType("int");

                    b.Property<decimal>("OriginalPrice")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<Guid?>("PairId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("ProductQuantityId")
                        .HasColumnType("int");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<int>("ReturnedQuantity")
                        .HasColumnType("int");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<int>("SaleId")
                        .HasColumnType("int");

                    b.Property<decimal>("SellPrice")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("ServiceId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("LensQuantityLeftId");

                    b.HasIndex("LensQuantityRightId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PairId");

                    b.HasIndex("ProductQuantityId");

                    b.HasIndex("SaleId");

                    b.HasIndex("ServiceId");

                    b.ToTable("SaleItems");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Service", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CreatedById")
                        .HasColumnType("int");

                    b.Property<int?>("ModifiedById")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal>("Price")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Services");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Treasury", b =>
                {
                    b.Property<byte>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<byte>("Id"));

                    b.Property<decimal>("Balance")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("WarehouseId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("WarehouseId");

                    b.ToTable("Treasuries");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<bool>("AllowBalanceOverride")
                        .HasColumnType("bit");

                    b.Property<decimal>("Balance")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<decimal>("Salary")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int?>("WarehouseId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.HasIndex("WarehouseId");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Warehouse", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("RowGuid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rowguid")
                        .HasDefaultValueSql("newsequentialid()");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Warehouses");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.Role", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.Role", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("VisionPoint.UI.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Client", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.User", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.Warehouse", "Warehouse")
                        .WithMany("Clients")
                        .HasForeignKey("WarehouseId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Warehouse");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Discount", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.User", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.DiscountLens", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.Discount", "Discount")
                        .WithMany("DiscountLenses")
                        .HasForeignKey("DiscountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("VisionPoint.UI.Models.Lens", "Lens")
                        .WithMany()
                        .HasForeignKey("LensId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Discount");

                    b.Navigation("Lens");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.DiscountProduct", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.Discount", "Discount")
                        .WithMany("DiscountProducts")
                        .HasForeignKey("DiscountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("VisionPoint.UI.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Discount");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.DiscountService", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.Discount", "Discount")
                        .WithMany("DiscountServices")
                        .HasForeignKey("DiscountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("VisionPoint.UI.Models.Service", "Service")
                        .WithMany()
                        .HasForeignKey("ServiceId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Discount");

                    b.Navigation("Service");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Expense", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.User", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Financial", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.User", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Lens", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.LensCategory", "Category")
                        .WithMany("Lenses")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.User", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Category");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.LensCategory", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.User", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.LensPrescription", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.Prescription", "Cylinder")
                        .WithMany()
                        .HasForeignKey("CylinderId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.Lens", "Lens")
                        .WithMany("LensPrescriptions")
                        .HasForeignKey("LensId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("VisionPoint.UI.Models.Prescription", "Pow")
                        .WithMany()
                        .HasForeignKey("PowId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.Prescription", "Sphere")
                        .WithMany()
                        .HasForeignKey("SphereId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Cylinder");

                    b.Navigation("Lens");

                    b.Navigation("Pow");

                    b.Navigation("Sphere");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.LensPrescriptionColor", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.Color", "Color")
                        .WithMany()
                        .HasForeignKey("ColorId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.LensPrescription", "LensPrescription")
                        .WithMany("LensPrescriptionColors")
                        .HasForeignKey("LensPrescriptionId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Color");

                    b.Navigation("LensPrescription");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.LensQuantity", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.LensPrescriptionColor", "LensPrescriptionColor")
                        .WithMany("LensQuantity")
                        .HasForeignKey("LensPrescriptionColorId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("VisionPoint.UI.Models.Warehouse", "Warehouse")
                        .WithMany("LensQuantities")
                        .HasForeignKey("WarehouseId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("LensPrescriptionColor");

                    b.Navigation("Warehouse");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Product", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.User", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.ProductColor", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.Color", "Color")
                        .WithMany()
                        .HasForeignKey("ColorId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.Product", "Product")
                        .WithMany("ProductColors")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Color");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.ProductQuantity", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.ProductColor", "ProductColor")
                        .WithMany("ProductQuantity")
                        .HasForeignKey("ProductColorId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("VisionPoint.UI.Models.Warehouse", "Warehouse")
                        .WithMany("ProductQuantities")
                        .HasForeignKey("WarehouseId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("ProductColor");

                    b.Navigation("Warehouse");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Purchase", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("VisionPoint.UI.Models.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.User", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.Warehouse", "Warehouse")
                        .WithMany("Purchases")
                        .HasForeignKey("WarehouseId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Warehouse");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.PurchaseItem", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.LensPrescriptionColor", "LensPrescriptionColor")
                        .WithMany()
                        .HasForeignKey("LensPrescriptionColorId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.User", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.ProductColor", "ProductColor")
                        .WithMany()
                        .HasForeignKey("ProductColorId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.Purchase", "Purchase")
                        .WithMany("PurchaseItems")
                        .HasForeignKey("PurchaseId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("LensPrescriptionColor");

                    b.Navigation("ModifiedBy");

                    b.Navigation("ProductColor");

                    b.Navigation("Purchase");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Receipt", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.User", "Employee")
                        .WithMany()
                        .HasForeignKey("EmployeeId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.Expense", "Expense")
                        .WithMany()
                        .HasForeignKey("ExpenseId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.Financial", "Financial")
                        .WithMany("Receipts")
                        .HasForeignKey("FinancialId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.User", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.Purchase", "Purchase")
                        .WithMany("Receipts")
                        .HasForeignKey("PurchaseId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.Sale", "Sale")
                        .WithMany("Receipts")
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.Treasury", "SourceTreasury")
                        .WithMany()
                        .HasForeignKey("SourceTreasuryId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.Treasury", "TargetTreasury")
                        .WithMany()
                        .HasForeignKey("TargetTreasuryId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.Treasury", "Treasury")
                        .WithMany()
                        .HasForeignKey("TreasuryId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.Warehouse", "Warehouse")
                        .WithMany()
                        .HasForeignKey("WarehouseId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("Employee");

                    b.Navigation("Expense");

                    b.Navigation("Financial");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Purchase");

                    b.Navigation("Sale");

                    b.Navigation("SourceTreasury");

                    b.Navigation("TargetTreasury");

                    b.Navigation("Treasury");

                    b.Navigation("Warehouse");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.SalaryPayment", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.User", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.Receipt", "Receipt")
                        .WithMany()
                        .HasForeignKey("ReceiptId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Receipt");

                    b.Navigation("User");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Sale", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("VisionPoint.UI.Models.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.User", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.Warehouse", "Warehouse")
                        .WithMany("Sales")
                        .HasForeignKey("WarehouseId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Warehouse");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.SaleItem", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.LensQuantity", "LensQuantityLeft")
                        .WithMany()
                        .HasForeignKey("LensQuantityLeftId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.LensQuantity", "LensQuantityRight")
                        .WithMany()
                        .HasForeignKey("LensQuantityRightId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.User", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.ProductQuantity", "ProductQuantity")
                        .WithMany()
                        .HasForeignKey("ProductQuantityId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.Sale", "Sale")
                        .WithMany("SaleItems")
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("VisionPoint.UI.Models.Service", "Service")
                        .WithMany()
                        .HasForeignKey("ServiceId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("LensQuantityLeft");

                    b.Navigation("LensQuantityRight");

                    b.Navigation("ModifiedBy");

                    b.Navigation("ProductQuantity");

                    b.Navigation("Sale");

                    b.Navigation("Service");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Service", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("VisionPoint.UI.Models.User", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Treasury", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.Warehouse", "Warehouse")
                        .WithMany("Treasuries")
                        .HasForeignKey("WarehouseId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Warehouse");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.User", b =>
                {
                    b.HasOne("VisionPoint.UI.Models.Warehouse", "Warehouse")
                        .WithMany()
                        .HasForeignKey("WarehouseId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Warehouse");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Discount", b =>
                {
                    b.Navigation("DiscountLenses");

                    b.Navigation("DiscountProducts");

                    b.Navigation("DiscountServices");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Financial", b =>
                {
                    b.Navigation("Receipts");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Lens", b =>
                {
                    b.Navigation("LensPrescriptions");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.LensCategory", b =>
                {
                    b.Navigation("Lenses");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.LensPrescription", b =>
                {
                    b.Navigation("LensPrescriptionColors");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.LensPrescriptionColor", b =>
                {
                    b.Navigation("LensQuantity");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Product", b =>
                {
                    b.Navigation("ProductColors");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.ProductColor", b =>
                {
                    b.Navigation("ProductQuantity");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Purchase", b =>
                {
                    b.Navigation("PurchaseItems");

                    b.Navigation("Receipts");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Sale", b =>
                {
                    b.Navigation("Receipts");

                    b.Navigation("SaleItems");
                });

            modelBuilder.Entity("VisionPoint.UI.Models.Warehouse", b =>
                {
                    b.Navigation("Clients");

                    b.Navigation("LensQuantities");

                    b.Navigation("ProductQuantities");

                    b.Navigation("Purchases");

                    b.Navigation("Sales");

                    b.Navigation("Treasuries");
                });
#pragma warning restore 612, 618
        }
    }
}
