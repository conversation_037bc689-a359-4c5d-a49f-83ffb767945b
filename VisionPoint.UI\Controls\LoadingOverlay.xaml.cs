﻿using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using VisionPoint.UI.Services;
using System.ComponentModel; // Required for DesignerProperties

namespace VisionPoint.UI.Controls
{
    /// <summary>
    /// Interaction logic for LoadingOverlay.xaml
    /// </summary>
    public partial class LoadingOverlay : UserControl
    {
        private readonly BusyService _busyService;
        private static LoadingOverlay _mainInstance;
        private bool _isMainInstance;

        public LoadingOverlay()
        {
            InitializeComponent();

            // Check if we are in design mode
            if (!DesignerProperties.GetIsInDesignMode(this))
            {
                _busyService = VisionPoint.UI.Converters.ServiceLocator.GetService<BusyService>();

                if (_busyService != null)
                {
                    _busyService.PropertyChanged += BusyService_PropertyChanged;

                    // Determine if this is the main instance (in MainWindow)
                    _isMainInstance = _mainInstance == null;
                    if (_isMainInstance)
                    {
                        _mainInstance = this;
                    }

                    UpdateVisibility();
                }
                else
                {
                    OverlayGrid.Visibility = Visibility.Collapsed;
                }
            }
        }

        private void BusyService_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(BusyService.IsBusy) || e.PropertyName == nameof(BusyService.CurrentMessage))
            {
                UpdateVisibility();
            }
        }

        private void UpdateVisibility()
        {
            // This method is only called when _busyService is available (i.e., not in design mode)
            Dispatcher.Invoke(() =>
            {
                // Only show if this is the main instance and the service is busy
                if (_isMainInstance)
                {
                    OverlayGrid.Visibility = _busyService.IsBusy ? Visibility.Visible : Visibility.Collapsed;

                    // Update the message text
                    if (_busyService.IsBusy)
                    {
                        MessageText.Text = _busyService.CurrentMessage;
                    }
                }
                else
                {
                    // Hide any duplicate instances (page-level overlays)
                    OverlayGrid.Visibility = Visibility.Collapsed;
                }
            }, DispatcherPriority.Render);
        }

        public string Message
        {
            get { return (string)GetValue(MessageProperty); }
            set { SetValue(MessageProperty, value); }
        }

        // Using a DependencyProperty as the backing store for Message
        public static readonly DependencyProperty MessageProperty =
            DependencyProperty.Register("Message", typeof(string), typeof(LoadingOverlay),
                new PropertyMetadata("جاري تنفيذ العملية...", OnMessageChanged));

        private static void OnMessageChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is LoadingOverlay overlay)
            {
                // Ensure the MessageText element exists (it will in runtime and design time XAML)
                overlay.MessageText.Text = (string)e.NewValue;
            }
        }

        /// <summary>
        /// Gets a value indicating whether this is the main (application-level) loading overlay instance.
        /// </summary>
        public bool IsMainInstance => _isMainInstance;

        /// <summary>
        /// Forces this instance to be the main instance (used for MainWindow).
        /// </summary>
        public void SetAsMainInstance()
        {
            _isMainInstance = true;
            _mainInstance = this;
            UpdateVisibility();
        }

        /// <summary>
        /// Cleanup method to unsubscribe from events when the control is disposed.
        /// </summary>
        public void Cleanup()
        {
            if (_busyService != null)
            {
                _busyService.PropertyChanged -= BusyService_PropertyChanged;
            }

            if (_isMainInstance && _mainInstance == this)
            {
                _mainInstance = null;
            }
        }
    }
}