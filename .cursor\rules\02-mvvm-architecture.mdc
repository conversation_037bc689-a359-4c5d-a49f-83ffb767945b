---
description:
globs:
alwaysApply: false
---
# MVVM Architecture

VisionPoint follows the MVVM (Model-View-ViewModel) pattern, which separates the UI (View) from the business logic (ViewModel) and data (Model).

## Models
The application's data entities are defined in the [Models](mdc:VisionPoint.UI/Models/) directory. 
Key model files include:
- [BaseEntity.cs](mdc:VisionPoint.UI/Models/BaseEntity.cs): Base class for entities
- [Product.cs](mdc:VisionPoint.UI/Models/Product.cs): Product data model
- [Lens.cs](mdc:VisionPoint.UI/Models/Lens.cs): Lens data model
- [Sale.cs](mdc:VisionPoint.UI/Models/Sale.cs) and [SaleItem.cs](mdc:VisionPoint.UI/Models/SaleItem.cs): Sales data models
- [Purchase.cs](mdc:VisionPoint.UI/Models/Purchase.cs) and [PurchaseItem.cs](mdc:VisionPoint.UI/Models/PurchaseItem.cs): Purchase data models

## ViewModels
ViewModels connect the Models to the Views. They are located in the [ViewModel](mdc:VisionPoint.UI/ViewModel/) directory.
Key ViewModels include:
- [ProductVM.cs](mdc:VisionPoint.UI/ViewModel/ProductVM.cs)
- [LensVM.cs](mdc:VisionPoint.UI/ViewModel/LensVM.cs)
- [SaleViewModel.cs](mdc:VisionPoint.UI/ViewModel/SaleViewModel.cs)
- [PurchaseViewModel.cs](mdc:VisionPoint.UI/ViewModel/PurchaseViewModel.cs)

## Views
The UI components are organized in the [views](mdc:VisionPoint.UI/views/) directory, which contains:
- [Windows](mdc:VisionPoint.UI/views/Windows/): Main application windows
- [Pages](mdc:VisionPoint.UI/views/Pages/): Pages within the application
- [Dialogs](mdc:VisionPoint.UI/views/Dialogs/): Dialog windows
