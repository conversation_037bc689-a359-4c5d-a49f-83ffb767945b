﻿<Window
    x:Class="VisionPoint.UI.views.Pages.Receipts.AddReceiptWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:VisionPoint.UI"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Receipts"
    xmlns:local2="clr-namespace:VisionPoint.UI.Converters"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils"
    Title="AddReceiptWindow"
    AllowsTransparency="True"
    Background="Transparent"
    FlowDirection="RightToLeft"
    Loaded="Window_Loaded"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <local2:IndexToNumberConverter x:Key="IndexToNumberConverter" />
        </ResourceDictionary>
    </Window.Resources>
    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">
        <Grid Width="1920" Height="1080">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="0.8*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="0.8*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Border
                Grid.Row="1"
                Grid.Column="1"
                Background="{StaticResource backgroundColor}"
                BorderBrush="LightGray"
                BorderThickness="1.5"
                CornerRadius="16">


                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="60" />
                        <RowDefinition />
                    </Grid.RowDefinitions>

                    <TextBlock VerticalAlignment="Center" FontSize="28">
                        ايصال
                    </TextBlock>


                    <Border
                        x:Name="btnclose"
                        Width="24"
                        Height="24"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Top"
                        Background="Red"
                        CornerRadius="50"
                        MouseLeftButtonDown="btnclose_MouseLeftButtonDown" />

                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>

                        <!-- المخزن -->
                        <Grid Grid.Row="0" Grid.ColumnSpan="3" Margin="8,0">
                            <ComboBox
                                x:Name="cmbWarehouse"
                                FontSize="21"
                                Tag="المخزن"
                                SelectionChanged="cmbWarehouse_SelectionChanged" />
                        </Grid>

                        <!-- طريقة الدفع -->
                        <Grid Grid.Row="1" Grid.ColumnSpan="2" Margin="8,0">
                            <ComboBox
                                x:Name="cmbTreasury"
                                FontSize="21"
                                Tag="طريقة الدفع" />
                        </Grid>

                        <!-- القيمة -->
                        <Grid Grid.Row="1" Grid.Column="2" Margin="8,0">
                            <TextBox
                                x:Name="txtPaid"
                                Height="60"
                                VerticalAlignment="Stretch"
                                VerticalContentAlignment="Stretch"
                                utils:NumericInputControl.IsDecimalOnly="True"
                                BorderThickness="1"
                                FontSize="21"
                                Tag="القيمة"
                                TextChanged="txtPaid_TextChanged" />
                        </Grid>



                        <Border
                            x:Name="btnSave"
                            Grid.Row="2"
                            Grid.Column="0"
                            Grid.ColumnSpan="3"
                            MaxHeight="44"
                            Margin="8,16,8,0"
                            Background="{StaticResource PrimaryColor}"
                            CornerRadius="8"
                            Cursor="Hand"
                            MouseLeftButtonDown="btnSave_MouseLeftButtonDown">
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="18"
                                Foreground="White">
                                تاكيد
                            </TextBlock>
                        </Border>

                    </Grid>
                </Grid>
            </Border>
        </Grid>
    </Viewbox>
</Window>