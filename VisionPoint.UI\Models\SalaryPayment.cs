﻿﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace VisionPoint.UI.Models;

/// <summary>
/// نموذج لتتبع دفعات المرتبات الشهرية
/// </summary>
[Index(nameof(UserId))]
[Index(nameof(PaymentDate))]
public class SalaryPayment : BaseEntity
{
    /// <summary>
    /// معرف المستخدم
    /// </summary>
    public int UserId { get; set; }

    /// <summary>
    /// المستخدم
    /// </summary>
    public User User { get; set; }

    /// <summary>
    /// تاريخ الدفع
    /// </summary>
    public DateTime PaymentDate { get; set; }

    /// <summary>
    /// قيمة المرتب
    /// </summary>
    [Precision(18, 3)]
    public decimal Amount { get; set; }

    /// <summary>
    /// الشهر (1-12)
    /// </summary>
    public int Month { get; set; }

    /// <summary>
    /// السنة
    /// </summary>
    public int Year { get; set; }

    /// <summary>
    /// ملاحظات
    /// </summary>
    [StringLength(500, ErrorMessage = "الملاحظات يجب ألا تتجاوز 500 حرف")]
    public string Notes { get; set; }

    /// <summary>
    /// معرف الإيصال المرتبط بالدفع
    /// </summary>
    public int? ReceiptId { get; set; }

    /// <summary>
    /// الإيصال المرتبط بالدفع
    /// </summary>
    public Receipt Receipt { get; set; }
}
