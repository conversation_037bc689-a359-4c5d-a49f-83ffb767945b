<Window
    x:Class="VisionPoint.UI.views.Dialogs.ProductExportPreviewWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:VisionPoint.UI.Converters"
    Title="معاينة تصدير المنتجات"
    AllowsTransparency="True"
    Background="Transparent"
    FlowDirection="RightToLeft"
    Loaded="Window_Loaded"
    ResizeMode="NoResize"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None">

    <Window.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
        <converters:InverseBooleanConverter x:Key="InverseBoolConverter" />

    </Window.Resources>

    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">
        <Grid Width="1920" Height="1080">
            <!--  خلفية شفافة  -->
            <Rectangle Fill="Black" Opacity="0.5" />

            <!--  المحتوى الرئيسي  -->
            <Border
                Margin="50"
                Background="{StaticResource backgroundColor}"
                BorderBrush="{StaticResource PrimaryColor}"
                BorderThickness="1"
                CornerRadius="15">
                <Border.Effect>
                    <DropShadowEffect
                        BlurRadius="20"
                        Direction="270"
                        Opacity="0.3"
                        ShadowDepth="5" />
                </Border.Effect>

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <!--  رأس النافذة  -->
                    <Border
                        Grid.Row="0"
                        Margin="0,24"
                        CornerRadius="12">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    FontSize="40"
                                    FontWeight="ExtraBold"
                                    Foreground="{StaticResource PrimaryColor}"
                                    Text="معاينة تصدير المنتجات" />
                                <TextBlock
                                    x:Name="txtSummary"
                                    Margin="0,10,0,0"
                                    HorizontalAlignment="Center"
                                    FontSize="22"
                                    FontWeight="SemiBold"
                                    Foreground="{StaticResource PrimaryColor}"
                                    Text="إجمالي المنتجات: 0" />
                            </StackPanel>

                        </Grid>
                    </Border>

                    <!--  المحتوى الرئيسي  -->
                    <TabControl
                        x:Name="tabControl"
                        Grid.Row="1"
                        Margin="30,0"
                        Background="Transparent"
                        BorderThickness="0"
                        FontSize="14">

                        <TabControl.Resources>
                            <Style TargetType="TabItem">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="TabItem">
                                            <Border
                                                x:Name="Border"
                                                Margin="2,0"
                                                Background="Transparent"
                                                BorderBrush="{StaticResource PrimaryColor}"
                                                BorderThickness="1,1,1,0"
                                                CornerRadius="8,8,0,0">
                                                <ContentPresenter
                                                    x:Name="ContentSite"
                                                    Margin="20,12"
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"
                                                    ContentSource="Header" />
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsSelected" Value="True">
                                                    <Setter TargetName="Border" Property="Background" Value="{StaticResource backgroundColor}" />
                                                    <Setter TargetName="Border" Property="BorderBrush" Value="{StaticResource PrimaryColor}" />
                                                    <Setter TargetName="Border" Property="BorderThickness" Value="1,2,1,0" />
                                                </Trigger>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter TargetName="Border" Property="Background" Value="#F0F8FF" />
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="FontFamily" Value="Tahoma" />
                                <Setter Property="FontSize" Value="16" />
                                <Setter Property="FontWeight" Value="SemiBold" />
                                <Setter Property="Foreground" Value="{StaticResource PrimaryTextColor}" />
                            </Style>
                        </TabControl.Resources>

                        <!--  تبويب إعدادات التصدير  -->
                        <TabItem Header="إعدادات التصدير">
                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                <Grid Margin="20">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="*" />
                                    </Grid.RowDefinitions>

                                    <!--  خيارات التصدير العامة  -->
                                    <GroupBox
                                        Grid.Row="0"
                                        Margin="0,10"
                                        Header="خيارات التصدير">
                                        <StackPanel Margin="15">
                                            <CheckBox
                                                x:Name="chkExportProducts"
                                                Margin="0,5"
                                                Content="تصدير المنتجات"
                                                FontSize="14"
                                                IsChecked="{Binding ExportInvoices}"
                                                Style="{StaticResource CircleCheckboxFL}" />
                                        </StackPanel>
                                    </GroupBox>

                                    <!--  حقول المنتجات  -->
                                    <GroupBox
                                        Grid.Row="1"
                                        Margin="0,10"
                                        Header="حقول المنتجات"
                                        Visibility="{Binding IsChecked, ElementName=chkExportProducts, Converter={StaticResource BoolToVisConverter}}">
                                        <Grid Margin="15">
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto" />
                                                <RowDefinition Height="*" />
                                            </Grid.RowDefinitions>


                                            <Grid Grid.Row="0">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*" />
                                                    <ColumnDefinition Width="*" />
                                                    <ColumnDefinition Width="8*" />
                                                </Grid.ColumnDefinitions>
                                                <Border
                                                    x:Name="btnSelectAllLensFields"
                                                    Grid.Column="0"
                                                    Height="44"
                                                    Margin="5"
                                                    Background="{StaticResource PrimaryColor}"
                                                    CornerRadius="12"
                                                    MouseLeftButtonDown="btnSelectAllProductFields_Click">
                                                    <TextBlock
                                                        HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        FontSize="16"
                                                        FontWeight="Bold"
                                                        Foreground="White"
                                                        Text="تحديد الكل" />
                                                </Border>
                                                <Border
                                                    x:Name="btnDeselectAllLensFields"
                                                    Grid.Column="1"
                                                    Height="44"
                                                    Margin="5"
                                                    Background="Transparent"
                                                    BorderBrush="{StaticResource errorColor}"
                                                    BorderThickness="1"
                                                    CornerRadius="12"
                                                    Cursor="Hand"
                                                    MouseLeftButtonDown="btnDeselectAllProductFields_Click">
                                                    <TextBlock
                                                        HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"
                                                        FontSize="16"
                                                        FontWeight="Bold"
                                                        Foreground="{StaticResource errorColor}"
                                                        Text="إلغاء تحديد الكل" />
                                                </Border>
                                            </Grid>


                                            <ListView
                                                x:Name="lstProductFields"
                                                Grid.Row="1"
                                                MaxHeight="300"
                                                Margin="0,10"
                                                ItemsSource="{Binding ItemFields}">
                                                <ListView.ItemTemplate>
                                                    <DataTemplate>
                                                        <Grid Margin="5">
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="*" />
                                                                <ColumnDefinition Width="*" />
                                                            </Grid.ColumnDefinitions>

                                                            <CheckBox
                                                                Grid.Column="0"
                                                                Content="{Binding DefaultName}"
                                                                IsChecked="{Binding IsSelected}"
                                                                IsEnabled="{Binding IsRequired, Converter={StaticResource InverseBoolConverter}}"
                                                                Style="{StaticResource CircleCheckboxFL}" />


                                                            <TextBox
                                                                Grid.Column="2"
                                                                FontSize="16"
                                                                Foreground="{StaticResource PrimaryColor}"
                                                                Text="{Binding CustomHeader, UpdateSourceTrigger=PropertyChanged}" />
                                                        </Grid>
                                                    </DataTemplate>
                                                </ListView.ItemTemplate>
                                            </ListView>
                                        </Grid>
                                    </GroupBox>
                                </Grid>
                            </ScrollViewer>
                        </TabItem>

                        <!--  تبويب المعاينة  -->
                        <TabItem Header="معاينة البيانات">
                            <Grid Margin="20">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>

                                <StackPanel Grid.Row="0" Margin="0,0,0,15">
                                    <TextBlock Text="معاينة المنتجات" />
                                    <TextBlock x:Name="txtProductsPreviewCount" Text="عدد المنتجات: 0" />
                                </StackPanel>

                                <DataGrid
                                    x:Name="dgProductsPreview"
                                    Grid.Row="1"
                                    AutoGenerateColumns="False"
                                    CanUserAddRows="False"
                                    CanUserDeleteRows="False"
                                    FlowDirection="RightToLeft"
                                    FontSize="11"
                                    IsReadOnly="True"
                                    ItemsSource="{Binding ProductsData}"
                                    RowHeaderWidth="0">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn
                                            Width="*"
                                            Binding="{Binding Name}"
                                            Header="اسم المنتج" />
                                        <DataGridTextColumn
                                            Width="*"
                                            Binding="{Binding CostPrice, StringFormat='{}{0:N3}'}"
                                            Header="سعر التكلفة" />
                                        <DataGridTextColumn
                                            Width="*"
                                            Binding="{Binding SellPrice, StringFormat='{}{0:N3}'}"
                                            Header="سعر البيع" />
                                        <DataGridTextColumn
                                            Width="*"
                                            Binding="{Binding TotalQuantity}"
                                            Header="إجمالي الكمية" />
                                        <DataGridTextColumn
                                            Width="*"
                                            Binding="{Binding MinimumQuantity}"
                                            Header="الحد الأدنى" />
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Grid>
                        </TabItem>
                    </TabControl>

                    <!--  أزرار التحكم  -->

                    <Border
                        Grid.Row="2"
                        Margin="0,16"
                        Padding="0,24">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="4*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="12" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="4*" />
                            </Grid.ColumnDefinitions>
                            <Border
                                x:Name="btnExportNow"
                                Grid.Column="1"
                                MinHeight="44"
                                Background="{StaticResource PrimaryColor}"
                                CornerRadius="12"
                                Cursor="Hand"
                                MouseLeftButtonDown="btnExport_Click">
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FontSize="20"
                                    FontWeight="Bold"
                                    Foreground="White"
                                    Text="تصدير الآن" />
                            </Border>

                            <Border
                                x:Name="btnCancel"
                                Grid.Column="3"
                                MinHeight="44"
                                Background="Transparent"
                                BorderBrush="{StaticResource errorColor}"
                                BorderThickness="1"
                                CornerRadius="12"
                                Cursor="Hand"
                                MouseLeftButtonDown="btnCancel_Click">
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FontSize="20"
                                    FontWeight="Bold"
                                    Foreground="{StaticResource errorColor}"
                                    Text="إلغاء" />
                            </Border>
                        </Grid>
                    </Border>



                    <!--  مؤشر التقدم  -->
                    <Grid
                        x:Name="progressOverlay"
                        Grid.RowSpan="3"
                        Background="Black"
                        Opacity="0.7"
                        Visibility="Collapsed">
                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                            <ProgressBar
                                x:Name="progressBar"
                                Width="300"
                                Height="20"
                                Margin="0,0,0,15" />
                            <TextBlock
                                x:Name="txtProgress"
                                HorizontalAlignment="Center"
                                FontSize="16"
                                FontWeight="SemiBold"
                                Foreground="White"
                                Text="جاري التصدير..." />
                        </StackPanel>
                    </Grid>
                </Grid>
            </Border>
        </Grid>
    </Viewbox>
</Window>
