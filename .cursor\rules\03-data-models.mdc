---
description:
globs:
alwaysApply: false
---
# Data Models and Relationships

The application has several key data models that represent the business entities. All models inherit from [BaseEntity.cs](mdc:VisionPoint.UI/Models/BaseEntity.cs).

## Core Business Entities

### Products and Inventory
- [Product.cs](mdc:VisionPoint.UI/Models/Product.cs): Represents a product in the inventory
- [ProductQuantity.cs](mdc:VisionPoint.UI/Models/ProductQuantity.cs): Tracks product quantities
- [Color.cs](mdc:VisionPoint.UI/Models/Color.cs): Product color information
- [ProductColor.cs](mdc:VisionPoint.UI/Models/ProductColor.cs): Links products with colors

### Lens Management
- [Lens.cs](mdc:VisionPoint.UI/Models/Lens.cs): Lens product data
- [LensQuantity.cs](mdc:VisionPoint.UI/Models/LensQuantity.cs): Tracks lens quantities
- [Prescription.cs](mdc:VisionPoint.UI/Models/Prescription.cs): Prescription information
- [LensPrescription.cs](mdc:VisionPoint.UI/Models/LensPrescription.cs): Links lenses with prescriptions
- [LensPrescriptionColor.cs](mdc:VisionPoint.UI/Models/LensPrescriptionColor.cs): Links lens prescriptions with colors

### Sales and Purchases
- [Sale.cs](mdc:VisionPoint.UI/Models/Sale.cs): Represents a sale transaction
- [SaleItem.cs](mdc:VisionPoint.UI/Models/SaleItem.cs): Individual items in a sale
- [Purchase.cs](mdc:VisionPoint.UI/Models/Purchase.cs): Represents a purchase transaction
- [PurchaseItem.cs](mdc:VisionPoint.UI/Models/PurchaseItem.cs): Individual items in a purchase
- [Receipt.cs](mdc:VisionPoint.UI/Models/Receipt.cs): Receipt information

### Users and Clients
- [User.cs](mdc:VisionPoint.UI/Models/User.cs): Application user
- [Client.cs](mdc:VisionPoint.UI/Models/Client.cs): Customer information
- [CurrentUser.cs](mdc:VisionPoint.UI/Models/CurrentUser.cs): Information about the currently logged-in user

## Database Context
The [AppDbContext.cs](mdc:VisionPoint.UI/Models/AppDbContext.cs) class defines the database structure and relationships between entities using Entity Framework.
