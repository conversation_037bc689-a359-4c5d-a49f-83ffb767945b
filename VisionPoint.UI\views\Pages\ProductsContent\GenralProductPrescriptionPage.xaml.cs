﻿using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.ProductsContent
{
    /// <summary>
    /// Interaction logic for GenralProductPrescriptionPage.xaml
    /// </summary>
    public partial class GenralProductPrescriptionPage : Window
    {
        private readonly ColorService _colorService;
        private readonly ProductVM _product;
        private ProductColor _selectedColor = new();
        private readonly ProductService _productService;
        private readonly WarehouseService _warehouseService;
        List<ProductColor> _productColors = new();
        private int? _selectedWarehouseId = null;

        public GenralProductPrescriptionPage(ProductVM product)
        {
            InitializeComponent();
            _product = product;
            if (!_product.Color) { cmbColor.Visibility = Visibility.Collapsed; btnAddColor.Visibility = Visibility.Collapsed; }
            _productService = new ProductService();
            _colorService = new ColorService();
            _warehouseService = new WarehouseService();
        }
        private async Task LoadWarehouses()
        {
            try
            {
                var warehouses = await _warehouseService.GetAllWarehousesAsync();

                // إضافة خيار "جميع المخازن" في البداية
                var allWarehouses = new List<Warehouse>
                {
                    new Warehouse { Id = 0, Name = "جميع المخازن" }
                };
                allWarehouses.AddRange(warehouses);

                cmbWarehouse.ItemsSource = allWarehouses;

                // تحديد المخزن الافتراضي للمستخدم الحالي
                if (CurrentUser.WarehouseId.HasValue)
                {
                    // اختيار مخزن المستخدم الحالي
                    cmbWarehouse.SelectedValue = CurrentUser.WarehouseId.Value;
                    _selectedWarehouseId = CurrentUser.WarehouseId.Value;
                }
                else
                {
                    // إذا لم يكن له مخزن محدد، نختار "جميع المخازن"
                    cmbWarehouse.SelectedIndex = 0;
                    _selectedWarehouseId = null;
                }

                // تطبيق منطق الصلاحيات لتغيير المخزن
                cmbWarehouse.IsEnabled = CurrentUser.CanChangeWarehouse;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل المخازن: {ex.Message}", "خطأ", true);
            }
        }

        private async Task LoadColors()
        {
            var colors = await _colorService.GetAllColorsAsync();
            cmbColor.ItemsSource = colors;
            cmbColor.DisplayMemberPath = "Name";
            cmbColor.SelectedValuePath = "Id";
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            cmbLensesName.Text = _product.Name;
            txtCostPrice.Text = _product.CostPrice.ToString();
            txtSellPrice.Text = _product.SellPrice.ToString();

            // Load warehouses first
            await LoadWarehouses();
            if (_product.Color) await LoadColors();
        }
        private bool IsBarcodeUnique(string barcode)
        {
            if (string.IsNullOrWhiteSpace(barcode))
                return true;

            var existingColors = list.ItemsSource as IEnumerable<ProductColor>;
            return !existingColors?.Any(c => c.Barcode == barcode && c.Id != _selectedColor.Id) ?? true;
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnSave.IsEnabled = false;
            btnNew.IsEnabled = false;
            btnDelete.IsEnabled = false;
            btnAddColor.IsEnabled = false;
            btnclose.IsEnabled = false;
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnSave.IsEnabled = true;
            btnNew.IsEnabled = true;
            btnDelete.IsEnabled = true;
            btnAddColor.IsEnabled = true;
            btnclose.IsEnabled = true;
        }

        private async void btnSave_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (cmbColor.SelectedItem == null && _product.Color)
                {
                    ErrorBox.Show("الرجاء اختيار اللون", "خطأ", true);
                    return;
                }

                if ((_selectedColor.ColorId == null || _selectedColor.ColorId == 0) && _product.Color)
                {
                    ErrorBox.Show("الرجاء اختيار لون", "خطأ", true);
                    return;
                }

                if (!IsBarcodeUnique(txtBarcode.Text.Trim()))
                {
                    ErrorBox.Show("الباركود موجود مسبقاً", "خطأ", true);
                    return;
                }

            _selectedColor.ProductId = _product.Id;
            _selectedColor.Barcode = string.IsNullOrWhiteSpace(txtBarcode.Text) ? null : txtBarcode.Text.Trim();
            if (_product.Color) _selectedColor.ColorId = (cmbColor.SelectedItem as Models.Color).Id;
            else
            {
                _selectedColor = _productColors.First();
                _selectedColor.Barcode = string.IsNullOrWhiteSpace(txtBarcode.Text) ? null : txtBarcode.Text.Trim();
            }
            var result = _selectedColor.Id == 0
                ? await _productService.AddProductColorAsync(_selectedColor)
                : await _productService.UpdateProductColorAsync(_selectedColor);

            if (result.State)
            {
                DialogBox.Show(result.Message, "نجاح");
                Clear();
                await LoadProductColors();
            }
            else
            {
                ErrorBox.Show(result.Message, "خطأ", true);
            }
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void cmbColor_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (cmbColor.SelectedItem is Models.Color selectedColor)
            {
                _selectedColor.ColorId = selectedColor.Id;
            }
        }

        private void btnclose_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                Close();
            }
            finally
            {
                EnableAllButtons();
            }
        }
        private async void btnAddColor_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                var colorsPage = new ColorsPage(_colorService);
                colorsPage.ShowDialog();
                await LoadColors();
            }
            finally
            {
                EnableAllButtons();
            }
        }
        private void Clear()
        {
            txtBarcode.Clear();
            cmbColor.SelectedIndex = -1;
            cmbColor.Text = string.Empty;
            _selectedColor = new ProductColor();
        }

        private void btnNew_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                Clear();
            }
            finally
            {
                EnableAllButtons();
            }
        }
        private async void btnDelete_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (_selectedColor.Id == 0)
                {
                    ErrorBox.Show("الرجاء اختيار عنصر للحذف", "خطأ", true);
                    return;
                }
                else if (!_product.Color)
                {
                    ErrorBox.Show("لا يمكن حذف العنصر", "خطأ", true);
                    return;
                }

                var result = await _productService.DeleteProductColorAsync(_selectedColor.Id);
                if (result.State)
                {
                    DialogBox.Show(result.Message, "نجاح");
                    Clear();
                    // Refresh list
                    await LoadProductColors();
                }
                else
                {
                    ErrorBox.Show(result.Message, "خطأ", true);
                }
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async Task LoadProductColors()
        {
            _productColors = await _productService.GetProductColorsByProductIdAsync(_product.Id, _selectedWarehouseId);
            list.ItemsSource = _productColors;
        }

        private async void cmbWarehouse_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // التحقق من أن المستخدم لديه صلاحية تغيير المخزن أو أن هذا هو التحديد الأولي
            if (cmbWarehouse.SelectedValue != null && (CurrentUser.CanChangeWarehouse || _selectedWarehouseId == null))
            {
                var selectedId = (int)cmbWarehouse.SelectedValue;
                _selectedWarehouseId = selectedId == 0 ? null : selectedId;
                await LoadProductColors();
            }
        }

        private void list_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (list.SelectedItem is ProductColor selectedProductColor)
            {
                _selectedColor = selectedProductColor;
                txtBarcode.Text = selectedProductColor.Barcode;
                cmbColor.SelectedItem = selectedProductColor.Color;
            }
            else
            {
                ErrorBox.Show("الرجاء اختيار عنصر", "خطأ", true);
            }
        }
        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            ListView listView = sender as ListView;
            GridView gridView = listView.View as GridView;

            var workingWidth = listView.ActualWidth - SystemParameters.VerticalScrollBarWidth;
            var columnsCount = gridView.Columns.Count;

            foreach (var column in gridView.Columns)
            {
                column.Width = workingWidth / columnsCount;
            }
        }
    }
}
