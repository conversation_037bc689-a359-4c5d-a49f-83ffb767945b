<Page
    x:Class="VisionPoint.UI.views.Pages.Users.UserPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Converters="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Users"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="UserPage"
    d:Background="White"
    d:Height="1080"
    d:Width="1570"
    FlowDirection="RightToLeft"
    Loaded="Page_Loaded"
    mc:Ignorable="d">
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>


            <DropShadowEffect
                x:Key="PopupShadow"
                BlurRadius="5"
                Opacity="0.3"
                ShadowDepth="2" />

            <DataTemplate x:Key="RoleComboTemplate">
                <DockPanel LastChildFill="True">
                    <TextBlock VerticalAlignment="Center" Text="{Binding}" />
                    <Path
                        Width="12"
                        Height="12"
                        Margin="5,0"
                        VerticalAlignment="Center"
                        Data="M 0 0 L 4 4 L 8 0 Z"
                        DockPanel.Dock="Right"
                        Fill="Gray"
                        RenderTransformOrigin="0.5,0.5">
                        <Path.RenderTransform>
                            <RotateTransform Angle="180" />
                        </Path.RenderTransform>
                    </Path>
                </DockPanel>
            </DataTemplate>
        </ResourceDictionary>
    </Page.Resources>
    <Grid Margin="16">
        <Grid.ColumnDefinitions>
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
        </Grid.RowDefinitions>


        <Grid Grid.ColumnSpan="2" Margin="8,0">

            <TextBox
                x:Name="txtUserName"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                Tag="اسم المستخدم" />

        </Grid>



        <Grid
            Grid.Row="0"
            Grid.Column="2"
            Grid.ColumnSpan="2"
            Margin="8,0">

            <TextBox
                x:Name="txtPassword"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                Tag="كلمة المرور" />

        </Grid>

        <Grid
            Grid.Row="0"
            Grid.Column="4"
            Grid.ColumnSpan="2"
            Margin="8,0">

            <TextBox
                x:Name="txtConfirmPassword"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                Tag="تأكيد كلمة المرور" />

        </Grid>

        <Grid
            Grid.Row="2"
            Grid.ColumnSpan="2"
            MaxHeight="60"
            Margin="8,0">
            <ToggleButton
                x:Name="DropDownToggle"
                Padding="10,4"
                HorizontalContentAlignment="Stretch"
                Background="White"
                BorderBrush="#CCCCCC"
                BorderThickness="1"
                Click="DropDownToggle_Click"
                ContentTemplate="{StaticResource RoleComboTemplate}"
                FontSize="18"
                Foreground="Black"
                Style="{StaticResource FloatingLabelToggleButton}"
                Tag="صلاحيات المستخدم" />

            <Popup
                x:Name="DropDownPopup"
                AllowsTransparency="True"
                Closed="DropDownPopup_Closed"
                IsOpen="False"
                Placement="Bottom"
                PlacementTarget="{Binding ElementName=DropDownToggle}"
                PopupAnimation="Fade"
                StaysOpen="False">
                <Border
                    MinWidth="{Binding ActualWidth, ElementName=DropDownToggle}"
                    Padding="6"
                    Background="White"
                    BorderBrush="{StaticResource PrimaryColor}"
                    BorderThickness="1"
                    CornerRadius="4"
                    Effect="{StaticResource PopupShadow}"
                    Focusable="False"
                    PreviewMouseWheel="Border_PreviewMouseWheel"
                    SnapsToDevicePixels="True">
                    <ScrollViewer
                        MaxHeight="250"
                        Focusable="False"
                        VerticalScrollBarVisibility="Auto">
                        <ListBox
                            x:Name="lstRoles"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Stretch"
                            Background="White"
                            BorderThickness="0"
                            Focusable="True"
                            ItemsSource="{Binding Roles}"
                            SelectionChanged="lstRoles_SelectionChanged"
                            SelectionMode="Multiple">
                            <ListBox.ItemContainerStyle>
                                <Style TargetType="ListBoxItem">
                                    <Setter Property="Background" Value="Transparent" />
                                    <Setter Property="BorderBrush" Value="Transparent" />
                                    <Setter Property="BorderThickness" Value="0" />
                                    <Setter Property="Padding" Value="0" />
                                    <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                                    <Setter Property="VerticalContentAlignment" Value="Center" />
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="ListBoxItem">
                                                <Border
                                                    x:Name="BackgroundBorder"
                                                    Margin="0,2"
                                                    Padding="{TemplateBinding Padding}"
                                                    Converters:ClipToCornerRadius.IsEnabled="True"
                                                    Background="{TemplateBinding Background}"
                                                    BorderBrush="{StaticResource PrimaryColor}"
                                                    BorderThickness="1"
                                                    CornerRadius="12"
                                                    SnapsToDevicePixels="True">
                                                    <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsSelected" Value="True">
                                                        <Setter TargetName="BackgroundBorder" Property="Background" Value="#6495ED" />
                                                    </Trigger>



                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </ListBox.ItemContainerStyle>

                            <ListBox.ItemTemplate>
                                <DataTemplate>

                                    <Grid
                                        Height="50"
                                        HorizontalAlignment="Stretch"
                                        Cursor="Hand">
                                        <Border HorizontalAlignment="Stretch" VerticalAlignment="Stretch">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition />
                                                    <ColumnDefinition Width="Auto" />
                                                </Grid.ColumnDefinitions>
                                                <TextBlock
                                                    Margin="10,0"
                                                    VerticalAlignment="Center"
                                                    FontSize="16"
                                                    Text="{Binding DisplayName}" />
                                                <CheckBox
                                                    Grid.Column="1"
                                                    Margin="10"
                                                    HorizontalAlignment="Right"
                                                    VerticalAlignment="Center"
                                                    Focusable="False"
                                                    IsChecked="{Binding RelativeSource={RelativeSource AncestorType=ListBoxItem}, Path=IsSelected}"
                                                    IsHitTestVisible="False"
                                                    Style="{StaticResource CircleCheckboxFL}" />
                                            </Grid>
                                        </Border>
                                    </Grid>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </ScrollViewer>
                </Border>
            </Popup>
        </Grid>

        <Grid
            Grid.Row="1"
            Grid.Column="0"
            Grid.ColumnSpan="2"
            Margin="8,0">

            <TextBox
                x:Name="txtFullName"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                Tag="الاسم الكامل" />

        </Grid>
        <Grid
            Grid.Row="1"
            Grid.Column="2"
            Grid.ColumnSpan="2"
            Margin="8,0">

            <TextBox
                x:Name="txtSalary"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                Tag="المرتب الشهري" />

        </Grid>

        <!--  ComboBox للمخزن  -->
        <Grid
            Grid.Row="2"
            Grid.Column="2"
            Grid.ColumnSpan="2"
            Margin="8,0">

            <ComboBox
                x:Name="cmbWarehouse"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Center"
                BorderThickness="1"
                DisplayMemberPath="Name"
                FontSize="21"
                IsEditable="False"
                SelectedValuePath="Id"
                Tag="المخزن" />

        </Grid>

        <!--  CheckBox لتجاوز حد الرصيد  -->


        <Grid
            Grid.Row="1"
            Grid.Column="4"
            Grid.ColumnSpan="1"
            Margin="8,0"
            VerticalAlignment="Center"
            Background="Transparent">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <TextBlock
                Grid.Row="0"
                Margin="0,4"
                VerticalAlignment="Top"
                FontSize="18"
                FontWeight="Black"
                Foreground="{StaticResource PrimaryColor}"
                Text="السماح بتجاوز حد الرصيد"
                TextAlignment="Center" />
            <CheckBox
                x:Name="chkAllowBalanceOverride"
                Grid.Row="1"
                Margin="0,4"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                IsChecked="False"
                Style="{StaticResource CircleCheckboxFL}" />
        </Grid>





        <Border
            x:Name="btnSave"
            Grid.Row="3"
            Grid.Column="0"
            MaxHeight="44"
            Margin="8,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnSave_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White">
                حفظ
            </TextBlock>
        </Border>

        <Border
            x:Name="btnNew"
            Grid.Row="3"
            Grid.Column="1"
            MaxHeight="44"
            Margin="8,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnNew_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}">
                جديد
            </TextBlock>
        </Border>
        <Border
            x:Name="btnStatement"
            Grid.Row="3"
            Grid.Column="3"
            MaxHeight="44"
            Margin="8,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnStatement_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}">
                كشف حساب
            </TextBlock>
        </Border>

        <Border
            x:Name="btnDelete"
            Grid.Row="3"
            Grid.Column="2"
            MaxHeight="44"
            Margin="8,0"
            Background="Transparent"
            BorderBrush="{StaticResource errorColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnDelete_MouseLeftButtonDown">

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition Width="30" />
                </Grid.ColumnDefinitions>
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="18"
                    Foreground="{StaticResource errorColor}">
                    حذف
                </TextBlock>

                <Path
                    Grid.Column="1"
                    Width="24"
                    Height="24"
                    HorizontalAlignment="Center"
                    Cursor="Hand"
                    Data="{StaticResource Trash}"
                    Fill="{StaticResource errorColor}"
                    FlowDirection="LeftToRight"
                    Stretch="Uniform"
                    StrokeThickness="1" />
            </Grid>

        </Border>

        <Grid
            Grid.Row="3"
            Grid.Column="4"
            Grid.ColumnSpan="1"
            Margin="8,0">
            <TextBox
                x:Name="txtSearch"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                Tag="بحث"
                TextChanged="txtSearch_TextChanged" />
        </Grid>

        <ListView
            x:Name="list"
            Grid.Row="4"
            Grid.RowSpan="6"
            Grid.ColumnSpan="8"
            Background="{DynamicResource PageColor}"
            BorderThickness="1"
            FontFamily="pack://application:,,,/Assets/#Cairo"
            ItemsSource="{Binding}"
            MouseDoubleClick="list_MouseDoubleClick"
            ScrollViewer.HorizontalScrollBarVisibility="Hidden"
            SizeChanged="list_SizeChanged">
            <ListView.BorderBrush>
                <SolidColorBrush Opacity="0.42" Color="Black" />
            </ListView.BorderBrush>
            <!--  SelectionChanged="ListView_SelectionChanged"  -->

            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Style.Triggers>
                        <Trigger Property="Control.IsMouseOver" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>

                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>

                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="False" />
                                <Condition Property="IsMouseOver" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter Property="FontWeight" Value="Thin" />
                            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextColor}" />
                        </MultiTrigger>

                    </Style.Triggers>
                    <Setter Property="Control.Background" Value="{DynamicResource PageColor}" />
                    <Setter Property="Control.HorizontalContentAlignment" Value="Stretch" />
                </Style>

            </ListView.ItemContainerStyle>
            <ListView.View>
                <GridView AllowsColumnReorder="False">
                    <GridView.ColumnHeaderContainerStyle>
                        <Style BasedOn="{StaticResource ListViewHeader}" TargetType="{x:Type GridViewColumnHeader}">
                            <Setter Property="IsEnabled" Value="False" />
                            <Setter Property="Height" Value="60" />
                            <Style.Triggers>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="TextElement.Foreground" Value="Black" />
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </GridView.ColumnHeaderContainerStyle>
                    <GridViewColumn Width="Auto" Header="اسم المستخدم">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    VerticalAlignment="Center"
                                    FontSize="18"
                                    FontWeight="Medium"
                                    Text="{Binding UserName, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="الاسم الكامل">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    VerticalAlignment="Center"
                                    FontSize="18"
                                    FontWeight="Medium"
                                    Text="{Binding Name, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="الصلاحيات">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    VerticalAlignment="Center"
                                    FontSize="18"
                                    FontWeight="Medium"
                                    Foreground="#1a73e8"
                                    Text="{Binding Roles, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="الرصيد">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    VerticalAlignment="Center"
                                    FontSize="18"
                                    FontWeight="Medium"
                                    Foreground="#28a745"
                                    Text="{Binding Balance, FallbackValue='0', TargetNullValue='0'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="المرتب">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    VerticalAlignment="Center"
                                    FontSize="18"
                                    FontWeight="Medium"
                                    Foreground="#007bff"
                                    Text="{Binding Salary, FallbackValue='0', TargetNullValue='0'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="المخزن">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    VerticalAlignment="Center"
                                    FontSize="18"
                                    FontWeight="Medium"
                                    Foreground="#6f42c1"
                                    Text="{Binding WarehouseName, FallbackValue='غير محدد', TargetNullValue='غير محدد'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="تجاوز حد الرصيد">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    VerticalAlignment="Center"
                                    FontSize="18"
                                    FontWeight="Medium"
                                    Foreground="#dc3545"
                                    Text="{Binding AllowBalanceOverrideText, FallbackValue='لا', TargetNullValue='لا'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
        </ListView>

    </Grid>
</Page>
