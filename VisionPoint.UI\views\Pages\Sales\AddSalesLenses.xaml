﻿<Window
    x:Class="VisionPoint.UI.views.Pages.Sales.AddSalesLenses"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:cc="clr-namespace:VisionPoint.UI.Controls"
    xmlns:converters="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:VisionPoint.UI"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Sales"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils"
    Title="إضافة عدسات للمبيعات"
    AllowsTransparency="True"
    Background="Transparent"
    FlowDirection="RightToLeft"
    Loaded="Window_Loaded"
    ResizeMode="NoResize"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    <WindowChrome.WindowChrome>
        <WindowChrome
            CaptionHeight="0"
            ResizeBorderThickness="0"
            UseAeroCaptionButtons="False" />
    </WindowChrome.WindowChrome>
    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">

        <Grid
            Width="1920"
            Height="1080"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Stretch"
            Background="Transparent">



            <Border
                Padding="16"
                Background="{DynamicResource backgroundColor}"
                BorderThickness="0"
                CornerRadius="5">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="70" />
                    </Grid.RowDefinitions>
                    <Grid Grid.ColumnSpan="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                            <RowDefinition />
                            <RowDefinition />
                            <RowDefinition />
                            <RowDefinition />
                            <RowDefinition />
                            <RowDefinition />

                        </Grid.RowDefinitions>

                        <DockPanel
                            Grid.ColumnSpan="6"
                            HorizontalAlignment="Center"
                            LastChildFill="False">
                            <CheckBox
                                x:Name="chkRghtLen"
                                Margin="16,0"
                                Checked="chkRghtLen_Checked"
                                IsChecked="True"
                                Style="{StaticResource CircleCheckboxFL}"
                                Unchecked="chkRghtLen_Unchecked" />

                            <TextBlock
                                Grid.Column="1"
                                Grid.ColumnSpan="2"
                                Margin="16,0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="24"
                                FontWeight="Bold"
                                Foreground="{StaticResource PrimaryColor}"
                                Text="العدسة اليمين" />
                        </DockPanel>



                        <Border
                            x:Name="btnclose"
                            Grid.Column="8"
                            Width="32"
                            Height="32"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Top"
                            Background="{StaticResource errorColor}"
                            CornerRadius="50"
                            Cursor="Hand"
                            MouseLeftButtonDown="btnclose_MouseLeftButtonDown" />

                        <Grid
                            Grid.Row="1"
                            Grid.Column="1"
                            Grid.ColumnSpan="2"
                            Margin="8,0">
                            <TextBox
                                x:Name="txtBarCodRight"
                                Height="60"
                                VerticalAlignment="Stretch"
                                VerticalContentAlignment="Stretch"
                                BorderThickness="1"
                                FontSize="21"
                                KeyDown="TxtBarCod_KeyDown"
                                Tag="الباركود" />

                        </Grid>

                        <Grid
                            Grid.Row="1"
                            Grid.Column="3"
                            Grid.ColumnSpan="2"
                            Margin="8,0">

                            <ComboBox
                                x:Name="cmbLensCategoryRight"
                                Grid.Row="0"
                                Margin="0,0,0,2"
                                VerticalAlignment="Stretch"
                                VerticalContentAlignment="Stretch"
                                BorderThickness="1"
                                FontSize="16"
                                IsEditable="True"
                                IsReadOnly="False"
                                SelectionChanged="cmbLensCategoryRight_SelectionChanged"
                                Tag="نوع العدسة" />


                        </Grid>

                        <Grid
                            Grid.Row="1"
                            Grid.Column="5"
                            Grid.ColumnSpan="2"
                            Margin="8,0">


                            <ComboBox
                                x:Name="cmbLensesNameRight"
                                Grid.Row="1"
                                Margin="0,2,0,0"
                                VerticalAlignment="Stretch"
                                VerticalContentAlignment="Stretch"
                                BorderThickness="1"
                                FontSize="16"
                                IsEditable="True"
                                IsReadOnly="False"
                                SelectionChanged="cmbLensesNameRight_SelectionChanged"
                                Tag="اسم العدسة" />
                        </Grid>



                        <DockPanel
                            Grid.Row="2"
                            Grid.ColumnSpan="88"
                            HorizontalAlignment="Center"
                            LastChildFill="False">
                            <Grid
                                Grid.Row="2"
                                Grid.Column="2"
                                Grid.ColumnSpan="4"
                                Margin="8,0">

                                <ComboBox
                                    x:Name="cmbPrescriptionRight"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    BorderThickness="1"
                                    FontSize="21"
                                    IsEditable="True"
                                    IsReadOnly="False"
                                    Tag="الوصفة (Sph/Cyl/Pow)" />
                            </Grid>


                            <Grid
                                Grid.Row="2"
                                Grid.Column="3"
                                Margin="8,0">
                                <ComboBox
                                    x:Name="cmbpowRight"
                                    Width="150"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    BorderThickness="1"
                                    FontSize="21"
                                    IsEditable="True"
                                    IsReadOnly="False"
                                    Tag="Pow" />
                            </Grid>

                            <Grid
                                Grid.Row="2"
                                Grid.Column="3"
                                Margin="8,0">
                                <ComboBox
                                    x:Name="cmbCylRight"
                                    Width="150"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    BorderThickness="1"
                                    FontSize="21"
                                    IsEditable="True"
                                    IsReadOnly="False"
                                    Tag="CYL" />
                            </Grid>
                            <Grid
                                Grid.Row="2"
                                Grid.Column="3"
                                Margin="8,0">
                                <ComboBox
                                    x:Name="cmbSphRight"
                                    Width="150"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    BorderThickness="1"
                                    FontSize="21"
                                    IsEditable="True"
                                    IsReadOnly="False"
                                    Tag="SPH" />
                            </Grid>

                            <Grid
                                Grid.Row="1"
                                Grid.Column="4"
                                Margin="8,0">
                                <ComboBox
                                    x:Name="cmbColorRight"
                                    Width="400"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    BorderThickness="1"
                                    FontSize="21"
                                    IsEditable="True"
                                    IsReadOnly="False"
                                    SelectionChanged="cmbColorRight_SelectionChanged"
                                    Tag="اللون" />
                            </Grid>

                        </DockPanel>

                        <DockPanel
                            Grid.Row="3"
                            Grid.Column="0"
                            Grid.ColumnSpan="8"
                            HorizontalAlignment="Center"
                            LastChildFill="False">

                            <Grid
                                Grid.Row="2"
                                Grid.Column="3"
                                Margin="8,0">
                                <TextBox
                                    x:Name="txtAddRight"
                                    Width="120"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    utils:NumericInputControl.IsDecimalOnly="True"
                                    BorderThickness="1"
                                    FontSize="21"
                                    IsReadOnly="True"
                                    Tag="Add" />
                            </Grid>

                            <Grid
                                Grid.Row="2"
                                Grid.Column="3"
                                Margin="8,0">
                                <TextBox
                                    x:Name="txtAxisRight"
                                    Width="120"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    utils:NumericInputControl.IsDecimalOnly="True"
                                    BorderThickness="1"
                                    FontSize="21"
                                    Tag="Axis" />
                            </Grid>
                            <Grid
                                Grid.Row="2"
                                Grid.Column="3"
                                Margin="8,0">
                                <TextBox
                                    x:Name="txtBCRight"
                                    Width="120"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    utils:NumericInputControl.IsDecimalOnly="True"
                                    BorderThickness="1"
                                    FontSize="21"
                                    IsReadOnly="True"
                                    Tag="BC" />
                            </Grid>

                            <Grid
                                Grid.Row="2"
                                Grid.Column="3"
                                Margin="8,0">
                                <TextBox
                                    x:Name="txtDiaRight"
                                    Width="120"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    utils:NumericInputControl.IsDecimalOnly="True"
                                    BorderThickness="1"
                                    FontSize="21"
                                    IsReadOnly="True"
                                    Tag="Dia" />
                            </Grid>

                            <Grid
                                Grid.Row="2"
                                Grid.Column="3"
                                Margin="8,0">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="*" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <TextBox
                                    x:Name="txtPriceRight"
                                    Width="120"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    utils:NumericInputControl.IsDecimalOnly="True"
                                    BorderThickness="1"
                                    FontSize="21"
                                    Tag="السعر" />
                                <Label
                                    x:Name="lblRightDiscount"
                                    Grid.Row="1"
                                    HorizontalAlignment="Center"
                                    Content="يوجد تخفيض"
                                    FontWeight="Bold"
                                    Foreground="Green"
                                    Visibility="Collapsed" />
                            </Grid>
                            <Grid
                                Grid.Row="1"
                                Grid.Column="5"
                                Grid.ColumnSpan="2"
                                Margin="8,0">
                                <ComboBox
                                    x:Name="cmbExpireOnRight"
                                    Width="300"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    BorderThickness="1"
                                    FontSize="21"
                                    IsEditable="True"
                                    IsReadOnly="False"
                                    Tag="تاريخ الصلاحية" />
                            </Grid>
                            <Grid
                                Grid.Row="2"
                                Grid.Column="4"
                                Margin="8,0">

                                <TextBox
                                    x:Name="txtQuantityRight"
                                    Width="120"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    utils:NumericInputControl.IsNumericOnly="True"
                                    BorderBrush="{StaticResource PrimaryColor}"
                                    BorderThickness="1"
                                    FontSize="21"
                                    Tag="الكمية" />

                            </Grid>

                            <Border
                                Grid.Row="2"
                                Grid.Column="5"
                                MinWidth=" 100"
                                MinHeight="60"
                                Margin="8,0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                BorderBrush="{StaticResource PrimaryColor}"
                                BorderThickness="1"
                                CornerRadius="12"
                                Visibility="{Binding ElementName=txtBarCodRight, Path=Visibility}">

                                <StackPanel
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Orientation="Horizontal">
                                    <TextBlock
                                        Margin="0,0,5,0"
                                        FontWeight="SemiBold"
                                        Text="متاح:" />
                                    <TextBlock
                                        x:Name="txtAvailableQuantityRight"
                                        FontWeight="Bold"
                                        Foreground="Green"
                                        Text="0" />
                                </StackPanel>
                            </Border>


                        </DockPanel>

                        <DockPanel
                            Grid.Row="4"
                            Grid.ColumnSpan="6"
                            HorizontalAlignment="Center"
                            LastChildFill="False">
                            <CheckBox
                                x:Name="chkLeftLen"
                                Margin="16,0"
                                Checked="chkLeftLen_Checked"
                                IsChecked="False"
                                Style="{StaticResource CircleCheckboxFL}"
                                Unchecked="chkLeftLen_Unchecked" />

                            <TextBlock
                                Grid.Column="1"
                                Grid.ColumnSpan="2"
                                Margin="16,0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="24"
                                FontWeight="Bold"
                                Foreground="{StaticResource PrimaryColor}"
                                Text="العدسة اليسار" />
                        </DockPanel>



                        <Grid
                            Grid.Row="5"
                            Grid.Column="1"
                            Grid.ColumnSpan="2"
                            Margin="8,0">
                            <TextBox
                                x:Name="txtBarCodLeft"
                                Height="60"
                                VerticalAlignment="Stretch"
                                VerticalContentAlignment="Stretch"
                                BorderThickness="1"
                                FontSize="21"
                                KeyDown="TxtBarCod_KeyDown"
                                Tag="الباركود" />
                        </Grid>

                        <Grid
                            Grid.Row="5"
                            Grid.Column="3"
                            Grid.ColumnSpan="2"
                            Margin="8,0">

                            <ComboBox
                                x:Name="cmbLensCategoryLeft"
                                Grid.Row="0"
                                Margin="0,0,0,2"
                                VerticalAlignment="Stretch"
                                VerticalContentAlignment="Stretch"
                                BorderThickness="1"
                                FontSize="16"
                                IsEditable="True"
                                IsReadOnly="False"
                                SelectionChanged="cmbLensCategoryLeft_SelectionChanged"
                                Tag="نوع العدسة" />

                        </Grid>
                        <Grid
                            Grid.Row="5"
                            Grid.Column="5"
                            Grid.ColumnSpan="2"
                            Margin="8,0">



                            <ComboBox
                                x:Name="cmbLensesNameLeft"
                                Grid.Row="1"
                                Margin="0,2,0,0"
                                VerticalAlignment="Stretch"
                                VerticalContentAlignment="Stretch"
                                BorderThickness="1"
                                FontSize="16"
                                IsEditable="True"
                                IsReadOnly="False"
                                SelectionChanged="cmbLensesNameLeft_SelectionChanged"
                                Tag="اسم العدسة" />
                        </Grid>




                        <DockPanel
                            Grid.Row="6"
                            Grid.Column="0"
                            Grid.ColumnSpan="8"
                            HorizontalAlignment="Center"
                            LastChildFill="False">


                            <Grid
                                Grid.Row="2"
                                Grid.Column="2"
                                Grid.ColumnSpan="4"
                                Margin="8,0">

                                <ComboBox
                                    x:Name="cmbPrescriptionLeft"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    BorderThickness="1"
                                    FontSize="21"
                                    IsEditable="True"
                                    IsReadOnly="False"
                                    Tag="الوصفة (Sph/Cyl/Pow)" />
                            </Grid>


                            <Grid
                                Grid.Row="2"
                                Grid.Column="3"
                                Margin="8,0">
                                <ComboBox
                                    x:Name="cmbpowLeft"
                                    Width="150"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    BorderThickness="1"
                                    FontSize="21"
                                    IsEditable="True"
                                    IsReadOnly="False"
                                    Tag="Pow" />
                            </Grid>

                            <Grid
                                Grid.Row="2"
                                Grid.Column="3"
                                Margin="8,0">
                                <ComboBox
                                    x:Name="cmbCylLeft"
                                    Width="150"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    BorderThickness="1"
                                    FontSize="21"
                                    IsEditable="True"
                                    IsReadOnly="False"
                                    Tag="CYL" />
                            </Grid>
                            <Grid
                                Grid.Row="2"
                                Grid.Column="3"
                                Margin="8,0">
                                <ComboBox
                                    x:Name="cmbSphLeft"
                                    Width="150"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    BorderThickness="1"
                                    FontSize="21"
                                    IsEditable="True"
                                    IsReadOnly="False"
                                    Tag="SPH" />
                            </Grid>

                            <Grid
                                Grid.Row="5"
                                Grid.Column="4"
                                Margin="8,0">
                                <ComboBox
                                    x:Name="cmbColorLeft"
                                    Width="400"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    BorderThickness="1"
                                    FontSize="21"
                                    IsEditable="True"
                                    IsReadOnly="False"
                                    SelectionChanged="cmbColorLeft_SelectionChanged"
                                    Tag="اللون" />
                            </Grid>

                        </DockPanel>

                        <DockPanel
                            Grid.Row="7"
                            Grid.Column="0"
                            Grid.ColumnSpan="8"
                            HorizontalAlignment="Center"
                            LastChildFill="False">

                            <Grid Grid.Column="0" Margin="8,0">
                                <TextBox
                                    x:Name="txtAddLeft"
                                    Width="120"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    utils:NumericInputControl.IsDecimalOnly="True"
                                    BorderThickness="1"
                                    FontSize="21"
                                    IsReadOnly="True"
                                    Tag="Add" />
                            </Grid>

                            <Grid Grid.Column="1" Margin="8,0">
                                <TextBox
                                    x:Name="txtAxisLeft"
                                    Width="120"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    utils:NumericInputControl.IsDecimalOnly="True"
                                    BorderThickness="1"
                                    FontSize="21"
                                    Tag="Axis" />
                            </Grid>

                            <Grid Grid.Column="2" Margin="8,0">
                                <TextBox
                                    x:Name="txtBCLeft"
                                    Width="120"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    utils:NumericInputControl.IsDecimalOnly="True"
                                    BorderThickness="1"
                                    FontSize="21"
                                    IsReadOnly="True"
                                    Tag="BC" />
                            </Grid>

                            <Grid Grid.Column="3" Margin="8,0">
                                <TextBox
                                    x:Name="txtDiaLeft"
                                    Width="120"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    utils:NumericInputControl.IsDecimalOnly="True"
                                    BorderThickness="1"
                                    FontSize="21"
                                    IsReadOnly="True"
                                    Tag="Dia" />
                            </Grid>

                            <Grid
                                Grid.Row="2"
                                Grid.Column="3"
                                Margin="8,0">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="*" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>
                                <TextBox
                                    x:Name="txtPriceLeft"
                                    Width="120"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    utils:NumericInputControl.IsDecimalOnly="True"
                                    BorderThickness="1"
                                    FontSize="21"
                                    Tag="السعر" />
                                <Label
                                    x:Name="lblLeftDiscount"
                                    Grid.Row="1"
                                    HorizontalAlignment="Center"
                                    Content="يوجد تخفيض"
                                    FontWeight="Bold"
                                    Foreground="Green"
                                    Visibility="Collapsed" />
                            </Grid>

                            <Grid
                                Grid.Row="5"
                                Grid.Column="5"
                                Grid.ColumnSpan="2"
                                Margin="8,0">
                                <ComboBox
                                    x:Name="cmbExpireOnLeft"
                                    Width="300"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    BorderThickness="1"
                                    FontSize="21"
                                    IsEditable="True"
                                    IsReadOnly="False"
                                    Tag="تاريخ الصلاحية" />
                            </Grid>
                            <Grid Grid.Column="4" Margin="8,0">

                                <TextBox
                                    x:Name="txtQuantityLeft"
                                    Width="120"
                                    Height="60"
                                    VerticalAlignment="Stretch"
                                    VerticalContentAlignment="Stretch"
                                    utils:NumericInputControl.IsNumericOnly="True"
                                    BorderThickness="1"
                                    FontSize="21"
                                    Tag="الكمية" />

                            </Grid>

                            <Border
                                Grid.Row="2"
                                Grid.Column="5"
                                MinWidth=" 100"
                                MinHeight="60"
                                Margin="8,0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                BorderBrush="{StaticResource PrimaryColor}"
                                BorderThickness="1"
                                CornerRadius="12"
                                Visibility="{Binding ElementName=txtBarCodLeft, Path=Visibility}">

                                <StackPanel
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Orientation="Horizontal">
                                    <TextBlock
                                        Margin="0,0,5,0"
                                        FontWeight="SemiBold"
                                        Text="متاح:" />
                                    <TextBlock
                                        x:Name="txtAvailableQuantityLeft"
                                        FontWeight="Bold"
                                        Foreground="Green"
                                        Text="0" />
                                </StackPanel>
                            </Border>


                        </DockPanel>
                    </Grid>

                    <Border
                        x:Name="btnAdd"
                        Grid.Row="2"
                        Grid.Column="0"
                        Grid.ColumnSpan="3"
                        MaxHeight="44"
                        Margin="256,0"
                        HorizontalAlignment="Stretch"
                        Background="{StaticResource PrimaryColor}"
                        CornerRadius="8"
                        Cursor="Hand"
                        MouseLeftButtonDown="btnAdd_MouseLeftButtonDown">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontSize="18"
                            FontWeight="Bold"
                            Foreground="White">
                            إضافة
                        </TextBlock>
                    </Border>


                </Grid>
            </Border>




        </Grid>
    </Viewbox>
</Window>