using Microsoft.EntityFrameworkCore;
using System;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Models;
using VisionPoint.UI.Services;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Pages.Expire;
using VisionPoint.UI.views.Pages.MinimumQuantity;

namespace VisionPoint.UI.PL;

public class ProductService : IDisposable
{
    private readonly AppDbContext _context = ServiceLocator.GetService<AppDbContext>();
    private bool _disposed = false;

    public ProductService()
    {
    }

    // Get all products as ProductVM
    public async Task<List<ProductVM>> GetAllProductVMsAsync()
    {
        return await GetAllProductVMsAsync(null);
    }

    // Get all products as ProductVM with optional warehouse filter
    public async Task<List<ProductVM>> GetAllProductVMsAsync(int? warehouseId)
    {
        return await _context.Products
            .Include(p => p.ProductColors)
            .ThenInclude(pc => pc.ProductQuantity)
            .Select(p => new ProductVM
            {
                Id = p.Id,
                Name = p.Name,
                CostPrice = p.CostPrice,
                SellPrice = p.SellPrice,
                Color = p.Color,
                Exp = p.Exp,
                MinimumQuantity = p.MinimumQuantity,
                TotalQuantity = warehouseId.HasValue
                    ? p.ProductColors.SelectMany(c => c.ProductQuantity)
                        .Where(q => q.WarehouseId == warehouseId.Value)
                        .Sum(q => q.Quantity)
                    : p.ProductColors.SelectMany(c => c.ProductQuantity).Sum(q => q.Quantity)
            })
            .ToListAsyncWithBusy("GetAllProductVMs");
    }

    public async Task<List<Product>> GetAllProductsForPurchaseAsync()
    {
        return await _context.Products
            .Include(x => x.ProductColors)
            .ThenInclude(x => x.Color)
            .AsNoTracking()
            .ToListAsyncWithBusy("GetAllProductsForPurchase");
    }

    public async Task<List<Product>> GetAllProductsForSellAsync()
    {
        return await _context.Products
            .Include(x => x.ProductColors)
            .ThenInclude(x => x.Color)
            .Include(x => x.ProductColors)
            .ThenInclude(x => x.ProductQuantity)
            .AsNoTracking()
            .ToListAsyncWithBusy("GetAllProductsForSell");
    }

    /// <summary>
    /// جلب المنتجات المتاحة للبيع في مخزن محدد
    /// </summary>
    /// <param name="warehouseId">معرف المخزن</param>
    /// <returns>قائمة المنتجات التي لها سجلات كمية في المخزن المحدد</returns>
    public async Task<List<Product>> GetProductsForSellByWarehouseAsync(int warehouseId)
    {
        return await _context.Products
            .Include(x => x.ProductColors)
            .ThenInclude(x => x.Color)
            .Include(x => x.ProductColors)
            .ThenInclude(x => x.ProductQuantity)
            .Where(p => p.ProductColors.Any(pc =>
                pc.ProductQuantity.Any(pq => pq.WarehouseId == warehouseId)))
            .AsNoTracking()
            .ToListAsyncWithBusy("GetProductsForSellByWarehouse");
    }
    // Search products returning ProductVM
    public async Task<List<ProductVM>> SearchVMByNameAsync(string searchTerm)
    {
        return await SearchVMByNameAsync(searchTerm, null);
    }

    // Search products returning ProductVM with optional warehouse filter
    public async Task<List<ProductVM>> SearchVMByNameAsync(string searchTerm, int? warehouseId)
    {
        return await _context.Products
            .Include(p => p.ProductColors)
            .ThenInclude(pc => pc.ProductQuantity)
            .Where(p => p.Name.Contains(searchTerm))
            .Select(p => new ProductVM
            {
                Id = p.Id,
                Name = p.Name,
                CostPrice = p.CostPrice,
                SellPrice = p.SellPrice,
                Color = p.Color,
                Exp = p.Exp,
                MinimumQuantity = p.MinimumQuantity,
                TotalQuantity = warehouseId.HasValue
                    ? p.ProductColors.SelectMany(c => c.ProductQuantity)
                        .Where(q => q.WarehouseId == warehouseId.Value)
                        .Sum(q => q.Quantity)
                    : p.ProductColors.SelectMany(c => c.ProductQuantity).Sum(q => q.Quantity)
            })
            .ToListAsyncWithBusy("SearchProductVMsByName");
    }

    // Get product by id with colors
    public async Task<Product?> GetProductByIdAsync(int id)
    {
        return await _context.Products
            .Include(p => p.ProductColors)
            .ThenInclude(pc => pc.ProductQuantity)
            .FirstOrDefaultAsyncWithBusy(p => p.Id == id, "GetProductById");
    }
    // Add new product with colors
    public async Task<(bool State, string Message)> AddProductAsync(ProductVM product)
    {
        var newProduct = new Product
        {
            Name = product.Name,
            CostPrice = product.CostPrice,
            SellPrice = product.SellPrice,
            Color = product.Color,
            Exp = product.Exp,
            MinimumQuantity = product.MinimumQuantity,
            CreatedById = CurrentUser.Id,
            ModifiedById = CurrentUser.Id
        };
        if (!product.Color) newProduct.ProductColors = new List<ProductColor>() { new() };
        await _context.Products.AddAsyncWithBusy(newProduct);
        return await _context.SaveWithTransactionAndBusy("AddProduct");
    }

    // Update product and its colors
    public async Task<(bool State, string Message)> UpdateProductAsync(ProductVM product)
    {
        var existingProduct = await _context.Products.FirstOrDefaultAsyncWithBusy(p => p.Id == product.Id, "FindProductForUpdate");

        if (existingProduct == null)
        {
            return (false, "المنتج غير موجود");
        }

        existingProduct.ModifiedById = CurrentUser.Id;
        existingProduct.Name = product.Name;
        existingProduct.CostPrice = product.CostPrice;
        existingProduct.SellPrice = product.SellPrice;
        existingProduct.Exp = product.Exp;
        existingProduct.MinimumQuantity = product.MinimumQuantity;

        if (!product.Color && existingProduct.Color)
        {
            var productColors = await _context.ProductColors
                .Where(x => x.ProductId == product.Id)
                .ToListAsyncWithBusy("GetProductColorsForUpdate");

            if (productColors.Any(x => x.ColorId != null))
            {
                _context.ProductColors.RemoveRange(productColors.Where(x => x.ColorId != null));
            }
            if (!productColors.Any(x => x.ColorId == null))
            {
                await _context.ProductColors.AddAsyncWithBusy(new() { ProductId = product.Id });
            }
        }
        else if (product.Color && !existingProduct.Color)
        {
            var productColors = await _context.ProductColors
                .Where(x => x.ProductId == product.Id)
                .ToListAsyncWithBusy("GetProductColorsForUpdate");

            if (productColors.Any(x => x.ColorId == null))
            {
                _context.ProductColors.RemoveRange(productColors.Where(x => x.ColorId == null));
            }
        }

        existingProduct.Color = product.Color;
        _context.Products.UpdateWithBusy(existingProduct);
        return await _context.SaveWithTransactionAndBusy("UpdateProduct");
    }

    // Delete product and related colors
    public async Task<(bool State, string Message)> DeleteProductAsync(int id)
    {
        var product = await _context.Products.FirstOrDefaultAsyncWithBusy(p => p.Id == id, "FindProductForDelete");

        if (product == null)
        {
            return (false, "المنتج غير موجود");
        }

        // التحقق من وجود عناصر مبيعات مرتبطة بالمنتج
        bool hasSaleItems = await _context.SaleItems
            .Include(si => si.ProductQuantity)
            .ThenInclude(pq => pq.ProductColor)
            .AnyAsyncWithBusy(si => si.ProductQuantity != null && si.ProductQuantity.ProductColor != null &&
                      si.ProductQuantity.ProductColor.ProductId == id, "CheckProductSaleItems");

        if (hasSaleItems)
        {
            return (false, "لا يمكن حذف المنتج لأنه مرتبط بعناصر مبيعات");
        }

        // التحقق من وجود عناصر مشتريات مرتبطة بالمنتج
        bool hasPurchaseItems = await _context.PurchaseItems
            .Include(pi => pi.ProductColor)
            .AnyAsyncWithBusy(pi => pi.ProductColor != null && pi.ProductColor.ProductId == id, "CheckProductPurchaseItems");

        if (hasPurchaseItems)
        {
            return (false, "لا يمكن حذف المنتج لأنه مرتبط بعناصر مشتريات");
        }

        // التحقق من وجود خصومات مرتبطة بالمنتج
        bool hasDiscountProducts = await _context.DiscountProducts.AnyAsyncWithBusy(dp => dp.ProductId == id, "CheckProductDiscounts");

        if (hasDiscountProducts)
        {
            return (false, "لا يمكن حذف المنتج لأنه مرتبط بخصومات");
        }

        _context.Products.RemoveWithBusy(product);
        return await _context.SaveWithTransactionAndBusy("DeleteProduct");
    }

    // Search products by name
    public async Task<List<Product>> SearchByNameAsync(string searchTerm)
    {
        return await _context.Products
            .Include(p => p.ProductColors)
            .Where(p => p.Name.Contains(searchTerm))
            .ToListAsyncWithBusy("SearchProductsByName");
    }

    // Get products with colors and quantities
    public async Task<List<Product>> GetProductsWithFullDetailsAsync()
    {
        return await _context.Products
            .Include(p => p.ProductColors)
                .ThenInclude(pc => pc.ProductQuantity)
            .Include(p => p.ProductColors)
                .ThenInclude(pc => pc.Color)
            .ToListAsyncWithBusy("GetProductsWithFullDetails");
    }

    // Get expired products with colors
    public async Task<List<ProductQuantity>> GetExpiredProductsAsync()
    {
        return await _context.ProductQuantities
            .Include(pq => pq.ProductColor)
                .ThenInclude(pc => pc.Product)
            .Where(pq => pq.Exp < DateOnly.FromDateTime(DateTime.Today))
            .ToListAsyncWithBusy("GetExpiredProducts");
    }

    /// <summary>
    /// جلب المنتجات منتهية الصلاحية مجمعة ومنسقة للعرض
    /// </summary>
    /// <param name="warehouseId">معرف المخزن (null لجميع المخازن)</param>
    /// <param name="remainingDays">عدد الأيام المتبقية للتنبيه</param>
    /// <param name="today">التاريخ الحالي</param>
    /// <returns>قائمة المنتجات منتهية الصلاحية مجمعة ومنسقة</returns>
    public async Task<List<ExpiredItemViewModel>> GetExpiredProductsViewModelAsync(int? warehouseId, int remainingDays, DateOnly today)
    {
        try
        {
            var thresholdDate = today.AddDays(remainingDays);

            var query = _context.ProductQuantities
                .Include(pq => pq.ProductColor)
                    .ThenInclude(pc => pc.Product)
                .Include(pq => pq.ProductColor)
                    .ThenInclude(pc => pc.Color)
                .Include(pq => pq.Warehouse)
                .Where(pq => pq.Exp.HasValue &&
                       (pq.Exp.Value <= today || (pq.Exp.Value > today && pq.Exp.Value <= thresholdDate)) &&
                       pq.Quantity > 0);

            if (warehouseId.HasValue)
                query = query.Where(pq => pq.WarehouseId == warehouseId.Value);

            return await query
                .GroupBy(pq => new {
                    Name = pq.ProductColor.Product.Name,
                    Color = pq.ProductColor.Color.Name,
                    ExpirationDate = pq.Exp
                })
                .Select(g => new ExpiredItemViewModel
                {
                    Id = g.First().Id,
                    Name = g.Key.Name ?? "غير معروف",
                    Color = g.Key.Color ?? "غير محدد",
                    ExpirationDate = g.Key.ExpirationDate.HasValue ? g.Key.ExpirationDate.Value.ToString("yyyy-MM-dd") : "غير محدد",
                    Quantity = g.Sum(pq => pq.Quantity),
                    ItemType = "منتج",
                    Status = g.Key.ExpirationDate.HasValue && g.Key.ExpirationDate.Value <= today ? "منتهي الصلاحية" : "على وشك الانتهاء",
                    RemainingDays = g.Key.ExpirationDate.HasValue ? (g.Key.ExpirationDate.Value.DayNumber - today.DayNumber) : 0,
                    WarehouseName = g.First().Warehouse.Name ?? "غير محدد"
                })
                .ToListAsyncWithBusy("GetExpiredProductsViewModel");
        }
        catch (Exception ex)
        {
            throw new Exception($"خطأ في جلب المنتجات منتهية الصلاحية: {ex.Message}", ex);
        }
    }

    // Add color to product
    public async Task<(bool State, string Message)> AddProductColorAsync(ProductColor productColor)
    {
        await _context.ProductColors.AddAsyncWithBusy(productColor);
        return await _context.SaveWithTransactionAndBusy("AddProductColor");
    }

    // Update product color
    public async Task<(bool State, string Message)> UpdateProductColorAsync(ProductColor productColor)
    {
        _context.ProductColors.UpdateWithBusy(productColor);
        return await _context.SaveWithTransactionAndBusy("UpdateProductColor");
    }

    // Delete product color
    public async Task<(bool State, string Message)> DeleteProductColorAsync(int id)
    {
        var productColor = await _context.ProductColors.FindAsyncWithBusy(id);
        if (productColor == null)
        {
            return (false, "لون المنتج غير موجود");
        }

        // التحقق من وجود كميات منتج مرتبطة بلون المنتج
        bool hasProductQuantities = await _context.ProductQuantities.AnyAsyncWithBusy(pq => pq.ProductColorId == id, "CheckProductColorQuantities");

        if (hasProductQuantities)
        {
            return (false, "لا يمكن حذف لون المنتج لأنه مرتبط بكميات منتج");
        }

        // التحقق من وجود عناصر مبيعات مرتبطة بلون المنتج
        bool hasSaleItems = await _context.SaleItems
            .Include(si => si.ProductQuantity)
            .AnyAsyncWithBusy(si => si.ProductQuantity != null && si.ProductQuantity.ProductColorId == id, "CheckProductColorSaleItems");

        if (hasSaleItems)
        {
            return (false, "لا يمكن حذف لون المنتج لأنه مرتبط بعناصر مبيعات");
        }

        // التحقق من وجود عناصر مشتريات مرتبطة بلون المنتج
        bool hasPurchaseItems = await _context.PurchaseItems.AnyAsyncWithBusy(pi => pi.ProductColorId == id, "CheckProductColorPurchaseItems");

        if (hasPurchaseItems)
        {
            return (false, "لا يمكن حذف لون المنتج لأنه مرتبط بعناصر مشتريات");
        }

        _context.ProductColors.RemoveWithBusy(productColor);
        return await _context.SaveWithTransactionAndBusy("DeleteProductColor");
    }

    // Get product colors by product id
    public async Task<List<ProductColor>> GetProductColorsByProductIdAsync(int productId)
    {
        return await GetProductColorsByProductIdAsync(productId, null);
    }

    // Get product colors by product id with optional warehouse filter
    public async Task<List<ProductColor>> GetProductColorsByProductIdAsync(int productId, int? warehouseId)
    {
        return await _context.ProductColors
            .Include(pc => pc.Color)
            .Include(pc => pc.ProductQuantity)
            .Where(pc => pc.ProductId == productId)
            .Select(pc => new ProductColor
            {
                Id = pc.Id,
                ProductId = pc.ProductId,
                Product = pc.Product,
                ColorId = pc.ColorId,
                Color = pc.Color,
                Barcode = pc.Barcode,
                ProductQuantity = warehouseId.HasValue
                    ? pc.ProductQuantity.Where(pq => pq.WarehouseId == warehouseId.Value).ToList()
                    : pc.ProductQuantity.ToList()
            })
            .ToListAsyncWithBusy("GetProductColorsByProductId");
    }

    // Get product quantities by color id
    public async Task<List<ProductQuantity>> GetProductQuantitiesByColorIdAsync(int colorId)
    {
        return await _context.ProductQuantities
            .Where(pq => pq.ProductColorId == colorId)
            .AsNoTracking()
            .ToListAsyncWithBusy("GetProductQuantitiesByColorId");
    }

    // Get product color by barcode
    public async Task<ProductColor?> GetProductColorByBarcodeAsync(string barcode)
    {
        if (string.IsNullOrEmpty(barcode))
            return null;

        return await _context.ProductColors
            .Include(pc => pc.Product)
            .Include(pc => pc.Color)
            .Include(pc => pc.ProductQuantity)
            .FirstOrDefaultAsyncWithBusy(pc => pc.Barcode == barcode, "GetProductColorByBarcode");
    }

    /// <summary>
    /// البحث عن المنتج بالباركود مع التحقق من وجوده في المخزن المحدد
    /// </summary>
    /// <param name="barcode">الباركود</param>
    /// <param name="warehouseId">معرف المخزن</param>
    /// <returns>لون المنتج إذا كان متوفراً في المخزن المحدد</returns>
    public async Task<ProductColor?> GetProductColorByBarcodeAndWarehouseAsync(string barcode, int warehouseId)
    {
        if (string.IsNullOrEmpty(barcode))
            return null;

        return await _context.ProductColors
            .Include(pc => pc.Product)
            .Include(pc => pc.Color)
            .Include(pc => pc.ProductQuantity)
            .Where(pc => pc.Barcode == barcode &&
                        pc.ProductQuantity.Any(pq => pq.WarehouseId == warehouseId))
            .FirstOrDefaultAsyncWithBusy("GetProductColorByBarcodeAndWarehouse");
    }

    // Get the database context (for use in ExpireWindow)
    public AppDbContext GetContext()
    {
        return _context;
    }

    /// <summary>
    /// جلب المنتجات التي وصلت للحد الأدنى للكمية مجمعة ومنسقة للعرض
    /// </summary>
    /// <param name="warehouseId">معرف المخزن (null لجميع المخازن)</param>
    /// <returns>قائمة المنتجات التي وصلت للحد الأدنى مجمعة ومنسقة</returns>
    public async Task<List<MinimumQuantityItemViewModel>> GetMinimumQuantityProductsViewModelAsync(int? warehouseId)
    {
        try
        {
            var query = _context.ProductQuantities
                .Include(pq => pq.ProductColor)
                    .ThenInclude(pc => pc.Product)
                .Include(pq => pq.ProductColor)
                    .ThenInclude(pc => pc.Color)
                .Include(pq => pq.Warehouse)
                .Where(pq => pq.ProductColor.Product.MinimumQuantity > 0 &&
                           pq.Quantity < pq.ProductColor.Product.MinimumQuantity);

            if (warehouseId.HasValue)
                query = query.Where(pq => pq.WarehouseId == warehouseId.Value);

            return await query
                .Select(pq => new MinimumQuantityItemViewModel
                {
                    Id = pq.ProductColor.Product.Id,
                    Name = pq.ProductColor.Product.Name ?? "غير معروف",
                    ColorName = pq.ProductColor.Color.Name ?? "غير محدد",
                    CurrentQuantity = pq.Quantity,
                    MinimumQuantity = pq.ProductColor.Product.MinimumQuantity,
                    ItemType = "منتج",
                    WarehouseName = pq.Warehouse.Name ?? "غير محدد",
                    Details = ""
                })
                .ToListAsyncWithBusy("GetMinimumQuantityProductsViewModel");
        }
        catch (Exception ex)
        {
            throw new Exception($"خطأ في جلب المنتجات التي وصلت للحد الأدنى: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// الحصول على بيانات المنتجات للتصدير مع التفاصيل الكاملة
    /// </summary>
    /// <param name="warehouseId">معرف المخزن للتصفية (اختياري)</param>
    /// <returns>قائمة المنتجات للتصدير</returns>
    public async Task<List<ProductVM>> GetProductsForExportAsync(int? warehouseId = null)
    {
        try
        {
            return await _context.Products
                .Include(p => p.ProductColors)
                .ThenInclude(pc => pc.ProductQuantity)
                .Select(p => new ProductVM
                {
                    Id = p.Id,
                    Name = p.Name,
                    CostPrice = p.CostPrice,
                    SellPrice = p.SellPrice,
                    Color = p.Color,
                    Exp = p.Exp,
                    MinimumQuantity = p.MinimumQuantity,
                    TotalQuantity = warehouseId.HasValue
                        ? p.ProductColors.SelectMany(c => c.ProductQuantity)
                            .Where(q => q.WarehouseId == warehouseId.Value)
                            .Sum(q => q.Quantity)
                        : p.ProductColors.SelectMany(c => c.ProductQuantity).Sum(q => q.Quantity)
                })
                .ToListAsyncWithBusy("GetProductsForExport");
        }
        catch (Exception ex)
        {
            throw new Exception($"خطأ في جلب بيانات المنتجات للتصدير: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// الحصول على بيانات المنتجات المفصلة للتصدير مع تفاصيل الألوان والكميات والمخازن
    /// </summary>
    /// <param name="warehouseId">معرف المخزن للتصفية (اختياري):
    /// - null = جميع المخازن (يتم تضمين جميع الكميات من جميع المخازن)
    /// - رقم محدد = مخزن واحد فقط (يتم تضمين الكميات من هذا المخزن فقط)
    /// </param>
    /// <returns>قائمة المنتجات المفصلة للتصدير - سجل منفصل لكل (منتج + لون + مخزن + تاريخ صلاحية)</returns>
    public async Task<List<ProductExportVM>> GetProductsDetailedForExportAsync(int? warehouseId = null)
    {
        try
        {
            var query = _context.Products
                .Include(p => p.ProductColors)
                    .ThenInclude(pc => pc.Color)
                .Include(p => p.ProductColors)
                    .ThenInclude(pc => pc.ProductQuantity)
                        .ThenInclude(pq => pq.Warehouse)
                .AsQueryable();

            var products = await query.ToListAsyncWithBusy("GetProductsDetailedForExport");

            // استخدام LINQ لتحسين الأداء وتطبيق منطق التصفية المحسن
            var exportList = products
                .SelectMany(product =>
                {
                    if (product.ProductColors?.Any() == true)
                    {
                        // منتج له ألوان - معالجة كل لون
                        return product.ProductColors
                            .SelectMany(productColor =>
                            {
                                var quantities = productColor.ProductQuantity?
                                    .Where(pq => !warehouseId.HasValue || pq.WarehouseId == warehouseId.Value)
                                    .ToList() ?? new List<ProductQuantity>();

                                if (quantities.Any())
                                {
                                    // إنشاء سجل لكل كمية في المخزن المطلوب
                                    return quantities.Select(quantity => new ProductExportVM
                                    {
                                        ProductId = product.Id,
                                        Name = product.Name,
                                        ColorName = productColor.Color?.Name ?? "بدون لون",
                                        ColorCode = productColor.Color?.HexCode ?? "",
                                        Barcode = productColor.Barcode ?? "",
                                        CostPrice = product.CostPrice,
                                        SellPrice = product.SellPrice,
                                        Quantity = quantity.Quantity,
                                        WarehouseName = quantity.Warehouse?.Name ?? "غير محدد",
                                        ExpiryDate = quantity.Exp,
                                        MinimumQuantity = product.MinimumQuantity,
                                        HasColor = product.Color,
                                        HasExpiry = product.Exp
                                    });
                                }
                                else if (!warehouseId.HasValue)
                                {
                                    // فقط عند عرض جميع المخازن - إظهار الألوان بدون كميات
                                    return new[] { new ProductExportVM
                                    {
                                        ProductId = product.Id,
                                        Name = product.Name,
                                        ColorName = productColor.Color?.Name ?? "بدون لون",
                                        Barcode = productColor.Barcode ?? "",
                                        CostPrice = product.CostPrice,
                                        SellPrice = product.SellPrice,
                                        Quantity = 0,
                                        WarehouseName = "لا يوجد",
                                        ExpiryDate = null,
                                        MinimumQuantity = product.MinimumQuantity,
                                        HasColor = product.Color,
                                        HasExpiry = product.Exp
                                    }};
                                }
                                else
                                {
                                    // مخزن محدد ولا توجد كميات - استبعاد هذا اللون
                                    return Enumerable.Empty<ProductExportVM>();
                                }
                            });
                    }
                    else if (!warehouseId.HasValue)
                    {
                        // منتج بدون ألوان - فقط عند عرض جميع المخازن
                        return new[] { new ProductExportVM
                        {
                            ProductId = product.Id,
                            Name = product.Name,
                            ColorName = "بدون لون",
                            Barcode = "",
                            CostPrice = product.CostPrice,
                            SellPrice = product.SellPrice,
                            Quantity = 0,
                            WarehouseName = "لا يوجد",
                            ExpiryDate = null,
                            MinimumQuantity = product.MinimumQuantity,
                            HasColor = product.Color,
                            HasExpiry = product.Exp
                        }};
                    }
                    else
                    {
                        // منتج بدون ألوان ومخزن محدد - استبعاد
                        return Enumerable.Empty<ProductExportVM>();
                    }
                })
                .Where(item =>
                {
                    // تصفية إضافية لضمان جودة البيانات
                    if (warehouseId.HasValue)
                    {
                        // عند اختيار مخزن محدد، استبعاد السجلات التي تحتوي على "لا يوجد" أو "غير محدد"
                        return !string.IsNullOrEmpty(item.WarehouseName) &&
                               item.WarehouseName != "لا يوجد" &&
                               item.WarehouseName != "غير محدد";
                    }
                    return true; // عرض جميع السجلات عند اختيار "جميع المخازن"
                })

                // ترتيب البيانات باستخدام LINQ: المنتج ← اللون ← المخزن
                .OrderBy(x => x.Name)
                .ThenBy(x => x.ColorName)
                .ThenBy(x => x.WarehouseName)
                .ToList();

            // إحصائيات مفصلة باستخدام LINQ
            var statistics = new
            {
                TotalProducts = products.Count,
                TotalRecords = exportList.Count,
                UniqueProducts = exportList.Select(x => x.ProductId).Distinct().Count(),
                UniqueWarehouses = exportList.Select(x => x.WarehouseName).Distinct().Count(),
                TotalQuantity = exportList.Sum(x => x.Quantity),
                TotalValue = exportList.Sum(x => x.Quantity * x.SellPrice),
                ProductsWithColors = exportList.Count(x => x.HasColor),
                ProductsWithExpiry = exportList.Count(x => x.HasExpiry),
                WarehouseBreakdown = exportList
                    .GroupBy(x => x.WarehouseName)
                    .ToDictionary(g => g.Key, g => new { Count = g.Count(), Quantity = g.Sum(x => x.Quantity) })
            };



            return exportList;
        }
        catch (Exception ex)
        {
            throw new Exception($"خطأ في الحصول على بيانات المنتجات المفصلة للتصدير: {ex.Message}", ex);
        }
    }

    // Implement IDisposable pattern
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // Dispose managed resources
                _context.Dispose();
            }

            // Free unmanaged resources
            _disposed = true;
        }
    }

    // Destructor
    ~ProductService()
    {
        Dispose(false);
    }
}