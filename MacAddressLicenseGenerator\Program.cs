﻿using Google.Apis.Auth.OAuth2;
using Google.Apis.Services;
using Google.Apis.Sheets.v4;
using Google.Apis.Sheets.v4.Data;
using System.Management;
using System.Net.NetworkInformation;
using System.Security.Cryptography;
using System.Text;

namespace MacAddressLicenseGenerator
{
    internal class Program
    {
        // Fixed IV for consistent encryption results - 16 bytes for AES
        private static readonly byte[] FixedIV = new byte[]
        {
            0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
            0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10
        };

        // Default encryption key
        private const string DefaultSecretKey = "VisionPointSecure2024KeyForEncryption";

        // Google Sheets configuration - يجب تعديل هذه القيم
        private const string GoogleCredentialsPath = "credentials.json";
        private const string SpreadsheetId = "1ax3E1El_jBoTjEYurFEws4PewfLwOc9YbEacK9VB9Y4"; // ضع هنا معرف جدول البيانات
        private const bool EnableGoogleSheetsUpload = true; // تفعيل رفع البيانات إلى Google Sheets

        static async Task Main(string[] args)
        {
            try
            {
                Console.WriteLine("VisionPoint Device Information Collector & License Generator");
                Console.WriteLine("============================================================");

                // الوظيفة الأصلية: إنشاء ملف الترخيص
                Console.WriteLine("\n1. Creating License File...");
                await CreateOriginalLicenseFile();

                // الوظيفة الجديدة: جمع معلومات الجهاز الشاملة
                Console.WriteLine("\n2. Collecting Comprehensive Device Information...");
                var deviceInfo = await CollectComprehensiveDeviceInfo();

                // عرض ملخص المعلومات المجمعة
                DisplayDeviceInfoSummary(deviceInfo);

                // رفع البيانات إلى Google Sheets إذا كان مفعلاً
                if (EnableGoogleSheetsUpload)
                {
                    Console.WriteLine("\n3. Uploading Device Information to Google Sheets...");
                    await UploadToGoogleSheets(deviceInfo);
                }
                else
                {
                    Console.WriteLine("\n3. Google Sheets upload is disabled.");
                }

                Console.WriteLine("\nAll operations completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }

        /// <summary>
        /// Retrieves all MAC addresses from network interfaces in the current device
        /// </summary>
        /// <returns>A list of MAC addresses as strings</returns>
        private static List<string> GetAllMacAddresses()
        {
            var result = new List<string>();

            // Get all network interfaces from the system
            var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces();

            foreach (var netInterface in networkInterfaces.OrderBy(x => x.Name))
            {
                // Get the MAC address
                string macAddress = netInterface.GetPhysicalAddress().ToString();

                // Only add non-empty MAC addresses
                if (!string.IsNullOrEmpty(macAddress))
                {
                    result.Add(macAddress);
                }
            }

            return result;
        }

        /// <summary>
        /// Encrypts a string using AES encryption with the provided secret key
        /// </summary>
        /// <param name="plainText">The text to encrypt</param>
        /// <param name="secretKey">The secret key for encryption</param>
        /// <returns>The encrypted text as a Base64 string</returns>
        private static string Encrypt(string plainText, string secretKey)
        {
            if (string.IsNullOrEmpty(plainText))
                return string.Empty;

            byte[] encryptedBytes;
            byte[] keyBytes = GetKeyBytes(secretKey);

            using (Aes aes = Aes.Create())
            {
                aes.Key = keyBytes;
                aes.IV = FixedIV; // Use fixed IV for consistent encryption results

                using (ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV))
                using (MemoryStream ms = new MemoryStream())
                {
                    using (CryptoStream cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                    using (StreamWriter sw = new StreamWriter(cs))
                    {
                        sw.Write(plainText);
                    }

                    encryptedBytes = ms.ToArray();
                }
            }

            // Convert the encrypted bytes to a Base64 string for storage
            return Convert.ToBase64String(encryptedBytes);
        }

        /// <summary>
        /// Converts a string key to a valid AES key byte array
        /// </summary>
        private static byte[] GetKeyBytes(string key)
        {
            // Ensure the key is exactly 32 bytes (256 bits) for AES-256
            using (SHA256 sha256 = SHA256.Create())
            {
                return sha256.ComputeHash(Encoding.UTF8.GetBytes(key));
            }
        }

        #region الوظائف الجديدة لجمع معلومات الجهاز

        /// <summary>
        /// الوظيفة الأصلية لإنشاء ملف الترخيص
        /// </summary>
        private static async Task CreateOriginalLicenseFile()
        {
            try
            {
                // Get all MAC addresses
                var macAddresses = GetAllMacAddresses();

                if (macAddresses.Count == 0)
                {
                    Console.WriteLine("Error: No network interfaces found.");
                    return;
                }

                Console.WriteLine($"Found {macAddresses.Count} network interfaces:");
                foreach (var mac in macAddresses)
                {
                    Console.WriteLine($"- {mac}");
                }

                // Join MAC addresses with hyphen separator
                string macAddressesString = string.Join("-", macAddresses);

                // Encrypt the MAC addresses
                string encryptedMacAddresses = Encrypt(macAddressesString, DefaultSecretKey);

                // Save to AppData\Local\t
                string localAppDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
                string filePath = Path.Combine(localAppDataPath, "Micro.dll");

                // Write the encrypted content to the file
                await File.WriteAllTextAsync(filePath, encryptedMacAddresses);

                Console.WriteLine($"License file created successfully at: {filePath}");
                Console.WriteLine("Original license generation completed.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in original license creation: {ex.Message}");
            }
        }

        /// <summary>
        /// جمع معلومات الجهاز الشاملة
        /// </summary>
        private static async Task<DeviceInformation> CollectComprehensiveDeviceInfo()
        {
            var deviceInfo = new DeviceInformation();

            try
            {
                // معلومات النظام الأساسية
                deviceInfo.ComputerName = Environment.MachineName;
                deviceInfo.UserName = Environment.UserName;
                deviceInfo.OperatingSystem = Environment.OSVersion.Platform.ToString();
                deviceInfo.OSVersion = Environment.OSVersion.VersionString;
                deviceInfo.OSArchitecture = Environment.Is64BitOperatingSystem ? "64-bit" : "32-bit";
                deviceInfo.ProcessorCores = Environment.ProcessorCount;
                deviceInfo.CollectionDateTime = DateTime.Now;
                deviceInfo.TimeZone = TimeZoneInfo.Local.DisplayName;

                // جمع عناوين MAC
                deviceInfo.MacAddresses = GetAllMacAddresses();

                // جمع معلومات مفصلة باستخدام WMI
                await CollectSystemInfoWMI(deviceInfo);
                await CollectProcessorInfo(deviceInfo);
                await CollectMemoryInfo(deviceInfo);
                await CollectNetworkInfo(deviceInfo);
                await CollectDiskInfo(deviceInfo);
                await CollectHardwareInfo(deviceInfo);

                // جمع المعلومات الفريدة الإضافية
                await CollectUniqueIdentifiers(deviceInfo);
                await CollectAdvancedHardwareInfo(deviceInfo);
                await CollectSecurityInfo(deviceInfo);
                await CollectSystemDetails(deviceInfo);

                Console.WriteLine("Device information collected successfully.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error collecting device information: {ex.Message}");
            }

            return deviceInfo;
        }

        /// <summary>
        /// جمع معلومات النظام باستخدام WMI
        /// </summary>
        private static async Task CollectSystemInfoWMI(DeviceInformation deviceInfo)
        {
            try
            {
                await Task.Run(() =>
                {
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_ComputerSystem"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            deviceInfo.SystemManufacturer = obj["Manufacturer"]?.ToString() ?? "";
                            deviceInfo.SystemModel = obj["Model"]?.ToString() ?? "";
                            deviceInfo.DomainWorkgroup = obj["Domain"]?.ToString() ?? obj["Workgroup"]?.ToString() ?? "";
                            break;
                        }
                    }

                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_BIOS"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            deviceInfo.BiosVersion = obj["Version"]?.ToString() ?? "";
                            deviceInfo.SystemSerialNumber = obj["SerialNumber"]?.ToString() ?? "";
                            break;
                        }
                    }

                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_BaseBoard"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            deviceInfo.MotherboardSerialNumber = obj["SerialNumber"]?.ToString() ?? "";
                            break;
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error collecting system info: {ex.Message}");
            }
        }

        /// <summary>
        /// جمع معلومات المعالج
        /// </summary>
        private static async Task CollectProcessorInfo(DeviceInformation deviceInfo)
        {
            try
            {
                await Task.Run(() =>
                {
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Processor"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            deviceInfo.ProcessorName = obj["Name"]?.ToString() ?? "";
                            deviceInfo.ProcessorArchitecture = obj["Architecture"]?.ToString() ?? "";
                            break;
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error collecting processor info: {ex.Message}");
            }
        }

        /// <summary>
        /// جمع معلومات الذاكرة
        /// </summary>
        private static async Task CollectMemoryInfo(DeviceInformation deviceInfo)
        {
            try
            {
                await Task.Run(() =>
                {
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_ComputerSystem"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            var totalMemory = Convert.ToUInt64(obj["TotalPhysicalMemory"]);
                            deviceInfo.TotalRAM = FormatBytes(totalMemory);
                            break;
                        }
                    }

                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            var availableMemory = Convert.ToUInt64(obj["FreePhysicalMemory"]) * 1024;
                            deviceInfo.AvailableRAM = FormatBytes(availableMemory);
                            break;
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error collecting memory info: {ex.Message}");
            }
        }

        /// <summary>
        /// جمع معلومات الشبكة
        /// </summary>
        private static async Task CollectNetworkInfo(DeviceInformation deviceInfo)
        {
            try
            {
                await Task.Run(() =>
                {
                    var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces();

                    foreach (var netInterface in networkInterfaces.OrderBy(x => x.Name))
                    {
                        var macAddress = netInterface.GetPhysicalAddress().ToString();

                        if (!string.IsNullOrEmpty(macAddress))
                        {
                            var adapterInfo = new NetworkAdapterInfo
                            {
                                Name = netInterface.Name,
                                Description = netInterface.Description,
                                MacAddress = macAddress,
                                Status = netInterface.OperationalStatus.ToString(),
                                NetworkInterfaceType = netInterface.NetworkInterfaceType.ToString(),
                                Speed = netInterface.Speed > 0 ? FormatBytes((ulong)netInterface.Speed) + "/s" : "Unknown"
                            };

                            // الحصول على عناوين IP
                            var ipProperties = netInterface.GetIPProperties();
                            foreach (var ip in ipProperties.UnicastAddresses)
                            {
                                adapterInfo.IPAddresses.Add(ip.Address.ToString());
                            }

                            // الحصول على البوابة الافتراضية
                            var gateway = ipProperties.GatewayAddresses.FirstOrDefault();
                            if (gateway != null)
                            {
                                adapterInfo.DefaultGateway = gateway.Address.ToString();
                            }

                            // الحصول على خوادم DNS
                            foreach (var dns in ipProperties.DnsAddresses)
                            {
                                adapterInfo.DNSServers.Add(dns.ToString());
                            }

                            deviceInfo.NetworkAdapters.Add(adapterInfo);
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error collecting network info: {ex.Message}");
            }
        }

        /// <summary>
        /// جمع معلومات الأقراص
        /// </summary>
        private static async Task CollectDiskInfo(DeviceInformation deviceInfo)
        {
            try
            {
                await Task.Run(() =>
                {
                    var drives = DriveInfo.GetDrives();

                    foreach (var drive in drives)
                    {
                        if (drive.IsReady)
                        {
                            var diskInfo = new DiskInfo
                            {
                                DriveLetter = drive.Name,
                                Label = drive.VolumeLabel,
                                FileSystem = drive.DriveFormat,
                                TotalSize = FormatBytes((ulong)drive.TotalSize),
                                FreeSpace = FormatBytes((ulong)drive.TotalFreeSpace),
                                UsedSpace = FormatBytes((ulong)(drive.TotalSize - drive.TotalFreeSpace)),
                                DriveType = drive.DriveType.ToString()
                            };

                            deviceInfo.DiskDrives.Add(diskInfo);
                        }
                    }

                    // الحصول على معلومات الأقراص الفيزيائية باستخدام WMI
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_DiskDrive"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            var model = obj["Model"]?.ToString() ?? "";
                            var serialNumber = obj["SerialNumber"]?.ToString() ?? "";

                            // محاولة ربط الأقراص المنطقية بالفيزيائية (مبسط)
                            var existingDisk = deviceInfo.DiskDrives.FirstOrDefault();
                            if (existingDisk != null && string.IsNullOrEmpty(existingDisk.Model))
                            {
                                existingDisk.Model = model;
                                existingDisk.SerialNumber = serialNumber;
                            }
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error collecting disk info: {ex.Message}");
            }
        }

        /// <summary>
        /// جمع معلومات الأجهزة الإضافية
        /// </summary>
        private static async Task CollectHardwareInfo(DeviceInformation deviceInfo)
        {
            try
            {
                await Task.Run(() =>
                {
                    // معلومات كرت الرسوميات
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_VideoController"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            var name = obj["Name"]?.ToString();
                            if (!string.IsNullOrEmpty(name))
                            {
                                deviceInfo.GraphicsCard = name;
                                break;
                            }
                        }
                    }

                    // معلومات كرت الصوت
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_SoundDevice"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            var name = obj["Name"]?.ToString();
                            if (!string.IsNullOrEmpty(name))
                            {
                                deviceInfo.SoundCard = name;
                                break;
                            }
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error collecting hardware info: {ex.Message}");
            }
        }

        /// <summary>
        /// جمع المعرفات الفريدة للجهاز
        /// </summary>
        private static async Task CollectUniqueIdentifiers(DeviceInformation deviceInfo)
        {
            try
            {
                await Task.Run(() =>
                {
                    // Machine GUID من الريجستري
                    try
                    {
                        using (var key = Microsoft.Win32.Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Cryptography"))
                        {
                            deviceInfo.MachineGUID = key?.GetValue("MachineGuid")?.ToString() ?? "";
                        }
                    }
                    catch { }

                    // UUID الجهاز
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_ComputerSystemProduct"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            deviceInfo.DeviceUUID = obj["UUID"]?.ToString() ?? "";
                            break;
                        }
                    }

                    // معرف المعالج الفريد
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Processor"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            try
                            {
                                deviceInfo.ProcessorID = obj["ProcessorId"]?.ToString() ?? "";
                                // استخدام خصائص متاحة فقط
                                var family = SafeGetProperty(obj, "Family");
                                var revision = SafeGetProperty(obj, "Revision");
                                var level = SafeGetProperty(obj, "Level");
                                deviceInfo.CPUSignature = $"Family:{family}-Level:{level}-Revision:{revision}";
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error reading processor info: {ex.Message}");
                                deviceInfo.ProcessorID = "Unknown";
                                deviceInfo.CPUSignature = "Unknown";
                            }
                            break;
                        }
                    }

                    // معلومات BIOS إضافية
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_BIOS"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            deviceInfo.BIOSDate = obj["ReleaseDate"]?.ToString() ?? "";
                            break;
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error collecting unique identifiers: {ex.Message}");
            }
        }

        /// <summary>
        /// جمع معلومات الأجهزة المتقدمة
        /// </summary>
        private static async Task CollectAdvancedHardwareInfo(DeviceInformation deviceInfo)
        {
            try
            {
                await Task.Run(() =>
                {
                    // الأرقام التسلسلية للأقراص الصلبة
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMedia"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            var serial = obj["SerialNumber"]?.ToString()?.Trim();
                            if (!string.IsNullOrEmpty(serial))
                            {
                                deviceInfo.HardDriveSerials.Add(serial);
                            }
                        }
                    }

                    // معلومات الذاكرة والأرقام التسلسلية
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMemory"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            var serial = obj["SerialNumber"]?.ToString()?.Trim();
                            if (!string.IsNullOrEmpty(serial))
                            {
                                deviceInfo.MemorySerialNumbers.Add(serial);
                            }
                        }
                    }

                    // معلومات كرت الرسوميات المتقدمة
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_VideoController"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            var pnpDeviceID = obj["PNPDeviceID"]?.ToString();
                            if (!string.IsNullOrEmpty(pnpDeviceID))
                            {
                                deviceInfo.GraphicsCardSerial = pnpDeviceID;
                                break;
                            }
                        }
                    }

                    // معلومات TPM - قد لا تكون متاحة في جميع الأجهزة
                    try
                    {
                        using (var searcher = new ManagementObjectSearcher("root\\CIMV2\\Security\\MicrosoftTpm", "SELECT * FROM Win32_Tpm"))
                        {
                            foreach (ManagementObject obj in searcher.Get())
                            {
                                deviceInfo.TPMVersion = obj["SpecVersion"]?.ToString() ?? "";
                                break;
                            }
                        }
                    }
                    catch
                    {
                        // TPM غير متاح أو غير مفعل
                        deviceInfo.TPMVersion = "Not Available";
                    }

                    // البحث عن Bluetooth و WiFi MAC addresses
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_NetworkAdapter"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            var name = obj["Name"]?.ToString()?.ToLower() ?? "";
                            var mac = obj["MACAddress"]?.ToString() ?? "";

                            if (!string.IsNullOrEmpty(mac))
                            {
                                if (name.Contains("bluetooth"))
                                {
                                    deviceInfo.BluetoothMAC = mac;
                                }
                                else if (name.Contains("wi-fi") || name.Contains("wireless") || name.Contains("wifi"))
                                {
                                    deviceInfo.WiFiMAC = mac;
                                }
                            }
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error collecting advanced hardware info: {ex.Message}");
            }
        }

        /// <summary>
        /// جمع معلومات الأمان والترخيص
        /// </summary>
        private static async Task CollectSecurityInfo(DeviceInformation deviceInfo)
        {
            try
            {
                await Task.Run(() =>
                {
                    // Security ID للمستخدم الحالي
                    try
                    {
                        var currentUser = System.Security.Principal.WindowsIdentity.GetCurrent();
                        deviceInfo.SecurityID = currentUser.User?.Value ?? "";
                    }
                    catch { }

                    // Installation ID
                    try
                    {
                        using (var key = Microsoft.Win32.Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows NT\CurrentVersion"))
                        {
                            deviceInfo.InstallationID = key?.GetValue("InstallDate")?.ToString() ?? "";
                        }
                    }
                    catch { }

                    // محاولة الحصول على Product Key (محدود بسبب الأمان)
                    try
                    {
                        using (var key = Microsoft.Win32.Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows NT\CurrentVersion"))
                        {
                            var digitalProductId = key?.GetValue("DigitalProductId");
                            if (digitalProductId != null)
                            {
                                deviceInfo.WindowsProductKey = "Protected"; // لأسباب أمنية
                            }
                        }
                    }
                    catch { }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error collecting security info: {ex.Message}");
            }
        }

        /// <summary>
        /// جمع تفاصيل النظام الإضافية
        /// </summary>
        private static async Task CollectSystemDetails(DeviceInformation deviceInfo)
        {
            try
            {
                await Task.Run(() =>
                {
                    // معلومات Windows الإضافية
                    try
                    {
                        using (var key = Microsoft.Win32.Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows NT\CurrentVersion"))
                        {
                            deviceInfo.WindowsBuildNumber = key?.GetValue("CurrentBuildNumber")?.ToString() ?? "";
                            deviceInfo.WindowsEdition = key?.GetValue("EditionID")?.ToString() ?? "";

                            // تاريخ تثبيت Windows
                            var installDate = key?.GetValue("InstallDate");
                            if (installDate != null)
                            {
                                var installDateTime = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(installDate));
                                deviceInfo.WindowsInstallDate = installDateTime.ToString("yyyy-MM-dd HH:mm:ss");
                            }
                        }
                    }
                    catch { }

                    // معلومات النظام الإضافية
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            var lastBootUpTime = obj["LastBootUpTime"]?.ToString();
                            if (!string.IsNullOrEmpty(lastBootUpTime))
                            {
                                deviceInfo.LastBootTime = ManagementDateTimeConverter.ToDateTime(lastBootUpTime).ToString("yyyy-MM-dd HH:mm:ss");

                                // حساب وقت التشغيل
                                var bootTime = ManagementDateTimeConverter.ToDateTime(lastBootUpTime);
                                var uptime = DateTime.Now - bootTime;
                                deviceInfo.SystemUptime = $"{uptime.Days}d {uptime.Hours}h {uptime.Minutes}m";
                            }
                            break;
                        }
                    }

                    // معلومات المعالج الإضافية
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Processor"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            deviceInfo.ProcessorManufacturer = obj["Manufacturer"]?.ToString() ?? "";
                            // استخدام خصائص متاحة
                            var family = obj["Family"]?.ToString() ?? "";
                            var description = obj["Description"]?.ToString() ?? "";
                            deviceInfo.ProcessorFamily = !string.IsNullOrEmpty(family) ? family : description;
                            break;
                        }
                    }

                    // معلومات الصندوق/الهيكل
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_SystemEnclosure"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            var serialNumbers = obj["SerialNumber"] as string[];
                            if (serialNumbers != null && serialNumbers.Length > 0)
                            {
                                deviceInfo.SystemEnclosureSerial = string.Join(", ", serialNumbers);
                            }
                            else
                            {
                                deviceInfo.SystemEnclosureSerial = obj["SerialNumber"]?.ToString() ?? "";
                            }
                            break;
                        }
                    }

                    // جمع معرفات PNP للشبكة
                    var pnpIds = new List<string>();
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_NetworkAdapter WHERE PNPDeviceID IS NOT NULL"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            var pnpId = obj["PNPDeviceID"]?.ToString();
                            if (!string.IsNullOrEmpty(pnpId))
                            {
                                pnpIds.Add(pnpId);
                            }
                        }
                    }
                    deviceInfo.NetworkAdapterPNPIDs = string.Join(" | ", pnpIds);

                    // جمع الأرقام التسلسلية لأجهزة USB
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_LogicalDisk WHERE DriveType = 2"))
                    {
                        foreach (ManagementObject obj in searcher.Get())
                        {
                            var serial = obj["VolumeSerialNumber"]?.ToString();
                            if (!string.IsNullOrEmpty(serial))
                            {
                                deviceInfo.USBDeviceSerials.Add(serial);
                            }
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error collecting system details: {ex.Message}");
            }
        }

        /// <summary>
        /// عرض ملخص معلومات الجهاز
        /// </summary>
        private static void DisplayDeviceInfoSummary(DeviceInformation deviceInfo)
        {
            Console.WriteLine("\n=== ملخص معلومات الجهاز ===");
            Console.WriteLine($"اسم الجهاز: {deviceInfo.ComputerName}");
            Console.WriteLine($"المستخدم: {deviceInfo.UserName}");
            Console.WriteLine($"نظام التشغيل: {deviceInfo.OperatingSystem} {deviceInfo.OSVersion}");
            Console.WriteLine($"المعالج: {deviceInfo.ProcessorName} ({deviceInfo.ProcessorCores} cores)");
            Console.WriteLine($"إجمالي الذاكرة: {deviceInfo.TotalRAM}");
            Console.WriteLine($"النظام: {deviceInfo.SystemManufacturer} {deviceInfo.SystemModel}");

            Console.WriteLine("\n=== المعرفات الفريدة ===");
            Console.WriteLine($"UUID الجهاز: {deviceInfo.DeviceUUID}");
            Console.WriteLine($"Machine GUID: {deviceInfo.MachineGUID}");
            Console.WriteLine($"معرف المعالج: {deviceInfo.ProcessorID}");
            Console.WriteLine($"Security ID: {deviceInfo.SecurityID}");
            Console.WriteLine($"CPU Signature: {deviceInfo.CPUSignature}");

            Console.WriteLine("\n=== معلومات النظام الإضافية ===");
            Console.WriteLine($"Windows Build: {deviceInfo.WindowsBuildNumber}");
            Console.WriteLine($"Windows Edition: {deviceInfo.WindowsEdition}");
            Console.WriteLine($"تاريخ التثبيت: {deviceInfo.WindowsInstallDate}");
            Console.WriteLine($"آخر إقلاع: {deviceInfo.LastBootTime}");
            Console.WriteLine($"وقت التشغيل: {deviceInfo.SystemUptime}");

            Console.WriteLine($"\n=== عناوين MAC المكتشفة: {deviceInfo.MacAddresses.Count} ===");
            foreach (var mac in deviceInfo.MacAddresses)
            {
                Console.WriteLine($"  - {mac}");
            }

            if (!string.IsNullOrEmpty(deviceInfo.WiFiMAC))
                Console.WriteLine($"WiFi MAC: {deviceInfo.WiFiMAC}");
            if (!string.IsNullOrEmpty(deviceInfo.BluetoothMAC))
                Console.WriteLine($"Bluetooth MAC: {deviceInfo.BluetoothMAC}");

            Console.WriteLine($"\n=== الأرقام التسلسلية ===");
            Console.WriteLine($"أقراص التخزين ({deviceInfo.HardDriveSerials.Count}): {string.Join(", ", deviceInfo.HardDriveSerials)}");
            Console.WriteLine($"الذاكرة ({deviceInfo.MemorySerialNumbers.Count}): {string.Join(", ", deviceInfo.MemorySerialNumbers)}");

            Console.WriteLine($"\nمحولات الشبكة: {deviceInfo.NetworkAdapters.Count}");
            Console.WriteLine($"أقراص التخزين: {deviceInfo.DiskDrives.Count}");
        }

        /// <summary>
        /// رفع معلومات الجهاز إلى Google Sheets
        /// </summary>
        private static async Task UploadToGoogleSheets(DeviceInformation deviceInfo)
        {
            try
            {
                if (string.IsNullOrEmpty(SpreadsheetId))
                {
                    Console.WriteLine("تحذير: لم يتم تعيين معرف جدول البيانات. يرجى تحديث SpreadsheetId في الكود.");
                    return;
                }

                if (!File.Exists(GoogleCredentialsPath))
                {
                    Console.WriteLine($"تحذير: ملف الاعتماد غير موجود في: {GoogleCredentialsPath}");
                    Console.WriteLine("يرجى إنشاء ملف credentials.json من Google Cloud Console.");
                    return;
                }

                using (var sheetsService = await CreateSheetsService())
                {
                    if (sheetsService != null)
                    {
                        bool success = await UploadDeviceInformationToSheets(sheetsService, deviceInfo);

                        if (success)
                        {
                            Console.WriteLine("تم رفع معلومات الجهاز بنجاح إلى Google Sheets!");
                            Console.WriteLine($"رابط الجدول: https://docs.google.com/spreadsheets/d/{SpreadsheetId}/edit");
                        }
                        else
                        {
                            Console.WriteLine("فشل في رفع معلومات الجهاز إلى Google Sheets.");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في رفع البيانات إلى Google Sheets: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء خدمة Google Sheets
        /// </summary>
        private static async Task<SheetsService?> CreateSheetsService()
        {
            try
            {
                GoogleCredential credential;

                using (var stream = new FileStream(GoogleCredentialsPath, FileMode.Open, FileAccess.Read))
                {
                    credential = GoogleCredential.FromStream(stream)
                        .CreateScoped(SheetsService.Scope.Spreadsheets);
                }

                return new SheetsService(new BaseClientService.Initializer()
                {
                    HttpClientInitializer = credential,
                    ApplicationName = "VisionPoint Device Information Collector"
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"فشل في إنشاء خدمة Google Sheets: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// رفع معلومات الجهاز إلى جدول البيانات
        /// </summary>
        private static async Task<bool> UploadDeviceInformationToSheets(SheetsService sheetsService, DeviceInformation deviceInfo)
        {
            try
            {
                const string sheetName = "Device Information";

                // التأكد من وجود الجدول والعناوين
                await EnsureSheetExistsWithHeaders(sheetsService, sheetName);

                // إعداد صف البيانات
                var values = PrepareDataRow(deviceInfo);

                // إضافة البيانات إلى الجدول
                var range = $"{sheetName}!A:AS"; // 45 أعمدة
                var valueRange = new ValueRange
                {
                    Values = new List<IList<object>> { values }
                };

                var appendRequest = sheetsService.Spreadsheets.Values.Append(valueRange, SpreadsheetId, range);
                appendRequest.ValueInputOption = SpreadsheetsResource.ValuesResource.AppendRequest.ValueInputOptionEnum.USERENTERED;
                appendRequest.InsertDataOption = SpreadsheetsResource.ValuesResource.AppendRequest.InsertDataOptionEnum.INSERTROWS;

                var response = await appendRequest.ExecuteAsync();

                Console.WriteLine($"تم تحديث النطاق: {response.Updates.UpdatedRange}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في رفع البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التأكد من وجود الجدول والعناوين
        /// </summary>
        private static async Task EnsureSheetExistsWithHeaders(SheetsService sheetsService, string sheetName)
        {
            try
            {
                // فحص وجود الجدول
                var spreadsheet = await sheetsService.Spreadsheets.Get(SpreadsheetId).ExecuteAsync();
                var sheet = spreadsheet.Sheets.FirstOrDefault(s => s.Properties.Title == sheetName);

                if (sheet == null)
                {
                    // إنشاء الجدول
                    await CreateSheet(sheetsService, sheetName);
                }

                // فحص وجود العناوين
                var range = $"{sheetName}!A1:AS1"; // 45 أعمدة
                var request = sheetsService.Spreadsheets.Values.Get(SpreadsheetId, range);
                var response = await request.ExecuteAsync();

                if (response.Values == null || response.Values.Count == 0)
                {
                    // إضافة العناوين
                    await AddHeaders(sheetsService, sheetName);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إعداد الجدول: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء جدول جديد
        /// </summary>
        private static async Task CreateSheet(SheetsService sheetsService, string sheetName)
        {
            var addSheetRequest = new AddSheetRequest
            {
                Properties = new SheetProperties
                {
                    Title = sheetName
                }
            };

            var batchUpdateRequest = new BatchUpdateSpreadsheetRequest
            {
                Requests = new List<Request>
                {
                    new Request { AddSheet = addSheetRequest }
                }
            };

            await sheetsService.Spreadsheets.BatchUpdate(batchUpdateRequest, SpreadsheetId).ExecuteAsync();
            Console.WriteLine($"تم إنشاء الجدول '{sheetName}' بنجاح.");
        }

        /// <summary>
        /// إضافة العناوين إلى الجدول
        /// </summary>
        private static async Task AddHeaders(SheetsService sheetsService, string sheetName)
        {
            var headers = new List<object>
            {
                "تاريخ ووقت الجمع", "اسم الجهاز", "اسم المستخدم", "نظام التشغيل", "إصدار النظام",
                "معمارية النظام", "اسم المعالج", "عدد الأنوية", "إجمالي الذاكرة", "الذاكرة المتاحة",
                "عناوين MAC", "الشركة المصنعة", "موديل النظام", "إصدار BIOS", "الرقم التسلسلي للنظام",
                "الرقم التسلسلي للوحة الأم", "المنطقة الزمنية", "المجال/مجموعة العمل", "كرت الرسوميات", "كرت الصوت",
                "محولات الشبكة", "أقراص التخزين",
                // المعلومات الفريدة الجديدة
                "UUID الجهاز", "Machine GUID", "معرف المعالج", "Security ID", "Installation ID",
                "WiFi MAC", "Bluetooth MAC", "TPM Version", "CPU Signature", "BIOS Date",
                "أرقام الأقراص التسلسلية", "أرقام الذاكرة التسلسلية", "معرف كرت الرسوميات",
                // معلومات النظام الإضافية
                "Windows Build", "Windows Edition", "تاريخ تثبيت Windows", "آخر إقلاع", "وقت التشغيل",
                "شركة المعالج", "عائلة المعالج", "رقم تسلسلي الهيكل", "معرفات PNP للشبكة", "أجهزة USB"
            };

            var range = $"{sheetName}!A1:AS1"; // 45 أعمدة (A إلى AS)
            var valueRange = new ValueRange
            {
                Values = new List<IList<object>> { headers }
            };

            var updateRequest = sheetsService.Spreadsheets.Values.Update(valueRange, SpreadsheetId, range);
            updateRequest.ValueInputOption = SpreadsheetsResource.ValuesResource.UpdateRequest.ValueInputOptionEnum.USERENTERED;

            await updateRequest.ExecuteAsync();
            Console.WriteLine("تم إضافة العناوين بنجاح.");
        }

        /// <summary>
        /// إعداد صف البيانات من معلومات الجهاز
        /// </summary>
        private static List<object> PrepareDataRow(DeviceInformation deviceInfo)
        {
            return new List<object>
            {
                deviceInfo.CollectionDateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                deviceInfo.ComputerName,
                deviceInfo.UserName,
                deviceInfo.OperatingSystem,
                deviceInfo.OSVersion,
                deviceInfo.OSArchitecture,
                deviceInfo.ProcessorName,
                deviceInfo.ProcessorCores.ToString(),
                deviceInfo.TotalRAM,
                deviceInfo.AvailableRAM,
                string.Join(", ", deviceInfo.MacAddresses),
                deviceInfo.SystemManufacturer,
                deviceInfo.SystemModel,
                deviceInfo.BiosVersion,
                deviceInfo.SystemSerialNumber,
                deviceInfo.MotherboardSerialNumber,
                deviceInfo.TimeZone,
                deviceInfo.DomainWorkgroup,
                deviceInfo.GraphicsCard,
                deviceInfo.SoundCard,
                FormatNetworkAdapters(deviceInfo.NetworkAdapters),
                FormatDiskDrives(deviceInfo.DiskDrives),
                // المعلومات الفريدة الجديدة
                deviceInfo.DeviceUUID,
                deviceInfo.MachineGUID,
                deviceInfo.ProcessorID,
                deviceInfo.SecurityID,
                deviceInfo.InstallationID,
                deviceInfo.WiFiMAC,
                deviceInfo.BluetoothMAC,
                deviceInfo.TPMVersion,
                deviceInfo.CPUSignature,
                deviceInfo.BIOSDate,
                string.Join(", ", deviceInfo.HardDriveSerials),
                string.Join(", ", deviceInfo.MemorySerialNumbers),
                deviceInfo.GraphicsCardSerial,
                // معلومات النظام الإضافية
                deviceInfo.WindowsBuildNumber,
                deviceInfo.WindowsEdition,
                deviceInfo.WindowsInstallDate,
                deviceInfo.LastBootTime,
                deviceInfo.SystemUptime,
                deviceInfo.ProcessorManufacturer,
                deviceInfo.ProcessorFamily,
                deviceInfo.SystemEnclosureSerial,
                deviceInfo.NetworkAdapterPNPIDs,
                string.Join(", ", deviceInfo.USBDeviceSerials)
            };
        }

        /// <summary>
        /// تنسيق محولات الشبكة للعرض
        /// </summary>
        private static string FormatNetworkAdapters(List<NetworkAdapterInfo> adapters)
        {
            if (adapters == null || adapters.Count == 0)
                return "";

            return string.Join(" | ", adapters.Select(a =>
                $"{a.Name} ({a.MacAddress}) - {a.Status}"));
        }

        /// <summary>
        /// تنسيق أقراص التخزين للعرض
        /// </summary>
        private static string FormatDiskDrives(List<DiskInfo> drives)
        {
            if (drives == null || drives.Count == 0)
                return "";

            return string.Join(" | ", drives.Select(d =>
                $"{d.DriveLetter} ({d.TotalSize}) - {d.FileSystem}"));
        }

        /// <summary>
        /// الحصول على خاصية من ManagementObject بأمان
        /// </summary>
        private static string SafeGetProperty(ManagementObject obj, string propertyName)
        {
            try
            {
                return obj[propertyName]?.ToString() ?? "";
            }
            catch
            {
                return "";
            }
        }

        /// <summary>
        /// تنسيق البايتات إلى تنسيق قابل للقراءة
        /// </summary>
        private static string FormatBytes(ulong bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;

            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }

            return $"{number:n1} {suffixes[counter]}";
        }

        #endregion
    }
}
