﻿using ClosedXML.Excel;
using ExcelDataReader;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.IO;
using System.Text;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Models;

namespace VisionPoint.UI.PL
{
    public class ExcelProductService : IDisposable
    {
        private readonly AppDbContext _context = ServiceLocator.GetService<AppDbContext>();
        private bool _disposed = false;
        // تخطيط الأعمدة المخصص
        private Dictionary<string, string> _customColumnMapping;
        // معرف المخزن المحدد للاستيراد
        private int? _warehouseId;

        public ExcelProductService()
        {
        }

        // تعيين معرف المخزن للاستيراد
        public void SetWarehouseId(int warehouseId)
        {
            _warehouseId = warehouseId;
        }
        public async Task<(int Added, int Updated, List<string> Errors, List<ExcelProductRecord> ErrorRecords)> ImportExcel(string filePath, IProgress<ProgressReport> progress = null)
        {
            var errors = new List<string>();
            var errorRecords = new List<ExcelProductRecord>();
            int added = 0, updated = 0;
            var state = new ProgressState();

            // التحقق من وجود مخزن محدد
            if (!_warehouseId.HasValue)
            {
                errors.Add("لم يتم تحديد مخزن للاستيراد");
                return (0, 0, errors, errorRecords);
            }

            try
            {
                // Check if file exists
                if (!File.Exists(filePath))
                {
                    errors.Add($"الملف غير موجود: {filePath}");
                    return (0, 0, errors, errorRecords);
                }

                // Check file extension
                string extension = Path.GetExtension(filePath).ToLower();
                if (extension != ".xlsx" && extension != ".xls")
                {
                    errors.Add($"نوع الملف غير مدعوم: {extension}. يجب أن يكون الملف بصيغة Excel (.xlsx أو .xls)");
                    return (0, 0, errors, errorRecords);
                }

                progress?.Report(new ProgressReport { Percentage = 5, Message = "جاري قراءة ملف Excel..." });
                var records = await ReadExcelData(filePath);

                if (records.Count == 0)
                {
                    errors.Add("لم يتم العثور على أي سجلات صالحة في الملف");
                    return (0, 0, errors, errorRecords);
                }

                state.Total = records.Count;
                progress?.Report(new ProgressReport { Percentage = 10, Message = $"تم العثور على {records.Count} سجل..." });

                // تجميع السجلات بحسب اسم المنتج
                var recordGroups = records.GroupBy(r => r.Name).ToList();
                var existingData = await GetExistingData(records);

                progress?.Report(new ProgressReport { Percentage = 15, Message = "جاري معالجة السجلات..." });

                foreach (var group in recordGroups)
                {
                    try
                    {
                        var productName = group.Key;
                        existingData.Products.TryGetValue(productName, out var existingProduct);

                        if (existingProduct == null)
                        {
                            added += await CreateNewProduct(group, existingData, errors, progress, state, errorRecords);
                        }
                        else
                        {
                            updated += await UpdateExistingProduct(existingProduct, group, existingData, errors, progress, state, errorRecords);
                        }
                    }
                    catch (Exception ex)
                    {
                        string errorMessage = $"خطأ في معالجة المنتج {group.Key}: {ex.Message}";
                        errors.Add(errorMessage);
                        System.Diagnostics.Debug.WriteLine(errorMessage);
                        System.Diagnostics.Debug.WriteLine(ex.StackTrace);

                        // Add all records in the group to error records (محسنة)
                        var groupErrorRecords = group.Select(record =>
                        {
                            record.Error = errorMessage;
                            return record;
                        }).ToList();
                        errorRecords.AddRange(groupErrorRecords);
                    }
                }

                progress?.Report(new ProgressReport { Percentage = 95, Message = "جاري حفظ التغييرات..." });
                var saveResult = await _context.SaveWithTransactionAndBusy("ImportExcel");

                if (!saveResult.State)
                {
                    errors.Add($"خطأ في حفظ البيانات: {saveResult.Message}");
                    return (0, 0, errors, errorRecords);
                }

                progress?.Report(new ProgressReport { Percentage = 100, Message = "تم الانتهاء من الاستيراد" });

                return (added, updated, errors, errorRecords);
            }
            catch (Exception ex)
            {
                _context.Reverse();
                string errorMessage = $"خطأ عام: {ex.Message}";
                errors.Add(errorMessage);
                System.Diagnostics.Debug.WriteLine(errorMessage);
                System.Diagnostics.Debug.WriteLine(ex.StackTrace);
                return (0, 0, errors, errorRecords);
            }
        }

        private async Task<int> CreateNewProduct(IGrouping<string, ExcelProductRecord> group, ExistingData existingData,
            List<string> errors, IProgress<ProgressReport> progress, ProgressState state, List<ExcelProductRecord> errorRecords)
        {
            var newProduct = new Product
            {
                Name = group.Key,
                CostPrice = group.First().CostPrice,
                SellPrice = group.First().SellPrice,
                MinimumQuantity = group.First().MinimumQuantity,
                ProductColors = new List<ProductColor>()
            };

            foreach (var record in group)
            {
                state.Processed++;
                progress?.Report(CreateProgressReport(state, $"جاري إضافة المنتج: {record.Name}"));

                // Handle color
                byte? colorId = null;
                if (!string.IsNullOrEmpty(record.HexCode))
                {
                    if (existingData.Colors.TryGetValue(record.HexCode, out var colorEntry))
                    {
                        colorId = colorEntry.Id;
                    }
                    else
                    {
                        // Create new color if it doesn't exist
                        var newColor = new Color
                        {
                            HexCode = record.HexCode,
                            Name = record.Color
                        };
                        _context.Colors.AddWithBusy(newColor);
                        var result = await _context.SaveWithTransactionAndBusy("SaveNewColorInProduct");
                        colorId = newColor.Id;
                        existingData.Colors[record.HexCode] = newColor;
                    }
                }

                try
                {
                    // البحث عن اللون بنفس Barcode داخل المنتج الحالي
                    var existingColor = newProduct.ProductColors.FirstOrDefault(pc =>
                        (colorId == null && pc.ColorId == null && (pc.Barcode == record.Barcode || (string.IsNullOrEmpty(pc.Barcode) && string.IsNullOrEmpty(record.Barcode)))) ||
                        (colorId != null && pc.ColorId == colorId && pc.Barcode == record.Barcode));

                    if (existingColor == null)
                    {
                        var newColor = new ProductColor
                        {
                            ColorId = colorId,
                            Barcode = string.IsNullOrWhiteSpace(record.Barcode) ? null : record.Barcode,
                            ProductQuantity = new List<ProductQuantity>()
                        };
                        newProduct.ProductColors.Add(newColor);
                        existingColor = newColor;
                    }

                    // تحديث أو إضافة الكمية للون المحدد
                    var existingQuantity = existingColor.ProductQuantity.FirstOrDefault(q =>
                        q.Exp == record.Exp && q.WarehouseId == _warehouseId.Value);
                    if (existingQuantity != null)
                    {
                        existingQuantity.Quantity += record.Qte;
                    }
                    else
                    {
                        existingColor.ProductQuantity.Add(new ProductQuantity
                        {
                            Exp = record.Exp,
                            Quantity = record.Qte,
                            WarehouseId = _warehouseId.Value
                        });
                    }
                }
                catch (Exception ex)
                {
                    // Log the error and add it to the errors list
                    string errorMessage = $"خطأ في معالجة المنتج {record.Name}: {ex.Message}";
                    errors.Add(errorMessage);
                    record.Error = errorMessage;
                    errorRecords.Add(record);
                    System.Diagnostics.Debug.WriteLine(errorMessage);
                }
            }

            // تحديث العلامات الخاصة بتوفر ألوان أو تواريخ انتهاء الصلاحية
            // إذا كان أي سجل يحتوي على قيمة فارغة في حقل اللون، قم بتعيين Color=false
            bool hasEmptyColor = group.Any(r => string.IsNullOrEmpty(r.Color) || string.IsNullOrEmpty(r.HexCode));
            if (hasEmptyColor)
            {
                newProduct.Color = false;
            }
            else
            {
                newProduct.Color = newProduct.ProductColors.Any();
            }
            newProduct.Exp = newProduct.ProductColors.Any(pc => pc.ProductQuantity.Any(q => q.Exp != null));

            await _context.Products.AddAsyncWithBusy(newProduct);
            return 1;
        }

        private async Task<int> UpdateExistingProduct(Product existingProduct, IGrouping<string, ExcelProductRecord> group,
            ExistingData existingData, List<string> errors, IProgress<ProgressReport> progress, ProgressState state, List<ExcelProductRecord> errorRecords)
        {
            int updates = 0;

            foreach (var record in group)
            {
                state.Processed++;
                progress?.Report(CreateProgressReport(state, $"جاري تحديث المنتج: {record.Name}"));

                // تحديث السعر والحد الأدنى
                existingProduct.CostPrice = record.CostPrice;
                existingProduct.SellPrice = record.SellPrice;
                existingProduct.MinimumQuantity = record.MinimumQuantity;

                // Handle color
                byte? colorId = null;
                if (!string.IsNullOrEmpty(record.HexCode))
                {
                    if (existingData.Colors.TryGetValue(record.HexCode, out var colorEntry))
                    {
                        colorId = colorEntry.Id;
                    }
                    else
                    {
                        // Create new color if it doesn't exist
                        var newColor = new Color
                        {
                            HexCode = record.HexCode,
                            Name = record.Color
                        };
                        _context.Colors.AddWithBusy(newColor);
                        var result = await _context.SaveWithTransactionAndBusy("SaveNewColorInUpdateProduct");
                        if (!result.State)
                        {
                            errors.Add($"خطأ في حفظ اللون الجديد: {result.Message}");
                            record.Error = $"خطأ في حفظ اللون الجديد: {result.Message}";
                            errorRecords.Add(record);
                            continue;
                        }
                        colorId = newColor.Id;
                        existingData.Colors[record.HexCode] = newColor;
                    }
                }

                try
                {
                    // البحث عن اللون بنفس Barcode داخل المنتج الحالي
                    var existingColor = existingProduct.ProductColors.FirstOrDefault(pc =>
                        (colorId == null && pc.ColorId == null && (pc.Barcode == record.Barcode || (string.IsNullOrEmpty(pc.Barcode) && string.IsNullOrEmpty(record.Barcode)))) ||
                        (colorId != null && pc.ColorId == colorId && pc.Barcode == record.Barcode));

                    if (existingColor == null)
                    {
                        var newColor = new ProductColor
                        {
                            ColorId = colorId,
                            Barcode = string.IsNullOrWhiteSpace(record.Barcode) ? null : record.Barcode,
                            ProductQuantity = new List<ProductQuantity>()
                        };
                        existingProduct.ProductColors.Add(newColor);
                        existingColor = newColor;
                        updates++;
                    }

                    // تحديث أو إضافة الكمية للون المحدد
                    var existingQuantity = existingColor.ProductQuantity.FirstOrDefault(q =>
                        q.Exp == record.Exp && q.WarehouseId == _warehouseId.Value);
                    if (existingQuantity != null)
                    {
                        existingQuantity.Quantity += record.Qte;
                    }
                    else
                    {
                        existingColor.ProductQuantity.Add(new ProductQuantity
                        {
                            Exp = record.Exp,
                            Quantity = record.Qte,
                            WarehouseId = _warehouseId.Value
                        });
                        updates++;
                    }
                }
                catch (Exception ex)
                {
                    // Log the error and add it to the errors list
                    string errorMessage = $"خطأ في تحديث المنتج {record.Name}: {ex.Message}";
                    errors.Add(errorMessage);
                    record.Error = errorMessage;
                    errorRecords.Add(record);
                    System.Diagnostics.Debug.WriteLine(errorMessage);
                }
            }

            // إذا كان أي سجل يحتوي على قيمة فارغة في حقل اللون، قم بتعيين Color=false
            bool hasEmptyColor = group.Any(r => string.IsNullOrEmpty(r.Color) || string.IsNullOrEmpty(r.HexCode));
            if (hasEmptyColor)
            {
                existingProduct.Color = false;
            }
            else
            {
                existingProduct.Color = existingProduct.ProductColors.Any();
            }
            existingProduct.Exp = existingProduct.ProductColors.Any(pc => pc.ProductQuantity.Any(q => q.Exp != null));
            return updates;
        }

        private async Task<List<ExcelProductRecord>> ReadExcelData(string filePath)
        {
            try
            {
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                var result = new List<ExcelProductRecord>();

                using var stream = File.Open(filePath, FileMode.Open, FileAccess.Read);
                using var reader = ExcelReaderFactory.CreateReader(stream);
                var dataSet = reader.AsDataSet(new ExcelDataSetConfiguration
                {
                    ConfigureDataTable = _ => new ExcelDataTableConfiguration
                    {
                        UseHeaderRow = true
                    }
                });

                var table = dataSet.Tables[0];
                for (int i = 0; i < table.Rows.Count; i++)
                {
                    var row = table.Rows[i];
                    try
                    {
                        // Check if the required columns exist
                        if (!HasRequiredColumns(row))
                        {
                            throw new Exception("الملف لا يحتوي على الأعمدة المطلوبة");
                        }

                        // Get column names that match our expected fields
                        var columnMap = GetColumnMapping(row.Table.Columns);
                        if (columnMap == null)
                        {
                            throw new Exception("لم يتم العثور على الأعمدة المطلوبة في الملف");
                        }

                        // Direct column access using the mapped column names
                        var record = new ExcelProductRecord
                        {
                            Name = row[columnMap["Name"]].ToString(),
                            Barcode = row[columnMap["Barcode"]].ToString(),
                            CostPrice = GetDecimal(row[columnMap["CostPrice"]]) ?? 0,
                            SellPrice = GetDecimal(row[columnMap["SellPrice"]]) ?? 0,
                            Qte = GetInt(row[columnMap["Quantity"]]),
                            MinimumQuantity = columnMap.ContainsKey("MinimumQuantity") ? GetInt(row[columnMap["MinimumQuantity"]]) : 0,
                            Color = columnMap.ContainsKey("Color") ? row[columnMap["Color"]].ToString() : "",
                            HexCode = columnMap.ContainsKey("HexCode") ? row[columnMap["HexCode"]].ToString() : ""
                        };

                        // Handle expiration date separately to avoid exceptions
                        try
                        {
                            if (columnMap.ContainsKey("Expiration"))
                            {
                                record.Exp = ParseExpDate(row[columnMap["Expiration"]]);
                            }
                        }
                        catch
                        {
                            // If there's an error parsing the expiration date, leave it as null
                            record.Exp = null;
                        }

                        // Skip rows with no product name (required field)
                        if (string.IsNullOrEmpty(record.Name))
                            continue;

                        result.Add(record);
                    }
                    catch (Exception ex)
                    {
                        // Skip invalid rows but log the error
                        System.Diagnostics.Debug.WriteLine($"Error processing row {i}: {ex.Message}");
                        continue;
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error reading Excel file: {ex.Message}");
                throw new Exception($"خطأ في قراءة ملف Excel: {ex.Message}");
            }
        }

        private bool HasRequiredColumns(DataRow row)
        {
            try
            {
                // Get column mapping
                var columnMap = GetColumnMapping(row.Table.Columns);
                if (columnMap == null)
                {
                    return false;
                }

                // Check if all required fields have a mapped column (محسنة)
                var requiredFields = new[] { "Name", "Barcode", "CostPrice", "SellPrice", "Quantity" };
                var missingFields = requiredFields.Where(field => !columnMap.ContainsKey(field)).ToList();

                if (missingFields.Any())
                {
                    System.Diagnostics.Debug.WriteLine($"Missing required field mappings: {string.Join(", ", missingFields)}");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in HasRequiredColumns: {ex.Message}");
                return false;
            }
        }

        // تعيين تخطيط الأعمدة المخصص
        public void SetColumnMapping(Dictionary<string, string> columnMapping)
        {
            _customColumnMapping = columnMapping;
        }

        // تعريف الحقول المطلوبة والاختيارية للمنتجات
        public Dictionary<string, views.Pages.ProductsContent.FieldInfo> GetRequiredFields()
        {
            return new Dictionary<string, views.Pages.ProductsContent.FieldInfo>
            {
                { "Name", new views.Pages.ProductsContent.FieldInfo { DisplayName = "اسم المنتج", IsRequired = true, PossibleColumnNames = new[] { "إسم الصنف", "اسم الصنف", "الصنف", "المنتج", "اسم المنتج", "Name", "Product", "ProductName" } } },
                { "Barcode", new views.Pages.ProductsContent.FieldInfo { DisplayName = "باركود", IsRequired = true, PossibleColumnNames = new[] { "باركود الوحدة", "باركود", "الباركود", "Barcode", "Code" } } },
                { "CostPrice", new views.Pages.ProductsContent.FieldInfo { DisplayName = "سعر تكلفة", IsRequired = true, PossibleColumnNames = new[] { "تكلفة العبوة", "تكلفة", "سعر التكلفة", "CostPrice", "Cost" } } },
                { "SellPrice", new views.Pages.ProductsContent.FieldInfo { DisplayName = "سعر بيع", IsRequired = true, PossibleColumnNames = new[] { "سعر بيع العبوة", "سعر البيع", "سعر", "SellPrice", "Price" } } },
                { "Quantity", new views.Pages.ProductsContent.FieldInfo { DisplayName = "الكمية", IsRequired = true, PossibleColumnNames = new[] { "الكمية", "كمية", "العدد", "Quantity", "Qty", "Count" } } },
                { "MinimumQuantity", new views.Pages.ProductsContent.FieldInfo { DisplayName = "الحد الأدنى", IsRequired = false, PossibleColumnNames = new[] { "الحد الأدنى", "الحد الادنى", "الحد الأدنى للكمية", "الحد الادنى للكمية", "MinimumQuantity", "MinQty", "MinStock" } } },
                { "Color", new views.Pages.ProductsContent.FieldInfo { DisplayName = "اسم اللون", IsRequired = false, PossibleColumnNames = new[] { "إسم اللون", "اسم اللون", "اللون", "ColorName", "Color" } } },
                { "HexCode", new views.Pages.ProductsContent.FieldInfo { DisplayName = "كود اللون", IsRequired = false, PossibleColumnNames = new[] { "كود اللون", "رمز اللون", "HexCode", "ColorCode" } } },
                { "Expiration", new views.Pages.ProductsContent.FieldInfo { DisplayName = "تاريخ الصلاحية", IsRequired = false, PossibleColumnNames = new[] { "له تاريخ صلاحية", "تاريخ الصلاحية", "الصلاحية", "تاريخ انتهاء الصلاحية", "ExpirationDate", "Expiration", "Exp" } } }
            };
        }

        private Dictionary<string, string> GetColumnMapping(DataColumnCollection columns)
        {
            try
            {
                // إذا تم تعيين تخطيط أعمدة مخصص، استخدمه
                if (_customColumnMapping != null && _customColumnMapping.Any())
                {
                    // التحقق من وجود الأعمدة المطلوبة
                    foreach (var mapping in _customColumnMapping)
                    {
                        if (!columns.Contains(mapping.Value))
                        {
                            System.Diagnostics.Debug.WriteLine($"Column not found: {mapping.Value}");
                        }
                    }

                    return _customColumnMapping;
                }

                // Debug: Print all column names to help diagnose issues
                System.Diagnostics.Debug.WriteLine("Available columns in Excel file:");
                foreach (DataColumn column in columns)
                {
                    System.Diagnostics.Debug.WriteLine($"- '{column.ColumnName}'");
                }

                var result = new Dictionary<string, string>();

                // Define possible column names for each field
                var nameColumns = new[] { "اسم المنتج", "المنتج", "إسم المنتج", "اسم الصنف", "الصنف", "Name", "Product", "ProductName" };
                var barcodeColumns = new[] { "باركود الوحدة", "باركود", "الباركود", "Barcode", "Code" };
                var costPriceColumns = new[] { "تكلفة العبوة", "تكلفة", "سعر التكلفة", "CostPrice", "Cost" };
                var sellPriceColumns = new[] { "سعر بيع العبوة", "سعر البيع", "سعر", "SellPrice", "Price" };
                var quantityColumns = new[] { "الكمية", "كمية", "العدد", "Quantity", "Qty", "Count" };
                var minimumQuantityColumns = new[] { "الحد الأدنى", "الحد الادنى", "الحد الأدنى للكمية", "الحد الادنى للكمية", "MinimumQuantity", "MinQty", "MinStock" };
                var colorNameColumns = new[] { "إسم اللون", "اسم اللون", "اللون", "ColorName", "Color" };
                var colorCodeColumns = new[] { "كود اللون", "رمز اللون", "HexCode", "ColorCode" };
                var expirationColumns = new[] { "تاريخ الصلاحية", "الصلاحية", "تاريخ انتهاء الصلاحية", "ExpirationDate", "Expiration", "Exp" };

                // Find matching columns for each field (محسنة - دالة مساعدة)
                var columnMappings = new Dictionary<string, string[]>
                {
                    ["Name"] = nameColumns,
                    ["Barcode"] = barcodeColumns,
                    ["CostPrice"] = costPriceColumns,
                    ["SellPrice"] = sellPriceColumns,
                    ["Quantity"] = quantityColumns,
                    ["MinimumQuantity"] = minimumQuantityColumns,
                    ["Color"] = colorNameColumns,
                    ["HexCode"] = colorCodeColumns,
                    ["Expiration"] = expirationColumns
                };

                foreach (var mapping in columnMappings)
                {
                    var matchingColumn = mapping.Value.FirstOrDefault(columnName => columns.Contains(columnName));
                    if (matchingColumn != null)
                    {
                        result[mapping.Key] = matchingColumn;
                    }
                }

                // Check if we found all required fields (محسنة)
                var requiredFields = new[] { "Name", "Barcode", "CostPrice", "SellPrice", "Quantity" };
                var missingRequiredFields = requiredFields.Where(field => !result.ContainsKey(field)).ToList();

                if (missingRequiredFields.Any())
                {
                    System.Diagnostics.Debug.WriteLine($"Could not find columns for required fields: {string.Join(", ", missingRequiredFields)}");
                    return null;
                }

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetColumnMapping: {ex.Message}");
                return null;
            }
        }

        private async Task<ExistingData> GetExistingData(List<ExcelProductRecord> records)
        {
            var productNames = records.Select(r => r.Name).Distinct().ToList();
            var hexCodes = records.Select(r => r.HexCode).Where(h => !string.IsNullOrEmpty(h)).Distinct().ToList();
            return new ExistingData
            {
                Products = await _context.Products
                    .Include(p => p.ProductColors)
                    .ThenInclude(pc => pc.ProductQuantity)
                    .Where(p => productNames.Contains(p.Name))
                    .ToDictionaryAsyncWithBusy(p => p.Name, "LoadExistingProducts"),
                Colors = await _context.Colors
                    .Where(c => hexCodes.Contains(c.HexCode))
                    .ToDictionaryAsyncWithBusy(c => c.HexCode, "LoadExistingColorsForProducts")
            };
        }

        private decimal? GetDecimal(object value)
        {
            return value == null ? null :
                decimal.TryParse(value.ToString(), out decimal result) ? result : (decimal?)null;
        }

        private DateOnly? GetDate(object value)
        {
            return value == null ? null :
                DateTime.TryParse(value.ToString(), out DateTime result) ?
                DateOnly.FromDateTime(result) : (DateOnly?)null;
        }

        private DateOnly? ParseExpDate(object value)
        {
            if (value == null)
                return null;

            string strValue = value.ToString().Trim();

            // If the value is "لا" or empty, return null (no expiration date)
            if (string.IsNullOrEmpty(strValue) || strValue == "لا")
                return null;

            // Try to parse as a date
            if (DateTime.TryParse(strValue, out DateTime result))
                return DateOnly.FromDateTime(result);

            return null;
        }

        private int GetInt(object value)
        {
            return value == null ? 0 :
                int.TryParse(value.ToString(), out int result) ? result : 0;
        }

        // دالة لإنشاء تقرير التقدم مع رسالة توضح نوع العملية وعدد السجلات المعالجة والنسبة المئوية
        private ProgressReport CreateProgressReport(ProgressState state, string action)
        {
            int percentage = (int)((state.Processed * 100.0) / state.Total);
            string message = $"{action} - تم معالجة {state.Processed} من {state.Total} سجل. نسبة التقدم: {percentage}%";
            return new ProgressReport
            {
                Percentage = percentage,
                Message = message
            };
        }

        public void ExportErrorExcel(List<ExcelProductRecord> errorRecords, string filePath)
        {
            // If the path is a directory, create a file name with timestamp
            string errorFilePath;
            if (Directory.Exists(filePath))
            {
                errorFilePath = Path.Combine(filePath, $"ProductImportErrors_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx");
            }
            else
            {
                // If it's already a file path, use it directly
                errorFilePath = filePath;
            }

            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add("ImportErrors");

            // Using Arabic column names to match import functionality
            worksheet.Cell(1, 1).Value = "إسم المنتج";
            worksheet.Cell(1, 2).Value = "الكمية";
            worksheet.Cell(1, 3).Value = "الحد الأدنى";
            worksheet.Cell(1, 4).Value = "باركود";
            worksheet.Cell(1, 5).Value = "تكلفة";
            worksheet.Cell(1, 6).Value = "سعر بيع";
            worksheet.Cell(1, 7).Value = "تاريخ الصلاحية";
            worksheet.Cell(1, 8).Value = "إسم اللون";
            worksheet.Cell(1, 9).Value = "كود اللون";
            worksheet.Cell(1, 10).Value = "الخطأ";

            int row = 2;
            foreach (var record in errorRecords)
            {
                worksheet.Cell(row, 1).Value = record.Name;
                worksheet.Cell(row, 2).Value = record.Qte;
                worksheet.Cell(row, 3).Value = record.MinimumQuantity;
                worksheet.Cell(row, 4).Value = record.Barcode;
                worksheet.Cell(row, 5).Value = record.CostPrice;
                worksheet.Cell(row, 6).Value = record.SellPrice;
                worksheet.Cell(row, 7).Value = record.Exp?.ToString() ?? "لا";
                worksheet.Cell(row, 8).Value = record.Color;
                worksheet.Cell(row, 9).Value = record.HexCode;
                worksheet.Cell(row, 10).Value = record.Error;
                row++;
            }

            // Auto-fit columns for better readability
            worksheet.Columns().AdjustToContents();
            workbook.SaveAs(errorFilePath);
        }

        // كلاس يمثل سجل بيانات المنتجات في ملف Excel
        public class ExcelProductRecord
        {
            public string Name { get; set; } = string.Empty;
            public string Barcode { get; set; } = string.Empty;
            public string HexCode { get; set; } = string.Empty;
            public string Color { get; set; } = string.Empty;
            public decimal CostPrice { get; set; } = decimal.Zero;
            public decimal SellPrice { get; set; } = decimal.Zero;
            public int Qte { get; set; }
            public int MinimumQuantity { get; set; } = 0;
            public DateOnly? Exp { get; set; }
            public string Error { get; set; } = string.Empty;
        }

        // بيانات موجودة مسبقاً من قاعدة البيانات
        private class ExistingData
        {
            public Dictionary<string, Product> Products { get; set; } = new Dictionary<string, Product>();
            public Dictionary<string, Color> Colors { get; set; } = new Dictionary<string, Color>();
        }

        private class ProgressState
        {
            public int Processed { get; set; }
            public int Total { get; set; }
        }

        public class ProgressReport
        {
            public int Percentage { get; set; }
            public string Message { get; set; }
        }

        // Implement IDisposable pattern
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                    _context?.Dispose();
                }

                // Free unmanaged resources
                _disposed = true;
            }
        }

        // Destructor
        ~ExcelProductService()
        {
            Dispose(false);
        }
    }
}
