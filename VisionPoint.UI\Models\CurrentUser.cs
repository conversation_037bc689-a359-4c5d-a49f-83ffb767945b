using System.Collections.Generic;

public static class CurrentUser
{
    public static int Id { get; set; }
    public static string Name { get; set; }
    public static string Role { get; set; } // للتوافق مع الكود القديم
    public static List<string> Roles { get; set; } = new List<string>();

    /// <summary>
    /// معرف المخزن المرتبط بالمستخدم الحالي
    /// </summary>
    public static int? WarehouseId { get; set; }

    /// <summary>
    /// اسم المخزن المرتبط بالمستخدم الحالي
    /// </summary>
    public static string WarehouseName { get; set; }

    // التحقق مما إذا كان المستخدم يملك دورًا معينًا
    public static bool HasRole(string role)
    {
        return Roles.Contains(role) || Role == role;
    }

    // التحقق مما إذا كان المستخدم يملك أيًا من الأدوار المحددة
    public static bool HasAnyRole(string[] roles)
    {
        if (roles == null || roles.Length == 0)
            return true; // إذا لم تكن هناك أدوار مطلوبة، فالوصول مسموح

        foreach (var role in roles)
        {
            if (HasRole(role))
                return true;
        }

        return false;
    }

    /// <summary>
    /// التحقق مما إذا كان المستخدم يستطيع تغيير المخزن في الواجهات
    /// المدير أو من لديه دور ChangeWarehouseRole يستطيع تغيير المخزن
    /// </summary>
    public static bool CanChangeWarehouse => HasRole("Admin") || HasRole("ChangeWarehouseRole");
}
