using System;
using System.Globalization;
using System.Windows.Data;

namespace VisionPoint.UI.Converters
{
    public class BoolToExchangeTypeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isExchange)
            {
                return isExchange ? "صرف" : "قبض";
            }
            return "غير معروف";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string strValue)
            {
                return strValue == "صرف";
            }
            return false;
        }
    }
} 