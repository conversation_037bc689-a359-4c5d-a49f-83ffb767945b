using DocumentFormat.OpenXml.InkML;
using Microsoft.Win32;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using VisionPoint.UI.Helper;
using VisionPoint.UI.Models;
using static VisionPoint.UI.Helper.ExportPreferencesHelper;
using VisionPoint.UI.PL;
using VisionPoint.UI.ViewModel;
using VisionPoint.UI.views.Pages.Warehouses;

namespace VisionPoint.UI.views.Dialogs
{
    /// <summary>
    /// نافذة معاينة تصدير المنتجات
    /// </summary>
    public partial class ProductExportPreviewWindow : Window
    {
        private readonly ExportPreviewModel _model;
        private readonly ExportService _exportService;
        private string _selectedSavePath = string.Empty;
        string _warehouseName;
        private bool _isExporting = false;
        private bool _isLoadingData = false;
        private List<ProductExportVM> _detailedProductsData = new();

        public bool ExportCompleted { get; private set; } = false;

        public ProductExportPreviewWindow(List<ProductExportVM> productsData, int? warehouseId = null,string warehouseName=null)
        {
            InitializeComponent();

            _exportService = new ExportService();
            _warehouseName= warehouseName ?? "المخزن الافتراضي";
            _detailedProductsData = productsData;
            _model = new ExportPreviewModel
            {
                WarehouseId = warehouseId,
                FileName = $"تقرير_المنتجات_{warehouseName}_{DateTime.Now.ToString("yyyyMMdd_HHmmss")}.xlsx"
            };

            DataContext = _model;

            // تهيئة النافذة
            InitializeWindow();
        }

    
        private async void InitializeWindow()
        {
            try
            {
                // تحميل البيانات المفصلة للمنتجات
                //await LoadDetailedProductsData();
                // التحقق من جودة البيانات المحملة
                ValidateLoadedData();
                // تهيئة حقول التصدير
                InitializeExportFields();

                // تطبيق التفضيلات المحفوظة للمنتجات
                ExportPreferencesHelper.ApplyPreferences(_model, ExportType.Products);

                // تحديث المعاينة
                UpdatePreview();

                // تحديث الملخص
                UpdateSummary();

                // تحديث حالة التبويبات
                UpdateTabsState();
                if (this.Content is Panel mainPanel)
                {
                    mainPanel.IsEnabled = false;
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تهيئة النافذة: {ex.Message}", "خطأ", true);
            }
        }


     
        /// <summary>
        /// التحقق من جودة البيانات المحملة
        /// </summary>
        private void ValidateLoadedData()
        {
            if (_detailedProductsData?.Any() != true)
            {
                System.Diagnostics.Debug.WriteLine("تحذير: لا توجد بيانات للتصدير");
                return;
            }

            // التحقق من التصفية الصحيحة للمخزن
            if (_model.WarehouseId.HasValue)
            {
                var invalidRecords = _detailedProductsData
                    .Where(d => string.IsNullOrEmpty(d.WarehouseName) ||
                               d.WarehouseName == "لا يوجد" ||
                               d.WarehouseName == "غير محدد")
                    .Count();

                if (invalidRecords > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"تحذير: {invalidRecords} سجل يحتوي على مخزن غير صحيح");
                }
            }

            // إحصائيات سريعة للتحقق
            var stats = _detailedProductsData
                .GroupBy(d => d.WarehouseName)
                .Select(g => new { Warehouse = g.Key, Count = g.Count(), Quantity = g.Sum(x => x.Quantity) })
                .ToList();


        }


        private void InitializeExportFields()
        {
            // تهيئة حقول المنتجات
            var productFields = _exportService.GetProductExportFields();
            _model.ItemFields.Clear();

            foreach (var field in productFields)
            {
                _model.ItemFields.Add(new ExportFieldOption
                {
                    FieldKey = field.Key,
                    DefaultName = field.Value.DisplayName,
                    CustomHeader = field.Value.DisplayName, // استخدام نفس الاسم كافتراضي
                    IsSelected = field.Value.IsRequired,
                    IsRequired = field.Value.IsRequired,
                    Description = field.Value.DisplayName
                });
            }

            // ربط الأحداث لتحديث المعاينة عند تغيير الحقول
            foreach (var field in _model.ItemFields)
            {
                field.PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == nameof(ExportFieldOption.IsSelected))
                        UpdateProductsPreview();
                };
            }
        }

        private void UpdatePreview()
        {
            // تحديث معاينة المنتجات
            UpdateProductsPreview();
        }

        private void UpdateProductsPreview()
        {
            try
            {
                if (_isLoadingData)
                {
                    txtProductsPreviewCount.Text = "جاري تحميل البيانات...";
                    dgProductsPreview.ItemsSource = null;
                    return;
                }

                // حساب الإحصائيات باستخدام LINQ
                var dataStats = _detailedProductsData.Any() ? new
                {
                    TotalRecords = _detailedProductsData.Count,
                    UniqueWarehouses = _detailedProductsData.Select(p => p.WarehouseName).Distinct().Count(),
                    TotalQuantity = _detailedProductsData.Sum(p => p.Quantity),
                    TotalValue = _detailedProductsData.Sum(p => p.Quantity * p.SellPrice),
                    WarehouseNames = _detailedProductsData.Select(p => p.WarehouseName).Distinct().ToList()
                } : null;

                // تحديث عدد السجلات مع معلومات مفصلة
                var warehouseInfo = _model.WarehouseId.HasValue ? _warehouseName : "جميع المخازن";

                if (dataStats != null)
                {
                    txtProductsPreviewCount.Text = $"عدد السجلات: {dataStats.TotalRecords:N0} | " +
                                                 $"الكمية: {dataStats.TotalQuantity:N0} | " +
                                                 $"القيمة: {dataStats.TotalValue:N2} | " +
                                                 $"({warehouseInfo})";
                }
                else
                {
                    txtProductsPreviewCount.Text = $"لا توجد بيانات ({warehouseInfo})";
                }

                // تحديث أعمدة DataGrid باستخدام LINQ
                dgProductsPreview.Columns.Clear();

                var selectedFields = _model.ItemFields.Where(f => f.IsSelected).ToList();
                var columns = selectedFields.Select(field => new DataGridTextColumn
                {
                    Header = field.CustomHeader,
                    Width = GetColumnWidth(field.FieldKey),
                    Binding = GetColumnBinding(field.FieldKey)
                }).ToList();

                // إضافة الأعمدة
                columns.ForEach(column => dgProductsPreview.Columns.Add(column));

                // تحديث مصدر البيانات للمعاينة
                dgProductsPreview.ItemsSource = _detailedProductsData;

                // تحذيرات ذكية للمستخدم
                if (_model.WarehouseId.HasValue && dataStats != null)
                {
                    if (dataStats.UniqueWarehouses > 1)
                    {
                        // تحذير: بيانات من مخازن متعددة عند اختيار مخزن محدد
                        txtProductsPreviewCount.Text += $" ⚠️ تحذير: {dataStats.UniqueWarehouses} مخازن مختلفة";
                    }
                    else if (dataStats.UniqueWarehouses == 1 &&
                             dataStats.WarehouseNames.Any(w => w == "لا يوجد" || w == "غير محدد"))
                    {
                        // تحذير: بيانات غير صحيحة
                        txtProductsPreviewCount.Text += " ⚠️ تحذير: بيانات مخزن غير صحيحة";
                    }
                }


            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحديث معاينة المنتجات: {ex.Message}", "خطأ", true);
            }
        }

        private Binding GetColumnBinding(string fieldKey)
        {
            var binding = new Binding();

            switch (fieldKey)
            {
                case "Name":
                    binding.Path = new PropertyPath("Name");
                    break;
                case "ColorName":
                    binding.Path = new PropertyPath("ColorName");
                    break;
                case "ColorCode":
                    binding.Path = new PropertyPath("ColorCode");
                    break;
                case "Barcode":
                    binding.Path = new PropertyPath("Barcode");
                    break;
                case "CostPrice":
                    binding.Path = new PropertyPath("CostPrice");
                    binding.StringFormat = "{0:N3}";
                    break;
                case "SellPrice":
                    binding.Path = new PropertyPath("SellPrice");
                    binding.StringFormat = "{0:N3}";
                    break;
                case "Quantity":
                    binding.Path = new PropertyPath("Quantity");
                    break;
                case "WarehouseName":
                    binding.Path = new PropertyPath("WarehouseName");
                    break;
                case "ExpiryDate":
                    binding.Path = new PropertyPath("ExpiryDateFormatted");
                    break;
                case "MinimumQuantity":
                    binding.Path = new PropertyPath("MinimumQuantity");
                    break;
                case "HasColor":
                    binding.Path = new PropertyPath("HasColorText");
                    break;
                case "HasExpiry":
                    binding.Path = new PropertyPath("HasExpiryText");
                    break;
                default:
                    binding.Path = new PropertyPath("Name");
                    break;
            }

            return binding;
        }

        private DataGridLength GetColumnWidth(string fieldKey)
        {
            return fieldKey switch
            {
                "Name" => new DataGridLength(2, DataGridLengthUnitType.Star),
                "ColorName" => new DataGridLength(1, DataGridLengthUnitType.Star),
                "ColorCode" => new DataGridLength(100),
                "Barcode" => new DataGridLength(120),
                "CostPrice" or "SellPrice" => new DataGridLength(120),
                "Quantity" or "MinimumQuantity" => new DataGridLength(100),
                "WarehouseName" => new DataGridLength(1.5, DataGridLengthUnitType.Star),
                "ExpiryDate" => new DataGridLength(120),
                "HasColor" or "HasExpiry" => new DataGridLength(80),
                _ => new DataGridLength(100)
            };
        }

        private void UpdateSummary()
        {
            try
            {
                if (!_detailedProductsData.Any())
                {
                    txtSummary.Text = "لا توجد بيانات للعرض";
                    return;
                }

                // حساب الإحصائيات الشاملة باستخدام LINQ
                var comprehensiveStats = _detailedProductsData
                    .GroupBy(p => new { p.ProductId, p.WarehouseName })
                    .Select(g => new
                    {
                        ProductId = g.Key.ProductId,
                        WarehouseName = g.Key.WarehouseName,
                        TotalQuantity = g.Sum(x => x.Quantity),
                        TotalValue = g.Sum(x => x.Quantity * x.SellPrice),
                        ColorCount = g.Select(x => x.ColorName).Distinct().Count(),
                        Records = g.Count()
                    })
                    .ToList();

                var overallStats = new
                {
                    TotalRecords = _detailedProductsData.Count,
                    UniqueProducts = _detailedProductsData.Select(p => p.ProductId).Distinct().Count(),
                    UniqueColors = _detailedProductsData.Select(p => p.ColorName).Distinct().Count(),
                    TotalQuantity = _detailedProductsData.Sum(p => p.Quantity),
                    TotalValue = _detailedProductsData.Sum(p => p.Quantity * p.SellPrice),
                    AveragePrice = _detailedProductsData.Where(p => p.Quantity > 0).Average(p => p.SellPrice),
                    ProductsWithColors = _detailedProductsData.Count(p => p.HasColor),
                    ProductsWithExpiry = _detailedProductsData.Count(p => p.HasExpiry),
                    ExpiredProducts = _detailedProductsData.Count(p => p.ExpiryDate.HasValue && p.ExpiryDate < DateOnly.FromDateTime(DateTime.Now)),
                    LowStockProducts = _detailedProductsData.Count(p => p.Quantity <= p.MinimumQuantity && p.MinimumQuantity > 0)
                };

                // إحصائيات المخازن المفصلة
                var warehouseStats = _detailedProductsData
                    .GroupBy(p => p.WarehouseName)
                    .Select(g => new
                    {
                        Name = g.Key,
                        Records = g.Count(),
                        Products = g.Select(x => x.ProductId).Distinct().Count(),
                        Quantity = g.Sum(x => x.Quantity),
                        Value = g.Sum(x => x.Quantity * x.SellPrice)
                    })
                    .OrderByDescending(w => w.Quantity)
                    .ToList();

                var warehouseInfo = _model.WarehouseId.HasValue ? _warehouseName : "جميع المخازن";

                // بناء النص التلخيصي
                var summaryLines = new List<string>
                {
                    $"📊 المخزن: {warehouseInfo}",
                    $"📋 السجلات: {overallStats.TotalRecords:N0} | المنتجات: {overallStats.UniqueProducts:N0} | الألوان: {overallStats.UniqueColors:N0}",
                    $"📦 الكمية: {overallStats.TotalQuantity:N0} | القيمة: {overallStats.TotalValue:N2} | متوسط السعر: {overallStats.AveragePrice:N2}",
                    $"🎨 بألوان: {overallStats.ProductsWithColors:N0} | بصلاحية: {overallStats.ProductsWithExpiry:N0}"
                };

                // إضافة تحذيرات إذا وجدت
                var warnings = new List<string>();
                if (overallStats.ExpiredProducts > 0)
                    warnings.Add($"⚠️ منتهية الصلاحية: {overallStats.ExpiredProducts:N0}");
                if (overallStats.LowStockProducts > 0)
                    warnings.Add($"⚠️ مخزون منخفض: {overallStats.LowStockProducts:N0}");

                if (warnings.Any())
                    summaryLines.Add(string.Join(" | ", warnings));

                // إضافة توزيع المخازن إذا كان هناك أكثر من مخزن
                if (warehouseStats.Count > 1)
                {
                    var warehouseDistribution = warehouseStats
                        .Take(3) // أول 3 مخازن فقط لتوفير المساحة
                        .Select(w => $"{w.Name}: {w.Quantity:N0}")
                        .ToList();

                    if (warehouseStats.Count > 3)
                        warehouseDistribution.Add($"و {warehouseStats.Count - 3} مخازن أخرى");

                    summaryLines.Add($"🏪 توزيع المخازن: {string.Join(" | ", warehouseDistribution)}");
                }

                txtSummary.Text = string.Join("\n", summaryLines);


            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحديث الملخص: {ex.Message}", "خطأ", true);
            }
        }


        private void UpdateTabsState()
        {
            // تحديث حالة التبويبات بناءً على البيانات المتاحة
            var hasProducts = _detailedProductsData.Any();

            // تمكين/تعطيل التبويبات
            foreach (TabItem tab in tabControl.Items)
            {
                    tab.IsEnabled = hasProducts;
            }
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // تحديث المعاينة عند تحميل النافذة
            UpdatePreview();
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnExportNow.IsEnabled = false;
            btnCancel.IsEnabled = false;
            btnSelectAllLensFields.IsEnabled = false;
            btnDeselectAllLensFields.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnExportNow.IsEnabled = true;
            btnCancel.IsEnabled = true;
            btnSelectAllLensFields.IsEnabled = true;
            btnDeselectAllLensFields.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private void btnSelectAllProductFields_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                foreach (var field in _model.ItemFields.Where(f => !f.IsRequired))
                {
                    field.IsSelected = true;
                }
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnDeselectAllProductFields_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                foreach (var field in _model.ItemFields.Where(f => !f.IsRequired))
                {
                    field.IsSelected = false;
                }
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnExport_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق من وجود حقول مختارة
                var selectedProductFields = _model.ItemFields.Where(f => f.IsSelected).ToList();
                if (!selectedProductFields.Any())
                {
                    ErrorBox.Show("يجب اختيار حقل واحد على الأقل للتصدير", "تحذير", true);
                    return;
                }

                // اختيار مكان حفظ الملف
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files (*.xlsx)|*.xlsx",
                    FileName = _model.FileName,
                    InitialDirectory = ExportPreferencesHelper.GetLastExportPath(ExportType.Products)
                };

                if (saveDialog.ShowDialog() != true)
                    return;

                _selectedSavePath = saveDialog.FileName;

                // حفظ مسار التصدير للمنتجات
                ExportPreferencesHelper.SaveLastExportPath(_selectedSavePath, ExportType.Products);

                // بدء عملية التصدير
                await StartExport();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء التصدير: {ex.Message}", "خطأ", true);
            }
            finally
            {
                _isExporting = false;
                progressOverlay.Visibility = Visibility.Collapsed;
            }
        }

        private async Task StartExport()
        {
            try
            {
                _isExporting = true;
                progressOverlay.Visibility = Visibility.Visible;
                progressBar.Value = 0;

                // إعداد مؤشر التقدم
                var progress = new Progress<int>(value =>
                {
                    progressBar.Value = value;
                    txtProgress.Text = $"جاري التصدير... {value}%";
                });

                // تحضير البيانات للتصدير
                var selectedProductFields = _model.ItemFields.Where(f => f.IsSelected).Select(f => f.FieldKey).ToList();
                var productHeaders = _model.ItemFields.Where(f => f.IsSelected)
                    .ToDictionary(f => f.FieldKey, f => f.CustomHeader);

                // تنفيذ التصدير باستخدام البيانات المفصلة
                await _exportService.ExportProductsWithPreviewAsync(
                    _selectedSavePath,
                    _detailedProductsData,
                    true, // exportProducts
                    selectedProductFields,
                    productHeaders,
                    _model.WarehouseId,
                    progress);

                // حفظ التفضيلات للاستخدام المستقبلي
                ExportPreferencesHelper.SavePreferences(_model, ExportType.Products);

                ExportCompleted = true;
                DialogBox.Show("تم بنجاح", "تم تصدير البيانات بنجاح");
                Close();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء التصدير: {ex.Message}", "خطأ", true);
            }
            finally
            {
                _isExporting = false;
                progressOverlay.Visibility = Visibility.Collapsed;
                EnableAllButtons();
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (_isExporting)
                {
                    var result = QuestionBox.Show("تأكيد الإلغاء", "هل أنت متأكد من إلغاء عملية التصدير؟");
                    if (result != MessageBoxResult.Yes) return;
                }

                ExportCompleted = false;
                Close();
            }
            finally
            {
                EnableAllButtons();
            }
        }
    }

    /// <summary>
    /// محول لتحويل القيم المنطقية إلى نعم/لا
    /// </summary>
    public class BooleanToYesNoConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            if (value is bool boolValue)
                return boolValue ? "نعم" : "لا";
            return "لا";
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
