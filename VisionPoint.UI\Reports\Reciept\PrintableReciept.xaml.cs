﻿using System.IO;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using VisionPoint.UI.Models;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.Reports.Reciept
{
    /// <summary>
    /// Interaction logic for PrintableReciept.xaml
    /// </summary>
    public partial class PrintableReciept : Page
    {
        public PrintableReciept(Receipt vm)
        {
            InitializeComponent();
            DataContext = vm;
            LoadInitialLogo();
            LoadData();
        }

        private void LoadInitialLogo()
        {
            string initialLogoPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Assets", "company_logo.png");

            if (File.Exists(initialLogoPath))
            {
                try
                {
                    BitmapImage bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri(initialLogoPath);
                    bitmap.CacheOption = BitmapCacheOption.OnLoad; // Important for releasing file lock
                    bitmap.EndInit();
                    LogoImageElement.Source = bitmap; // Assuming LogoImageElement is your Image's x:Name
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"Error loading initial logo: {ex.Message}", "خطأ");
                }
            }
        }

        private void LoadData()
        {
            txtCompanyName.Text = Properties.Settings.Default.CompanyName;
            txtCompanyNameEng.Text = Properties.Settings.Default.CompanyNameEng;

            string phoneNumber = Properties.Settings.Default.PhoneNumber;
            txtPhone.Text = phoneNumber; // Set the TextBlock next to the Arabic label
        }
    }
}
