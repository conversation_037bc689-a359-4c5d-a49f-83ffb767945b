﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace VisionPoint.UI.views.Dialogs
{

    public partial class DeleteBox : Window
    {
        private static DeleteBox instance;
        private bool windowResult = false;

        private string deleteHeader = "";
        public string DeleteHeader
        {
            set
            {
                deleteHeader = value;
                txtDeleteHeader.Text = deleteHeader;
            }
            get { return deleteHeader; }
        }

        private string deleteName = "";
        public string DeleteName
        {
            set
            {
                deleteName = value;
                txtName.Text = deleteName;
            }
            get { return deleteName; }
        }

        private DeleteBox() // Private constructor
        {
            InitializeComponent();
            
        }

        public static DeleteBox Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new DeleteBox();
                }
                return instance;
            }
        }

        public static bool? Show(string header, string name)
        {
            var dialog = Instance;
            dialog.DeleteHeader = header;
            dialog.DeleteName = name;
            dialog.ShowDialog();
            return dialog.windowResult ? (bool?)true : false;
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnYes.IsEnabled = false;
            btnNo.IsEnabled = false;
            btnClose.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnYes.IsEnabled = true;
            btnNo.IsEnabled = true;
            btnClose.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private void btnYes_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                windowResult = true;
                this.DialogResult = true;
                this.Hide();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnNo_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                windowResult = false;
                this.DialogResult = false;
                this.Hide();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnClose_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                windowResult = false;
                this.DialogResult = false;
                this.Hide();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            if ((Keyboard.Modifiers & ModifierKeys.Alt) == ModifierKeys.Alt)
            {
                e.Cancel = true;
            }

            e.Cancel = true;
        }
    }

}
