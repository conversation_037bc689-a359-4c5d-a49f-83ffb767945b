﻿<Page
    x:Class="VisionPoint.UI.views.Pages.Products.GenralProductPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:VisionPoint.UI.Controls"
    xmlns:converter="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Products"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils"
    Title="GenralProductPage"
    d:Background="White"
    d:Height="1080"
    d:Width="1570"
    FlowDirection="RightToLeft"
    Loaded="Page_Loaded"
    mc:Ignorable="d">
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <converter:IndexToNumberConverter x:Key="IndexToNumberConverter" />
        </ResourceDictionary>
    </Page.Resources>

    <Grid Margin="16">


        <Grid.ColumnDefinitions>
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
            <RowDefinition />
        </Grid.RowDefinitions>







        <Grid Grid.ColumnSpan="2" Margin="0,0,8,0">
            <TextBox
                x:Name="txtName"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                Tag="الاسم" />
        </Grid>

        <Grid Grid.Column="2" Margin="8,0">
            <TextBox
                x:Name="txtCostPrice"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                utils:NumericInputControl.AllowNegative="True"
                utils:NumericInputControl.IsDecimalOnly="True"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                IsReadOnly="True"
                Tag="سعر التكلفة" />
        </Grid>

        <Grid Grid.Column="3" Margin="8,0">
            <TextBox
                x:Name="txtSellPrice"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                utils:NumericInputControl.AllowNegative="True"
                utils:NumericInputControl.IsDecimalOnly="True"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                Tag="سعر البيع" />
        </Grid>

        <Grid
            Grid.Row="1"
            Grid.Column="2"
            Margin="8,0">
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition />
            </Grid.RowDefinitions>
            <TextBlock VerticalAlignment="Center" TextAlignment="Center">لديه تاريخ صلاحية</TextBlock>

            <CheckBox
                x:Name="chkExpire"
                Grid.Row="1"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FlowDirection="LeftToRight"
                IsChecked="True"
                Style="{StaticResource CircleCheckboxFL}" />
        </Grid>

        <Grid
            Grid.Row="1"
            Grid.Column="1"
            Grid.ColumnSpan="1">
            <Grid.RowDefinitions>
                <RowDefinition />
                <RowDefinition />
            </Grid.RowDefinitions>
            <TextBlock VerticalAlignment="Center" TextAlignment="Center">لديه لون</TextBlock>

            <CheckBox
                x:Name="chkColor"
                Grid.Row="1"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FlowDirection="LeftToRight"
                IsChecked="True"
                Style="{StaticResource CircleCheckboxFL}" />
        </Grid>

        <Grid Grid.Row="1" Margin="8,0">
            <TextBox
                x:Name="txtMinimumQuantity"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                utils:NumericInputControl.IsNumericOnly="True"
                BorderThickness="1"
                FontSize="21"
                GotFocus="TextBox_GotFocus"
                Tag="الحد الأدنى للكمية" />
        </Grid>



        <Grid
            Grid.Row="0"
            Grid.Column="4"
            Grid.ColumnSpan="2"
            Margin="8,0">
            <ComboBox
                x:Name="cmbWarehouse"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                DisplayMemberPath="Name"
                FontSize="21"
                IsEditable="False"
                SelectedValuePath="Id"
                SelectionChanged="cmbWarehouse_SelectionChanged"
                Tag="المخزن" />
        </Grid>

        <Grid
            Grid.Row="2"
            Grid.Column="2"
            Grid.ColumnSpan="2"
            Margin="8,0">
            <TextBox
                x:Name="txtSearch"
                Height="60"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                Tag="بحث" />
        </Grid>

        <Border
            x:Name="btnImport"
            Grid.Row="2"
            Grid.Column="0"
            MaxHeight="44"
            Margin="8,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnImport_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White">
                استيراد اكسل
            </TextBlock>
        </Border>

        <Border
            x:Name="btnExport"
            Grid.Row="2"
            Grid.Column="1"
            MaxHeight="44"
            Margin="8,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnExport_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}">
                تصدير اكسل
            </TextBlock>
        </Border>

        <Border
            x:Name="btnSearch"
            Grid.Row="3"
            Grid.Column="4"
            Grid.ColumnSpan="1"
            MaxHeight="44"
            Margin="8,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnSearch_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White">
                بحث
            </TextBlock>
        </Border>

        <Border
            x:Name="btnAddValues"
            Grid.Row="3"
            Grid.Column="5"
            MaxHeight="44"
            Margin="16,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnAddValues_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}">
                عرض المنتج
            </TextBlock>
        </Border>

        <Border
            x:Name="btnSave"
            Grid.Row="3"
            Grid.Column="0"
            MaxHeight="44"
            Margin="8,0"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnSave_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White">
                حفظ
            </TextBlock>
        </Border>

        <Border
            x:Name="btnNew"
            Grid.Row="3"
            Grid.Column="1"
            MaxHeight="44"
            Margin="8,0"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnNew_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}">
                جديد
            </TextBlock>
        </Border>



        <Border
            x:Name="btnDelete"
            Grid.Row="3"
            Grid.Column="2"
            MaxHeight="44"
            Margin="8,0"
            Background="Transparent"
            BorderBrush="{StaticResource errorColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnDelete_MouseLeftButtonDown">

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition />
                    <ColumnDefinition Width="30" />
                </Grid.ColumnDefinitions>
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="18"
                    Foreground="{StaticResource errorColor}">
                    حذف
                </TextBlock>

                <Path
                    Grid.Column="1"
                    Width="24"
                    Height="24"
                    HorizontalAlignment="Center"
                    Cursor="Hand"
                    Data="{StaticResource Trash}"
                    Fill="{StaticResource errorColor}"
                    FlowDirection="LeftToRight"
                    Stretch="Uniform"
                    StrokeThickness="1" />
            </Grid>
        </Border>
        <ListView
            x:Name="list"
            Grid.Row="4"
            Grid.RowSpan="6"
            Grid.ColumnSpan="8"
            AlternationCount="2147483647"
            Background="{DynamicResource PageColor}"
            BorderThickness="1"
            FontFamily="pack://application:,,,/Assets/#Cairo"
            ItemsSource="{Binding}"
            MouseDoubleClick="list_MouseDoubleClick"
            ScrollViewer.HorizontalScrollBarVisibility="Hidden"
            SizeChanged="list_SizeChanged">
            <ListView.BorderBrush>
                <SolidColorBrush Opacity="0.42" Color="Black" />
            </ListView.BorderBrush>
            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Style.Triggers>
                        <Trigger Property="Control.IsMouseOver" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="Foreground" Value="Black" />
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="False" />
                                <Condition Property="IsMouseOver" Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter Property="FontWeight" Value="Thin" />
                            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextColor}" />
                        </MultiTrigger>
                    </Style.Triggers>
                    <Setter Property="Control.Background" Value="{DynamicResource PageColor}" />
                    <Setter Property="Control.HorizontalContentAlignment" Value="Stretch" />
                </Style>
            </ListView.ItemContainerStyle>
            <ListView.View>
                <GridView AllowsColumnReorder="False">
                    <GridView.ColumnHeaderContainerStyle>
                        <Style BasedOn="{StaticResource ListViewHeader}" TargetType="{x:Type GridViewColumnHeader}">
                            <Setter Property="IsEnabled" Value="False" />
                            <Setter Property="Height" Value="60" />
                            <Style.Triggers>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="TextElement.Foreground" Value="Black" />
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </GridView.ColumnHeaderContainerStyle>
                    <GridViewColumn Width="Auto" Header="#">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="35"
                                    HorizontalAlignment="Center"
                                    Text="{Binding ., Converter={StaticResource IndexToNumberConverter}, ConverterParameter={x:Reference list}}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="اسم المنتج">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="150"
                                    HorizontalAlignment="Center"
                                    Text="{Binding Name, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>


                    <GridViewColumn Width="Auto" Header="السعر التكلفة">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="100"
                                    HorizontalAlignment="Center"
                                    Text="{Binding CostPrice, FallbackValue='0', TargetNullValue='0'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="السعر البيع">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="100"
                                    HorizontalAlignment="Center"
                                    Text="{Binding SellPrice, FallbackValue='0', TargetNullValue='0'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="Auto" Header="الكمية">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    Height="45"
                                    MinWidth="80"
                                    HorizontalAlignment="Center"
                                    Text="{Binding TotalQuantity, FallbackValue='0', TargetNullValue='0'}"
                                    TextAlignment="Center" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
        </ListView>

        <!--  Loading Overlay removed - now handled at application level in MainWindow  -->
    </Grid>
</Page>
