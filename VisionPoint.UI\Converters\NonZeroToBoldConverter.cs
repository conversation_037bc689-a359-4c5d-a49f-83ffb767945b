﻿﻿using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace VisionPoint.UI.Converters
{
    /// <summary>
    /// محول يقوم بتحويل القيم غير الصفرية إلى خط عريض
    /// </summary>
    public class NonZeroToBoldConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal decimalValue)
            {
                return decimalValue != 0 ? FontWeights.Bold : FontWeights.Normal;
            }
            
            return FontWeights.Normal;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
