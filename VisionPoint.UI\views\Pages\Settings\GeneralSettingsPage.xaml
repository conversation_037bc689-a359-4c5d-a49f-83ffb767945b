﻿<Page
    x:Class="VisionPoint.UI.views.Pages.Settings.GeneralSettingsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Settings"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="GeneralSettingsPage"
    d:Background="White"
    d:Height="1080"
    d:Width="1570"
    FlowDirection="RightToLeft"
    Loaded="Page_Loaded"
    mc:Ignorable="d">

    <Grid Margin="32">
        <Grid.ColumnDefinitions>
            <ColumnDefinition />
            <ColumnDefinition />
            <ColumnDefinition />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>





        <Border
            x:Name="btnExpenses"
            Height="60"
            Margin="16,8"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnExpenses_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White"
                Text="المصروفات" />
        </Border>

        <Border
            x:Name="btnDiscount"
            Grid.Column="1"
            Height="60"
            Margin="16,8"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnDiscount_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}"
                Text="التخفيض" />
        </Border>

        <Border
            x:Name="btnExipres"
            Grid.Row="1"
            Grid.Column="0"
            Height="60"
            Margin="16,8"
            Background="{StaticResource PrimaryColor}"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnExipres_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="White"
                Text="صلاحية الاصناف" />
        </Border>

        <Border
            x:Name="btnMinimumQuantity"
            Grid.Row="1"
            Grid.Column="1"
            Height="60"
            Margin="16,8"
            Background="Transparent"
            BorderBrush="{StaticResource PrimaryColor}"
            BorderThickness="1"
            CornerRadius="8"
            Cursor="Hand"
            MouseLeftButtonDown="btnMinimumQuantity_MouseLeftButtonDown">
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="18"
                Foreground="{StaticResource PrimaryColor}"
                Text="الحد الأدنى للكمية" />
        </Border>

        <Grid
            Grid.Row="3"
            Grid.Column="0"
            Margin="8,0">

            <DockPanel>
                <TextBlock
                    HorizontalAlignment="Center"
                    DockPanel.Dock="Top"
                    Text="السماح بالكميات بالسالب" />
                <CheckBox
                    x:Name="chkAllowNegativeQuantities"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Checked="chkAllowNegativeQuantities_Checked"
                    DockPanel.Dock="Top"
                    Style="{StaticResource CircleCheckboxFL}"
                    Unchecked="chkAllowNegativeQuantities_Unchecked" />
            </DockPanel>
        </Grid>


        <Grid
            Grid.Row="3"
            Grid.Column="1"
            Margin="8,0">

            <DockPanel>
                <TextBlock
                    HorizontalAlignment="Center"
                    DockPanel.Dock="Top"
                    Text="تفعيل فحص الحد الأدنى للكمية" />
                <CheckBox
                    x:Name="chkEnableMinimumQuantityCheck"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Checked="chkEnableMinimumQuantityCheck_Checked"
                    DockPanel.Dock="Top"
                    Style="{StaticResource CircleCheckboxFL}"
                    Unchecked="chkEnableMinimumQuantityCheck_Unchecked" />
            </DockPanel>
        </Grid>

    </Grid>
</Page>
