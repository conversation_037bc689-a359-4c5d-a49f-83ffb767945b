﻿<Page
    x:Class="VisionPoint.UI.Reports.SalesReciept.SalesHeader"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.Reports.SalesReciept"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="SalesHeader"
    Width="800"
    Height="300"
    Background="White"
    FlowDirection="RightToLeft"
    mc:Ignorable="d">
    <Border>


        <Grid Margin="32,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Image
                x:Name="LogoImageElement"
                Grid.RowSpan="4"
                Grid.ColumnSpan="4"
                Source="{x:Null}" />

            <TextBlock
                x:Name="CNameAr"
                Grid.Row="0"
                Grid.Column="7"
                Grid.ColumnSpan="5"
                VerticalAlignment="Center"
                FontSize="21"
                FontWeight="Bold"
                Text="مجموعة النمارق للبصريات"
                TextAlignment="Center" />

            <DockPanel
                Grid.RowSpan="4"
                Grid.Column="8"
                Grid.ColumnSpan="4"
                VerticalAlignment="Center"
                LastChildFill="True">
                <TextBlock
                    VerticalAlignment="Top"
                    DockPanel.Dock="Top"
                    FontSize="18"
                    Text="باركود"
                    TextAlignment="Center"
                    TextWrapping="Wrap" />

                <Image
                    x:Name="imgBarcode"
                    Grid.Row="2"
                    Grid.RowSpan="3"
                    Grid.Column="5"
                    Grid.ColumnSpan="3"
                    MaxHeight="80"
                    VerticalAlignment="Center"
                    DockPanel.Dock="Top"
                    Stretch="Uniform" />




            </DockPanel>

            <TextBlock
                x:Name="txtHeader"
                Grid.Row="1"
                Grid.RowSpan="2"
                Grid.Column="4"
                Grid.ColumnSpan="4"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="46"
                Text="فاتورة" />

            <TextBlock
                x:Name="ReceiptNumber"
                Grid.Row="2"
                Grid.RowSpan="2"
                Grid.Column="3"
                Grid.ColumnSpan="6"
                VerticalAlignment="Center"
                d:Text="00000"
                DockPanel.Dock="Top"
                FontSize="18"
                TextAlignment="Center"
                TextWrapping="Wrap" />
            <DockPanel
                Grid.Row="4"
                Grid.RowSpan="2"
                Grid.ColumnSpan="5"
                HorizontalAlignment="Center"
                LastChildFill="False">
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="18"
                    Text="تاريخ  :  " />
                <TextBlock
                    x:Name="txtDate"
                    VerticalAlignment="Center"
                    FontSize="18"
                    Text="24/4/2025" />
            </DockPanel>



            <DockPanel
                Grid.Row="4"
                Grid.Column="6"
                Grid.ColumnSpan="10"
                HorizontalAlignment="Center"
                LastChildFill="False">
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="18"
                    Text="الاسم  :  " />
                <TextBlock
                    x:Name="txtName"
                    VerticalAlignment="Center"
                    FontSize="18"
                    Text="عبدالرحمن سالم مسعود" />
            </DockPanel>



            <DockPanel
                Grid.Row="5"
                Grid.Column="6"
                Grid.ColumnSpan="10"
                HorizontalAlignment="Center"
                LastChildFill="False">
                <TextBlock
                    VerticalAlignment="Center"
                    FontSize="18"
                    Text="رقم الهاتف  :  " />
                <TextBlock
                    x:Name="txtPhone"
                    VerticalAlignment="Center"
                    d:Text="090000000"
                    FontSize="18" />
            </DockPanel>

        </Grid>
    </Border>
</Page>
