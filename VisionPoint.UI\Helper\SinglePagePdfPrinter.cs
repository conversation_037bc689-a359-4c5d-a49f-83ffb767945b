﻿using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using PdfSharp.Pdf;
using PdfSharp.Drawing;
using System.Diagnostics;


namespace VisionPoint.UI.Helper
{

    public class SinglePagePdfPrinter
    {
        public void ExportAndPrint(Page page, string outputFolder, string fileName, string printerName, bool Landscape = false)
        {
            // Keep a reference to the page initially
            Page pageToRender = page;
            string tempImagePath = null;
            string pdfPath = null;

            try
            {
                if (!Directory.Exists(outputFolder))
                    Directory.CreateDirectory(outputFolder);

                pdfPath = Path.Combine(outputFolder, $"{fileName}.pdf");

                // Render the page to a bitmap
                BitmapSource bitmap = RenderPageToBitmap(pageToRender);

                // Save image temporarily
                tempImagePath = Path.Combine(Path.GetTempPath(), Guid.NewGuid() + ".png");

                // Save to PNG
                BitmapEncoder encoder = new PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(bitmap));
                using (FileStream fs = new FileStream(tempImagePath, FileMode.Create))
                {
                    encoder.Save(fs);
                }

                // --- IMPORTANT: Release reference to bitmap after saving to file ---
                bitmap = null; // Allow bitmap to be garbage collected

                // Load image into PDF
                using (var image = XImage.FromFile(tempImagePath))
                {
                    BitmapImage tempbitmap = new BitmapImage(new Uri(tempImagePath, UriKind.Absolute));
                    double widthInPoints = tempbitmap.PixelWidth * 72.0 / 96.0;
                    double heightInPoints = tempbitmap.PixelHeight * 72.0 / 96.0;


                    PdfDocument pdf = new PdfDocument();
                    PdfPage pdfPage = pdf.AddPage();
                    pdfPage.Width = XUnit.FromPoint(image.PointWidth);
                    pdfPage.Height = XUnit.FromPoint(image.PointHeight);
                    pdfPage.Width = widthInPoints;
                    pdfPage.Height = heightInPoints;

                    using (XGraphics gfx = XGraphics.FromPdfPage(pdfPage))
                    {
                        gfx.DrawImage(image, 0, 0, pdfPage.Width, pdfPage.Height);
                    }

                    pdf.Save(pdfPath);
                }

                // Print the generated PDF
                PrintPdf(pdfPath, printerName, Landscape);
            }
            finally
            {
                // Clean up temporary image file
                if (File.Exists(tempImagePath))
                {
                    try { File.Delete(tempImagePath); } catch { /* Suppress */ }
                }
                // pdfPath deletion is handled within PrintPdf, but ensure it's removed if printing fails
                if (File.Exists(pdfPath) && !File.Exists(tempImagePath)) // tempImagePath might not exist if it was deleted
                {
                    // Small delay to ensure printer has released the file
                    System.Threading.Thread.Sleep(500);
                    try { File.Delete(pdfPath); } catch { /* Suppress */ }
                }

                // --- CRITICAL: Release the Page object and clear its visual tree to aid garbage collection ---
                if (pageToRender != null)
                {
                    // Unparent the page from its logical and visual tree
                    if (pageToRender.Parent is ContentControl parentContentControl)
                    {
                        parentContentControl.Content = null;
                    }
                    else if (pageToRender.Parent is Panel parentPanel)
                    {
                        parentPanel.Children.Remove(pageToRender);
                    }
                    // If it's a Window's content
                    else if (pageToRender.Parent is Window parentWindow)
                    {
                        parentWindow.Content = null;
                    }
                    // Null out the reference
                    pageToRender = null;
                }
                // Force a garbage collection if memory is a significant concern, but use sparingly.
                // GC.Collect(); 
                // GC.WaitForPendingFinalizers();
            }
        }

        private BitmapSource RenderPageToBitmap(Page page)
        {
            if (page == null)
                return new RenderTargetBitmap(1, 1, 96, 96, PixelFormats.Pbgra32);

            double dpi = 96d;

            // Dynamically measure and arrange without fixed size
            page.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
            page.Arrange(new Rect(0, 0, page.DesiredSize.Width, page.DesiredSize.Height));
            page.UpdateLayout();

            ForceImageRefresh(page);

            int width = (int)(page.DesiredSize.Width);
            int height = (int)(page.DesiredSize.Height);

            if (width <= 0 || height <= 0)
            {
                width = 800;  // Fallback default
                height = 1120;
            }

            var renderBitmap = new RenderTargetBitmap(width, height, dpi, dpi, PixelFormats.Pbgra32);
            renderBitmap.Render(page);
            renderBitmap.Freeze();

            return renderBitmap;
        }


        // Existing ForceImageRefresh method (good for freezing BitmapSources)
        private void ForceImageRefresh(DependencyObject obj)
        {
            if (obj is Image img && img.Source is BitmapSource bitmapSource)
            {
                bitmapSource.Freeze(); // Freeze the bitmap source
            }

            int count = VisualTreeHelper.GetChildrenCount(obj);
            for (int i = 0; i < count; i++)
            {
                ForceImageRefresh(VisualTreeHelper.GetChild(obj, i));
            }
        }

        public void PrintPdf(string pdfPath, string printerName, bool Landscape = false)
        {
            try
            {
                var sumatraPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Sumatra", "SumatraPDF.exe");

                if (!File.Exists(sumatraPath) || !File.Exists(pdfPath))
                {
                    Console.WriteLine($"Error: SumatraPDF or PDF file not found. Sumatra Path: {sumatraPath}, PDF Path: {pdfPath}");
                    return;
                }

                string settings = "fit";
                if (Landscape)
                {
                    settings = "fit,landscape";
                }
                var psi = new ProcessStartInfo
                {
                    FileName = sumatraPath,
                    Arguments = $"-silent -print-to \"{printerName}\" -print-settings \"{settings}\" \"{pdfPath}\"",
                    CreateNoWindow = true,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                };

                using (var process = Process.Start(psi))
                {
                    process?.WaitForExit(60000); // Wait up to 60 seconds for Sumatra to finish printing
                    if (process?.HasExited == false)
                    {
                        process.Kill(); // Force kill if it's still running
                        Console.WriteLine("SumatraPDF process timed out and was killed.");
                    }

                    var errorOutput = process?.StandardError.ReadToEnd();
                    if (!string.IsNullOrEmpty(errorOutput))
                    {
                        Console.WriteLine($"SumatraPDF Error: {errorOutput}");
                    }
                    var stdOutput = process?.StandardOutput.ReadToEnd();
                    if (!string.IsNullOrEmpty(stdOutput))
                    {
                        Console.WriteLine($"SumatraPDF Output: {stdOutput}");
                    }
                }

                // Give the printer spooler a moment to grab the file before deleting
                System.Threading.Thread.Sleep(2000); // Wait 2 seconds

                if (File.Exists(pdfPath))
                {
                    try { File.Delete(pdfPath); }
                    catch (IOException ex)
                    {
                        Console.WriteLine($"Could not delete PDF file {pdfPath}: {ex.Message}. It might still be in use.");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error deleting PDF file {pdfPath}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred during PDF printing: {ex.Message}");
            }
        }
    }
}
