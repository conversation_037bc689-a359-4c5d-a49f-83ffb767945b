﻿<Window
    x:Class="VisionPoint.UI.views.Pages.ProductsContent.ExportFieldsWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:Converters="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:VisionPoint.UI"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.ProductsContent"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="اختيار حقول التصدير"
    AllowsTransparency="True"
    Background="Transparent"
    FlowDirection="RightToLeft"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">

        <Grid Width="1920" Height="1080">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="5*" />
                <ColumnDefinition Width="6*" />
                <ColumnDefinition Width="5*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="1*" />
                <RowDefinition Height="6*" />
                <RowDefinition Height="1*" />
            </Grid.RowDefinitions>
            <Border
                Grid.Row="1"
                Grid.Column="1"
                Padding="24"
                Background="{StaticResource backgroundColor}"
                CornerRadius="24">

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="60" />
                        <RowDefinition Height="60" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="60" />
                    </Grid.RowDefinitions>


                    <TextBlock
                        Grid.Row="0"
                        FontSize="28"
                        FontWeight="Bold"
                        Text="اختر الحقول التي تريد تصديرها" />

                    <Grid Grid.Row="1">

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <Border
                            x:Name="btnSelectAll"
                            Height="44"
                            Margin="8,0"
                            Background="{StaticResource PrimaryColor}"
                            CornerRadius="16"
                            MouseLeftButtonDown="btnSelectAll_Click">
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="16"
                                FontWeight="Bold"
                                Foreground="White"
                                Text="تحديد الكل" />
                        </Border>
                        <Border
                            x:Name="btnDeselectAll"
                            Grid.Column="1"
                            Height="44"
                            Margin="8,0"
                            Background="Transparent"
                            BorderBrush="{StaticResource errorColor}"
                            BorderThickness="1"
                            CornerRadius="16"
                            MouseLeftButtonDown="btnDeselectAll_Click">
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="16"
                                FontWeight="Bold"
                                Foreground="{StaticResource errorColor}"
                                Text="إلغاء تحديد الكل" />
                        </Border>
                    </Grid>

                    <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
                        <ItemsControl x:Name="FieldsList">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border
                                        Margin="0,2"
                                        Converters:ClipToCornerRadius.IsEnabled="True"
                                        BorderBrush="{StaticResource PrimaryColor}"
                                        BorderThickness="1"
                                        CornerRadius="24">
                                        <Grid Height="80" MouseLeftButtonUp="Grid_MouseLeftButtonUp">
                                            <Grid.Style>
                                                <Style TargetType="Grid">
                                                    <Setter Property="Background" Value="Transparent" />
                                                    <Setter Property="Opacity" Value="1" />
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsSelected}" Value="true">
                                                            <Setter Property="Background" Value="#666495ED" />
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Grid.Style>
                                            <Border Margin="0,0,8,0" VerticalAlignment="Stretch">

                                                <Grid>

                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition />
                                                        <ColumnDefinition />
                                                    </Grid.ColumnDefinitions>
                                                    <TextBlock
                                                        Margin="5,0"
                                                        VerticalAlignment="Center"
                                                        Text="{Binding DisplayName}" />
                                                    <CheckBox
                                                        x:Name="CheckBoxControlName"
                                                        Grid.Column="1"
                                                        HorizontalAlignment="Right"
                                                        Focusable="False"
                                                        IsChecked="{Binding IsSelected, Mode=TwoWay}"
                                                        IsEnabled="{Binding IsEnabled}"
                                                        IsHitTestVisible="False"
                                                        Style="{StaticResource CircleCheckboxFL}" />
                                                </Grid>
                                            </Border>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>

                    <Grid Grid.Row="3">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <Border
                            x:Name="btnConfirm"
                            Height="44"
                            Margin="8,0"
                            Background="{StaticResource PrimaryColor}"
                            CornerRadius="16"
                            MouseLeftButtonDown="btnConfirm_Click">
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="16"
                                FontWeight="Bold"
                                Foreground="White"
                                Text="تصدير" />
                        </Border>
                        <Border
                            x:Name="btnCancel"
                            Grid.Column="1"
                            Height="44"
                            Margin="8,0"
                            Background="Transparent"
                            BorderBrush="{StaticResource errorColor}"
                            BorderThickness="1"
                            CornerRadius="16"
                            MouseLeftButtonDown="btnCancel_Click">
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="16"
                                FontWeight="Bold"
                                Foreground="{StaticResource errorColor}"
                                Text="إلغاء" />
                        </Border>
                    </Grid>
                </Grid>

            </Border>
        </Grid>
    </Viewbox>
</Window>

