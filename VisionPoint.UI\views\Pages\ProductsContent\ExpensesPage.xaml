﻿<Window
    x:Class="VisionPoint.UI.views.Pages.ProductsContent.ExpensesPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:VisionPoint.UI.Controls"
    xmlns:converter="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:VisionPoint.UI"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.ProductsContent"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="ExpensesPage"
    AllowsTransparency="True"
    Background="Transparent"
    FlowDirection="RightToLeft"
    Loaded="Window_Loaded"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Window.Resources>
        <ResourceDictionary>
            <converter:IndexToNumberConverter x:Key="IndexToNumberConverter" />
        </ResourceDictionary>
    </Window.Resources>
    <Viewbox Stretch="Uniform" StretchDirection="Both">
        <Grid Width="1920" Height="1080">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="3*" />
                <ColumnDefinition Width="2*" />
                <ColumnDefinition Width="3*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="3*" />
                <RowDefinition Height="12*" />
                <RowDefinition Height="3*" />
            </Grid.RowDefinitions>
            <Border
                Grid.Row="1"
                Grid.Column="1"
                Background="{StaticResource backgroundColor}"
                BorderBrush="LightGray"
                BorderThickness="1.5"
                CornerRadius="16">

                <Grid Margin="16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="361*" />
                        <ColumnDefinition Width="84*" />
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="60" />
                        <RowDefinition />
                    </Grid.RowDefinitions>

                    <TextBlock
                        Grid.ColumnSpan="2"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        d:Text="المصروفات"
                        FontSize="24"
                        FontWeight="Bold"
                        Foreground="#333333" />

                    <Border
                        x:Name="btnclose"
                        Grid.Column="1"
                        Width="24"
                        Height="24"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Top"
                        Background="Red"
                        CornerRadius="50"
                        MouseLeftButtonDown="btnclose_MouseLeftButtonDown" />

                    <Grid Grid.Row="1" Grid.ColumnSpan="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                            <RowDefinition />
                            <RowDefinition />
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>

                        <Grid Grid.ColumnSpan="3">
                            <TextBox
                                x:Name="txtName"
                                Height="60"
                                VerticalAlignment="Stretch"
                                VerticalContentAlignment="Stretch"
                                BorderThickness="1"
                                FontSize="21"
                                Tag="اسم المصروف" />
                        </Grid>

                        <Border
                            x:Name="btnSave"
                            Grid.Row="1"
                            Grid.Column="0"
                            MaxHeight="44"
                            Margin="16,0,16,0"
                            Background="{StaticResource PrimaryColor}"
                            CornerRadius="8"
                            Cursor="Hand"
                            MouseLeftButtonDown="btnSave_MouseLeftButtonDown">
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="18"
                                Foreground="White">
                                حفظ
                            </TextBlock>
                        </Border>
                        <Border
                            x:Name="btnNew"
                            Grid.Row="1"
                            Grid.Column="1"
                            MaxHeight="44"
                            Margin="8,0"
                            BorderBrush="{StaticResource PrimaryColor}"
                            BorderThickness="1"
                            CornerRadius="8"
                            Cursor="Hand"
                            MouseLeftButtonDown="btnNew_MouseLeftButtonDown">
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="18"
                                Foreground="{StaticResource PrimaryColor}">
                                جديد
                            </TextBlock>
                        </Border>
                        <Border
                            x:Name="btnDelete"
                            Grid.Row="1"
                            Grid.Column="2"
                            MaxHeight="44"
                            Margin="16,0,16,0"
                            Background="Transparent"
                            BorderBrush="{StaticResource errorColor}"
                            BorderThickness="1"
                            CornerRadius="8"
                            Cursor="Hand"
                            MouseLeftButtonDown="btnDelete_MouseLeftButtonDown">
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="18"
                                Foreground="{StaticResource errorColor}">
                                حذف
                            </TextBlock>
                        </Border>
                        <ListView
                            x:Name="list"
                            Grid.Row="2"
                            Grid.RowSpan="4"
                            Grid.ColumnSpan="3"
                            AlternationCount="2147483647"
                            Background="{DynamicResource PageColor}"
                            BorderThickness="1"
                            FontFamily="pack://application:,,,/Assets/#Cairo"
                            ItemsSource="{Binding}"
                            MouseDoubleClick="list_MouseDoubleClick"
                            ScrollViewer.HorizontalScrollBarVisibility="Hidden"
                            SizeChanged="list_SizeChanged">
                            <ListView.BorderBrush>
                                <SolidColorBrush Opacity="0.42" Color="Black" />
                            </ListView.BorderBrush>
                            <ListView.ItemContainerStyle>
                                <Style TargetType="ListViewItem">
                                    <Style.Triggers>
                                        <Trigger Property="ItemsControl.AlternationIndex" Value="0">
                                            <Setter Property="Background" Value="{DynamicResource PageColor}" />
                                        </Trigger>
                                        <Trigger Property="ItemsControl.AlternationIndex" Value="1">
                                            <Setter Property="Background" Value="{DynamicResource opacitybackgroundHighlight}" />
                                        </Trigger>
                                        <MultiTrigger>
                                            <MultiTrigger.Conditions>
                                                <Condition Property="IsSelected" Value="True" />
                                                <Condition Property="IsMouseOver" Value="False" />
                                            </MultiTrigger.Conditions>
                                            <Setter Property="FontWeight" Value="Bold" />
                                            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextColor}" />
                                        </MultiTrigger>
                                        <MultiTrigger>
                                            <MultiTrigger.Conditions>
                                                <Condition Property="IsSelected" Value="False" />
                                                <Condition Property="IsMouseOver" Value="False" />
                                            </MultiTrigger.Conditions>
                                            <Setter Property="FontWeight" Value="Thin" />
                                            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextColor}" />
                                        </MultiTrigger>
                                    </Style.Triggers>
                                    <Setter Property="Control.Background" Value="{DynamicResource PageColor}" />
                                    <Setter Property="Control.HorizontalContentAlignment" Value="Stretch" />
                                </Style>
                            </ListView.ItemContainerStyle>
                            <ListView.View>
                                <GridView AllowsColumnReorder="False">
                                    <GridView.ColumnHeaderContainerStyle>
                                        <Style BasedOn="{StaticResource ListViewHeader}" TargetType="{x:Type GridViewColumnHeader}">
                                            <Setter Property="IsEnabled" Value="False" />
                                            <Setter Property="Height" Value="60" />
                                            <Style.Triggers>
                                                <Trigger Property="IsEnabled" Value="False">
                                                    <Setter Property="TextElement.Foreground" Value="Black" />
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </GridView.ColumnHeaderContainerStyle>
                                    <GridViewColumn Width="Auto" Header="#">
                                        <GridViewColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock
                                                    Height="45"
                                                    MinWidth="35"
                                                    HorizontalAlignment="Center"
                                                    Text="{Binding ., Converter={StaticResource IndexToNumberConverter}, ConverterParameter={x:Reference list}}"
                                                    TextAlignment="Center" />
                                            </DataTemplate>
                                        </GridViewColumn.CellTemplate>
                                    </GridViewColumn>
                                    <GridViewColumn Width="Auto" Header="اسم المصروف">
                                        <GridViewColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock
                                                    Height="45"
                                                    MinWidth="350"
                                                    HorizontalAlignment="Center"
                                                    Background="Transparent"
                                                    Text="{Binding Name, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}"
                                                    TextAlignment="Center" />
                                            </DataTemplate>
                                        </GridViewColumn.CellTemplate>
                                    </GridViewColumn>
                                </GridView>
                            </ListView.View>
                        </ListView>
                    </Grid>
                </Grid>
            </Border>

            <!--  Loading Overlay removed - now handled at application level in MainWindow  -->
        </Grid>
    </Viewbox>
</Window>

