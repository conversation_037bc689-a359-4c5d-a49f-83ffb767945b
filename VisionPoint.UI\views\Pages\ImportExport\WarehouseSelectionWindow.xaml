<Window
    x:Class="VisionPoint.UI.views.Pages.ImportExport.WarehouseSelectionWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="اختيار المخزن للتصدير"
    AllowsTransparency="True"
    Background="Transparent"
    FlowDirection="RightToLeft"
    Loaded="Window_Loaded"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">

        <Grid Width="1920" Height="1080">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="5*" />
                <ColumnDefinition Width="6*" />
                <ColumnDefinition Width="5*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="2*" />
                <RowDefinition Height="2*" />
                <RowDefinition Height="2*" />
            </Grid.RowDefinitions>
            <Border
                Grid.Row="1"
                Grid.Column="1"
                Padding="24"
                Background="{StaticResource backgroundColor}"
                BorderBrush="{StaticResource PrimaryColor}"
                BorderThickness="1"
                CornerRadius="24">

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="80" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="80" />
                    </Grid.RowDefinitions>

                    <TextBlock
                        Grid.Row="0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        FontSize="28"
                        FontWeight="Bold"
                        Text="اختر المخزن للتصدير" />

                    <Grid Grid.Row="1">

                        <ComboBox
                            x:Name="cmbWarehouse"
                            Grid.Row="2"
                            Height="60"
                            DisplayMemberPath="Name"
                            FontSize="18"
                            SelectedValuePath="Id"
                            SelectionChanged="cmbWarehouse_SelectionChanged"
                            Tag="اختر المخزن" />
                    </Grid>

                    <Grid Grid.Row="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <Border
                            x:Name="btnConfirm"
                            Height="50"
                            Margin="8,0"
                            Background="{StaticResource PrimaryColor}"
                            CornerRadius="16"
                            Cursor="Hand"
                            MouseLeftButtonDown="btnConfirm_Click">
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="18"
                                FontWeight="Bold"
                                Foreground="White"
                                Text="متابعة" />
                        </Border>
                        <Border
                            x:Name="btnCancel"
                            Grid.Column="1"
                            Height="50"
                            Margin="8,0"
                            Background="Transparent"
                            BorderBrush="{StaticResource errorColor}"
                            BorderThickness="1"
                            CornerRadius="16"
                            Cursor="Hand"
                            MouseLeftButtonDown="btnCancel_Click">
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="18"
                                FontWeight="Bold"
                                Foreground="{StaticResource errorColor}"
                                Text="إلغاء" />
                        </Border>
                    </Grid>
                </Grid>

            </Border>
        </Grid>
    </Viewbox>
</Window>
