using System;
using System.Globalization;
using System.Windows.Data;

namespace VisionPoint.UI.Converters
{
    /// <summary>
    /// محول لعرض نوع العميل بناءً على خصائص IsCustomer و IsSupplier
    /// </summary>
    public class ClientTypeConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length >= 2 && values[0] is bool isCustomer && values[1] is bool isSupplier)
            {
                if (isCustomer && isSupplier)
                {
                    return "زبون / مورد";
                }
                else if (isCustomer)
                {
                    return "زبون";
                }
                else if (isSupplier)
                {
                    return "مورد";
                }
            }
            
            return "غير محدد";
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
