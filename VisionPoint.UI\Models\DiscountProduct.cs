﻿﻿﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VisionPoint.UI.Models;

public class DiscountProduct
{
    [Key]
    public int Id { get; set; }

    public int DiscountId { get; set; }
    public Discount Discount { get; set; }

    public int ProductId { get; set; }
    public Product Product { get; set; }

    /// <summary>
    /// Row GUID for SQL Server Merge Replication - managed by database
    /// </summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid RowGuid { get; set; }
}
