# VisionPoint Device Information Collector & License Generator

هذا البرنامج يقوم بجمع معلومات شاملة عن الجهاز وإنشاء ملف ترخيص مشفر، بالإضافة إلى رفع المعلومات إلى Google Sheets.

## الميزات

### الوظائف الأصلية (محفوظة):
- جمع عناوين MAC من جميع محولات الشبكة
- تشفير عناوين MAC باستخدام AES-256
- حفظ الملف المشفر في `AppData\Local\t`

### الوظائف الجديدة:
- جمع معلومات شاملة عن الجهاز:
  - معلومات النظام (اسم الجهاز، المستخدم، نظام التشغيل)
  - معلومات المعالج والذاكرة
  - معلومات الشبكة التفصيلية
  - معلومات أقراص التخزين
  - معلومات الأجهزة (كرت الرسوميات، كرت الصوت)
  - معلومات النظام (BIOS، الشركة المصنعة، الموديل)
- رفع جميع المعلومات إلى Google Sheets تلقائياً

## متطلبات التشغيل

- .NET 8.0 أو أحدث
- Windows (للوصول إلى WMI)
- حساب Google Cloud مع تفعيل Google Sheets API

## الإعداد

### 1. إعداد Google Cloud Console

1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. فعّل Google Sheets API:
   - اذهب إلى "APIs & Services" > "Library"
   - ابحث عن "Google Sheets API" وفعّله
4. أنشئ Service Account:
   - اذهب إلى "APIs & Services" > "Credentials"
   - انقر "Create Credentials" > "Service Account"
   - املأ التفاصيل وانقر "Create"
5. أنشئ مفتاح JSON:
   - انقر على Service Account المُنشأ
   - اذهب إلى تبويب "Keys"
   - انقر "Add Key" > "Create New Key" > "JSON"
   - احفظ الملف باسم `credentials.json` في مجلد البرنامج

### 2. إعداد Google Sheets

1. أنشئ جدول بيانات جديد في [Google Sheets](https://sheets.google.com/)
2. انسخ معرف الجدول من الرابط (الجزء بين `/d/` و `/edit`)
3. شارك الجدول مع Service Account email (موجود في credentials.json)

### 3. إعداد البرنامج

1. افتح ملف `Program.cs`
2. عدّل المتغير `SpreadsheetId` وضع معرف جدول البيانات:
   ```csharp
   private const string SpreadsheetId = "YOUR_ACTUAL_SPREADSHEET_ID";
   ```
3. تأكد من وجود ملف `credentials.json` في مجلد البرنامج

## الاستخدام

1. شغّل البرنامج:
   ```bash
   dotnet run
   ```

2. سيقوم البرنامج بـ:
   - إنشاء ملف الترخيص الأصلي (وظيفة محفوظة)
   - جمع معلومات الجهاز الشاملة
   - عرض ملخص المعلومات
   - رفع البيانات إلى Google Sheets

## هيكل البيانات في Google Sheets

سيتم إنشاء جدول بالعناوين التالية:
- تاريخ ووقت الجمع
- اسم الجهاز
- اسم المستخدم
- نظام التشغيل
- إصدار النظام
- معمارية النظام
- اسم المعالج
- عدد الأنوية
- إجمالي الذاكرة
- الذاكرة المتاحة
- عناوين MAC
- الشركة المصنعة
- موديل النظام
- إصدار BIOS
- الرقم التسلسلي للنظام
- الرقم التسلسلي للوحة الأم
- المنطقة الزمنية
- المجال/مجموعة العمل
- كرت الرسوميات
- كرت الصوت
- محولات الشبكة
- أقراص التخزين

## استكشاف الأخطاء

### خطأ في الاعتماد
- تأكد من وجود ملف `credentials.json`
- تأكد من صحة محتويات الملف
- تأكد من تفعيل Google Sheets API

### خطأ في الوصول للجدول
- تأكد من صحة معرف الجدول
- تأكد من مشاركة الجدول مع Service Account

### خطأ في جمع معلومات النظام
- تأكد من تشغيل البرنامج بصلاحيات كافية
- بعض المعلومات قد تتطلب صلاحيات إدارية

## الأمان

- ملف `credentials.json` يحتوي على معلومات حساسة - لا تشاركه
- عناوين MAC مشفرة في ملف الترخيص
- البيانات المرفوعة إلى Google Sheets غير مشفرة

## الدعم

للمساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.
