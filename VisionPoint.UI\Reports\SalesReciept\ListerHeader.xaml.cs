﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace VisionPoint.UI.Reports.SalesReciept
{
    /// <summary>
    /// Interaction logic for ListerHeader.xaml
    /// </summary>
    public partial class ListerHeader : Page
    {
        public ListerHeader()
        {
            InitializeComponent();
        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            try
            {
                if (sender is ListView listView && listView.View is GridView gView)
                {
                    var workingWidth = listView.ActualWidth - SystemParameters.VerticalScrollBarWidth;

                    var col0 = 0.15; // Barcode
                    var col1 = 0.3; // Color
                    var col2 = 0.3; // Quantity
                    var col3 = 0.10; // Additional column
                    var col4 = 0.15; // Additional column

                    for (int i = 0; i < gView.Columns.Count; i++)
                    {
                        var width = i switch
                        {
                            0 => col0,
                            1 => col1,
                            2 => col2,
                            3 => col3,
                            4 => col4,
                            _ => 0.25
                        };
                        gView.Columns[i].Width = workingWidth * width;
                    }
                }
            }
            catch
            {
                return;
            }
        }
    }
}
