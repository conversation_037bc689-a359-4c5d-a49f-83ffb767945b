using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Shapes;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using Microsoft.Extensions.DependencyInjection;
using VisionPoint.UI.Controls;
using VisionPoint.UI.views.Dialogs;
using VisionPoint.UI.Utils;
using VisionPoint.UI.Helper;
using VisionPoint.UI.Helper.ImportExport;
using VisionPoint.UI.Converters;

namespace VisionPoint.UI.views.Pages.Receipts
{
    /// <summary>
    /// Interaction logic for ReceiptListView.xaml
    /// </summary>
    public partial class ReceiptListView : Page
    {
        private readonly ReceiptService _receiptService;
        private readonly ClientService _clientService;
        private readonly WarehouseService _warehouseService;
        private ObservableCollection<Receipt> _receipts;

        // فلاتر البحث
        private DateTime? _fromDate;
        private DateTime? _toDate;
        private int? _clientId;
        private byte? _financialId;
        private int? _warehouseId;
        private bool? _isExchange;
        private int? _receiptNumber;
        private string _statement;
        private int? _invoiceNumber;

        public ReceiptListView()
        {
            InitializeComponent();
            _receiptService = ServiceLocator.GetService<ReceiptService>();
            _clientService = ServiceLocator.GetService<ClientService>();
            _warehouseService = ServiceLocator.GetService<WarehouseService>();
            _receipts = new ObservableCollection<Receipt>();
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // تحميل المخازن في القائمة المنسدلة
            await LoadWarehousesAsync();

            // تحميل أنواع الإيصالات
            await LoadFinancialTypesAsync();

            // تعيين تاريخ اليوم كتاريخ افتراضي للبحث
            _fromDate = DateTime.Today;
            dpFromDate.SelectedDate = _fromDate;

            // تعيين حالة عناصر التاريخ
            chkAllPeriods.IsChecked = false;
            UpdateDateControlsState();

            // تحميل إيصالات اليوم فقط
            await ApplyFiltersAsync();
        }

        private async Task LoadWarehousesAsync()
        {
            try
            {
                var warehouses = await _warehouseService.GetAllWarehousesAsync();
                
                // إضافة خيار "الكل" في البداية
                var warehouseList = new List<object> { new { Id = -1, Name = "الكل" } };
                warehouseList.AddRange(warehouses.Select(w => new { Id = w.Id, Name = w.Name }));

                cmbWarehouses.ItemsSource = warehouseList;
                cmbWarehouses.DisplayMemberPath = "Name";
                cmbWarehouses.SelectedValuePath = "Id";

                // تحديد المخزن الافتراضي للمستخدم الحالي
                if (CurrentUser.WarehouseId.HasValue)
                {
                    // التحقق من صلاحية تغيير المخزن
                    bool canChangeWarehouse = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ChangeWarehouseRole");
                    if (!canChangeWarehouse)
                    {
                        // إذا لم يكن لديه صلاحية، نعطل القائمة ونحدد مخزنه فقط
                        cmbWarehouses.IsEnabled = false;
                        cmbWarehouses.SelectedValue = CurrentUser.WarehouseId.Value;
                        _warehouseId = CurrentUser.WarehouseId.Value;
                    }
                    else
                    {
                        // إذا كان لديه صلاحية، نحدد مخزنه كافتراضي
                        cmbWarehouses.SelectedValue = CurrentUser.WarehouseId.Value;
                        _warehouseId = CurrentUser.WarehouseId.Value;
                    }
                }
                else
                {
                    // إذا لم يكن له مخزن محدد، نختار "الكل"
                    cmbWarehouses.SelectedIndex = 0;
                    _warehouseId = null;
                }

            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل المخازن: {ex.Message}", "خطأ في تحميل البيانات", true);
            }
        }

        private async Task LoadFinancialTypesAsync()
        {
            try
            {
                var financialTypes = await _receiptService.GetFinancials();

                // إضافة خيار "الكل" في البداية
                var typeList = new List<object> { new { Id = (byte)0, Name = "الكل" } };
                typeList.AddRange(financialTypes.Select(f => new { Id = f.Id, Name = f.Name }));

                cmbType.ItemsSource = typeList;
                cmbType.DisplayMemberPath = "Name";
                cmbType.SelectedValuePath = "Id";
                cmbType.SelectedIndex = 0; // اختيار "الكل" افتراضياً
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل أنواع الإيصالات: {ex.Message}", "خطأ في تحميل البيانات", true);
            }
        }

        private async Task LoadClientsAsync()
        {
            try
            {
                List<Client> clients;
                
                if (_warehouseId.HasValue && _warehouseId.Value != -1)
                {
                    // تحميل العملاء المرتبطين بالمخزن المحدد
                    clients = await _clientService.GetClientsByWarehouseAsync(_warehouseId.Value);
                }
                else
                {
                    // تحميل جميع العملاء
                    clients = await _clientService.GetAllClientsAsync();
                }

                // إضافة خيار "الكل" في البداية
                var clientList = new List<object> { new { Id = -1, Name = "الكل" } };
                clientList.AddRange(clients.Select(c => new { Id = c.Id, Name = c.Name }));

                cmbClients.ItemsSource = clientList;
                cmbClients.DisplayMemberPath = "Name";
                cmbClients.SelectedValuePath = "Id";
                cmbClients.SelectedIndex = 0; // اختيار "الكل" افتراضياً
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تحميل العملاء: {ex.Message}", "خطأ في تحميل البيانات", true);
            }
        }

        private void UpdateDateControlsState()
        {
            bool isAllPeriodsChecked = chkAllPeriods.IsChecked == true;
            dpFromDate.IsEnabled = !isAllPeriodsChecked;
            dpToDate.IsEnabled = !isAllPeriodsChecked;
        }

        private async Task ApplyFiltersAsync()
        {
            try
            {
                // جمع قيم الفلاتر
                _receiptNumber = null;
                if (!string.IsNullOrEmpty(txtReceiptNumber.Text) && int.TryParse(txtReceiptNumber.Text, out int receiptNo))
                {
                    _receiptNumber = receiptNo;
                }

                _financialId = null;
                if (cmbType.SelectedValue != null && (byte)cmbType.SelectedValue != 0)
                {
                    _financialId = (byte)cmbType.SelectedValue;
                }

                _clientId = null;
                if (cmbClients.SelectedValue != null && (int)cmbClients.SelectedValue != -1)
                {
                    _clientId = (int)cmbClients.SelectedValue;
                }

                _statement = string.IsNullOrEmpty(txtStatement.Text) ? null : txtStatement.Text.Trim();

                int? invoiceId = null;
                if (!string.IsNullOrEmpty(txtInvoiceNumber.Text) && int.TryParse(txtInvoiceNumber.Text, out int invNo))
                {
                    invoiceId = invNo;
                }

                // تحقق من خيار "كل الفترات"
                if (chkAllPeriods.IsChecked == true)
                {
                    _fromDate = null;
                    _toDate = null;
                }

                // تحميل الإيصالات المصفاة
                _receipts.Clear();
                _receipts = new ObservableCollection<Receipt>(
                    await _receiptService.SearchReceipts(
                        receiptNo: _receiptNumber,
                        financialId: _financialId,
                        date: _fromDate,
                        clientId: _clientId,
                        employeeId: null,
                        treasuryId: null,
                        isExchange: _isExchange,
                        statement: _statement,
                        purchaseId: null,
                        saleId: null,
                        expenseId: null));

                list.ItemsSource = _receipts;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تطبيق الفلاتر: {ex.Message}", "خطأ في البحث", true);
            }
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnAdd.IsEnabled = false;
            btnSearch.IsEnabled = false;
            btnExport.IsEnabled = false;
            btnResetFilters.IsEnabled = false;

            // تعطيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.DisableAllButtons();
            }
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnAdd.IsEnabled = true;
            btnSearch.IsEnabled = true;
            btnExport.IsEnabled = true;
            btnResetFilters.IsEnabled = true;

            // تفعيل أزرار النافذة الرئيسية
            if (Application.Current.MainWindow is MainWindow mainWindow)
            {
                mainWindow.EnableAllButtons();
            }
        }

        private async void btnAdd_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // فتح نافذة إضافة الإيصالات كـ ShowDialog
                var receiptWindow = new ReceiptWindow();
                receiptWindow.ShowDialog();

                // إعادة تحميل البيانات بعد الإضافة مع تطبيق الفلاتر الحالية
                await ApplyFiltersAsync();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnSearch_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                await ApplyFiltersAsync();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnResetFilters_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // إعادة تعيين قيم الفلاتر
                _fromDate = DateTime.Today; // تعيين تاريخ اليوم
                _toDate = null;
                _clientId = null;
                _financialId = null;
                _isExchange = null;
                _receiptNumber = null;
                _statement = null;
                _invoiceNumber = null;

                // إعادة تعيين عناصر واجهة المستخدم
                chkAllPeriods.IsChecked = false;
                dpFromDate.SelectedDate = _fromDate;
                dpToDate.SelectedDate = null;
                cmbType.SelectedIndex = 0;
                cmbClients.SelectedIndex = 0;
                chkIsExchange.IsChecked = null;
                txtReceiptNumber.Text = string.Empty;
                txtStatement.Text = string.Empty;
                txtInvoiceNumber.Text = string.Empty;

                // إعادة تحديد المخزن الافتراضي
                if (CurrentUser.WarehouseId.HasValue)
                {
                    cmbWarehouses.SelectedValue = CurrentUser.WarehouseId.Value;
                    _warehouseId = CurrentUser.WarehouseId.Value;
                }
                else
                {
                    cmbWarehouses.SelectedIndex = 0;
                    _warehouseId = null;
                }

                // تحديث حالة عناصر التاريخ
                UpdateDateControlsState();

                // تطبيق الفلاتر لعرض إيصالات اليوم فقط
                await ApplyFiltersAsync();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnExport_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                if (_receipts.Count == 0)
                {
                    ErrorBox.Show("لا توجد بيانات لتصديرها", "تنبيه", false);
                    return;
                }

                // إنشاء مساعد التصدير مع الإيصالات المجلبة والمخزن المحدد
                var exportHelper = new ReceiptsExportHelper(_receipts.ToList(), _warehouseId);

                // بدء عملية التصدير
                await exportHelper.StartExportProcess();
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تصدير البيانات: {ex.Message}", "خطأ في التصدير", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        // أحداث تغيير قيم الفلاتر
        private async void dpFromDate_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            _fromDate = dpFromDate.SelectedDate;
        }

        private async void dpToDate_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            _toDate = dpToDate.SelectedDate;
        }

        private async void cmbType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (cmbType.SelectedValue != null)
            {
                _financialId = (byte)cmbType.SelectedValue == 0 ? null : (byte)cmbType.SelectedValue;
            }
        }

        private async void cmbWarehouses_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (cmbWarehouses.SelectedValue != null && cmbWarehouses.SelectedValue is int warehouseId && warehouseId != -1)
            {
                _warehouseId = warehouseId;
            }
            else
            {
                _warehouseId = null;
            }
            // إعادة تحميل العملاء حسب المخزن المختار
            await LoadClientsAsync();
        }

        private async void cmbClients_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (cmbClients.SelectedValue != null && cmbClients.SelectedValue is int clientId && clientId != -1)
            {
                _clientId = clientId;
            }
            else
            {
                _clientId = null;
            }
        }

        private async void chkAllPeriods_CheckedChanged(object sender, RoutedEventArgs e)
        {
            // تحديث حالة عناصر التاريخ
            UpdateDateControlsState();

            if (chkAllPeriods.IsChecked == true)
            {
                _fromDate = null;
                _toDate = null;
            }
            else
            {
                // إذا تم إلغاء اختيار "كل الفترات"، نعيد تعيين تاريخ اليوم كتاريخ بداية افتراضي
                _fromDate = DateTime.Today;
                dpFromDate.SelectedDate = _fromDate;
            }
        }

        private async void chkIsExchange_CheckedChanged(object sender, RoutedEventArgs e)
        {
            if (chkIsExchange.IsChecked == true)
            {
                _isExchange = false; // قبض
            }
            else if (chkIsExchange.IsChecked == false)
            {
                _isExchange = true; // صرف
            }
            else
            {
                _isExchange = null; // الكل
            }
        }

        private async void EditButton_Click(object sender, MouseButtonEventArgs e)
        {
            // التحقق من صلاحيات المستخدم
            bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditReceiptRole");
            bool canView = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ReceiptRole") || CurrentUser.HasRole("EditReceiptRole") || CurrentUser.HasRole("DeleteReceiptRole");

            // إذا لم يكن المستخدم يملك صلاحية العرض على الأقل، نمنع الوصول
            if (!canView)
            {
                ErrorBox.Show("لا تملك صلاحية عرض الإيصالات", "خطأ في الصلاحيات", true);
                return;
            }

            // Get the receipt associated with the Path from the data context
            if (sender is Path path && path.DataContext is Receipt selectedReceipt)
            {
                if (selectedReceipt == null)
                {
                    ErrorBox.Show("الرجاء اختيار إيصال للعرض", "تنبيه", false);
                    return;
                }

                try
                {
                    // فتح نافذة عرض/تعديل الإيصالات مع تمرير الإيصال المحدد كـ ShowDialog
                    var receiptWindow = new ReceiptWindow(selectedReceipt.Id);
                    receiptWindow.ShowDialog();

                    // إعادة تحميل البيانات بعد العرض/التعديل مع تطبيق الفلاتر الحالية
                    await ApplyFiltersAsync();
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"حدث خطأ أثناء فتح صفحة الإيصال: {ex.Message}", "خطأ في النظام", true);
                }
            }
        }

        private async void DeleteButton_Click(object sender, MouseButtonEventArgs e)
        {
            // التحقق من صلاحية الحذف
            bool canDelete = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("DeleteReceiptRole");
            if (!canDelete)
            {
                ErrorBox.Show("لا تملك صلاحية حذف الإيصالات", "خطأ في الصلاحيات", true);
                return;
            }

            // Get the receipt associated with the Path from the data context
            if (sender is Path path && path.DataContext is Receipt selectedReceipt)
            {
                if (selectedReceipt == null)
                {
                    ErrorBox.Show("الرجاء اختيار إيصال للحذف", "تنبيه", false);
                    return;
                }

                var result = QuestionBox.Show("تأكيد الحذف", $"هل أنت متأكد من حذف الإيصال رقم {selectedReceipt.ReceiptNumber}؟");
                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // Call the DeleteReceipt method in the ReceiptService
                        var (success, message) = await _receiptService.DeleteReceipt(selectedReceipt.Id);

                        if (success)
                        {
                            await ApplyFiltersAsync();
                            DialogBox.Show("تم بنجاح", "تم حذف الإيصال بنجاح");
                        }
                        else
                        {
                            ErrorBox.Show(message, "خطأ في الحذف", false);
                        }
                    }
                    catch (Exception ex)
                    {
                        ErrorBox.Show($"حدث خطأ أثناء حذف الإيصال: {ex.Message}", "خطأ في الحذف", true);
                    }
                }
            }
        }

        private async void list_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            // التحقق من صلاحيات المستخدم
            bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditReceiptRole");
            bool canView = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ReceiptRole") || CurrentUser.HasRole("EditReceiptRole") || CurrentUser.HasRole("DeleteReceiptRole");

            // إذا لم يكن المستخدم يملك صلاحية العرض على الأقل، نمنع الوصول
            if (!canView)
            {
                ErrorBox.Show("لا تملك صلاحية عرض الإيصالات", "خطأ في الصلاحيات", true);
                return;
            }

            // Get the selected receipt
            if (list.SelectedItem is Receipt selectedReceipt)
            {
                if (selectedReceipt == null)
                {
                    return;
                }

                try
                {
                    // فتح نافذة عرض/تعديل الإيصالات مع تمرير الإيصال المحدد كـ ShowDialog
                    var receiptWindow = new ReceiptWindow(selectedReceipt.Id);
                    receiptWindow.ShowDialog();

                    // إعادة تحميل البيانات بعد العرض/التعديل مع تطبيق الفلاتر الحالية
                    await ApplyFiltersAsync();
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"حدث خطأ أثناء فتح صفحة الإيصال: {ex.Message}", "خطأ في النظام", true);
                }
            }
        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            // Ensure the sender is a ListView, it uses a GridView, and has at least 8 columns
            // (7 flexible columns + 1 fixed 'Operations' column)
            if (sender is ListView listView && listView.View is GridView gridView && gridView.Columns.Count >= 8)
            {
                double actionColumnWidth = 100; // Fixed width for the 'Operations' column
                double padding = 10; // Extra padding/margin, adjust as needed
                double scrollbarWidth = SystemParameters.VerticalScrollBarWidth;

                // Define weights for each of the 7 flexible columns.
                // Adjust these values to control the relative width of each column.
                double col1Weight = 1.0; // رقم الإيصال والتاريخ (Index 0)
                double col2Weight = 1.5; // تفاصيل العملية (Index 1)
                double col3Weight = 0.8; // المبلغ (Index 2)
                double col4Weight = 1.0; // طريقة الدفع (Index 3)
                double col5Weight = 1.2; // الطرف المقابل (Index 4)
                double col6Weight = 0.7; // NEW COLUMN: الملاحظات (Index 5) - Adjust this weight!
                double col7Weight = 1.5; // معلومات إضافية (Index 6)

                // Calculate the sum of all flexible column weights
                double totalFlexibleWeight = col1Weight + col2Weight + col3Weight +
                                             col4Weight + col5Weight + col6Weight + // Include the new column's weight
                                             col7Weight;

                // Calculate the total width available for the flexible columns
                double availableWidth = listView.ActualWidth - actionColumnWidth - scrollbarWidth - padding;

                // Ensure availableWidth is not negative to prevent division by zero or invalid calculations
                if (availableWidth < 0) availableWidth = 0;

                // Calculate the 'unit' width that will be distributed based on weights
                double unitWidth = (totalFlexibleWeight > 0) ? availableWidth / totalFlexibleWeight : 0;

                // Assign widths to the columns based on their weights
                gridView.Columns[0].Width = unitWidth * col1Weight; // Column 1 (Index 0)
                gridView.Columns[1].Width = unitWidth * col2Weight; // Column 2 (Index 1)
                gridView.Columns[2].Width = unitWidth * col3Weight; // Column 3 (Index 2)
                gridView.Columns[3].Width = unitWidth * col4Weight; // Column 4 (Index 3)
                gridView.Columns[4].Width = unitWidth * col5Weight; // Column 5 (Index 4)
                gridView.Columns[5].Width = unitWidth * col6Weight; // NEW Column 6 (Index 5 - الملاحظات)
                gridView.Columns[6].Width = unitWidth * col7Weight; // Column 7 (Index 6 - معلومات إضافية)
                gridView.Columns[7].Width = actionColumnWidth;      // Column 8 (Index 7 - العمليات, fixed width)
            }
        }

        private void GridCheckBox_Toggle(object sender, MouseButtonEventArgs e)
        {
            if (sender is Grid grid)
            {
                var checkBox = grid.Children.OfType<CheckBox>().FirstOrDefault();
                if (checkBox != null)
                {
                    if (checkBox.IsThreeState)
                    {
                        // للـ CheckBox ثلاثي الحالة
                        if (checkBox.IsChecked == null)
                            checkBox.IsChecked = true;
                        else if (checkBox.IsChecked == true)
                            checkBox.IsChecked = false;
                        else
                            checkBox.IsChecked = null;
                    }
                    else
                    {
                        // للـ CheckBox عادي
                        checkBox.IsChecked = !checkBox.IsChecked;
                    }
                }
            }
        }
    }
}
