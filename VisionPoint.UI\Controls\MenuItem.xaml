﻿<local:MenuBase
    x:Class="VisionPoint.UI.Controls.MenuItem"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.Controls"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:Background="#EEF4ED"
    d:DesignHeight="450"
    d:DesignWidth="800"
    FontFamily="El Messiri"
    mc:Ignorable="d">

    <local:MenuBase.Resources>



        <!--  Start: Indicator Style  -->
        <Style x:Key="IndicatorStyle" TargetType="{x:Type Border}">
            <Setter Property="Visibility" Value="Hidden" />
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsMouseOver, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ToggleButton}}" Value="True">
                    <Setter Property="Visibility" Value="Visible" />
                </DataTrigger>
                <DataTrigger Binding="{Binding IsChecked, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ToggleButton}}" Value="True">
                    <Setter Property="Visibility" Value="Visible" />
                </DataTrigger>
            </Style.Triggers>
        </Style>
        <!--  End: Indicator Style  -->


        <!--  Start: Menu Icon Style  -->

        <Style x:Key="recMenuIconStyle" TargetType="{x:Type Rectangle}">
            <Setter Property="Opacity" Value="0.8" />

            <Style.Triggers>
                <DataTrigger Binding="{Binding IsMouseOver, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ToggleButton}}" Value="True">
                    <Setter Property="Opacity" Value="1" />
                </DataTrigger>
                <DataTrigger Binding="{Binding IsChecked, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ToggleButton}}" Value="True">
                    <Setter Property="Opacity" Value="1" />
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="MenuIconStyle" TargetType="{x:Type Path}">
            <Setter Property="Opacity" Value="0.8" />
            <Setter Property="Stroke" Value="{StaticResource PrimaryIconColor}" />

            <Style.Triggers>
                <DataTrigger Binding="{Binding IsMouseOver, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ToggleButton}}" Value="True">
                    <Setter Property="Opacity" Value="1" />
                    <Setter Property="Stroke" Value="{StaticResource PrimaryColor}" />
                </DataTrigger>
                <DataTrigger Binding="{Binding IsChecked, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ToggleButton}}" Value="True">
                    <Setter Property="Stroke" Value="{StaticResource PrimaryColor}" />
                    <Setter Property="Opacity" Value="1" />
                </DataTrigger>
            </Style.Triggers>
        </Style>
        <!--  End: Menu Icon Style  -->

        <!--  Start: Menu Text Style  -->
        <Style x:Key="MenuTextStyle" TargetType="{x:Type TextBlock}">
            <Setter Property="Opacity" Value="0.8" />
            <Setter Property="Foreground" Value="#252422" />
            <Setter Property="FontWeight" Value="Regular" />
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsMouseOver, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ToggleButton}}" Value="True">
                    <Setter Property="Opacity" Value="1" />
                </DataTrigger>
                <DataTrigger Binding="{Binding IsChecked, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=ToggleButton}}" Value="True">
                    <Setter Property="Opacity" Value="1" />
                </DataTrigger>
            </Style.Triggers>
        </Style>
        <!--  End: Menu Text Style  -->

        <!--  Start: Menu Item Style  -->
        <Style x:Key="MenuButtonStyle" TargetType="{x:Type ToggleButton}">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="{DynamicResource BorderThicknes}" />
            <Setter Property="HorizontalContentAlignment" Value="Center" />
            <Setter Property="VerticalAlignment" Value="Bottom" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ToggleButton}">
                        <Border
                            Padding="0"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Bottom"
                            Background="{TemplateBinding Background}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                            <Grid VerticalAlignment="Stretch">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="auto" />
                                    <ColumnDefinition Width="16" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>


                                <!--  Indicator  -->
                                <Border
                                    Grid.RowSpan="4"
                                    Grid.ColumnSpan="4"
                                    Background="#134074"
                                    CornerRadius="8"
                                    Opacity="0.2"
                                    Style="{StaticResource IndicatorStyle}" />

                                <!--  Icon  -->


                                <Path
                                    Grid.Column="0"
                                    Width="24"
                                    Height="24"
                                    Margin="8,8,0,8"
                                    Data="{Binding IconProperty, RelativeSource={RelativeSource AncestorType=UserControl}, FallbackValue={StaticResource settings}, TargetNullValue={StaticResource settings}}"
                                    Fill="{Binding Fill, RelativeSource={RelativeSource AncestorType=UserControl}, FallbackValue=Transparent, TargetNullValue=Transparent}"
                                    FlowDirection="LeftToRight"
                                    Stretch="Uniform"
                                    StrokeThickness="{Binding StrokeThik, RelativeSource={RelativeSource AncestorType=UserControl}, FallbackValue='2', TargetNullValue='2'}"
                                    Style="{StaticResource MenuIconStyle}" />

                                <!--  Data="{Binding Icon, RelativeSource={RelativeSource AncestorType=UserControl}, FallbackValue={StaticResource DefaultIcon}, TargetNullValue={StaticResource DefaultIcon}}"  -->



                                <!--<Image
                                    Grid.Column="0"
                                    Width="24"
                                    Height="24"
                                    Margin="{Binding IconMargin, FallbackValue='0,0,0,0', TargetNullValue='0,0,0,0'}"
                                    Source="{Binding IconProperty, RelativeSource={RelativeSource AncestorType=UserControl}, FallbackValue={StaticResource userLogin}, TargetNullValue={StaticResource userLogin}}"
                                    Stretch="Uniform"
                                    Style="{StaticResource MenuIconStyle}"
                                    Visibility="Hidden" />-->

                                <!--  Menu Text  -->
                                <TextBlock
                                    Grid.Column="2"
                                    Margin="8,8,0,8"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    FontSize="21"
                                    FontWeight="Regular"
                                    Style="{StaticResource MenuTextStyle}"
                                    Text="{Binding Text, RelativeSource={RelativeSource AncestorType=UserControl}, FallbackValue=الموظفين, TargetNullValue=MenuText}"
                                    TextAlignment="Center" />


                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <!--  To set IsChecked property to true on default  -->
                            <DataTrigger Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType=UserControl}}" Value="True">
                                <Setter Property="IsChecked" Value="True" />
                            </DataTrigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <!--  End: Menu Item Style  -->

    </local:MenuBase.Resources>
    <Grid HorizontalAlignment="Stretch">
        <RadioButton
            d:IsChecked="True"
            GroupName="{Binding GroupName, RelativeSource={RelativeSource AncestorType=UserControl}}"
            Style="{StaticResource MenuButtonStyle}" />
    </Grid>
</local:MenuBase>
