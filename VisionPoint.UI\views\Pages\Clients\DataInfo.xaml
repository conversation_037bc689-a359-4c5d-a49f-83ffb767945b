﻿<Window
    x:Class="VisionPoint.UI.views.Pages.Clients.DataInfo"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Clients"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="DataInfo"
    AllowsTransparency="True"
    Background="Transparent"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">

    <Viewbox Stretch="Uniform">

        <Grid Width="1920" Height="1080">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="7*" />
                <ColumnDefinition Width="10*" />
                <ColumnDefinition Width="7*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="6*" />
                <RowDefinition Height="10*" />
                <RowDefinition Height="6*" />
            </Grid.RowDefinitions>


            <!--  Centered 800x450 content  -->
            <Border
                Grid.Row="1"
                Grid.Column="1"
                Padding="16"
                Background="#EEF4ED"
                BorderBrush="{StaticResource PrimaryColor}"
                BorderThickness="1"
                CornerRadius="16">
                <Grid>
                    <Border
                        x:Name="btnclose"
                        Grid.ColumnSpan="10"
                        Width="24"
                        Height="24"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Top"
                        Panel.ZIndex="10"
                        Background="Red"
                        CornerRadius="50"
                        MouseLeftButtonDown="btnclose_MouseLeftButtonDown" />
                    <TextBlock
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Center"
                        FontSize="21"
                        Foreground="Black"
                        TextAlignment="Center"
                        TextWrapping="Wrap">
                        <Run FontSize="28" Text="💡 " />
                        <Run
                            FontSize="28"
                            FontWeight="Bold"
                            Text="شرح الرصيد المسموح:" />
                        <LineBreak />
                        <LineBreak />
                        <Run
                            FontSize="24"
                            FontWeight="Bold"
                            Text="🟢 " />
                        <Run
                            FontSize="24"
                            FontWeight="Bold"
                            Foreground="#2E7D32"
                            Text="0 = بلا قيود" />
                        <LineBreak />
                        <Run Foreground="Black" Text="لا توجد حدود على رصيد العميل" />
                        <LineBreak />
                        <LineBreak />
                        <Run
                            FontSize="24"
                            FontWeight="Bold"
                            Text="🔴 " />
                        <Run
                            FontSize="24"
                            FontWeight="Bold"
                            Foreground="#D32F2F"
                            Text="قيمة موجبة = حد أقصى للمديونية" />
                        <LineBreak />
                        <Run Foreground="Black" Text="(العميل مدين للمتجر) العميل يدين المتجر" />
                        <LineBreak />
                        <Run Foreground="Black" Text="   مثال: 1000 = العميل يمكن أن يدين المتجر حتى 1000" />
                        <LineBreak />
                        <LineBreak />
                        <Run
                            FontSize="24"
                            FontWeight="Bold"
                            Text="🔵 " />
                        <Run
                            FontSize="24"
                            FontWeight="Bold"
                            Foreground="#1976D2"
                            Text="قيمة سالبة = حد أقصى للدائنية" />
                        <LineBreak />
                        <Run Text="   المتجر مدين للعميل (المتجر يدين العميل)" />
                        <LineBreak />
                        <Run Text="   مثال: -500 = المتجر يمكن أن يدين العميل حتى 500" />
                    </TextBlock>
                </Grid>
            </Border>
        </Grid>
    </Viewbox>
</Window>
