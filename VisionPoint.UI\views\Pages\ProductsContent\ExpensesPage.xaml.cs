using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.views.Pages.ProductsContent
{
    /// <summary>
    /// Interaction logic for ExpensesPage.xaml
    /// </summary>
    public partial class ExpensesPage : Window
    {
        private readonly ExpenseService _expenseService;
        private Models.Expense _selectedExpense = new();

        public ExpensesPage(ExpenseService expenseService)
        {
            InitializeComponent();
            _expenseService = expenseService;
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadData();
        }

        private async Task LoadData()
        {
            try
            {
                // The BusyService is automatically set by the ExpenseService
                var expenses = await _expenseService.GetAllExpensesAsync();
                list.ItemsSource = expenses;
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ: {ex.Message}", "خطأ", true);
            }
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnSave.IsEnabled = false;
            btnNew.IsEnabled = false;
            btnDelete.IsEnabled = false;
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnSave.IsEnabled = true;
            btnNew.IsEnabled = true;
            btnDelete.IsEnabled = true;
        }

        private async void btnSave_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق من صلاحيات المستخدم
                bool canAdd = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("ExpenseRole");
                bool canEdit = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("EditExpenseRole");

            // إذا كان مصروف جديد، نحتاج صلاحية الإضافة
            if (_selectedExpense.Id == 0 && !canAdd)
            {
                ErrorBox.Show("لا تملك صلاحية إضافة المصروفات", "خطأ في الصلاحيات", true);
                return;
            }

            // إذا كان تعديل مصروف موجود، نحتاج صلاحية التعديل
            if (_selectedExpense.Id != 0 && !canEdit)
            {
                ErrorBox.Show("لا تملك صلاحية تعديل المصروفات", "خطأ في الصلاحيات", true);
                return;
            }

            if (string.IsNullOrEmpty(txtName.Text.Trim()))
            {
                ErrorBox.Show("الرجاء إدخال اسم المصروف", "خطأ", true);
                txtName.Focus();
                return;
            }

        
                _selectedExpense.Name = txtName.Text.Trim();

                var result = _selectedExpense.Id == 0
                    ? await _expenseService.AddExpenseAsync(_selectedExpense)
                    : await _expenseService.UpdateExpenseAsync(_selectedExpense);

                if (result.State)
                {
                    DialogBox.Show(result.Message, "نجاح");
                    btnNew_MouseLeftButtonDown(sender, e);
                    await LoadData();
                }
                else
                {
                    ErrorBox.Show(result.Message, "خطأ", true);
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnNew_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                _selectedExpense = new Models.Expense();
                txtName.Clear();
                list.SelectedItem = null;
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private async void btnDelete_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                // التحقق من صلاحية الحذف
                bool canDelete = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("DeleteExpenseRole");
                if (!canDelete)
                {
                    ErrorBox.Show("لا تملك صلاحية حذف المصروفات", "خطأ في الصلاحيات", true);
                    return;
                }

                if (_selectedExpense.Id == 0)
                {
                    ErrorBox.Show("الرجاء اختيار مصروف للحذف", "تنبيه", false);
                    return;
                }

                var result = DeleteBox.Show("هل انت متاكد من حذف المصروف", _selectedExpense.Name);

                if (result == true)
                {
                    var deleteResult = await _expenseService.DeleteExpenseAsync(_selectedExpense.Id);

                    if (deleteResult.State)
                    {
                        DialogBox.Show("تم الحذف", "نجاح");
                        btnNew_MouseLeftButtonDown(sender, e);
                        await LoadData();
                    }
                    else
                    {
                        ErrorBox.Show(deleteResult.Message, "خطأ", true);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnclose_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            Close();
        }

        private void list_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            try
            {
                ListView listView = sender as ListView;
                if (listView != null)
                {
                    GridView gView = listView.View as GridView;
                    if (gView != null)
                    {
                        var workingWidth = listView.ActualWidth - SystemParameters.VerticalScrollBarWidth;
                        var col1 = 0.1;  // numbering column
                        var col2 = 0.9;  // name column
                        gView.Columns[0].Width = workingWidth * col1;
                        gView.Columns[1].Width = workingWidth * col2;
                    }
                }
            }
            catch
            {
                return;
            }
        }

        private void list_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (list.SelectedItem is Models.Expense selectedExpense)
            {
                _selectedExpense = selectedExpense;
                txtName.Text = selectedExpense.Name;
            }
            else
            {
                ErrorBox.Show("الرجاء اختيار مصروف", "تنبيه", false);
            }
        }
    }
}
