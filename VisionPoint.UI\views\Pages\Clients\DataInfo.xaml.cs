﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace VisionPoint.UI.views.Pages.Clients
{
    /// <summary>
    /// Interaction logic for DataInfo.xaml
    /// </summary>
    public partial class DataInfo : Window
    {
        public DataInfo()
        {
            InitializeComponent();
        }

        private void btnclose_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            Close();
        }
    }
}
