﻿using System;
using System.Globalization;
using System.Windows.Data;

namespace VisionPoint.UI.Converters
{
    public class HeightToOffsetConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length < 3 || !(values[0] is double textBlockActualHeight) || !(values[1] is double borderThicknessTop) || !(values[2] is double contentHostMarginTop))
            {
                return 0.0; // Default or error value
            }

            double translateY = -textBlockActualHeight / 2 // Move up by half its height to get its top edge to its center
                    - (borderThicknessTop / 2)   // Account for top border thickness (if it's half its thickness)
                    - (contentHostMarginTop / 2); // Account for content host top margin

            double desiredOffsetAboveBorder = 8; // Adjust this value to fine-tune how high it floats


            double verticalOffset = textBlockActualHeight / 2.0 + contentHostMarginTop / 2.0;
            return -verticalOffset; // Needs to be negative to move upwards
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
