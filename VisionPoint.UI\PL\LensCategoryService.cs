﻿﻿﻿using Microsoft.EntityFrameworkCore;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Models;
using VisionPoint.UI.Services;
using VisionPoint.UI.ViewModel;

namespace VisionPoint.UI.PL;

public class LensCategoryService : IDisposable
{
    private readonly AppDbContext _context = ServiceLocator.GetService<AppDbContext>();
    private bool _disposed = false;

    public LensCategoryService()
    {
    }

    // Obtener todas las categorías
    public async Task<List<LensCategory>> GetAllCategoriesAsync()
    {
        return await _context.LensCategories
            .AsNoTracking()
            .ToListAsyncWithBusy("GetAllLensCategories");
    }

    // Obtener categoría por ID
    public async Task<LensCategory?> GetCategoryByIdAsync(int id)
    {
        return await _context.LensCategories.FindAsyncWithBusy(id);
    }

    // Agregar nueva categoría
    public async Task<(bool State, string Message)> AddCategoryAsync(LensCategory category)
    {
        category.CreatedById = CurrentUser.Id;
        category.ModifiedById = CurrentUser.Id;
        category.CreatedAt = DateTime.Now;
        category.UpdatedAt = DateTime.Now;
        await _context.LensCategories.AddAsyncWithBusy(category);
        return await _context.SaveWithTransactionAndBusy("AddLensCategory");
    }

    // Actualizar categoría existente
    public async Task<(bool State, string Message)> UpdateCategoryAsync(LensCategory category)
    {
        var existingCategory = await _context.LensCategories.FindAsyncWithBusy(category.Id);
        if (existingCategory == null)
        {
            return (false, "الفئة غير موجودة");
        }

        existingCategory.Name = category.Name;
        existingCategory.Description = category.Description;
        existingCategory.ModifiedById = CurrentUser.Id;
        existingCategory.UpdatedAt = DateTime.Now;

        _context.LensCategories.UpdateWithBusy(existingCategory);
        return await _context.SaveWithTransactionAndBusy("UpdateLensCategory");
    }

    // Eliminar categoría
    public async Task<(bool State, string Message)> DeleteCategoryAsync(int id)
    {
        var category = await _context.LensCategories.FindAsyncWithBusy(id);
        if (category == null)
        {
            return (false, "الفئة غير موجودة");
        }

        // Verificar si hay lentes asociadas a esta categoría
        bool hasLenses = await _context.Lenses.AnyAsyncWithBusy(l => l.CategoryId == id, "CheckCategoryLenses");

        if (hasLenses)
        {
            return (false, "لا يمكن حذف الفئة لأنها مرتبطة بعدسات");
        }

        _context.LensCategories.RemoveWithBusy(category);
        return await _context.SaveWithTransactionAndBusy("DeleteLensCategory");
    }

    // Buscar categorías por nombre
    public async Task<List<LensCategory>> SearchByNameAsync(string searchTerm)
    {
        return await _context.LensCategories
            .Where(c => c.Name.Contains(searchTerm))
            .AsNoTracking()
            .ToListAsyncWithBusy("SearchLensCategoriesByName");
    }

    // Implement IDisposable pattern
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // Dispose managed resources
                _context?.Dispose();
            }

            // Free unmanaged resources
            _disposed = true;
        }
    }

    // Destructor
    ~LensCategoryService()
    {
        Dispose(false);
    }
}
