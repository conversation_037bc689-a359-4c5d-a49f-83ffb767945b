﻿<Window
    x:Class="VisionPoint.UI.views.Pages.Users.BorrowMoneyPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:VisionPoint.UI"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Users"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils"
    Title="اخذ عهدة/رد عهدة"
    AllowsTransparency="True"
    Background="Transparent"
    FlowDirection="RightToLeft"
    Loaded="Window_Loaded"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">
        <Grid Width="1920" Height="1080">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="0.8*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="0.8*" />
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="0.1*" />
                <RowDefinition Height="*" />
                <RowDefinition Height="0.1*" />
            </Grid.RowDefinitions>
            <Border
                Grid.Row="1"
                Grid.Column="1"
                Background="{StaticResource backgroundColor}"
                BorderBrush="LightGray"
                BorderThickness="1.5"
                CornerRadius="16">


                <Grid Margin="16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="361*" />
                        <ColumnDefinition Width="84*" />
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="60" />
                        <RowDefinition />
                    </Grid.RowDefinitions>


                    <TextBlock
                        Grid.ColumnSpan="2"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        FontSize="24"
                        FontWeight="Bold"
                        Foreground="#333333"
                        Text="اخذ عهدة/رد عهدة" />


                    <Border
                        x:Name="btnclose"
                        Grid.Column="1"
                        Width="24"
                        Height="24"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Top"
                        Background="Red"
                        CornerRadius="50"
                        MouseLeftButtonDown="btnclose_MouseLeftButtonDown" />

                    <Grid Grid.Row="1" Grid.ColumnSpan="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition />
                            <ColumnDefinition />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition />
                            <RowDefinition />
                            <RowDefinition />
                            <RowDefinition />
                            <RowDefinition />
                            <RowDefinition />
                            <RowDefinition />
                            <RowDefinition />
                            <RowDefinition />
                        </Grid.RowDefinitions>






                        <!--  عرض رصيد الموظف  -->
                        <Grid Grid.ColumnSpan="3">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                Grid.Column="0"
                                Margin="5,0,5,0"
                                VerticalAlignment="Center"
                                FontSize="18"
                                FontWeight="Bold"
                                Text="الرصيد:" />

                            <TextBlock
                                x:Name="txtBalance"
                                Grid.Column="1"
                                Margin="5,0,15,0"
                                VerticalAlignment="Center"
                                FontSize="18"
                                Text="0.000" />

                            <TextBlock
                                Grid.Column="2"
                                Margin="5,0,5,0"
                                VerticalAlignment="Center"
                                FontSize="18"
                                FontWeight="Bold"
                                Text="الحالة:" />

                            <TextBlock
                                x:Name="txtBalanceStatus"
                                Grid.Column="3"
                                Margin="5,0,5,0"
                                VerticalAlignment="Center"
                                FontSize="18"
                                Text="متوازن" />
                        </Grid>

                        <!--  حقل إدخال القيمة  -->
                        <Grid Grid.Row="1" Grid.ColumnSpan="3">
                            <TextBox
                                x:Name="txtName"
                                Height="60"
                                VerticalAlignment="Stretch"
                                VerticalContentAlignment="Stretch"
                                utils:NumericInputControl.IsDecimalOnly="True"
                                BorderThickness="1"
                                FontSize="21"
                                Tag="القيمة" />
                        </Grid>

                        <Grid Grid.Row="2" Grid.ColumnSpan="3">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition />
                                <ColumnDefinition />
                                <ColumnDefinition />
                            </Grid.ColumnDefinitions>
                            <Grid x:Name="GrdExchange" Margin="8,0">
                                <Grid.RowDefinitions>
                                    <RowDefinition />
                                    <RowDefinition />
                                </Grid.RowDefinitions>
                                <TextBlock
                                    VerticalAlignment="Center"
                                    FontSize="21"
                                    TextAlignment="Center">
                                    اخذ عهدة
                                </TextBlock>

                                <RadioButton
                                    x:Name="RdbExchange"
                                    Grid.Row="1"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FlowDirection="LeftToRight"
                                    GroupName="ProductType"
                                    IsChecked="True" />
                            </Grid>


                            <Grid
                                x:Name="GrdCatch"
                                Grid.Column="1"
                                Margin="8,0">
                                <Grid.RowDefinitions>
                                    <RowDefinition />
                                    <RowDefinition />
                                </Grid.RowDefinitions>
                                <TextBlock
                                    VerticalAlignment="Center"
                                    FontSize="21"
                                    TextAlignment="Center">
                                    رد عهدة
                                </TextBlock>

                                <RadioButton
                                    x:Name="RdbCatch"
                                    Grid.Row="1"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FlowDirection="LeftToRight"
                                    GroupName="ProductType" />
                            </Grid>

                            <Grid
                                x:Name="GrdSalaryAdvance"
                                Grid.Column="2"
                                Margin="8,0">
                                <Grid.RowDefinitions>
                                    <RowDefinition />
                                    <RowDefinition />
                                </Grid.RowDefinitions>
                                <TextBlock
                                    VerticalAlignment="Center"
                                    FontSize="21"
                                    TextAlignment="Center">
                                    سلفة راتب
                                </TextBlock>

                                <RadioButton
                                    x:Name="RdbSalaryAdvance"
                                    Grid.Row="1"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FlowDirection="LeftToRight"
                                    GroupName="ProductType" />
                            </Grid>
                        </Grid>

                        <Grid Grid.Row="3" Grid.ColumnSpan="3">
                            <ComboBox
                                x:Name="cmbTreasury"
                                Height="60"
                                FontSize="21"
                                Tag="طريقة الدفع" />
                        </Grid>


                        <Border
                            x:Name="btnSave"
                            Grid.Row="4"
                            Grid.Column="0"
                            Grid.ColumnSpan="3"
                            MaxHeight="44"
                            Margin="16,0,16,0"
                            Background="{StaticResource PrimaryColor}"
                            CornerRadius="8"
                            Cursor="Hand"
                            MouseLeftButtonDown="btnSave_MouseLeftButtonDown">
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="18"
                                Foreground="White">
                                حفظ
                            </TextBlock>
                        </Border>



                        <!--  Refresh Button  -->

                        <ListView
                            x:Name="list"
                            Grid.Row="5"
                            Grid.RowSpan="4"
                            Grid.ColumnSpan="8"
                            Background="{DynamicResource PageColor}"
                            BorderThickness="1"
                            FontFamily="pack://application:,,,/Assets/#Cairo"
                            ItemsSource="{Binding}"
                            ScrollViewer.HorizontalScrollBarVisibility="Hidden"
                            SizeChanged="list_SizeChanged">
                            <ListView.BorderBrush>
                                <SolidColorBrush Opacity="0.42" Color="Black" />
                            </ListView.BorderBrush>
                            <!--  SelectionChanged="ListView_SelectionChanged"  -->

                            <ListView.ItemContainerStyle>
                                <Style TargetType="ListViewItem">
                                    <Style.Triggers>
                                        <Trigger Property="Control.IsMouseOver" Value="True">
                                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                                            <Setter Property="FontWeight" Value="Bold" />
                                            <Setter Property="Foreground" Value="Black" />
                                        </Trigger>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                                            <Setter Property="Foreground" Value="Black" />
                                        </Trigger>

                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter Property="FontWeight" Value="Bold" />
                                            <Setter Property="Foreground" Value="Black" />
                                        </Trigger>

                                        <MultiTrigger>
                                            <MultiTrigger.Conditions>
                                                <Condition Property="IsSelected" Value="False" />
                                                <Condition Property="IsMouseOver" Value="False" />
                                            </MultiTrigger.Conditions>
                                            <Setter Property="FontWeight" Value="Thin" />
                                            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextColor}" />
                                        </MultiTrigger>

                                    </Style.Triggers>
                                    <Setter Property="Control.Background" Value="{DynamicResource PageColor}" />
                                    <Setter Property="Control.HorizontalContentAlignment" Value="Stretch" />
                                </Style>

                            </ListView.ItemContainerStyle>
                            <ListView.View>
                                <GridView AllowsColumnReorder="False">
                                    <GridView.ColumnHeaderContainerStyle>
                                        <Style BasedOn="{StaticResource ListViewHeader}" TargetType="{x:Type GridViewColumnHeader}">
                                            <Setter Property="IsEnabled" Value="False" />
                                            <Setter Property="Height" Value="60" />
                                            <Style.Triggers>
                                                <Trigger Property="IsEnabled" Value="False">
                                                    <Setter Property="TextElement.Foreground" Value="Black" />
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </GridView.ColumnHeaderContainerStyle>
                                    <GridViewColumn Width="Auto" Header="القيمة">
                                        <GridViewColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock
                                                    Height="45"
                                                    MinWidth="35"
                                                    HorizontalAlignment="Center"
                                                    Background="Transparent"
                                                    Text="{Binding Value, StringFormat='{}{0:N3}', FallbackValue='0.000', TargetNullValue='0.000'}" />
                                            </DataTemplate>
                                        </GridViewColumn.CellTemplate>
                                    </GridViewColumn>

                                    <GridViewColumn Width="Auto" Header="التاريخ">
                                        <GridViewColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock
                                                    Height="45"
                                                    MinWidth="35"
                                                    HorizontalAlignment="Center"
                                                    Background="Transparent"
                                                    Text="{Binding Date, StringFormat='{}{0:yyyy-MM-dd HH:mm}', FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}" />
                                            </DataTemplate>
                                        </GridViewColumn.CellTemplate>
                                    </GridViewColumn>

                                    <GridViewColumn Width="Auto" Header="البيان">
                                        <GridViewColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock
                                                    Height="45"
                                                    MinWidth="35"
                                                    HorizontalAlignment="Center"
                                                    Background="Transparent"
                                                    Text="{Binding Statement, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}" />
                                            </DataTemplate>
                                        </GridViewColumn.CellTemplate>
                                    </GridViewColumn>

                                    <GridViewColumn Width="Auto" Header="طريقة الدفع">
                                        <GridViewColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock
                                                    Height="45"
                                                    MinWidth="35"
                                                    HorizontalAlignment="Center"
                                                    Background="Transparent"
                                                    Text="{Binding Treasury.Name, FallbackValue='لا يوجد', TargetNullValue='لا يوجد'}" />
                                            </DataTemplate>
                                        </GridViewColumn.CellTemplate>
                                    </GridViewColumn>

                                </GridView>
                            </ListView.View>
                        </ListView>

                    </Grid>
                </Grid>
            </Border>
        </Grid>
    </Viewbox>
</Window>

