using Microsoft.EntityFrameworkCore;
using System;

namespace VisionPoint.UI.ViewModel;

public class SaleItemVM
{
    public int Index { get; set; }
    public int Id { get; set; }
    public int SaleId { get; set; }
    public string Type { get; set; } // Type: Product, Lens, Service
    public string Name { get; set; }
    public int? ProductQuantityId { get; set; }
    public int? LensQuantityRightId { get; set; }
    public int? LensQuantityLeftId { get; set; }
    public int? ServiceId { get; set; }
    /// <summary>
    /// معرف فريد للربط بين العدسة اليمنى واليسرى المضافة معًا
    /// يكون null إذا تمت إضافة العدسة بشكل منفرد
    /// </summary>
    public Guid? PairId { get; set; }
    [Precision(18, 3)] public decimal CostPrice { get; set; }
    [Precision(18, 3)] public decimal OriginalPrice { get; set; }
    [Precision(18, 3)] public decimal Discount { get; set; }
    [Precision(18, 3)] public decimal SellPrice { get; set; }
    public int Quantity { get; set; }
    /// <summary>
    /// الكمية المسترجعة
    /// </summary>
    public int ReturnedQuantity { get; set; } = 0;
    /// <summary>
    /// الكمية المتبقية (الكمية الأصلية - الكمية المسترجعة)
    /// </summary>
    public int RemainingQuantity => Quantity - ReturnedQuantity;
    public string ColorName { get; set; }
    public decimal? Axis { get; set; }
    public string ClientName { get; set; }
    public string WarehouseName { get; set; }
}