using ExcelDataReader;
using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;
using VisionPoint.UI.views.Pages.ImportExport;
using VisionPoint.UI.views.Pages.ProductsContent;

namespace VisionPoint.UI.Helper.ImportExport
{
    /// <summary>
    /// مساعد لعملية استيراد العدسات
    /// </summary>
    public class LensImportHelper
    {
        public LensImportHelper()
        {
        }

        /// <summary>
        /// بدء عملية استيراد العدسات
        /// </summary>
        /// <returns>قيمة تشير إلى نجاح العملية</returns>
        public async Task<bool> StartImportProcess()
        {
            // 1. اختيار ملف Excel
            var filePath = SelectExcelFile();
            if (string.IsNullOrEmpty(filePath))
            {
                return false; // تم إلغاء العملية
            }

            // 2. قراءة أعمدة الملف
            var excelColumns = GetExcelColumns(filePath);
            if (excelColumns == null)
            {
                ErrorBox.Show("فشل في قراءة ملف Excel. تأكد من أن الملف بتنسيق صحيح.", "خطأ في قراءة الملف", true);
                return false;
            }

            // 3. إنشاء خدمة استيراد العدسات
            var excelService = new ExcelLensService();

            // 4. الحصول على الحقول المطلوبة
            var requiredFields = excelService.GetRequiredFields();

            // 5. عرض نافذة تخطيط الأعمدة
            var columnMapping = ShowColumnMappingWindow(excelColumns, requiredFields);
            if (columnMapping == null)
            {
                return false; // تم إلغاء العملية
            }

            // 6. تعيين تخطيط الأعمدة
            excelService.SetColumnMapping(columnMapping);

            // 7. عرض نافذة الاستيراد وبدء العملية
            var importerPage = new LenssesImporterPage(filePath, excelService);
            importerPage.ShowDialog();

            return true;
        }

        /// <summary>
        /// اختيار ملف Excel
        /// </summary>
        /// <returns>مسار الملف المختار أو سلسلة فارغة إذا تم الإلغاء</returns>
        private string SelectExcelFile()
        {
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Filter = "Excel Files|*.xls;*.xlsx"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                return openFileDialog.FileName;
            }

            return string.Empty;
        }

        /// <summary>
        /// عرض نافذة تخطيط الأعمدة
        /// </summary>
        /// <param name="excelColumns">أعمدة ملف Excel</param>
        /// <param name="requiredFields">الحقول المطلوبة</param>
        /// <returns>تخطيط الأعمدة أو null إذا تم الإلغاء</returns>
        private Dictionary<string, string> ShowColumnMappingWindow(DataColumnCollection excelColumns, Dictionary<string, FieldInfo> requiredFields)
        {
            var mappingWindow = new ColumnMappingWindow(excelColumns, requiredFields);
            var result = mappingWindow.ShowDialog();

            if (result == true)
            {
                return mappingWindow.ColumnMapping;
            }

            return null;
        }

        /// <summary>
        /// قراءة أعمدة ملف Excel
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>مجموعة أعمدة الملف أو null في حالة الخطأ</returns>
        private DataColumnCollection GetExcelColumns(string filePath)
        {
            try
            {
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                using var stream = File.Open(filePath, FileMode.Open, FileAccess.Read);
                using var reader = ExcelReaderFactory.CreateReader(stream);
                var dataSet = reader.AsDataSet(new ExcelDataSetConfiguration
                {
                    ConfigureDataTable = _ => new ExcelDataTableConfiguration { UseHeaderRow = true }
                });

                if (dataSet.Tables.Count > 0 && dataSet.Tables[0].Columns.Count > 0)
                {
                    return dataSet.Tables[0].Columns;
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error reading Excel file: {ex.Message}");
                return null;
            }
        }
    }
}
