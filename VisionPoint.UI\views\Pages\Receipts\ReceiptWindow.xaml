<Window
    x:Class="VisionPoint.UI.views.Pages.Receipts.ReceiptWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Receipts"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils"
    Title="إدارة الإيصالات"
    AllowsTransparency="True"
    Background="Transparent"
    FlowDirection="RightToLeft"
    Loaded="Window_Loaded"
    ResizeMode="CanResize"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>


    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">
        <Grid Width="1920" Height="1080">
            <!--  Column Definitions  -->
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="3*" />
                <ColumnDefinition Width="10*" />
                <ColumnDefinition Width="3*" />
            </Grid.ColumnDefinitions>

            <!--  Row Definitions  -->
            <Grid.RowDefinitions>
                <RowDefinition Height="1*" />
                <RowDefinition Height="4*" />
                <RowDefinition Height="1*" />
            </Grid.RowDefinitions>

            <!--  Center Cell Content  -->
            <Border
                Grid.Row="1"
                Grid.Column="1"
                Background="{StaticResource backgroundColor}"
                BorderBrush="{StaticResource PrimaryColor}"
                BorderThickness="1"
                CornerRadius="16">

                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <!--  العنوان  -->
                    <TextBlock
                        Grid.Row="0"
                        Margin="0,0,0,20"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        FontSize="24"
                        FontWeight="Bold"
                        Foreground="#333333"
                        Text="إدارة الإيصالات" />

                    <!--  محتوى النافذة  -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <Grid Margin="16">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="auto" />
                                <RowDefinition Height="auto" />
                                <RowDefinition Height="auto" />
                                <RowDefinition Height="auto" />
                                <RowDefinition Height="auto" />
                                <RowDefinition Height="auto" />
                                <RowDefinition Height="auto" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  نوع الإيصال  -->
                            <ComboBox
                                x:Name="cmbType"
                                Grid.Row="0"
                                Grid.Column="0"
                                Grid.ColumnSpan="2"
                                Height="60"
                                Margin="8,16"
                                VerticalContentAlignment="Center"
                                DisplayMemberPath="Name"
                                FontSize="21"
                                IsEditable="False"
                                SelectedValuePath="Id"
                                SelectionChanged="cmbType_SelectionChanged"
                                Tag="نوع الإيصال" />

                            <!--  المخزن  -->
                            <ComboBox
                                x:Name="cmbWarehouse"
                                Grid.Row="0"
                                Grid.Column="2"
                                Grid.ColumnSpan="2"
                                Height="60"
                                Margin="8"
                                VerticalContentAlignment="Center"
                                DisplayMemberPath="Name"
                                FontSize="21"
                                IsEditable="False"
                                SelectedValuePath="Id"
                                SelectionChanged="cmbWarehouse_SelectionChanged"
                                Tag="المخزن" />

                            <!--  اتجاه العملية  -->
                            <Grid
                                Grid.Row="1"
                                Grid.ColumnSpan="4"
                                Margin="8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <RadioButton
                                    x:Name="RdbExchange"
                                    Grid.Column="0"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Checked="RdbExchange_Checked"
                                    Content="قبض"
                                    FontSize="18"
                                    GroupName="ExchangeType"
                                    IsChecked="True" />

                                <RadioButton
                                    x:Name="RdbCatch"
                                    Grid.Column="1"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Checked="RdbCatch_Checked"
                                    Content="صرف"
                                    FontSize="18"
                                    GroupName="ExchangeType" />
                            </Grid>

                            <!--  العميل/المورد  -->
                            <Grid
                                Grid.Row="2"
                                Grid.ColumnSpan="3"
                                Margin="8,16"
                                VerticalAlignment="Center">

                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="auto" />
                                </Grid.RowDefinitions>



                                <ComboBox
                                    x:Name="cmbClient"
                                    Grid.Row="0"
                                    Height="60"
                                    Margin="0,0,0,4"
                                    VerticalContentAlignment="Center"
                                    DisplayMemberPath="Name"
                                    FontSize="21"
                                    IsEditable="True"
                                    SelectedValuePath="Id"
                                    SelectionChanged="cmbClient_SelectionChanged"
                                    Tag="العميل/المورد" />

                                <TextBlock
                                    x:Name="txtClientMoney"
                                    Grid.Row="1"
                                    Height="auto"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    d:Text="القيمة : 500 دل"
                                    FontSize="21"
                                    Foreground="{StaticResource SuccessColor}" />
                            </Grid>

                            <!--  الموظف  -->
                            <Grid
                                Grid.Row="2"
                                Grid.ColumnSpan="4"
                                Margin="8,16"
                                VerticalAlignment="Center">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>

                                <ComboBox
                                    x:Name="cmbEmploye"
                                    Grid.Row="0"
                                    Height="60"
                                    Margin="0,0,0,4"
                                    VerticalContentAlignment="Center"
                                    DisplayMemberPath="Name"
                                    FontSize="21"
                                    IsEditable="False"
                                    SelectedValuePath="Id"
                                    SelectionChanged="cmbEmploye_SelectionChanged"
                                    Tag="الموظف"
                                    Visibility="Collapsed" />

                                <TextBlock
                                    x:Name="txtEmployeMoney"
                                    Grid.Row="1"
                                    Height="auto"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    d:Text="القيمة : 500 دل"
                                    FontSize="21"
                                    Foreground="{StaticResource SuccessColor}" />

                            </Grid>

                            <!--  المصروف  -->
                            <ComboBox
                                x:Name="cmbExpense"
                                Grid.Row="2"
                                Grid.ColumnSpan="4"
                                Height="60"
                                Margin="8"
                                VerticalContentAlignment="Center"
                                DisplayMemberPath="Name"
                                FontSize="21"
                                IsEditable="False"
                                SelectedValuePath="Id"
                                SelectionChanged="cmbExpense_SelectionChanged"
                                Tag="المصروف"
                                Visibility="Collapsed" />

                            <!--  طريقة الدفع  -->
                            <Grid
                                Grid.Row="3"
                                Grid.Column="2"
                                Grid.ColumnSpan="2"
                                Margin="8,16">

                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>

                                <ComboBox
                                    x:Name="cmbTreasury"
                                    Grid.Row="0"
                                    Height="60"
                                    Margin="0,0,0,4"
                                    VerticalContentAlignment="Center"
                                    DisplayMemberPath="Name"
                                    FontSize="21"
                                    IsEditable="False"
                                    SelectedValuePath="Id"
                                    SelectionChanged="cmbTreasury_SelectionChanged"
                                    Tag="طريقة الدفع" />

                                <TextBlock
                                    x:Name="txtTreasuryMoney"
                                    Grid.Row="1"
                                    Height="auto"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    d:Text="القيمة : 500 دل"
                                    FontSize="21"
                                    Foreground="{StaticResource SuccessColor}" />

                            </Grid>

                            <!--  من طريقة الدفع (للتحويل)  -->
                            <Grid
                                Grid.Row="3"
                                Grid.Column="2"
                                Grid.ColumnSpan="2"
                                Margin="8"
                                VerticalAlignment="Center">

                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>

                                <ComboBox
                                    x:Name="cmbSourceTreasury"
                                    Grid.Row="0"
                                    Height="60"
                                    Margin="0,0,0,4"
                                    VerticalContentAlignment="Center"
                                    DisplayMemberPath="Name"
                                    FontSize="21"
                                    IsEditable="False"
                                    SelectedValuePath="Id"
                                    SelectionChanged="cmbSourceTreasury_SelectionChanged"
                                    Tag="من طريقة الدفع"
                                    Visibility="Collapsed" />

                                <TextBlock
                                    x:Name="txtSourceTreasuryMoney"
                                    Grid.Row="1"
                                    Height="auto"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    d:Text="القيمة : 500 دل"
                                    FontSize="21"
                                    Foreground="{StaticResource SuccessColor}" />

                            </Grid>
                            <!--  إلى طريقة الدفع (للتحويل)  -->
                            <Grid
                                Grid.Row="3"
                                Grid.Column="4"
                                Grid.ColumnSpan="2"
                                Margin="8,16"
                                VerticalAlignment="Center">

                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>

                                <ComboBox
                                    x:Name="cmbTargetTreasury"
                                    Grid.Row="0"
                                    Height="60"
                                    Margin="0,0,0,4"
                                    VerticalContentAlignment="Center"
                                    DisplayMemberPath="Name"
                                    FontSize="21"
                                    IsEditable="False"
                                    SelectedValuePath="Id"
                                    SelectionChanged="cmbTargetTreasury_SelectionChanged"
                                    Tag="إلى طريقة الدفع"
                                    Visibility="Collapsed" />

                                <TextBlock
                                    x:Name="txtTargetTreasuryInfo"
                                    Grid.Row="1"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    d:Text="القيمة : 500 دل"
                                    FontSize="21"
                                    Foreground="{StaticResource SuccessColor}" />

                            </Grid>

                            <!--  رقم الإيصال والمبلغ  -->
                            <TextBox
                                x:Name="txtReceiptNo"
                                Grid.Row="4"
                                Grid.Column="0"
                                Grid.ColumnSpan="2"
                                Height="60"
                                Margin="8,16"
                                VerticalAlignment="Top"
                                VerticalContentAlignment="Center"
                                BorderThickness="1"
                                FontSize="21"
                                IsReadOnly="True"
                                Tag="رقم الإيصال" />

                            <TextBox
                                x:Name="txtValue"
                                Grid.Row="3"
                                Grid.Column="0"
                                Grid.ColumnSpan="2"
                                Height="60"
                                Margin="8,16"
                                VerticalAlignment="Top"
                                VerticalContentAlignment="Center"
                                utils:NumericInputControl.AllowNegative="False"
                                utils:NumericInputControl.IsDecimalOnly="True"
                                BorderThickness="1"
                                FontSize="21"
                                Tag="المبلغ" />

                            <!--  التاريخ  -->

                            <Grid
                                Grid.Row="0"
                                Grid.Column="4"
                                Grid.ColumnSpan="2">
                                <DatePicker
                                    x:Name="DtpGeneralExpireOn"
                                    Height="60"
                                    Margin="8"
                                    VerticalContentAlignment="Center"
                                    BorderBrush="{StaticResource PrimaryTextColor}"
                                    BorderThickness="1"
                                    FontSize="21"
                                    SelectedDateFormat="Short">
                                    <DatePicker.Resources>
                                        <Style TargetType="{x:Type DatePickerTextBox}">
                                            <Setter Property="Control.Template">
                                                <Setter.Value>
                                                    <ControlTemplate>
                                                        <TextBox
                                                            x:Name="PART_TextBox"
                                                            VerticalAlignment="Stretch"
                                                            BorderThickness="0"
                                                            FontSize="21"
                                                            Foreground="{StaticResource PrimaryTextColor}"
                                                            IsReadOnly="True"
                                                            Style="{StaticResource txtDatePick}"
                                                            Tag="تاريخ"
                                                            Text="{Binding Path=SelectedDate, StringFormat='yyyy-MM-dd', RelativeSource={RelativeSource AncestorType={x:Type DatePicker}}}" />
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </DatePicker.Resources>
                                </DatePicker>
                            </Grid>
                            <!--  رقم الفاتورة  -->
                            <Grid
                                Grid.Row="4"
                                Grid.Column="2"
                                Grid.ColumnSpan="4"
                                Margin="8,16"
                                VerticalAlignment="Center">

                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                </Grid.RowDefinitions>

                                <TextBox
                                    x:Name="txtInvoiceNo"
                                    Grid.Row="0"
                                    Height="60"
                                    Margin="0,0,0,4"
                                    VerticalContentAlignment="Center"
                                    utils:NumericInputControl.IsNumericOnly="True"
                                    BorderThickness="1"
                                    FontSize="21"
                                    Tag="رقم الفاتورة"
                                    TextChanged="txtInvoiceNo_TextChanged" />

                                <TextBlock
                                    x:Name="txtInvoiceNoMoney"
                                    Grid.Row="1"
                                    Height="auto"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    d:Text="القيمة : 500 دل"
                                    FontSize="21"
                                    Foreground="{StaticResource SuccessColor}" />

                            </Grid>

                            <!--  البيان  -->
                            <TextBox
                                x:Name="txtStatment"
                                Grid.Row="7"
                                Grid.ColumnSpan="6"
                                MinHeight="60"
                                Margin="8,16"
                                VerticalContentAlignment="Top"
                                AcceptsReturn="True"
                                BorderThickness="1"
                                FontSize="18"
                                Tag="البيان"
                                TextWrapping="Wrap"
                                VerticalScrollBarVisibility="Auto" />
                        </Grid>
                    </ScrollViewer>

                    <!--  أزرار الإجراءات  -->
                    <Grid Grid.Row="2" Margin="0,16,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <Border
                            x:Name="btnSave"
                            Grid.Column="0"
                            Height="50"
                            Margin="8"
                            Background="{StaticResource PrimaryColor}"
                            CornerRadius="8"
                            Cursor="Hand"
                            MouseLeftButtonDown="btnSave_MouseLeftButtonDown">
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="18"
                                FontWeight="Bold"
                                Foreground="White"
                                Text="حفظ" />
                        </Border>
                        <Border
                            x:Name="btnNew"
                            Grid.Column="1"
                            Height="50"
                            Margin="8"
                            Background="Transparent"
                            BorderBrush="{StaticResource PrimaryColor}"
                            BorderThickness="2"
                            CornerRadius="8"
                            Cursor="Hand"
                            IsEnabled="True"
                            MouseLeftButtonDown="btnNew_MouseLeftButtonDown"
                            Opacity="1.0">
                            <TextBlock
                                x:Name="txtPrintLabel"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="18"
                                FontWeight="Bold"
                                Foreground="{StaticResource PrimaryColor}"
                                Text="جديد" />
                        </Border>
                        <Border
                            x:Name="btnPrint"
                            Grid.Column="2"
                            Height="50"
                            Margin="8"
                            Background="Transparent"
                            BorderBrush="{StaticResource PrimaryColor}"
                            BorderThickness="2"
                            CornerRadius="8"
                            Cursor="Hand"
                            IsEnabled="True"
                            MouseLeftButtonDown="btnPrint_MouseLeftButtonDown"
                            Opacity="1.0">
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="18"
                                FontWeight="Bold"
                                Foreground="{StaticResource PrimaryColor}"
                                Text="طباعة" />
                        </Border>

                        <Border
                            x:Name="btnCancel"
                            Grid.Column="3"
                            Height="50"
                            Margin="8"
                            Background="Transparent"
                            BorderBrush="{StaticResource errorColor}"
                            BorderThickness="1"
                            CornerRadius="8"
                            Cursor="Hand"
                            MouseLeftButtonDown="btnCancel_MouseLeftButtonDown">
                            <TextBlock
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="18"
                                FontWeight="Bold"
                                Foreground="{StaticResource errorColor}"
                                Text="إلغاء" />
                        </Border>
                    </Grid>
                </Grid>
            </Border>
        </Grid>
    </Viewbox>
</Window>
