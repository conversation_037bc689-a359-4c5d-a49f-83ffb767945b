﻿using System.IO;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.Reports.client
{
    /// <summary>
    /// Interaction logic for ClientReportHeader.xaml
    /// </summary>
    public partial class ClientReportHeader : Page
    {
        public ClientReportHeader(string DateFrom, string DateTo, string phone, string ClientName)
        {
            InitializeComponent();
            txtDateFrom.Text = DateFrom;
            txtDateTo.Text = DateTo;
            txtPhone.Text = phone;
            txtName.Text = ClientName;
            CNameAr.Text = Properties.Settings.Default.CompanyName;
            LoadInitialLogo(); // Call this here
        }

        private void LoadInitialLogo()
        {
            string initialLogoPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Assets", "company_logo.png");

            if (File.Exists(initialLogoPath))
            {
                try
                {
                    BitmapImage bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri(initialLogoPath);
                    bitmap.CacheOption = BitmapCacheOption.OnLoad; // Important for releasing file lock
                    bitmap.EndInit();
                    LogoImageElement.Source = bitmap; // Assuming LogoImageElement is your Image's x:Name
                }
                catch (Exception ex)
                {
                    ErrorBox.Show($"Error loading initial logo: {ex.Message}", "خطأ");
                }
            }
        }
    }
}
