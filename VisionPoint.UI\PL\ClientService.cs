using Microsoft.EntityFrameworkCore;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Models;
using VisionPoint.UI.ViewModel;

namespace VisionPoint.UI.PL;

public class ClientService : IDisposable
{
    private readonly AppDbContext _context = ServiceLocator.GetService<AppDbContext>();
    private bool _disposed = false;

    public ClientService()
    {
    }
    public async Task<List<Client>> GetAllClientsAsync()
    {
        return await _context.Clients.ToListAsyncWithBusy("GetAllClients");
    }

    public async Task<Client> GetClientByIdAsync(int id)
    {
        return await _context.Clients.FindAsyncWithBusy(id);
    }

    public async Task<(bool State, string Message, Client Client)> AddClientAsync(Client client)
    {
        client.CreatedById = CurrentUser.Id;
        client.ModifiedById = CurrentUser.Id;
        await _context.Clients.AddAsyncWithBusy(client);
        var result = await _context.SaveWithTransactionAndBusy("AddClient");
        return (result.State, result.Message, client);
    }

    public async Task<(bool State, string Message, Client Client)> UpdateClientAsync(Client client)
    {
        if (client.Id is 1 or 2) // لا يمكن تحديث العميلين الافتراضيين
        {
            return (false, "لا يمكن تحديث العميل الافتراضي", client);
        }
        client.ModifiedById = CurrentUser.Id;
        _context.Clients.UpdateWithBusy(client);
        var result = await _context.SaveWithTransactionAndBusy("UpdateClient");
        return (result.State, result.Message, client);
    }

    public async Task<(bool State, string Message)> DeleteClientAsync(int id)
    {
        if (id is 1 or 2) // لا يمكن حذف العميلين الافتراضيين
        {
            return (false, "لا يمكن حذف العميل الافتراضي");
        }
        var client = await _context.Clients.FindAsyncWithBusy(id);
        if (client == null)
        {
            return (false, "العميل غير موجود");
        }

        // التحقق من وجود فواتير مبيعات مرتبطة بالعميل
        bool hasSales = await _context.Sales.AnyAsyncWithBusy(s => s.ClientId == id, "CheckClientSales");

        if (hasSales)
        {
            return (false, "لا يمكن حذف العميل لأنه مرتبط بفواتير مبيعات");
        }

        // التحقق من وجود فواتير مشتريات مرتبطة بالعميل
        bool hasPurchases = await _context.Purchases.AnyAsyncWithBusy(p => p.ClientId == id, "CheckClientPurchases");

        if (hasPurchases)
        {
            return (false, "لا يمكن حذف العميل لأنه مرتبط بفواتير مشتريات");
        }

        // التحقق من وجود إيصالات مرتبطة بالعميل
        bool hasReceipts = await _context.Receipts.AnyAsyncWithBusy(r => r.ClientId == id, "CheckClientReceipts");

        if (hasReceipts)
        {
            return (false, "لا يمكن حذف العميل لأنه مرتبط بإيصالات");
        }

        _context.Clients.RemoveWithBusy(client);
        return await _context.SaveWithTransactionAndBusy("DeleteClient");
    }

    public async Task<List<Client>> GetCustomersOnlyAsync()
    {
        return await _context.Clients.Where(c => c.IsCustomer).AsNoTracking().ToListAsyncWithBusy("GetCustomersOnly");
    }

    public async Task<List<Client>> GetSuppliersOnlyAsync()
    {
        return await _context.Clients.Where(c => c.IsSupplier).AsNoTracking().ToListAsyncWithBusy("GetSuppliersOnly");
    }

    public async Task<List<Client>> SearchClientsAsync(int? warehouseId, string? name, string? phone, bool isCustomer, bool isSupplier)
    {
        return await _context.Clients
            .Where(c => (c.WarehouseId == warehouseId || c.WarehouseId == null || warehouseId == null)
            && (c.Name == name || name == null)
            && (c.Phone == phone || phone == null)
            && (c.IsCustomer == isCustomer || c.IsSupplier == isSupplier)
            ).Include(c => c.Warehouse).ToListAsyncWithBusy("SearchClients");
    }

    /// <summary>
    /// الحصول على العملاء المرتبطين بمخزن معين أو العملاء العامين (WarehouseId = null)
    /// </summary>
    /// <param name="warehouseId">معرف المخزن - إذا كان null فسيتم إرجاع العملاء العامين فقط</param>
    /// <returns>قائمة العملاء</returns>
    public async Task<List<Client>> GetClientsByWarehouseAsync(int? warehouseId)
    {
        var query = _context.Clients.AsQueryable();  // إرجاع العملاء المرتبطين بالمخزن المحدد أو العملاء العامين (WarehouseId = null)
        query = query.Where(c => c.WarehouseId == warehouseId || c.WarehouseId == null || warehouseId == null);

        return await query.Include(c => c.Warehouse).ToListAsyncWithBusy("GetClientsByWarehouse");
    }

    /// <summary>
    /// الحصول على العملاء (الزبائن) المرتبطين بمخزن معين أو العملاء العامين
    /// </summary>
    /// <param name="warehouseId">معرف المخزن</param>
    /// <returns>قائمة الزبائن</returns>
    public async Task<List<Client>> GetCustomersByWarehouseAsync(int? warehouseId)
    {
        var query = _context.Clients.Where(c => c.IsCustomer);
        query = query.Where(c => c.WarehouseId == warehouseId || c.WarehouseId == null);
        return await query.Include(c => c.Warehouse).ToListAsyncWithBusy("GetCustomersByWarehouse");
    }

    /// <summary>
    /// الحصول على الموردين المرتبطين بمخزن معين أو الموردين العامين
    /// </summary>
    /// <param name="warehouseId">معرف المخزن</param>
    /// <returns>قائمة الموردين</returns>
    public async Task<List<Client>> GetSuppliersByWarehouseAsync(int? warehouseId)
    {
        var query = _context.Clients.Where(c => c.IsSupplier);
        query = query.Where(c => c.WarehouseId == warehouseId || c.WarehouseId == null);
        return await query.Include(c => c.Warehouse).ToListAsyncWithBusy("GetSuppliersByWarehouse");
    }

    public async Task<decimal> GetClientBalanceAsync(int id)
    {
        var client = await _context.Clients.FindAsyncWithBusy(id);
        return client?.Balance ?? 0;
    }
    public async Task<(bool State, string Message, decimal Balance)> IncreaseBalanceAsync(int clientId, decimal amount)
    {
        var client = await _context.Clients.FindAsyncWithBusy(clientId);
        if (client != null)
        {
            decimal newBalance = client.Balance + amount;

            // التحقق من الحد المسموح
            var (isAllowed, message) = CheckBalanceLimit(client, newBalance);
            if (!isAllowed)
            {
                return (false, message, client.Balance);
            }

            client.Balance = newBalance;
            var result = await _context.SaveWithTransactionAndBusy("IncreaseBalance");
            return (result.State, result.Message, client.Balance);
        }
        return (false, "العميل غير موجود", 0m);
    }

    public async Task<(bool State, string Message, decimal Balance)> DecreaseBalanceAsync(int clientId, decimal amount)
    {
        var client = await _context.Clients.FindAsyncWithBusy(clientId);
        if (client != null)
        {
            decimal newBalance = client.Balance - amount;

            // التحقق من الحد المسموح
            var (isAllowed, message) = CheckBalanceLimit(client, newBalance);
            if (!isAllowed)
            {
                return (false, message, client.Balance);
            }

            client.Balance = newBalance;
            var result = await _context.SaveWithTransactionAndBusy("DecreaseBalance");
            return (result.State, result.Message, client.Balance);
        }
        return (false, "العميل غير موجود", 0m);
    }

    /// <summary>
    /// التحقق من الحد المسموح للرصيد
    /// </summary>
    /// <param name="client">العميل</param>
    /// <param name="newBalance">الرصيد الجديد المقترح</param>
    /// <returns>هل الرصيد مسموح ورسالة الخطأ إن وجدت</returns>
    private (bool IsAllowed, string Message) CheckBalanceLimit(Client client, decimal newBalance)
    {
        // إذا كان الحد المسموح = 0، فلا توجد قيود
        if (client.AllowedBalance == 0)
        {
            return (true, string.Empty);
        }

        // إذا كان الحد المسموح موجب (حد أقصى للمديونية)
        if (client.AllowedBalance > 0)
        {
            if (newBalance > client.AllowedBalance)
            {
                return (false, $"تجاوز الحد الأقصى المسموح للمديونية ({client.AllowedBalance:N3}). الرصيد المطلوب: {newBalance:N3}");
            }
        }
        // إذا كان الحد المسموح سالب (حد أقصى للدائنية)
        else if (client.AllowedBalance < 0)
        {
            if (newBalance < client.AllowedBalance)
            {
                return (false, $"تجاوز الحد الأقصى المسموح للدائنية ({Math.Abs(client.AllowedBalance):N3}). الرصيد المطلوب: {Math.Abs(newBalance):N3}");
            }
        }

        return (true, string.Empty);
    }

    /// <summary>
    /// التحقق من إمكانية تحديث رصيد العميل قبل العملية
    /// </summary>
    /// <param name="clientId">معرف العميل</param>
    /// <param name="balanceChange">التغيير في الرصيد</param>
    /// <returns>هل العملية مسموحة ورسالة الخطأ إن وجدت</returns>
    public async Task<(bool IsAllowed, string Message)> CheckBalanceChangeAsync(int clientId, decimal balanceChange)
    {
        var client = await _context.Clients.FindAsyncWithBusy(clientId);
        if (client == null)
        {
            return (false, "العميل غير موجود");
        }

        decimal newBalance = client.Balance + balanceChange;
        return CheckBalanceLimit(client, newBalance);
    }

    /// <summary>
    /// استرجاع كشف حساب العميل
    /// </summary>
    /// <param name="clientId">معرف العميل</param>
    /// <param name="fromDate">تاريخ البداية (اختياري)</param>
    /// <param name="toDate">تاريخ النهاية (اختياري)</param>
    /// <returns>قائمة بعمليات العميل</returns>
    public async Task<(List<ClientStatementVM> Transactions, ClientStatementSummaryVM Summary)> GetClientStatementAsync(int clientId, DateTime? fromDate = null, DateTime? toDate = null)
    {
        // التحقق من وجود العميل
        var client = await _context.Clients.FindAsyncWithBusy(clientId);
        if (client == null)
        {
            return (new List<ClientStatementVM>(), new ClientStatementSummaryVM { ClientName = "غير موجود" });
        }

        var result = new List<ClientStatementVM>();

        // استعلام الإيصالات
        var receiptsQuery = _context.Receipts
            .Include(r => r.Financial)
            .Include(r => r.Treasury) // تضمين معلومات الخزينة
            .Where(r => r.ClientId == clientId);

        // استعلام فواتير المبيعات
        var salesQuery = _context.Sales
            .Where(s => s.ClientId == clientId);

        // استعلام فواتير المشتريات
        var purchasesQuery = _context.Purchases
            .Where(p => p.ClientId == clientId);

        // تطبيق فلتر التاريخ إذا تم تحديده
        if (fromDate.HasValue)
        {
            receiptsQuery = receiptsQuery.Where(r => r.Date >= fromDate.Value);
            salesQuery = salesQuery.Where(s => s.SaleDate >= fromDate.Value);
            purchasesQuery = purchasesQuery.Where(p => p.PurchaseDate >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            // إضافة يوم واحد لتاريخ النهاية للحصول على نهاية اليوم
            var endDate = toDate.Value.AddDays(1).AddSeconds(-1);
            receiptsQuery = receiptsQuery.Where(r => r.Date <= endDate);
            salesQuery = salesQuery.Where(s => s.SaleDate <= endDate);
            purchasesQuery = purchasesQuery.Where(p => p.PurchaseDate <= endDate);
        }

        // جمع كل العمليات
        var receipts = await receiptsQuery.ToListAsyncWithBusy("GetReceiptsForClientStatement");
        var sales = await salesQuery.ToListAsyncWithBusy("GetSalesForClientStatement");
        var purchases = await purchasesQuery.ToListAsyncWithBusy("GetPurchasesForClientStatement");

        // لا نستخدم الرصيد الحالي للعميل، بل نحسب الرصيد من العمليات فقط
        var clientInfo = await _context.Clients.FindAsyncWithBusy(clientId);

        // جمع كل العمليات في قائمة واحدة للترتيب حسب التاريخ
        var allTransactions = new List<ClientStatementVM>();

        // حساب الرصيد السابق (قبل تاريخ "من")
        decimal previousBalance = 0;
        if (fromDate.HasValue)
        {
            // جلب العمليات قبل تاريخ "من"
            var previousReceipts = await _context.Receipts
                .Include(r => r.Financial)
                .Where(r => r.ClientId == clientId && r.Date < fromDate.Value)
                .ToListAsyncWithBusy("GetPreviousReceiptsForClientStatement");

            var previousSales = await _context.Sales
                .Where(s => s.ClientId == clientId && s.SaleDate < fromDate.Value)
                .ToListAsyncWithBusy("GetPreviousSalesForClientStatement");

            var previousPurchases = await _context.Purchases
                .Where(p => p.ClientId == clientId && p.PurchaseDate < fromDate.Value)
                .ToListAsyncWithBusy("GetPreviousPurchasesForClientStatement");

            // حساب الرصيد السابق من الإيصالات
            foreach (var receipt in previousReceipts)
            {
                decimal incoming = 0;
                decimal outgoing = 0;

                if (receipt.FinancialId.HasValue)
                {
                    FinancialId financialId = (FinancialId)receipt.FinancialId.Value;

                    switch (financialId)
                    {
                        case FinancialId.Client:
                            if (receipt.IsExchange == true) // دفع للعميل
                                outgoing = receipt.Value;
                            else // قبض من العميل
                                incoming = receipt.Value;
                            break;
                        case FinancialId.Sale:
                            incoming = receipt.Value;
                            break;
                        case FinancialId.Purchase:
                            outgoing = receipt.Value;
                            break;
                        case FinancialId.OpeningBalanceForClient:
                            if (receipt.Value >= 0)
                                incoming = receipt.Value;
                            else
                                outgoing = Math.Abs(receipt.Value);
                            break;
                        default:
                            if (receipt.IsExchange == true)
                                outgoing = receipt.Value;
                            else
                                incoming = receipt.Value;
                            break;
                    }
                }
                else
                {
                    if (receipt.IsExchange == true)
                        outgoing = receipt.Value;
                    else
                        incoming = receipt.Value;
                }

                previousBalance = previousBalance - incoming + outgoing;
            }

            // حساب الرصيد السابق من فواتير المبيعات
            foreach (var sale in previousSales)
            {
                decimal netAmount = sale.TotalAmount - sale.TotalReturned;
                previousBalance = previousBalance + netAmount; // فاتورة المبيعات تزيد مديونية العميل
            }

            // حساب الرصيد السابق من فواتير المشتريات
            foreach (var purchase in previousPurchases)
            {
                previousBalance = previousBalance - purchase.TotalAmount; // فاتورة المشتريات تنقص مديونية العميل
            }

            // إضافة صف الرصيد السابق إذا كان هناك عمليات سابقة
            if (previousReceipts.Count > 0 || previousSales.Count > 0 || previousPurchases.Count > 0)
            {
                allTransactions.Add(new ClientStatementVM
                {
                    Id = -1, // معرف خاص للرصيد السابق
                    Date = fromDate.Value.AddSeconds(-1),
                    OperationType = "الرصيد السابق",
                    OperationNumber = 0,
                    PaymentMethod = "-",
                    Description = "الرصيد قبل " + fromDate.Value.ToString("yyyy-MM-dd"),
                    Incoming = 0,
                    Outgoing = 0,
                    Balance = previousBalance,
                    IsSpecialRow = true
                });
            }
        }

        // تحويل الإيصالات إلى نموذج كشف الحساب
        foreach (var receipt in receipts)
        {
            decimal incoming = 0;
            decimal outgoing = 0;
            string operationType = "";

            // تحديد نوع العملية بناءً على FinancialId وIsExchange
            if (receipt.FinancialId.HasValue)
            {
                FinancialId financialId = (FinancialId)receipt.FinancialId.Value;

                switch (financialId)
                {
                    case FinancialId.Client:
                        if (receipt.IsExchange == true) // دفع للعميل
                        {
                            outgoing = receipt.Value; // يزيد مديونية العميل (موجب)
                            operationType = "دفع للعميل";
                        }
                        else // قبض من العميل
                        {
                            incoming = receipt.Value; // ينقص مديونية العميل (سالب)
                            operationType = "قبض من العميل";
                        }
                        break;

                    case FinancialId.Sale:
                        incoming = receipt.Value; // ينقص مديونية العميل (سالب) - العميل يسدد جزء من دينه
                        operationType = "دفعة فاتورة مبيعات";
                        break;

                    case FinancialId.Purchase:
                        outgoing = receipt.Value; // يزيد مديونية العميل (موجب) - المتجر يسدد جزء من دينه للعميل
                        operationType = "دفعة فاتورة مشتريات";
                        break;

                    case FinancialId.OpeningBalanceForClient:
                        if (receipt.Value >= 0)
                        {
                            incoming = receipt.Value;
                            operationType = "رصيد افتتاحي (دائن)";
                        }
                        else
                        {
                            outgoing = Math.Abs(receipt.Value);
                            operationType = "رصيد افتتاحي (مدين)";
                        }
                        break;

                    default:
                        // للأنواع الأخرى، نستخدم اسم النوع المالي
                        if (receipt.IsExchange == true)
                        {
                            outgoing = receipt.Value;
                            operationType = receipt.Financial?.Name ?? "دفع";
                        }
                        else
                        {
                            incoming = receipt.Value;
                            operationType = receipt.Financial?.Name ?? "قبض";
                        }
                        break;
                }
            }
            else
            {
                // إذا لم يكن هناك نوع مالي محدد
                if (receipt.IsExchange == true)
                {
                    outgoing = receipt.Value;
                    operationType = "دفع";
                }
                else
                {
                    incoming = receipt.Value;
                    operationType = "قبض";
                }
            }

            // تحديد طريقة الدفع للإيصالات فقط
            string paymentMethod;
            if (receipt.Treasury != null)
            {
                paymentMethod = receipt.Treasury.Name;
            }
            else if (receipt.TreasuryId.HasValue)
            {
                // محاولة جلب الخزينة إذا كان لدينا معرف الخزينة
                var treasury = await _context.Treasuries.FindAsyncWithBusy(receipt.TreasuryId.Value);
                paymentMethod = treasury?.Name ?? "نقدي";
            }
            else
            {
                paymentMethod = "نقدي";
            }

            allTransactions.Add(new ClientStatementVM
            {
                Id = receipt.Id,
                Date = receipt.Date,
                OperationType = operationType,
                OperationNumber = receipt.ReceiptNo,
                PaymentMethod = paymentMethod,
                Description = receipt.Statement ?? "",
                Incoming = incoming, // مبلغ داخل للمتجر (خارج من العميل)
                Outgoing = outgoing, // مبلغ خارج من المتجر (داخل للعميل)
                Balance = 0 // سيتم حسابه لاحقاً
            });
        }

        // تحويل فواتير المبيعات إلى نموذج كشف الحساب
        foreach (var sale in sales)
        {
            // فاتورة المبيعات تزيد المديونية على العميل (العميل مدين للمتجر)
            decimal netAmount = sale.TotalAmount - sale.TotalReturned;

            allTransactions.Add(new ClientStatementVM
            {
                Id = sale.Id,
                Date = sale.SaleDate,
                OperationType = "فاتورة مبيعات",
                OperationNumber = sale.Id,
                PaymentMethod = "-", // وضع "-" لفواتير المبيعات
                Description = sale.Notes ?? "فاتورة مبيعات",
                Incoming = 0, // لا يوجد مبلغ داخل للمتجر في وقت إنشاء الفاتورة
                Outgoing = netAmount, // المبلغ الذي سيدفعه العميل للمتجر (يزيد مديونية العميل - موجب)
                Balance = 0 // سيتم حسابه لاحقاً
            });
        }

        // تحويل فواتير المشتريات إلى نموذج كشف الحساب
        foreach (var purchase in purchases)
        {
            allTransactions.Add(new ClientStatementVM
            {
                Id = purchase.Id,
                Date = purchase.PurchaseDate,
                OperationType = "فاتورة مشتريات",
                OperationNumber = purchase.Id,
                PaymentMethod = "-", // وضع "-" لفواتير المشتريات
                Description = purchase.Notes ?? "فاتورة مشتريات",
                Incoming = purchase.TotalAmount, // المبلغ الذي سيحصل عليه العميل من المتجر (ينقص مديونية العميل - سالب)
                Outgoing = 0, // لا يوجد مبلغ خارج من العميل في وقت إنشاء الفاتورة
                Balance = 0 // سيتم حسابه لاحقاً
            });
        }

        // ترتيب العمليات حسب التاريخ (من الأقدم للأحدث)
        allTransactions = allTransactions.OrderBy(t => t.Date).ToList();

        // نبدأ الرصيد من الرصيد السابق (إذا كان موجودًا) ونحسب تراكميًا
        decimal currentBalance = previousBalance;
        foreach (var transaction in allTransactions)
        {
            // إذا كان هذا صف الرصيد السابق، نستخدم الرصيد المحسوب مسبقًا
            if (transaction.IsSpecialRow && transaction.OperationType == "الرصيد السابق")
            {
                transaction.Balance = previousBalance;
                result.Add(transaction);
                continue;
            }

            // نحسب الرصيد الجديد بناءً على القبض والدفع
            // في المحاسبة: الرصيد الموجب = العميل مدين للمتجر، الرصيد السالب = المتجر مدين للعميل
            // فاتورة المبيعات: تزيد مديونية العميل (موجب)
            // فاتورة المشتريات: تنقص مديونية العميل (سالب)
            // القبض من العميل: ينقص مديونية العميل (سالب)
            // الدفع للعميل: يزيد مديونية العميل (موجب)
            currentBalance = currentBalance - transaction.Incoming + transaction.Outgoing;
            transaction.Balance = currentBalance;
            result.Add(transaction);
        }

        // إضافة صف الرصيد اللاحق إذا تم تحديد تاريخ "إلى" وكانت هناك عمليات لاحقة
        if (toDate.HasValue)
        {
            // جلب العمليات بعد تاريخ "إلى"
            var endDate = toDate.Value.AddDays(1).AddSeconds(-1); // نهاية اليوم

            var futureReceipts = await _context.Receipts
                .Include(r => r.Financial)
                .Where(r => r.ClientId == clientId && r.Date > endDate)
                .ToListAsyncWithBusy("GetFutureReceiptsForClientStatement");

            var futureSales = await _context.Sales
                .Where(s => s.ClientId == clientId && s.SaleDate > endDate)
                .ToListAsyncWithBusy("GetFutureSalesForClientStatement");

            var futurePurchases = await _context.Purchases
                .Where(p => p.ClientId == clientId && p.PurchaseDate > endDate)
                .ToListAsyncWithBusy("GetFuturePurchasesForClientStatement");

            // حساب الرصيد اللاحق
            decimal futureBalance = currentBalance;

            // حساب الرصيد اللاحق من الإيصالات
            foreach (var receipt in futureReceipts)
            {
                decimal incoming = 0;
                decimal outgoing = 0;

                if (receipt.FinancialId.HasValue)
                {
                    FinancialId financialId = (FinancialId)receipt.FinancialId.Value;

                    switch (financialId)
                    {
                        case FinancialId.Client:
                            if (receipt.IsExchange == true) // دفع للعميل
                                outgoing = receipt.Value;
                            else // قبض من العميل
                                incoming = receipt.Value;
                            break;
                        case FinancialId.Sale:
                            incoming = receipt.Value;
                            break;
                        case FinancialId.Purchase:
                            outgoing = receipt.Value;
                            break;
                        case FinancialId.OpeningBalanceForClient:
                            if (receipt.Value >= 0)
                                incoming = receipt.Value;
                            else
                                outgoing = Math.Abs(receipt.Value);
                            break;
                        default:
                            if (receipt.IsExchange == true)
                                outgoing = receipt.Value;
                            else
                                incoming = receipt.Value;
                            break;
                    }
                }
                else
                {
                    if (receipt.IsExchange == true)
                        outgoing = receipt.Value;
                    else
                        incoming = receipt.Value;
                }

                futureBalance = futureBalance - incoming + outgoing;
            }

            // حساب الرصيد اللاحق من فواتير المبيعات
            foreach (var sale in futureSales)
            {
                decimal netAmount = sale.TotalAmount - sale.TotalReturned;
                futureBalance = futureBalance + netAmount; // فاتورة المبيعات تزيد مديونية العميل
            }

            // حساب الرصيد اللاحق من فواتير المشتريات
            foreach (var purchase in futurePurchases)
            {
                futureBalance = futureBalance - purchase.TotalAmount; // فاتورة المشتريات تنقص مديونية العميل
            }

            // إضافة صف الرصيد اللاحق إذا كانت هناك عمليات لاحقة
            if (futureReceipts.Count > 0 || futureSales.Count > 0 || futurePurchases.Count > 0)
            {
                result.Add(new ClientStatementVM
                {
                    Id = -2, // معرف خاص للرصيد اللاحق
                    Date = endDate.AddSeconds(1),
                    OperationType = "الرصيد اللاحق",
                    OperationNumber = 0,
                    PaymentMethod = "-",
                    Description = "الرصيد بعد " + endDate.ToString("yyyy-MM-dd"),
                    Incoming = 0,
                    Outgoing = 0,
                    Balance = futureBalance,
                    IsSpecialRow = true
                });
            }
        }

        // إنشاء ملخص كشف الحساب
        var summary = new ClientStatementSummaryVM
        {
            ClientName = clientInfo?.Name ?? "غير معروف",
            // حساب المجاميع فقط للعمليات العادية (غير الصفوف الخاصة)
            TotalDebit = result.Where(t => !t.IsSpecialRow).Sum(t => t.Outgoing),
            TotalCredit = result.Where(t => !t.IsSpecialRow).Sum(t => t.Incoming),
            // استخدام الرصيد النهائي من آخر صف في النتائج
            FinalBalance = result.LastOrDefault()?.Balance ?? 0
        };

        // تحديد حالة الرصيد (مدين/دائن)
        if (currentBalance > 0)
        {
            summary.BalanceStatus = "مدين (العميل مدين للمتجر)";
        }
        else if (currentBalance < 0)
        {
            summary.BalanceStatus = "دائن (المتجر مدين للعميل)";
        }
        else
        {
            summary.BalanceStatus = "متوازن (لا يوجد دين)";
        }

        return (result, summary);
    }

    // Implement IDisposable pattern
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // Dispose managed resources
                _context?.Dispose();
            }

            // Free unmanaged resources
            _disposed = true;
        }
    }

    // Destructor
    ~ClientService()
    {
        Dispose(false);
    }
}
