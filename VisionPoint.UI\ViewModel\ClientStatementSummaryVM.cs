using Microsoft.EntityFrameworkCore;
using System;

namespace VisionPoint.UI.ViewModel;

/// <summary>
/// نموذج ملخص كشف حساب العميل
/// </summary>
public class ClientStatementSummaryVM
{
    /// <summary>
    /// اسم العميل
    /// </summary>
    public string ClientName { get; set; }
    
    /// <summary>
    /// مجموع المدين (مجموع ما على العميل)
    /// </summary>
    [Precision(18, 3)] public decimal TotalDebit { get; set; } = 0;
    
    /// <summary>
    /// مجموع الدائن (مجموع ما للعميل)
    /// </summary>
    [Precision(18, 3)] public decimal TotalCredit { get; set; } = 0;
    
    /// <summary>
    /// الرصيد النهائي
    /// </summary>
    [Precision(18, 3)] public decimal FinalBalance { get; set; } = 0;
    
    /// <summary>
    /// توضيح حالة الرصيد (مدين/دائن)
    /// </summary>
    public string BalanceStatus { get; set; }
}
