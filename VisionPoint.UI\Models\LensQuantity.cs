﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VisionPoint.UI.Models;

[Index(nameof(LensPrescriptionColorId))]
[Index(nameof(Exp))]
[Index(nameof(WarehouseId))]
[Index(nameof(LensPrescriptionColorId), nameof(Exp))]
[Index(nameof(LensPrescriptionColorId), nameof(WarehouseId))]
[Index(nameof(LensPrescriptionColorId), nameof(WarehouseId), nameof(Exp))]
public class LensQuantity
{
    [Key]
    public int Id { get; set; }
    public int? LensPrescriptionColorId { get; set; }
    public LensPrescriptionColor? LensPrescriptionColor { get; set; }
    public int WarehouseId { get; set; }
    public Warehouse Warehouse { get; set; } = null!;
    public DateOnly? Exp { get; set; }
    public int Quantity { get; set; }

    /// <summary>
    /// Row GUID for SQL Server Merge Replication - managed by database
    /// </summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid RowGuid { get; set; }
}
