﻿﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace VisionPoint.UI.Helper
{
    /// <summary>
    /// قاموس لترجمة أسماء الأدوار من الإنجليزية إلى العربية
    /// </summary>
    public static class RoleTranslations
    {
        // قاموس الترجمة
        private static readonly Dictionary<string, string> _translations = new Dictionary<string, string>
        {
            { "Admin", "مدير النظام" },
            { "SalesRole", "عرض وإضافة المبيعات" },
            { "EditSalesRole", "تعديل المبيعات" },
            { "DeleteSalesRole", "حذف المبيعات" },
            { "ReturnSalesItemRole", "استرجاع أصناف المبيعات" },
            { "PurchaseRole", "عرض وإضافة المشتريات" },
            { "EditPurchaseRole", "تعديل المشتريات" },
            { "DeletePurchaseRole", "حذف المشتريات" },
            { "ProductRole", "عرض وإضافة المنتجات" },
            { "EditProductRole", "تعديل المنتجات" },
            { "DeleteProductRole", "حذف المنتجات" },
            { "LensRole", "عرض وإضافة العدسات" },
            { "EditLensRole", "تعديل العدسات" },
            { "DeleteLensRole", "حذف العدسات" },
            { "LensCategoryRole", "عرض وإضافة أنواع العدسات" },
            { "EditLensCategoryRole", "تعديل أنواع العدسات" },
            { "DeleteLensCategoryRole", "حذف أنواع العدسات" },
            { "ServiceRole", "عرض وإضافة الخدمات" },
            { "EditServiceRole", "تعديل الخدمات" },
            { "DeleteServiceRole", "حذف الخدمات" },
            { "ClientRole", "عرض وإضافة العملاء" },
            { "EditClientRole", "تعديل العملاء" },
            { "DeleteClientRole", "حذف العملاء" },
            { "ReceiptRole", "عرض وإضافة الإيصالات" },
            { "EditReceiptRole", "تعديل الإيصالات" },
            { "DeleteReceiptRole", "حذف الإيصالات" },
            { "ExpenseRole", "عرض وإضافة المصروفات" },
            { "EditExpenseRole", "تعديل المصروفات" },
            { "DeleteExpenseRole", "حذف المصروفات" },

            // أدوار تغيير التواريخ
            { "ChangePurchaseDateRole", "تغيير تاريخ فاتورة المشتريات" },
            { "ChangeSaleDateRole", "تغيير تاريخ فاتورة المبيعات" },
            { "ChangeReceiptDateRole", "تغيير تاريخ الإيصالات" },

            // أدوار طرق الدفع
            { "TreasuryRole", "عرض وإضافة طرق الدفع" },
            { "EditTreasuryRole", "تعديل طرق الدفع" },
            { "DeleteTreasuryRole", "حذف طرق الدفع" },

            // أدوار المخازن
            { "WarehouseRole", "عرض وإضافة المخازن" },
            { "EditWarehouseRole", "تعديل المخازن" },
            { "DeleteWarehouseRole", "حذف المخازن" },
            { "ChangeWarehouseRole", "تغيير المخزن في الواجهات" },

            // أدوار العمليات المالية للموظفين
            { "EmployeeFinancialRole", "سلف مرتب وأخذ ورد العهدة" },

            // أدوار إدارة المستخدمين
            { "UserRole", "عرض وإضافة المستخدمين" },
            { "EditUserRole", "تعديل المستخدمين" },
            { "DeleteUserRole", "حذف المستخدمين" },

            { "SettingsRole", "الإعدادات" },
            { "BackupRole", "النسخ الاحتياطي" },
            { "SystemConfigRole", "إعدادات النظام" },
            { "ExpirationAlertRole", "تنبيهات انتهاء الصلاحية" },
            { "MinimumQuantityAlertRole", "تنبيهات الحد الأدنى للكمية" }
        };

        /// <summary>
        /// الحصول على الترجمة العربية لاسم الدور
        /// </summary>
        /// <param name="roleName">اسم الدور بالإنجليزية</param>
        /// <returns>الترجمة العربية للدور، أو اسم الدور الأصلي إذا لم تكن هناك ترجمة</returns>
        public static string GetArabicName(string roleName)
        {
            if (string.IsNullOrEmpty(roleName))
                return string.Empty;

            return _translations.TryGetValue(roleName, out var arabicName) ? arabicName : roleName;
        }

        /// <summary>
        /// الحصول على اسم الدور بالإنجليزية من الترجمة العربية
        /// </summary>
        /// <param name="arabicName">اسم الدور بالعربية</param>
        /// <returns>اسم الدور بالإنجليزية، أو الاسم العربي إذا لم يتم العثور على مطابقة</returns>
        public static string GetEnglishName(string arabicName)
        {
            if (string.IsNullOrEmpty(arabicName))
                return string.Empty;

            foreach (var pair in _translations)
            {
                if (pair.Value == arabicName)
                    return pair.Key;
            }

            return arabicName;
        }

        /// <summary>
        /// الحصول على قائمة بجميع الأدوار مترجمة إلى العربية
        /// </summary>
        /// <param name="roleNames">قائمة بأسماء الأدوار بالإنجليزية</param>
        /// <returns>قائمة بأسماء الأدوار مترجمة إلى العربية</returns>
        public static List<string> GetArabicNames(IEnumerable<string> roleNames)
        {
            if (roleNames == null)
                return new List<string>();

            return roleNames.Select(GetArabicName).ToList();
        }

        /// <summary>
        /// الحصول على قائمة بجميع الأدوار المتاحة مترجمة إلى العربية
        /// </summary>
        /// <returns>قائمة بأسماء الأدوار مترجمة إلى العربية</returns>
        public static List<KeyValuePair<string, string>> GetAllRoleTranslations()
        {
            return _translations.ToList();
        }
    }
}
