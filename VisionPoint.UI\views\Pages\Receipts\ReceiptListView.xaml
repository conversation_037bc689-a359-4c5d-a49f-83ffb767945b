<Page
    x:Class="VisionPoint.UI.views.Pages.Receipts.ReceiptListView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converter="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:VisionPoint.UI.views.Pages.Receipts"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:utils="clr-namespace:VisionPoint.UI.Utils"
    Title="ReceiptListView"
    d:Background="White"
    d:Height="1080"
    d:Width="1570"
    FlowDirection="RightToLeft"
    Loaded="Window_Loaded"
    mc:Ignorable="d">
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <converter:IndexToNumberConverter x:Key="IndexToNumberConverter" />
            <converter:NullToVisibilityConverter x:Key="NullToVisibilityConverter" />
            <converter:BoolToExchangeTypeConverter x:Key="BoolToExchangeTypeConverter" />
            <converter:StringVisibilityConverter x:Key="StringVisibilityConverter" />
        </ResourceDictionary>
    </Page.Resources>

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="60" />
            <RowDefinition Height="1.5*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <DockPanel Grid.ColumnSpan="2" HorizontalAlignment="Center">
            <TextBlock
                Grid.ColumnSpan="2"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="24"
                FontWeight="Bold"
                Foreground="#333333"
                Text="الإيصالات" />
        </DockPanel>

        <!--  Filters and Buttons  -->
        <Grid
            Grid.Row="1"
            Grid.RowSpan="2"
            Grid.ColumnSpan="2"
            Margin="8,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  All Periods Checkbox  -->
            <Grid
                Grid.Row="2"
                Grid.Column="0"
                Background="Transparent"
                MouseLeftButtonDown="GridCheckBox_Toggle">
                <TextBlock
                    Grid.Row="1"
                    VerticalAlignment="Top"
                    FontSize="18"
                    FontWeight="Black"
                    Foreground="{StaticResource PrimaryColor}"
                    Text="كل الفترات"
                    TextAlignment="Center" />
                <CheckBox
                    x:Name="chkAllPeriods"
                    Grid.Row="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Checked="chkAllPeriods_CheckedChanged"
                    Style="{StaticResource CircleCheckboxFL}"
                    Unchecked="chkAllPeriods_CheckedChanged" />
            </Grid>

            <!--  From Date  -->
            <Grid
                Grid.Row="2"
                Grid.Column="2"
                Grid.ColumnSpan="2">
                <DatePicker
                    x:Name="dpFromDate"
                    Grid.Row="1"
                    Height="60"
                    Margin="8,0"
                    VerticalContentAlignment="Center"
                    BorderBrush="{StaticResource PrimaryTextColor}"
                    BorderThickness="1"
                    DisplayDateStart="2000-01-01"
                    FontSize="18"
                    SelectedDateChanged="dpFromDate_SelectedDateChanged"
                    SelectedDateFormat="Short">
                    <DatePicker.Resources>
                        <Style TargetType="{x:Type DatePickerTextBox}">
                            <Setter Property="Control.Template">
                                <Setter.Value>
                                    <ControlTemplate>
                                        <TextBox
                                            x:Name="PART_TextBox"
                                            VerticalAlignment="Stretch"
                                            BorderThickness="0"
                                            FontSize="18"
                                            Foreground="{StaticResource PrimaryTextColor}"
                                            IsReadOnly="True"
                                            Style="{StaticResource txtDatePick}"
                                            Tag="من تاريخ"
                                            Text="{Binding Path=SelectedDate, StringFormat='yyyy-MM-dd', RelativeSource={RelativeSource AncestorType={x:Type DatePicker}}}" />
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </DatePicker.Resources>
                </DatePicker>
            </Grid>

            <!--  To Date  -->
            <Grid
                Grid.Row="2"
                Grid.Column="4"
                Grid.ColumnSpan="2">
                <DatePicker
                    x:Name="dpToDate"
                    Grid.Row="1"
                    Height="60"
                    Margin="8,0"
                    VerticalContentAlignment="Center"
                    BorderBrush="{StaticResource PrimaryTextColor}"
                    BorderThickness="1"
                    DisplayDateStart="2000-01-01"
                    FontSize="18"
                    SelectedDateChanged="dpToDate_SelectedDateChanged"
                    SelectedDateFormat="Short">
                    <DatePicker.Resources>
                        <Style TargetType="{x:Type DatePickerTextBox}">
                            <Setter Property="Control.Template">
                                <Setter.Value>
                                    <ControlTemplate>
                                        <TextBox
                                            x:Name="PART_TextBox"
                                            VerticalAlignment="Stretch"
                                            BorderThickness="0"
                                            FontSize="18"
                                            Foreground="{StaticResource PrimaryTextColor}"
                                            IsReadOnly="True"
                                            Style="{StaticResource txtDatePick}"
                                            Tag="إلى تاريخ"
                                            Text="{Binding Path=SelectedDate, StringFormat='yyyy-MM-dd', RelativeSource={RelativeSource AncestorType={x:Type DatePicker}}}" />
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </DatePicker.Resources>
                </DatePicker>
            </Grid>

            <!--  Type ComboBox  -->
            <Grid Grid.ColumnSpan="2">
                <ComboBox
                    x:Name="cmbType"
                    Grid.Row="1"
                    Height="60"
                    Margin="8,0"
                    VerticalContentAlignment="Center"
                    DisplayMemberPath="Name"
                    FontSize="21"
                    IsEditable="False"
                    SelectedValuePath="Id"
                    SelectionChanged="cmbType_SelectionChanged"
                    Tag="نوع الإيصال" />
            </Grid>

            <!--  Exchange Type Checkbox  -->
            <Grid
                Grid.Row="2"
                Grid.Column="1"
                Background="Transparent"
                MouseLeftButtonDown="GridCheckBox_Toggle">
                <TextBlock
                    Grid.Row="0"
                    VerticalAlignment="Top"
                    FontSize="21"
                    Text="قبض فقط"
                    TextAlignment="Center" />
                <CheckBox
                    x:Name="chkIsExchange"
                    Grid.Row="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Checked="chkIsExchange_CheckedChanged"
                    Indeterminate="chkIsExchange_CheckedChanged"
                    IsThreeState="True"
                    Style="{StaticResource CircleCheckboxFL}"
                    Unchecked="chkIsExchange_CheckedChanged" />
            </Grid>

            <!--  Warehouse ComboBox  -->
            <ComboBox
                x:Name="cmbWarehouses"
                Grid.Row="1"
                Grid.Column="2"
                Grid.ColumnSpan="2"
                Height="60"
                Margin="8,0"
                VerticalContentAlignment="Center"
                DisplayMemberPath="Name"
                FontSize="21"
                IsEditable="False"
                SelectedValuePath="Id"
                SelectionChanged="cmbWarehouses_SelectionChanged"
                Tag="المخزن" />

            <!--  Client ComboBox  -->
            <Grid Grid.Row="1" Grid.ColumnSpan="2">
                <ComboBox
                    x:Name="cmbClients"
                    Grid.Row="1"
                    Height="60"
                    Margin="8,0"
                    VerticalContentAlignment="Center"
                    DisplayMemberPath="Name"
                    FontSize="21"
                    IsEditable="True"
                    IsReadOnly="False"
                    SelectedValuePath="Id"
                    SelectionChanged="cmbClients_SelectionChanged"
                    Tag="العميل" />
            </Grid>

            <!--  Receipt Number TextBox  -->
            <TextBox
                x:Name="txtReceiptNumber"
                Grid.Column="2"
                Grid.ColumnSpan="2"
                Height="60"
                Margin="8,0"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                utils:NumericInputControl.IsNumericOnly="True"
                BorderThickness="1"
                FontSize="21"
                Tag="رقم الإيصال" />

            <!--  Statement TextBox  -->
            <TextBox
                x:Name="txtStatement"
                Grid.Row="1"
                Grid.Column="4"
                Grid.ColumnSpan="2"
                MaxHeight="60"
                Margin="8,16"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                BorderThickness="1"
                FontSize="21"
                Tag="البيان" />

            <!--  Invoice Number TextBox  -->
            <TextBox
                x:Name="txtInvoiceNumber"
                Grid.Column="4"
                Grid.ColumnSpan="2"
                Height="60"
                Margin="8,0"
                VerticalAlignment="Stretch"
                VerticalContentAlignment="Stretch"
                utils:NumericInputControl.IsNumericOnly="True"
                BorderThickness="1"
                FontSize="21"
                Tag="رقم الفاتورة" />

            <!--  Add Button  -->
            <Border
                x:Name="btnAdd"
                Grid.Row="3"
                MaxHeight="44"
                Margin="8,0"
                Background="{StaticResource PrimaryColor}"
                CornerRadius="8"
                Cursor="Hand"
                MouseLeftButtonDown="btnAdd_MouseLeftButtonDown">
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="21"
                    Foreground="White">
                    اضافة
                </TextBlock>
            </Border>

            <!--  Search Button  -->
            <Border
                x:Name="btnSearch"
                Grid.Row="3"
                Grid.Column="1"
                MaxHeight="44"
                Margin="8,0"
                Background="Transparent"
                BorderBrush="{StaticResource PrimaryColor}"
                BorderThickness="1"
                CornerRadius="8"
                Cursor="Hand"
                MouseLeftButtonDown="btnSearch_MouseLeftButtonDown">
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="21"
                    Foreground="{StaticResource PrimaryColor}">
                    بحث
                </TextBlock>
            </Border>

            <!--  Export Button  -->
            <Border
                x:Name="btnExport"
                Grid.Row="3"
                Grid.Column="2"
                MaxHeight="44"
                Margin="8,0"
                Background="Transparent"
                BorderBrush="{StaticResource PrimaryColor}"
                BorderThickness="1"
                CornerRadius="8"
                Cursor="Hand"
                MouseLeftButtonDown="btnExport_MouseLeftButtonDown">
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="21"
                    Foreground="{StaticResource PrimaryColor}">
                    تصدير
                </TextBlock>
            </Border>

            <!--  Reset Filters Button  -->
            <Border
                x:Name="btnResetFilters"
                Grid.Row="3"
                Grid.Column="3"
                Grid.ColumnSpan="2"
                MaxHeight="44"
                Margin="8,0"
                Background="Transparent"
                BorderBrush="{StaticResource PrimaryColor}"
                BorderThickness="1"
                CornerRadius="8"
                Cursor="Hand"
                MouseLeftButtonDown="btnResetFilters_MouseLeftButtonDown">
                <TextBlock
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="18"
                    Foreground="{StaticResource PrimaryColor}">
                    إعادة تعيين الفلاتر
                </TextBlock>
            </Border>
        </Grid>

        <!--  ListView  -->
        <ListView
            x:Name="list"
            Grid.Row="3"
            Grid.RowSpan="3"
            Grid.ColumnSpan="2"
            Margin="8,0"
            AlternationCount="100"
            Background="{DynamicResource PageColor}"
            BorderThickness="1"
            FontFamily="pack://application:,,,/Assets/#Cairo"
            ItemsSource="{Binding}"
            MouseDoubleClick="list_MouseDoubleClick"
            ScrollViewer.HorizontalScrollBarVisibility="Hidden"
            SizeChanged="list_SizeChanged">
            <ListView.BorderBrush>
                <SolidColorBrush Opacity="0.42" Color="Black" />
            </ListView.BorderBrush>
            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Style.Triggers>
                        <Trigger Property="Control.IsMouseOver" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="FontWeight" Value="Bold" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Control.Background" Value="{StaticResource SemiTransparentPrimaryColor}" />
                            <Setter Property="FontWeight" Value="Bold" />
                        </Trigger>
                    </Style.Triggers>
                    <Setter Property="Control.Background" Value="{DynamicResource PageColor}" />
                    <Setter Property="Control.HorizontalContentAlignment" Value="Stretch" />
                    <Setter Property="Control.VerticalContentAlignment" Value="Center" />
                    <Setter Property="MinHeight" Value="70" />
                    <Setter Property="Padding" Value="8,10" />
                    <Setter Property="Margin" Value="0,2" />
                </Style>
            </ListView.ItemContainerStyle>

            <ListView.View>
                <GridView AllowsColumnReorder="False">
                    <GridView.ColumnHeaderContainerStyle>
                        <Style TargetType="{x:Type GridViewColumnHeader}">
                            <Setter Property="IsEnabled" Value="False" />
                            <Setter Property="Height" Value="60" />
                            <Setter Property="FontWeight" Value="Bold" />
                            <Setter Property="FontFamily" Value="Segoe UI" />
                            <Setter Property="Padding" Value="10,8" />
                            <Setter Property="TextElement.FontSize" Value="13" />
                            <Style.Triggers>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="TextElement.Foreground" Value="Black" />
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </GridView.ColumnHeaderContainerStyle>

                    <GridViewColumn Header="رقم الإيصال والتاريخ">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel HorizontalAlignment="Center" Orientation="Vertical">
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="Transparent"
                                        FontSize="18"
                                        FontWeight="Bold"
                                        Text="{Binding ReceiptNumber}"
                                        TextAlignment="Center"
                                        TextWrapping="Wrap" />
                                    <TextBlock
                                        Margin="0,8,0,0"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="Transparent"
                                        FontSize="18"
                                        Foreground="{StaticResource SecondaryColor}"
                                        Text="{Binding Date, StringFormat={}{0:yyyy-MM-dd}}"
                                        TextAlignment="Center" />
                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Header="تفاصيل العملية">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel
                                    Margin="0,8"
                                    HorizontalAlignment="Center"
                                    Orientation="Vertical">
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="Transparent"
                                        FontSize="18"
                                        FontWeight="Bold"
                                        Text="{Binding Financial.Name}"
                                        TextAlignment="Center"
                                        TextWrapping="Wrap" />
                                    <TextBlock
                                        Margin="0,8,0,0"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="Transparent"
                                        FontSize="18"
                                        Foreground="Crimson"
                                        Text="{Binding IsExchange, Converter={StaticResource BoolToExchangeTypeConverter}}"
                                        TextAlignment="Center"
                                        TextWrapping="Wrap" />
                                    <TextBlock
                                        Margin="0,8,0,0"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Background="Transparent"
                                        FontSize="18"
                                        Foreground="{StaticResource PrimaryTextColor}"
                                        Text="{Binding Statement}"
                                        TextAlignment="Center"
                                        TextWrapping="Wrap"
                                        Visibility="{Binding Statement, Converter={StaticResource StringVisibilityConverter}}" />
                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Header="المبلغ">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Background="Transparent"
                                    FontSize="18"
                                    FontWeight="Bold"
                                    Text="{Binding Value, StringFormat={}{0:N2}}"
                                    TextAlignment="Center"
                                    TextWrapping="Wrap" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Header="طريقة الدفع">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel
                                    Margin="0,8"
                                    HorizontalAlignment="Center"
                                    Orientation="Vertical">
                                    <TextBlock
                                        HorizontalAlignment="Center"
                                        FontSize="14"
                                        FontWeight="Bold"
                                        Foreground="DarkBlue"
                                        Text="{Binding Treasury.Name}" />

                                    <StackPanel
                                        HorizontalAlignment="Center"
                                        Orientation="Vertical"
                                        Visibility="{Binding SourceTreasury, Converter={StaticResource NullToVisibilityConverter}}">
                                        <Border
                                            Padding="8,4"
                                            Background="LightCoral"
                                            CornerRadius="5">
                                            <TextBlock
                                                FontSize="12"
                                                FontWeight="Bold"
                                                Foreground="Black"
                                                Text="{Binding SourceTreasury.Name}"
                                                TextAlignment="Center" />
                                        </Border>
                                        <Canvas
                                            Width="24"
                                            Height="24"
                                            Margin="0,8">
                                            <Canvas.Resources>
                                                <Storyboard x:Key="ArrowAnimation" BeginTime="0:0:0">
                                                    <!--  Animate vertical line more slowly and smoothly  -->
                                                    <DoubleAnimation
                                                        Storyboard.TargetName="VerticalLine"
                                                        Storyboard.TargetProperty="StrokeDashOffset"
                                                        From="20"
                                                        To="0"
                                                        Duration="0:0:0.8">
                                                        <DoubleAnimation.EasingFunction>
                                                            <SineEase EasingMode="EaseOut" />
                                                        </DoubleAnimation.EasingFunction>
                                                    </DoubleAnimation>

                                                    <!--  Animate arrows with delay and easing  -->
                                                    <DoubleAnimation
                                                        BeginTime="0:0:0.6"
                                                        Storyboard.TargetName="LeftArrow"
                                                        Storyboard.TargetProperty="StrokeDashOffset"
                                                        From="12"
                                                        To="0"
                                                        Duration="0:0:0.8">
                                                        <DoubleAnimation.EasingFunction>
                                                            <SineEase EasingMode="EaseOut" />
                                                        </DoubleAnimation.EasingFunction>
                                                    </DoubleAnimation>

                                                    <DoubleAnimation
                                                        BeginTime="0:0:0.6"
                                                        Storyboard.TargetName="RightArrow"
                                                        Storyboard.TargetProperty="StrokeDashOffset"
                                                        From="12"
                                                        To="0"
                                                        Duration="0:0:0.8">
                                                        <DoubleAnimation.EasingFunction>
                                                            <SineEase EasingMode="EaseOut" />
                                                        </DoubleAnimation.EasingFunction>
                                                    </DoubleAnimation>
                                                </Storyboard>
                                            </Canvas.Resources>

                                            <!--  Vertical line  -->
                                            <Line
                                                x:Name="VerticalLine"
                                                Stroke="Black"
                                                StrokeDashArray="20"
                                                StrokeDashOffset="20"
                                                StrokeEndLineCap="Round"
                                                StrokeStartLineCap="Round"
                                                StrokeThickness="2"
                                                X1="12"
                                                X2="12"
                                                Y1="3"
                                                Y2="20.5" />

                                            <!--  Left arrow  -->
                                            <Line
                                                x:Name="LeftArrow"
                                                Stroke="Black"
                                                StrokeDashArray="12"
                                                StrokeDashOffset="12"
                                                StrokeEndLineCap="Round"
                                                StrokeStartLineCap="Round"
                                                StrokeThickness="2"
                                                X1="12"
                                                X2="5"
                                                Y1="21"
                                                Y2="14" />

                                            <!--  Right arrow  -->
                                            <Line
                                                x:Name="RightArrow"
                                                Stroke="Black"
                                                StrokeDashArray="12"
                                                StrokeDashOffset="12"
                                                StrokeEndLineCap="Round"
                                                StrokeStartLineCap="Round"
                                                StrokeThickness="2"
                                                X1="12"
                                                X2="19"
                                                Y1="21"
                                                Y2="14" />

                                            <!--  Trigger animation when loaded  -->
                                            <Canvas.Triggers>
                                                <EventTrigger RoutedEvent="Canvas.Loaded">
                                                    <BeginStoryboard Storyboard="{StaticResource ArrowAnimation}" />
                                                </EventTrigger>
                                            </Canvas.Triggers>
                                        </Canvas>

                                        <Border
                                            Padding="8,4"
                                            Background="LightGreen"
                                            CornerRadius="5">
                                            <TextBlock
                                                FontSize="12"
                                                FontWeight="Bold"
                                                Foreground="{StaticResource PrimaryColor}"
                                                Text="{Binding TargetTreasury.Name}"
                                                TextAlignment="Center" />
                                        </Border>
                                    </StackPanel>
                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Header="الطرف المقابل">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel
                                    Margin="0,8"
                                    HorizontalAlignment="Center"
                                    Orientation="Vertical">
                                    <Border
                                        MinWidth="180"
                                        Padding="12,6"
                                        Background="LightSkyBlue"
                                        CornerRadius="6"
                                        Visibility="{Binding Client, Converter={StaticResource NullToVisibilityConverter}}">
                                        <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                            <TextBlock
                                                FontSize="14"
                                                FontWeight="Bold"
                                                Foreground="{StaticResource PrimaryColor}"
                                                Text="عميل: " />
                                            <TextBlock
                                                FontSize="14"
                                                FontWeight="Bold"
                                                Foreground="{StaticResource PrimaryColor}"
                                                Text="{Binding Client.Name}" />
                                        </StackPanel>
                                    </Border>

                                    <Border
                                        MinWidth="180"
                                        Padding="12,6"
                                        CornerRadius="6"
                                        Visibility="{Binding Employee, Converter={StaticResource NullToVisibilityConverter}}">
                                        <Border.Background>
                                            <SolidColorBrush Opacity="0.6" Color="#1340ee" />
                                        </Border.Background>
                                        <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                            <TextBlock
                                                FontSize="14"
                                                FontWeight="Bold"
                                                Foreground="Black"
                                                Text="موظف: " />
                                            <TextBlock
                                                FontSize="14"
                                                FontWeight="Bold"
                                                Foreground="Black"
                                                Text="{Binding Employee.UserName}" />
                                        </StackPanel>
                                    </Border>

                                    <Border
                                        MinWidth="180"
                                        Padding="12,6"
                                        Background="PeachPuff"
                                        CornerRadius="6"
                                        Visibility="{Binding Expense, Converter={StaticResource NullToVisibilityConverter}}">
                                        <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                            <TextBlock
                                                FontSize="14"
                                                FontWeight="Bold"
                                                Foreground="{StaticResource PrimaryColor}"
                                                Text="مصروف: " />
                                            <TextBlock
                                                FontSize="14"
                                                FontWeight="Bold"
                                                Foreground="{StaticResource PrimaryColor}"
                                                Text="{Binding Expense.Name}" />
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Header="الملاحظات">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    Background="Transparent"
                                    FontSize="14"
                                    Foreground="DarkSlateGray"
                                    Text="{Binding Sale.Notes, FallbackValue='', TargetNullValue=''}"
                                    TextWrapping="Wrap" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Header="معلومات إضافية">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel
                                    Margin="0,12"
                                    HorizontalAlignment="Center"
                                    Orientation="Vertical">
                                    <TextBlock
                                        MinWidth="250"
                                        MaxWidth="250"
                                        Margin="0,4"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        FontSize="18"
                                        FontWeight="Bold"
                                        Foreground="Green"
                                        Text="{Binding Sale.Id, StringFormat='مبيعات: {0}', TargetNullValue='', FallbackValue=''}"
                                        TextAlignment="Center"
                                        TextWrapping="Wrap"
                                        Visibility="{Binding Sale, Converter={StaticResource NullToVisibilityConverter}}" />
                                    <TextBlock
                                        MinWidth="250"
                                        MaxWidth="250"
                                        Margin="0,4"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        FontSize="18"
                                        FontWeight="Bold"
                                        Foreground="Red"
                                        Text="{Binding Purchase.Id, StringFormat='مشتريات: {0}', TargetNullValue='', FallbackValue=''}"
                                        TextAlignment="Center"
                                        TextWrapping="Wrap"
                                        Visibility="{Binding Purchase, Converter={StaticResource NullToVisibilityConverter}}" />

                                    <TextBlock
                                        MinWidth="250"
                                        MaxWidth="250"
                                        Margin="0,6,0,0"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        FontSize="14"
                                        Foreground="Gray"
                                        Text="{Binding Warehouse.Name, StringFormat='مخزن: {0}', TargetNullValue='', FallbackValue=''}"
                                        TextAlignment="Center"
                                        TextWrapping="Wrap"
                                        Visibility="{Binding Warehouse, Converter={StaticResource NullToVisibilityConverter}}" />
                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>

                    <GridViewColumn Header="العمليات">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                    <Border
                                        Width="24"
                                        Height="16"
                                        Margin="8,0">
                                        <Path
                                            x:Name="EditButton"
                                            HorizontalAlignment="Stretch"
                                            VerticalAlignment="Stretch"
                                            Cursor="Hand"
                                            Data="{StaticResource pinedit}"
                                            Fill="{StaticResource PrimaryColor}"
                                            MouseLeftButtonDown="EditButton_Click"
                                            Stretch="Uniform" />
                                    </Border>

                                    <Border
                                        Width="24"
                                        Height="16"
                                        Margin="8,0">
                                        <Path
                                            x:Name="DeleteButton"
                                            HorizontalAlignment="Stretch"
                                            VerticalAlignment="Stretch"
                                            Cursor="Hand"
                                            Data="{StaticResource Trash}"
                                            Fill="{StaticResource errorColor}"
                                            MouseLeftButtonDown="DeleteButton_Click"
                                            Stretch="Uniform" />
                                    </Border>

                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
        </ListView>
    </Grid>
</Page>
