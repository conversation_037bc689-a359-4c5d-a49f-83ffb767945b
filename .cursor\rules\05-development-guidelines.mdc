---
description:
globs:
alwaysApply: false
---
# Development Guidelines

## MVVM Pattern
When developing new features:
1. Create/update the Model in the [Models](mdc:VisionPoint.UI/Models/) directory
2. Create/update the ViewModel in the [ViewModel](mdc:VisionPoint.UI/ViewModel/) directory
3. Create/update the View in the [views](mdc:VisionPoint.UI/views/) directory

## Database Modifications
When modifying the database structure:
1. Update the relevant model classes
2. If needed, update the [AppDbContext.cs](mdc:VisionPoint.UI/Models/AppDbContext.cs)
3. Create a migration in the [Migrations](mdc:VisionPoint.UI/Migrations/) directory

## UI Guidelines
- Use existing styles from the [styles](mdc:VisionPoint.UI/styles/) directory
- Follow the established naming patterns for new components
- For reusable UI components, consider adding them to the [Controls](mdc:VisionPoint.UI/Controls/) directory

## Business Logic
The business logic layer appears to be in the [PL](mdc:VisionPoint.UI/PL/) directory (Presentation Layer). When adding new functionality, follow the existing patterns in this directory.
