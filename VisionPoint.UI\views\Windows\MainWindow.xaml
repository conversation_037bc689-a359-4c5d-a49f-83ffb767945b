﻿<Window
    x:Class="VisionPoint.UI.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:cc="clr-namespace:VisionPoint.UI.Controls"
    xmlns:converters="clr-namespace:VisionPoint.UI.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:helper="clr-namespace:VisionPoint.UI"
    xmlns:local="clr-namespace:VisionPoint.UI"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="MainWindow"
    AllowsTransparency="True"
    Closing="Window_Closing"
    FlowDirection="RightToLeft"
    Loaded="Window_Loaded"
    WindowStartupLocation="CenterScreen"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Window.Icon>
        <BitmapImage UriSource="pack://application:,,,/Assets/Logo.png" />
    </Window.Icon>
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Themes/MainTheme.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Styles/MainStyle.xaml" />
                <ResourceDictionary Source="pack://application:,,,/VisionPoint.UI;component/Assets/Icons.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        </ResourceDictionary>
    </Window.Resources>
    <WindowChrome.WindowChrome>
        <WindowChrome
            CaptionHeight="0"
            ResizeBorderThickness="0"
            UseAeroCaptionButtons="False" />
    </WindowChrome.WindowChrome>
    <Viewbox
        CacheMode="BitmapCache"
        Stretch="Uniform"
        StretchDirection="Both">
        <Grid
            Width="1920"
            Height="1080"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Stretch"
            Background="White">

            <!--  زر التصغير - يظهر فقط للمستخدم Admin  -->



            <Border
                Background="{DynamicResource PrimaryColor}"
                BorderThickness="0"
                CornerRadius="5">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="350" />
                        <ColumnDefinition Width="1570" />
                    </Grid.ColumnDefinitions>




                    <Border
                        x:Name="borderList"
                        Grid.Column="0"
                        Width="350"
                        Height="1080"
                        Padding="16,16,0,16"
                        Background="#EEF4ED"
                        CornerRadius="5 0 0 5">
                        <Grid x:Name="GridNav">

                            <!--  Start: MenuItem  -->

                            <DockPanel
                                x:Name="MenuPanel"
                                Grid.Row="0"
                                MinWidth="109"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Stretch"
                                LastChildFill="False">

                                <Border
                                    Padding="0,16"
                                    BorderThickness="0,0,0,0.5"
                                    DockPanel.Dock="Top">
                                    <Border.BorderBrush>
                                        <SolidColorBrush Opacity="0.6" Color="Gray" />
                                    </Border.BorderBrush>
                                    <Grid>

                                        <Border
                                            x:Name="btnMinimize"
                                            Width="30"
                                            Height="30"
                                            Margin="16,0"
                                            HorizontalAlignment="Left"
                                            VerticalAlignment="Center"
                                            Panel.ZIndex="999"
                                            Background="Transparent"
                                            BorderBrush="{StaticResource PrimaryColor}"
                                            BorderThickness="2"
                                            CornerRadius="50"
                                            Cursor="Hand"
                                            MouseLeftButtonDown="btnMinimize_MouseLeftButtonDown"
                                            Visibility="{Binding IsAdminUser, RelativeSource={RelativeSource AncestorType=Window}, Converter={StaticResource BooleanToVisibilityConverter}}">
                                            <Path
                                                Width="16"
                                                Height="2"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Data="{StaticResource minimize}"
                                                Fill="{StaticResource PrimaryColor}"
                                                Stretch="Uniform" />
                                        </Border>
                                        <DockPanel
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            DockPanel.Dock="Top"
                                            LastChildFill="False">
                                            <TextBlock
                                                x:Name="txtUserName"
                                                Margin="0,4"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                DockPanel.Dock="Top"
                                                FontSize="18"
                                                FontWeight="SemiBold"
                                                Text="اسم المستخدم" />

                                        </DockPanel>
                                    </Grid>


                                </Border>

                                <Button
                                    x:Name="btnSales"
                                    Margin="8,12,8,16"
                                    HorizontalAlignment="Stretch"
                                    HorizontalContentAlignment="Left"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Click="btnSales_Click"
                                    DockPanel.Dock="Top"
                                    Style="{DynamicResource noHover}">
                                    <cc:MenuItem
                                        Fill="{StaticResource PrimaryIconColor}"
                                        GroupName="MenuItem"
                                        IconProperty="{StaticResource Sales}"
                                        IsSelected="True"
                                        StrokeThik="0"
                                        Text="المبيعات" />
                                </Button>

                                <Button
                                    x:Name="btnpurchases"
                                    Margin="8,12,8,16"
                                    HorizontalAlignment="Stretch"
                                    HorizontalContentAlignment="Left"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Click="btnpurchases_Click"
                                    DockPanel.Dock="Top"
                                    Style="{DynamicResource noHover}">
                                    <cc:MenuItem
                                        Fill="{StaticResource PrimaryIconColor}"
                                        GroupName="MenuItem"
                                        IconProperty="{StaticResource purchases}"
                                        StrokeThik="0"
                                        Text="المشتريات" />
                                </Button>

                                <Button
                                    x:Name="btnProduct"
                                    Margin="8,0,8,16"
                                    HorizontalAlignment="Stretch"
                                    HorizontalContentAlignment="Left"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Click="btnProduct_Click"
                                    DockPanel.Dock="Top"
                                    Style="{DynamicResource noHover}">
                                    <cc:MenuItem
                                        Margin="0"
                                        Fill="{StaticResource PrimaryIconColor}"
                                        GroupName="MenuItem"
                                        IconProperty="{DynamicResource product}"
                                        Paddings="0"
                                        StrokeThik="0"
                                        Text="الاصناف" />
                                </Button>

                                <Button
                                    x:Name="btnClients"
                                    Margin="8,0,8,16"
                                    HorizontalAlignment="Stretch"
                                    HorizontalContentAlignment="Left"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Click="btnClients_Click"
                                    DockPanel.Dock="Top"
                                    Style="{DynamicResource noHover}">
                                    <cc:MenuItem
                                        VerticalAlignment="Center"
                                        GroupName="MenuItem"
                                        IconProperty="{DynamicResource Emplye}"
                                        IndicatorBrush="{DynamicResource SecundaryColor}"
                                        StrokeThik="2"
                                        Text="العملاء" />
                                </Button>

                                <Button
                                    x:Name="btnWarehouses"
                                    Margin="8,0,8,16"
                                    HorizontalAlignment="Stretch"
                                    HorizontalContentAlignment="Left"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Click="btnWarehouses_Click"
                                    DockPanel.Dock="Top"
                                    Style="{DynamicResource noHover}">
                                    <cc:MenuItem
                                        VerticalAlignment="Center"
                                        Fill="Black"
                                        GroupName="MenuItem"
                                        IconProperty="{DynamicResource Store}"
                                        IndicatorBrush="{DynamicResource SecundaryColor}"
                                        Text="المخازن" />
                                </Button>

                                <Button
                                    x:Name="btnTreasuries"
                                    Margin="8,0,8,16"
                                    HorizontalAlignment="Stretch"
                                    HorizontalContentAlignment="Left"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Click="btnTreasuries_Click"
                                    DockPanel.Dock="Top"
                                    Style="{DynamicResource noHover}">
                                    <cc:MenuItem
                                        VerticalAlignment="Center"
                                        GroupName="MenuItem"
                                        IconProperty="{DynamicResource receipt}"
                                        IndicatorBrush="{DynamicResource SecundaryColor}"
                                        StrokeThik="2"
                                        Text="طرق الدفع" />
                                </Button>

                                <Button
                                    x:Name="btnUsers"
                                    Margin="8,0,8,16"
                                    HorizontalAlignment="Stretch"
                                    HorizontalContentAlignment="Left"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Click="btnUsers_Click"
                                    DockPanel.Dock="Top"
                                    Style="{DynamicResource noHover}">
                                    <cc:MenuItem
                                        VerticalAlignment="Center"
                                        GroupName="MenuItem"
                                        IconProperty="{DynamicResource users}"
                                        IndicatorBrush="{DynamicResource SecundaryColor}"
                                        StrokeThik="1"
                                        Text="المستخدمين" />
                                </Button>

                                <Button
                                    x:Name="btnReseipt"
                                    Margin="8,0,8,16"
                                    HorizontalAlignment="Stretch"
                                    HorizontalContentAlignment="Left"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Click="btnReseipt_Click"
                                    DockPanel.Dock="Top"
                                    Style="{DynamicResource noHover}">
                                    <cc:MenuItem
                                        GroupName="MenuItem"
                                        IconProperty="{DynamicResource receipt}"
                                        IndicatorBrush="{DynamicResource SecundaryColor}"
                                        StrokeThik="2"
                                        Text="الايصالات" />
                                </Button>

                                <Button
                                    x:Name="btnExpenses"
                                    Margin="8,0,8,16"
                                    HorizontalAlignment="Stretch"
                                    HorizontalContentAlignment="Left"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Click="btnExpenses_Click"
                                    DockPanel.Dock="Top"
                                    Style="{DynamicResource noHover}">
                                    <cc:MenuItem
                                        Fill="Black"
                                        GroupName="MenuItem"
                                        IconProperty="{DynamicResource BorrowMoney}"
                                        IndicatorBrush="{DynamicResource SecundaryColor}"
                                        Text="سحب/ايداع موظف" />
                                </Button>


                                <Button
                                    x:Name="btnSettings"
                                    Margin="8,0,8,16"
                                    HorizontalAlignment="Stretch"
                                    HorizontalContentAlignment="Left"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Click="btnSettings_Click"
                                    DockPanel.Dock="Top"
                                    Style="{DynamicResource noHover}">
                                    <cc:MenuItem
                                        GroupName="MenuItem"
                                        IconProperty="{DynamicResource settings}"
                                        IndicatorBrush="{DynamicResource SecundaryColor}"
                                        StrokeThik="2"
                                        Text="الاعدادات" />
                                </Button>



                                <Button
                                    x:Name="btnLogout"
                                    Margin="8,0,8,16"
                                    HorizontalAlignment="Stretch"
                                    HorizontalContentAlignment="Left"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Click="btnLogout_Click"
                                    DockPanel.Dock="Bottom"
                                    Style="{DynamicResource noHover}">
                                    <cc:MenuItem
                                        GroupName="lgout"
                                        IconProperty="{DynamicResource logout}"
                                        IsSelected="False"
                                        StrokeThik="2"
                                        Text="تسجيل الخروج" />
                                </Button>
                            </DockPanel>

                            <!--  End: MenuItem  -->

                        </Grid>
                    </Border>

                    <!--  End: Button Close | Restore | Minimize  -->
                    <Grid
                        Grid.Column="1"
                        Width="1570"
                        Height="1080"
                        Background="#F0F8FF">

                        <Border
                            Grid.Row="1"
                            Padding="0,16,16,16"
                            Background="Transparent"
                            BorderBrush="Gray"
                            BorderThickness="0">
                            <Frame
                                Name="fContainer"
                                Grid.Column="1"
                                Margin="0"
                                BorderThickness="0"
                                NavigationUIVisibility="Hidden" />
                        </Border>
                    </Grid>

                </Grid>
            </Border>

            <!--  Loading Overlay - Full Screen Coverage  -->
            <cc:LoadingOverlay
                x:Name="MainLoadingOverlay"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Stretch"
                Panel.ZIndex="9999" />
        </Grid>
    </Viewbox>
</Window>

