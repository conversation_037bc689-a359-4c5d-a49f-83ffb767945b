﻿using System.Windows;
using System.Windows.Controls;
using VisionPoint.UI.ViewModel;

namespace VisionPoint.UI.Reports.SalesReciept
{
    /// <summary>
    /// Interaction logic for PageLister.xaml
    /// </summary>
    public partial class PageLister : Page
    {
        public ParsedSaleItem ParsedItem { get; set; }

        public PageLister(SaleItemVM itemVM, int itemNumber)
        {
            InitializeComponent();

            ParsedItem = ParsedSaleItem.Parse(itemVM.Name);
            DataContext = this;

            list.Tag = itemNumber;
            list.ItemsSource = new List<SaleItemVM> { itemVM };
        }

        private void list_SizeChanged(object sender, System.Windows.SizeChangedEventArgs e)
        {
            try
            {
                if (sender is ListView listView && listView.View is GridView gView)
                {
                    var workingWidth = listView.ActualWidth - SystemParameters.VerticalScrollBarWidth;

                    var col0 = 0.15; // Barcode
                    var col1 = 0.3; // Color
                    var col2 = 0.3; // Quantity
                    var col3 = 0.10; // Additional column
                    var col4 = 0.15; // Additional column

                    for (int i = 0; i < gView.Columns.Count; i++)
                    {
                        var width = i switch
                        {
                            0 => col0,
                            1 => col1,
                            2 => col2,
                            3 => col3,
                            4 => col4,
                            _ => 0.25
                        };
                        gView.Columns[i].Width = workingWidth * width;
                    }
                }
            }
            catch
            {
                return;
            }
        }
    }

    public class ParsedSaleItem
    {
        public string DisplayName { get; set; }          // عدسة طبية
        public string EyeSide { get; set; }              // يمين or يسار (optional)
        public string SPH { get; set; }
        public string CYL { get; set; }
        public string Axis { get; set; }
        public string ADD { get; set; }
        public string POW { get; set; }
        public string DisplayedType { get; set; }        // Lens type

        public static ParsedSaleItem Parse(string rawName)
        {
            var result = new ParsedSaleItem();
            if (string.IsNullOrWhiteSpace(rawName))
                return result;

            // Extract EyeSide (يمين or يسار)
            if (rawName.Contains("يمين"))
            {
                result.EyeSide = "يمين";
            }
            else if (rawName.Contains("يسار"))
            {
                result.EyeSide = "يسار";
            }

            // Remove EyeSide from raw name
            var nameWithoutEye = rawName.Replace("يمين", "").Replace("يسار", "").Trim();

            // Split name and potential type/details at the "|"
            var nameParts = nameWithoutEye.Split('|', 2);

            string detailsPart = string.Empty;

            if (nameParts.Length > 1)
            {
                // The part after '|' contains the type and potentially prescription details
                detailsPart = nameParts[1].Trim();
                result.DisplayName = nameParts[0].Trim();  // The part before "|" is the DisplayName
            }
            else
            {
                // If no "|" is present, the whole string (after removing eye side) is the details part
                detailsPart = nameParts[0].Trim();
                result.DisplayName = string.Empty; // Or you might want to handle this case differently
            }

            // Now, split the details part by the first '-' to separate type from prescription
            var detailsParts = detailsPart.Split('-', 2);

            if (detailsParts.Length > 0)
            {
                // The first part before the first '-' is the DisplayedType
                result.DisplayedType = detailsParts[0].Trim();

                // If there's a part after the first '-', it contains the prescription details
                if (detailsParts.Length > 1)
                {
                    var spec = detailsParts[1];

                    void Extract(string label, Action<string> assign)
                    {
                        var match = System.Text.RegularExpressions.Regex.Match(spec, $@"\b{label}[:：]?\s*(-?\d+(\.\d+)?)");
                        if (match.Success)
                        {
                            assign(match.Groups[1].Value);
                        }
                    }

                    Extract("SPH", v => result.SPH = v);
                    Extract("CYL", v => result.CYL = v);
                    Extract("Axis", v => result.Axis = v);
                    Extract("ADD", v => result.ADD = v);
                    Extract("POW", v => result.POW = v);
                }
            }

            // The RemovePrescriptionDetails helper is no longer strictly necessary for DisplayedType
            // if the split by '-' is done correctly to isolate the type.
            // However, keeping it might be useful for robustness or other scenarios.
            // For your specific requirement, the split by '-' handles it.

            return result;
        }

        // Helper method remains the same (can be useful for cleaning other strings if needed)
        private static string RemovePrescriptionDetails(string input)
        {
            var prescriptionPattern = @"\s*\b(SPH|CYL|Axis|ADD|POW)[:：]?\s*-?\d+(\.\d+)?";
            return System.Text.RegularExpressions.Regex.Replace(input, prescriptionPattern, "").Trim();
        }
    }

}
