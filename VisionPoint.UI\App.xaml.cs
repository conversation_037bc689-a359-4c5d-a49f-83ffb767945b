﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System.Windows;
using System.Windows.Threading;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.Properties;
using VisionPoint.UI.Utils;
using VisionPoint.UI.views.Dialogs;
using VisionPoint.UI.views.Windows;

namespace VisionPoint.UI;

public partial class App : Application
{
    private ServiceProvider serviceProvider;
    public App()
    {
        ServiceCollection services = new();
        services.ConfigureServices();
        serviceProvider = services.BuildServiceProvider();
        // Start background tasks service


        // Register to handle page/window loaded events
        this.Startup += App_Startup;
        this.Exit += OnExit;

        // Initialize ServiceLocator
        ServiceLocator.Initialize(serviceProvider);

        // LoginWindow will be shown after license validation in OnStartup
    }

    private void App_Startup(object sender, StartupEventArgs e)
    {
        // Register handlers for Window and Page loaded events
        EventManager.RegisterClassHandler(typeof(Window), Window.LoadedEvent,
            new RoutedEventHandler((s, args) =>
            {
                if (s is Window window)
                {
                    window.ApplyNumericValidationToAllTextBoxes();
                }
            }));
    }

    protected override async void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);
        // Seed data initialization is now handled in the login window

        // Check MAC addresses against the 't' file in AppData\Local
        try
        {
            var networkService = serviceProvider.GetRequiredService<NetworkService>();

            // Read the 't' file from AppData\Local
            string fileContent = networkService.ReadFileFromAppDataLocal("Micro.dll");

            // Compare MAC addresses with file content
            bool isLicenseValid = !string.IsNullOrEmpty(fileContent) &&
                                  networkService.CompareMacAddressesWithContent(fileContent);

            if (!isLicenseValid)
            {
                // Show message and close application
                ErrorBox.Show("يرجى شراء المنتج قبل استخدامه", "خطأ في التحقق", true);

                // Shutdown the application
                Current.Shutdown();
            }
        }
        catch (Exception ex)
        {
            // Show message and close application for any error
            ErrorBox.Show("يرجى شراء المنتج قبل استخدامه", "خطأ في التحقق", true);

            // Shutdown the application
            Current.Shutdown();
        }
        // If license is valid, continue with normal application flow
        int retryCount = 0;
        int maxRetries = 5; // Number of retry attempts
        bool systemLoaded = false;
        int RetryDelayMilliseconds = 5000;

        while (!systemLoaded)
        {
            try
            {
                var backgroundTaskService = serviceProvider.GetRequiredService<Services.BackgroundTaskService>();
                backgroundTaskService.Start();

                // Show login window only if license is valid
                var loginWindow = serviceProvider.GetRequiredService<LoginWindow>();
                loginWindow.Show();

                systemLoaded = true; // Mark as loaded if no exception occurs
            }
            catch (Exception ex)
            {
                // Show message that system is not loaded yet and trying again
                ErrorBox.Show("الرجاء الانتظار حتى يتم تحميل النظام. جاري إعادة المحاولة...", "خطأ في تحميل النظام", false); // 'false' for not shutting down



                // Wait before retrying to avoid busy-looping
                Thread.Sleep(RetryDelayMilliseconds);
            }
        }
    }

    private DispatcherTimer _retryTimer;
    private const int RetryDelaySeconds = 5; // 5 seconds delay
    // Simulated check for system readiness
    private void TryLoadSystem()
    {
        try
        {
            var backgroundTaskService = serviceProvider.GetRequiredService<Services.BackgroundTaskService>();
            backgroundTaskService.Start();

            // Show login window only if license is valid
            var loginWindow = serviceProvider.GetRequiredService<LoginWindow>();
            loginWindow.Show();

            // Stop the timer if it was running (in case of a previous retry attempt)
            if (_retryTimer != null)
            {
                _retryTimer.Stop();
                _retryTimer = null; // Clear the timer
            }
            DialogBox.Show("تحميل النظام", "تم تحميل النظام بنجاح!");
        }
        catch (Exception ex)
        {
            ErrorBox.Show("الرجاء الانتظار حتى يتم تحميل النظام. جاري إعادة المحاولة...", "خطأ في تحميل النظام", false);



            // Start or reset the retry timer
            if (_retryTimer == null)
            {
                _retryTimer = new DispatcherTimer();
                _retryTimer.Interval = TimeSpan.FromSeconds(RetryDelaySeconds);
                _retryTimer.Tick += (sender, args) => TryLoadSystem(); // Re-attempt on tick
            }
            _retryTimer.Start();
        }
    }



    private void OnExit(object sender, ExitEventArgs e)
    {
        try
        {
            // Stop background tasks service
            var backgroundTaskService = serviceProvider.GetRequiredService<Services.BackgroundTaskService>();
            backgroundTaskService.Stop();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error stopping background tasks: {ex.Message}");
        }

        // Dispose of services if needed
        if (serviceProvider is IDisposable disposable)
        {
            disposable.Dispose();
        }
    }
}


public static class ServiceCollectionExtensions
{
    public static void ConfigureServices(this ServiceCollection services)
    {
        services.AddLogging();
        services.AddDbContext<AppDbContext>(options =>
            options.UseSqlServer(Settings.Default.ConnectionString, sqlOptions =>
            {
                // تعطيل OUTPUT clause لحل مشكلة SQL Server Merge Replication triggers
                sqlOptions.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
            }), ServiceLifetime.Transient);
        services.AddIdentity<User, Role>(x => x.SignIn.RequireConfirmedEmail = false)
            .AddEntityFrameworkStores<AppDbContext>()
            .AddDefaultTokenProviders();

        services.Configure<IdentityOptions>(options =>
        {
            options.Password.RequiredLength = 1;
            options.Password.RequireNonAlphanumeric = false;
            options.Password.RequireDigit = false;
            options.Password.RequireLowercase = false;
            options.Password.RequireUppercase = false;
            options.Lockout.MaxFailedAccessAttempts = 3;
            options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(3);
            options.SignIn.RequireConfirmedEmail = false;
            options.SignIn.RequireConfirmedPhoneNumber = false;
            options.SignIn.RequireConfirmedAccount = false;
            options.User.RequireUniqueEmail = false;
        });
        services.AddTransient<LoginWindow>(provider =>
            new LoginWindow(
                provider.GetRequiredService<UserService>(),
                provider.GetRequiredService<IServiceProvider>()));
        services.AddTransient<UserService>(provider =>
            new UserService(
                provider.GetRequiredService<UserManager<User>>(),
                provider.GetRequiredService<RoleManager<Role>>()));
        services.AddTransient<ColorService>();
        services.AddTransient<ClientService>();
        services.AddTransient<LensService>();
        services.AddTransient<ProductService>();
        services.AddTransient<ServiceService>();
        services.AddTransient<PurchaseService>();
        services.AddTransient<TreasuryService>();
        services.AddTransient<WarehouseService>();
        services.AddTransient<ReceiptService>();
        services.AddTransient<SaleService>();
        services.AddTransient<DiscountManager>();
        services.AddTransient<NetworkService>();
        services.AddTransient<ExpenseService>();
        services.AddTransient<SalaryService>();

        // Register the BusyService as a singleton so it can be accessed from anywhere
        services.AddSingleton<Services.BusyService>();

        // Register the BackgroundTaskService as a singleton
        services.AddSingleton<Services.BackgroundTaskService>();
    }
}