﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VisionPoint.UI.Models;

namespace VisionPoint.UI.ViewModel;

public class LensVM
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public bool Sphere { get; set; }
    public bool Cylinder { get; set; }
    public bool Power { get; set; }
    [Precision(18, 3)] public decimal? Axis { get; set; }
    [Precision(18, 3)] public decimal? Addtion { get; set; }
    [Precision(18, 3)] public decimal? BC { get; set; }
    [Precision(18, 3)] public decimal? Dia { get; set; }
    public bool IsAbleToEditExp { get; set; }=true;
    public bool Exp { get; set; }
    public int MinimumQuantity { get; set; } = 0;

    // Categoría
    public int? CategoryId { get; set; }
    public string CategoryName { get; set; } = string.Empty;

    public int TotalQuantity { get; set; }
}
