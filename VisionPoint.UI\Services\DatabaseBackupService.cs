using Microsoft.Data.SqlClient;
using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using VisionPoint.UI.Properties;
using VisionPoint.UI.views.Dialogs;

namespace VisionPoint.UI.Services
{
    public class DatabaseBackupService
    {
        /// <summary>
        /// Creates a backup of the database
        /// </summary>
        /// <param name="backupPath">Optional path to save the backup. If not provided, a dialog will be shown to select the path.</param>
        /// <returns>A tuple with success status and message</returns>
        public async Task<(bool Success, string Message)> CreateBackupAsync(string backupPath = null)
        {
            try
            {
                // Parse connection string to get database name and server
                var connectionString = Settings.Default.ConnectionString;
                var builder = new SqlConnectionBuilder(connectionString);
                var databaseName = builder.InitialCatalog;

                if (string.IsNullOrEmpty(databaseName))
                {
                    return (false, "لم يتم العثور على اسم قاعدة البيانات في سلسلة الاتصال");
                }

                // If no backup path provided, show dialog to select one
                if (string.IsNullOrEmpty(backupPath))
                {
                    var saveFileDialog = new SaveFileDialog
                    {
                        Filter = "Backup Files (*.bak)|*.bak",
                        FileName = $"{databaseName}_Backup_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.bak",
                        Title = "حفظ نسخة احتياطية من قاعدة البيانات"
                    };

                    if (saveFileDialog.ShowDialog() != true)
                    {
                        return (false, "تم إلغاء عملية النسخ الاحتياطي");
                    }

                    backupPath = saveFileDialog.FileName;
                }

                // Ensure directory exists
                var directory = Path.GetDirectoryName(backupPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Create backup using SQL command
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    var backupCommand = new SqlCommand($@"
                        BACKUP DATABASE [{databaseName}]
                        TO DISK = N'{backupPath}'
                        WITH NOFORMAT, NOINIT, NAME = N'{databaseName} Backup',
                        SKIP, NOREWIND, NOUNLOAD, STATS = 10", connection);

                    await backupCommand.ExecuteNonQueryAsync();
                }

                return (true, $"تم إنشاء نسخة احتياطية بنجاح في: {backupPath}");
            }
            catch (Exception ex)
            {
                return (false, $"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {ex.Message}");
            }
        }

        /// <summary>
        /// Restores a database from a backup file
        /// </summary>
        /// <param name="backupPath">Optional path to the backup file. If not provided, a dialog will be shown to select the file.</param>
        /// <returns>A tuple with success status and message</returns>
        public async Task<(bool Success, string Message)> RestoreBackupAsync(string backupPath = null)
        {
            try
            {
                // Parse connection string to get database name and server
                var connectionString = Settings.Default.ConnectionString;
                var builder = new SqlConnectionBuilder(connectionString);
                var databaseName = builder.InitialCatalog;

                if (string.IsNullOrEmpty(databaseName))
                {
                    return (false, "لم يتم العثور على اسم قاعدة البيانات في سلسلة الاتصال");
                }

                // If no backup path provided, show dialog to select one
                if (string.IsNullOrEmpty(backupPath))
                {
                    var openFileDialog = new OpenFileDialog
                    {
                        Filter = "Backup Files (*.bak)|*.bak",
                        Title = "اختر ملف النسخة الاحتياطية"
                    };

                    if (openFileDialog.ShowDialog() != true)
                    {
                        return (false, "تم إلغاء عملية استعادة النسخة الاحتياطية");
                    }

                    backupPath = openFileDialog.FileName;
                }

                // Check if file exists
                if (!File.Exists(backupPath))
                {
                    return (false, "ملف النسخة الاحتياطية غير موجود");
                }

                // Create a connection to the master database to restore
                var masterConnectionString = connectionString.Replace($"Database={databaseName}", "Database=master");

                using (var connection = new SqlConnection(masterConnectionString))
                {
                    await connection.OpenAsync();

                    // Set database to single user mode
                    var singleUserCommand = new SqlCommand($@"
                        ALTER DATABASE [{databaseName}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE", connection);
                    await singleUserCommand.ExecuteNonQueryAsync();

                    // Restore database
                    var restoreCommand = new SqlCommand($@"
                        RESTORE DATABASE [{databaseName}]
                        FROM DISK = N'{backupPath}'
                        WITH FILE = 1, NOUNLOAD, REPLACE, STATS = 10", connection);
                    await restoreCommand.ExecuteNonQueryAsync();

                    // Set database back to multi user mode
                    var multiUserCommand = new SqlCommand($@"
                        ALTER DATABASE [{databaseName}] SET MULTI_USER", connection);
                    await multiUserCommand.ExecuteNonQueryAsync();
                }

                return (true, "تم استعادة النسخة الاحتياطية بنجاح");
            }
            catch (Exception ex)
            {
                return (false, $"حدث خطأ أثناء استعادة النسخة الاحتياطية: {ex.Message}");
            }
        }

        /// <summary>
        /// Performs an automatic backup if enabled and due
        /// </summary>
        /// <returns>A tuple with success status and message</returns>
        public async Task<(bool Success, string Message)> PerformAutoBackupIfNeededAsync()
        {
            try
            {
                // Check if auto backup is enabled
                if (!Settings.Default.AutoBackupEnabled)
                {
                    return (true, "النسخ الاحتياطي التلقائي غير مفعل");
                }

                // Check if backup path is set
                string backupPath = Settings.Default.AutoBackupPath;
                if (string.IsNullOrEmpty(backupPath))
                {
                    return (false, "مسار النسخ الاحتياطي التلقائي غير محدد");
                }

                // Check if we need to perform a backup today
                DateTime lastBackupDate = Settings.Default.LastAutoBackupDate;
                DateTime today = DateTime.Today;

                if (lastBackupDate.Date >= today)
                {
                    return (true, "تم إجراء النسخ الاحتياطي التلقائي اليوم بالفعل");
                }

                // Create the backup directory if it doesn't exist
                if (!Directory.Exists(backupPath))
                {
                    Directory.CreateDirectory(backupPath);
                }

                // Parse connection string to get database name
                var connectionString = Settings.Default.ConnectionString;
                var builder = new SqlConnectionBuilder(connectionString);
                var databaseName = builder.InitialCatalog;

                if (string.IsNullOrEmpty(databaseName))
                {
                    return (false, "لم يتم العثور على اسم قاعدة البيانات في سلسلة الاتصال");
                }

                // Create backup file path with date
                string backupFileName = $"{databaseName}_AutoBackup_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.bak";
                string fullBackupPath = Path.Combine(backupPath, backupFileName);

                // Create backup
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    var backupCommand = new SqlCommand($@"
                        BACKUP DATABASE [{databaseName}]
                        TO DISK = N'{fullBackupPath}'
                        WITH NOFORMAT, NOINIT, NAME = N'{databaseName} Auto Backup',
                        SKIP, NOREWIND, NOUNLOAD, STATS = 10", connection);

                    await backupCommand.ExecuteNonQueryAsync();
                }

                // Update last backup date
                Settings.Default.LastAutoBackupDate = today;
                Settings.Default.Save();

                // Manage backup files count
                await ManageBackupFilesCountAsync(backupPath);

                return (true, $"تم إنشاء نسخة احتياطية تلقائية بنجاح في: {fullBackupPath}");
            }
            catch (Exception ex)
            {
                return (false, $"حدث خطأ أثناء إنشاء النسخة الاحتياطية التلقائية: {ex.Message}");
            }
        }

        /// <summary>
        /// Manages the number of backup files, deleting the oldest ones if the maximum count is exceeded
        /// </summary>
        /// <param name="backupPath">The directory containing backup files</param>
        private async Task ManageBackupFilesCountAsync(string backupPath)
        {
            try
            {
                int maxBackupCount = Settings.Default.AutoBackupMaxCount;
                if (maxBackupCount <= 0)
                {
                    maxBackupCount = 5; // Default to 5 if invalid value
                }

                // Get all backup files
                var backupFiles = Directory.GetFiles(backupPath, "*.bak")
                    .Select(f => new FileInfo(f))
                    .OrderByDescending(f => f.CreationTime)
                    .ToList();

                // Delete oldest files if count exceeds maximum
                if (backupFiles.Count > maxBackupCount)
                {
                    for (int i = maxBackupCount; i < backupFiles.Count; i++)
                    {
                        try
                        {
                            File.Delete(backupFiles[i].FullName);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error deleting old backup file: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error managing backup files: {ex.Message}");
            }
        }
    }

    // Helper class to parse connection string
    internal class SqlConnectionBuilder
    {
        public string DataSource { get; private set; }
        public string InitialCatalog { get; private set; }
        public string UserId { get; private set; }
        public string Password { get; private set; }
        public bool IntegratedSecurity { get; private set; }

        public SqlConnectionBuilder(string connectionString)
        {
            var parts = connectionString.Split(';');
            foreach (var part in parts)
            {
                if (string.IsNullOrWhiteSpace(part)) continue;

                var keyValue = part.Split('=');
                if (keyValue.Length != 2) continue;

                var key = keyValue[0].Trim().ToLower();
                var value = keyValue[1].Trim();

                switch (key)
                {
                    case "server":
                    case "data source":
                        DataSource = value;
                        break;
                    case "database":
                    case "initial catalog":
                        InitialCatalog = value;
                        break;
                    case "user id":
                    case "uid":
                        UserId = value;
                        break;
                    case "password":
                    case "pwd":
                        Password = value;
                        break;
                    case "integrated security":
                    case "trusted_connection":
                        IntegratedSecurity = value.ToLower() == "true" || value.ToLower() == "sspi";
                        break;
                }
            }
        }
    }
}
