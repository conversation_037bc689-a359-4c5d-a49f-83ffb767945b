---
description:
globs:
alwaysApply: false
---
# VisionPoint Project Overview

VisionPoint is a WPF desktop application that follows the MVVM (Model-View-ViewModel) architecture pattern. The application appears to be for managing an optical store, with features for handling products (glasses/lenses), prescriptions, sales, and purchases.

## Main Components
- **Models**: Data entities and database context
- **ViewModel**: View models that implement the MVVM pattern
- **Views**: The UI components (Windows, Pages, Dialogs)
- **Controls**: Custom UI controls
- **PL**: Likely contains the business logic layer
- **Helper**: Utility and helper classes
- **Themes & Styles**: Application styling

## Project Entry Point
The application starts with [App.xaml](mdc:VisionPoint.UI/App.xaml) and [App.xaml.cs](mdc:VisionPoint.UI/App.xaml.cs).

## Database
The application uses Entity Framework, with [AppDbContext.cs](mdc:VisionPoint.UI/Models/AppDbContext.cs) as the main database context.
