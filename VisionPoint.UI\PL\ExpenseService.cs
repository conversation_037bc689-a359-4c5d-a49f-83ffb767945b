using Microsoft.EntityFrameworkCore;
using VisionPoint.UI.Converters;
using VisionPoint.UI.Extensions;
using VisionPoint.UI.Models;

namespace VisionPoint.UI.PL
{
    public class ExpenseService : IDisposable
    {
        private readonly AppDbContext _context = ServiceLocator.GetService<AppDbContext>();
        private bool _disposed = false;

        public ExpenseService()
        {
        }
        public async Task<List<Expense>> GetAllExpensesAsync()
        {
            return await _context.Expenses.ToListAsyncWithBusy("GetAllExpenses");
        }

        public async Task<Expense> GetExpenseByIdAsync(int id)
        {
            return await _context.Expenses.FindAsyncWithBusy(id);
        }

        public async Task<(bool State, string Message)> AddExpenseAsync(Expense expense)
        {
            expense.CreatedById = CurrentUser.Id;
            expense.ModifiedById = CurrentUser.Id;
            expense.CreatedAt = DateTime.Now;
            expense.UpdatedAt = DateTime.Now;
            await _context.Expenses.AddAsyncWithBusy(expense);
            return await _context.SaveWithTransactionAndBusy("AddExpense");
        }

        public async Task<(bool State, string Message)> UpdateExpenseAsync(Expense expense)
        {
            expense.ModifiedById = CurrentUser.Id;
            expense.UpdatedAt = DateTime.Now;
            _context.Expenses.UpdateWithBusy(expense);
            return await _context.SaveWithTransactionAndBusy("UpdateExpense");
        }

        public async Task<(bool State, string Message)> DeleteExpenseAsync(int id)
        {
            var expense = await _context.Expenses.FindAsyncWithBusy(id);
            if (expense == null)
            {
                return (false, "المصروف غير موجود");
            }

            // التحقق من وجود إيصالات مرتبطة بالمصروف
            bool hasReceipts = await _context.Receipts.AnyAsyncWithBusy(r => r.ExpenseId == id, "CheckExpenseReceipts");

            if (hasReceipts)
            {
                return (false, "لا يمكن حذف المصروف لأنه مرتبط بإيصالات");
            }

            _context.Expenses.RemoveWithBusy(expense);
            return await _context.SaveWithTransactionAndBusy("DeleteExpense");
        }

        // Implement IDisposable pattern
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                    _context?.Dispose();
                }

                // Free unmanaged resources
                _disposed = true;
            }
        }

        // Destructor
        ~ExpenseService()
        {
            Dispose(false);
        }
    }
}
