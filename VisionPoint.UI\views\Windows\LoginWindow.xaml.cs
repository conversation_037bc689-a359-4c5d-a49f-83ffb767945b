﻿using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Threading;
using VisionPoint.UI.Helper;
using VisionPoint.UI.Models;
using VisionPoint.UI.PL;
using VisionPoint.UI.views.Dialogs;
using VisionPoint.UI.views.Pages.Expire;
using VisionPoint.UI.views.Pages.MinimumQuantity;

namespace VisionPoint.UI.views.Windows
{

    public partial class LoginWindow : Window
    {
        UserService service;
        private readonly IServiceProvider _serviceProvider;


        public LoginWindow(UserService uService, IServiceProvider serviceProvider)
        {
            InitializeComponent();
            service = uService;
            _serviceProvider = serviceProvider;
            InitializeSeedData();
        }

        private async void InitializeSeedData()
        {
            try
            {
                using (var scope = _serviceProvider.CreateScope())
                {
                    var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                    var userManager = scope.ServiceProvider.GetRequiredService<UserManager<User>>();
                    var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<Role>>();
                    await SeedData.Initialize(dbContext, userManager, roleManager);
                }
            }
            catch (System.Exception ex)
            {
                Dispatcher.Invoke(() =>
                {
                    ErrorBox.Show("حدث خطأ أثناء تهيئة البيانات", "خطأ", true);
                });
            }
            finally
            {
                Dispatcher.Invoke(() =>
                {
                    txtUserName.Focus();
                });
            }
        }

        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {

        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            txtUserName.Focus();
            TxtPassword.Password = "Admin123*";
        }

        // طريقة لتعطيل جميع الأزرار
        private void DisableAllButtons()
        {
            btnLogin.IsEnabled = false;
            btnClose.IsEnabled = false;
        }

        // طريقة لتفعيل جميع الأزرار
        private void EnableAllButtons()
        {
            btnLogin.IsEnabled = true;
            btnClose.IsEnabled = true;
        }

        private async void btnLogin_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            await Login();
        }

        private async Task Login()
        {
            DisableAllButtons();
            try
            {
                if (string.IsNullOrEmpty(txtUserName.Text))
                {
                    ErrorBox.Show("الرجاء ادخال اسم المستخدم.", "خطاء في عملية الادخال", false);
                    txtUserName.Focus();
                    return; // Exit early to prevent further processing
                }

                if (string.IsNullOrEmpty(TxtPassword.Password))
                {
                    ErrorBox.Show("الرجاء ادخال كلمة المرور.", "خطاء في عملية الادخال", false);
                    TxtPassword.Focus();
                    return; // Exit early to prevent further processing
                }

                bool loginSuccessful = await service.Login(txtUserName.Text, TxtPassword.Password);

                if (loginSuccessful)
                {
                    // Clear fields and focus on username if login is successful
                    txtUserName.Focus();
                    txtUserName.Text = string.Empty;
                    TxtPassword.Password = string.Empty;

                    // Check for expiring items
                    await CheckForExpiringItemsAndNotify();

                    // Check for items below minimum quantity
                    await CheckForItemsBelowMinimumQuantityAndNotify();
                    this.Hide();
                    // Show the main window and hide the current window
                    MainWindow mainWindow = new MainWindow();
                    mainWindow.ShowDialog();
                    this.Show();
                }
                else
                {
                    // Handle invalid login (e.g., incorrect credentials)
                    ErrorBox.Show("خطاء في اسم المستخدم او كلمة المرور", "فشل التسجيل", true);
                }
            }
            catch (Exception ex)
            {
                ErrorBox.Show($"حدث خطأ أثناء تسجيل الدخول: {ex.Message}", "خطأ", true);
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void btnClose_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DisableAllButtons();
            try
            {
                Application.Current.Shutdown();
            }
            finally
            {
                EnableAllButtons();
            }
        }

        private void txtUserName_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                // Call the login function when Enter is pressed in PasswordBox
                TxtPassword.Focus();
            }
        }

        private async void TxtPassword_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                // Call the login function when Enter is pressed in PasswordBox
                await Login();
            }
        }

        /// <summary>
        /// Checks for expiring items and notifies the user if any are found
        /// </summary>
        private async Task CheckForExpiringItemsAndNotify()
        {
            try
            {
                // Check for expiring items with warehouse information
                var (hasExpiringProducts, hasExpiringLenses, warehouseInfo) = await ExpirationChecker.CheckForExpiringItems();

                // If there are expiring items, ask the user if they want to view them
                if (hasExpiringProducts || hasExpiringLenses)
                {
                    string message = "يوجد ";
                    if (hasExpiringProducts && hasExpiringLenses)
                        message += "منتجات وعدسات";
                    else if (hasExpiringProducts)
                        message += "منتجات";
                    else
                        message += "عدسات";

                    message += " قاربت على انتهاء الصلاحية";

                    // Add warehouse information if available
                    if (!string.IsNullOrEmpty(warehouseInfo))
                    {
                        message += $"\n{warehouseInfo}";
                    }

                    message += "\n\nهل تريد عرضها؟";

                    // Ask the user if they want to view the expiring items
                    if (QuestionBox.Show("تنبيه الصلاحية", message) == MessageBoxResult.Yes)
                    {
                        // Create and show the ExpireWindow
                        ExpireWindow expireWindow = new ExpireWindow(hasExpiringProducts);
                        // Show the window
                        expireWindow.ShowDialog();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في التحقق من الصلاحيات: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks for items below minimum quantity and notifies the user if any are found
        /// </summary>
        private async Task CheckForItemsBelowMinimumQuantityAndNotify()
        {
            try
            {
                // فحص صلاحية عرض تنبيهات الحد الأدنى
                bool hasMinimumQuantityAlertPermission = CurrentUser.HasRole("Admin") || CurrentUser.HasRole("MinimumQuantityAlertRole");
                if (!hasMinimumQuantityAlertPermission)
                {
                    return; // المستخدم ليس لديه صلاحية لرؤية التنبيهات
                }

                // Check for items below minimum quantity
                var (hasProductsBelowMinimum, hasLensesBelowMinimum) = await MinimumQuantityChecker.CheckForItemsBelowMinimumQuantity();

                // If there are items below minimum quantity, ask the user if they want to view them
                if (hasProductsBelowMinimum || hasLensesBelowMinimum)
                {
                    string message = "يوجد ";
                    if (hasProductsBelowMinimum && hasLensesBelowMinimum)
                        message += "منتجات وعدسات";
                    else if (hasProductsBelowMinimum)
                        message += "منتجات";
                    else
                        message += "عدسات";

                    message += " وصلت للحد الأدنى للكمية. هل تريد عرضها؟";

                    // Ask the user if they want to view the items below minimum quantity
                    if (QuestionBox.Show("تنبيه الحد الأدنى للكمية", message) == MessageBoxResult.Yes)
                    {
                        // Create the MinimumQuantityWindow with the appropriate setting
                        MinimumQuantityWindow minimumQuantityWindow = new MinimumQuantityWindow(hasProductsBelowMinimum);

                        // Show the window
                        minimumQuantityWindow.ShowDialog();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في التحقق من الحد الأدنى للكمية: {ex.Message}");
            }
        }
    }

    // dont touch
    public class PasswordBoxMonitor : DependencyObject
    {
        public static bool GetIsMonitoring(DependencyObject obj)
        {
            return (bool)obj.GetValue(IsMonitoringProperty);
        }

        public static void SetIsMonitoring(DependencyObject obj, bool value)
        {
            obj.SetValue(IsMonitoringProperty, value);
        }

        public static readonly DependencyProperty IsMonitoringProperty =
            DependencyProperty.RegisterAttached("IsMonitoring", typeof(bool), typeof(PasswordBoxMonitor), new UIPropertyMetadata(false, OnIsMonitoringChanged));



        public static int GetPasswordLength(DependencyObject obj)
        {
            return (int)obj.GetValue(PasswordLengthProperty);
        }

        public static void SetPasswordLength(DependencyObject obj, int value)
        {
            obj.SetValue(PasswordLengthProperty, value);
        }

        public static readonly DependencyProperty PasswordLengthProperty =
            DependencyProperty.RegisterAttached("PasswordLength", typeof(int), typeof(PasswordBoxMonitor), new UIPropertyMetadata(0));

        private static void OnIsMonitoringChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var pb = d as PasswordBox;
            if (pb == null)
            {
                return;
            }
            if ((bool)e.NewValue)
            {
                pb.PasswordChanged += PasswordChanged;
            }
            else
            {
                pb.PasswordChanged -= PasswordChanged;
            }
        }

        static void PasswordChanged(object sender, RoutedEventArgs e)
        {
            var pb = sender as PasswordBox;
            if (pb == null)
            {
                return;
            }
            SetPasswordLength(pb, pb.Password.Length);
        }


    }
}
